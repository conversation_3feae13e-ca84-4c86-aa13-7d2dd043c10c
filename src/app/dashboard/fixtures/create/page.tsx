'use client';

import { useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  InputField,
  SelectField,
  FormSection,
  FormActions
} from '@/components/ui/form-field';
import { SearchableSelectField } from '@/components/ui/SearchableSelectField';
import { Skeleton } from '@/components/ui/skeleton';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { fixturesApi } from '@/lib/api/fixtures';
import { leaguesApi } from '@/lib/api/leagues';
import { teamsApi } from '@/lib/api/teams';
import { ArrowLeft, Save, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { FixtureNavigation } from '@/components/fixtures/FixtureNavigation';

// Status options (same as edit page)
const statusOptions = [
  { value: 'TBD', label: 'Time To Be Defined' },
  { value: 'NS', label: 'Not Started' },
  { value: 'ST', label: 'Scheduled' },
  { value: '1H', label: 'First Half' },
  { value: 'HT', label: 'Halftime' },
  { value: '2H', label: 'Second Half' },
  { value: 'ET', label: 'Extra Time' },
  { value: 'BT', label: 'Break Time' },
  { value: 'P', label: 'Penalty In Progress' },
  { value: 'SUSP', label: 'Match Suspended' },
  { value: 'INT', label: 'Match Interrupted' },
  { value: 'FT', label: 'Match Finished (Regular Time)' },
  { value: 'AET', label: 'Match Finished (After Extra Time)' },
  { value: 'PEN', label: 'Match Finished (After Penalty)' },
  { value: 'PST', label: 'Match Postponed' },
  { value: 'CANC', label: 'Match Cancelled' },
  { value: 'ABD', label: 'Match Abandoned' },
  { value: 'AWD', label: 'Technical Loss' },
  { value: 'WO', label: 'WalkOver' },
  { value: 'LIVE', label: 'In Progress' },
];

interface FixtureFormData {
  homeTeamId: string;
  awayTeamId: string;
  leagueId: string;
  date: string;
  time: string;
  venueName: string;
  venueCity: string;
  round: string;
  status: string;
  goalsHome: string;
  goalsAway: string;
  elapsed: string;
  referee?: string;
  temperature?: string;
  weather?: string;
  attendance?: string;
  isHot: boolean;
}

export default function CreateFixturePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<FixtureFormData>({
    homeTeamId: '',
    awayTeamId: '',
    leagueId: '',
    date: '',
    time: '',
    venueName: '',
    venueCity: '',
    round: '',
    status: 'NS', // Default to Not Started
    goalsHome: '',
    goalsAway: '',
    elapsed: '',
    isHot: false,
  });

  const [errors, setErrors] = useState<Partial<FixtureFormData>>({});

  // Search states for debouncing
  const [homeTeamSearch, setHomeTeamSearch] = useState('');
  const [awayTeamSearch, setAwayTeamSearch] = useState('');
  const [leagueSearch, setLeagueSearch] = useState('');

  // Fetch active leagues with search
  const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = useQuery({
    queryKey: ['leagues', 'active', 'search', leagueSearch],
    queryFn: () => leaguesApi.getLeagues({
      limit: 100,
      active: true,  // ✅ Only fetch active leagues
      search: leagueSearch || undefined
    }),
  });

  // Fetch teams with search
  const { data: teams, isLoading: teamsLoading, error: teamsError } = useQuery({
    queryKey: ['teams', 'search', homeTeamSearch, awayTeamSearch],
    queryFn: () => teamsApi.getTeams({
      limit: 100,
      search: homeTeamSearch || awayTeamSearch || undefined
    }),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => fixturesApi.createFixture(data),
    onSuccess: () => {
      toast.success('Fixture created successfully');
      router.push('/dashboard/fixtures');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create fixture');
    },
  });

  // Search handlers with debouncing
  const handleHomeTeamSearch = useCallback((query: string) => {
    setHomeTeamSearch(query);
  }, []);

  const handleAwayTeamSearch = useCallback((query: string) => {
    setAwayTeamSearch(query);
  }, []);

  const handleLeagueSearch = useCallback((query: string) => {
    setLeagueSearch(query);
  }, []);

  // Prepare options for dropdowns
  const leagueOptions = useMemo(() => {
    return leagues?.data?.map(league => {
      // Format season display
      let seasonInfo = '';
      let subtitleInfo = league.country;

      if (league.season_detail?.year) {
        seasonInfo = `Season ${league.season_detail.year}`;
        if (league.season_detail.current) {
          seasonInfo += ' (Current)';
        }
      } else if (league.season) {
        seasonInfo = `Season ${league.season}`;
      }

      // Combine country and season info for subtitle
      if (seasonInfo) {
        subtitleInfo = `${league.country} • ${seasonInfo}`;
      }

      return {
        value: league.id.toString(),
        label: league.name,
        logo: league.logo,
        uniqueKey: `league-${league.id}`,
        subtitle: subtitleInfo,
        // Store additional data for API submission
        externalId: league.externalId,
        season: league.season_detail?.year || league.season,
      };
    }) || [];
  }, [leagues]);

  const homeTeamOptions = useMemo(() => {
    return teams?.data?.map(team => ({
      value: team.id.toString(),
      label: team.name,
      logo: team.logo,
      uniqueKey: `home-team-${team.id}`,
      // Store additional data for API submission
      externalId: team.externalId,
    })) || [];
  }, [teams]);

  const awayTeamOptions = useMemo(() => {
    return teams?.data?.map(team => ({
      value: team.id.toString(),
      label: team.name,
      logo: team.logo,
      uniqueKey: `away-team-${team.id}`,
      // Store additional data for API submission
      externalId: team.externalId,
    })) || [];
  }, [teams]);

  // Selected options for preview
  const selectedHomeTeam = homeTeamOptions.find(team => team.value === formData.homeTeamId);
  const selectedAwayTeam = awayTeamOptions.find(team => team.value === formData.awayTeamId);
  const selectedLeague = leagueOptions.find(league => league.value === formData.leagueId);

  const validateForm = (): boolean => {
    const newErrors: Partial<FixtureFormData> = {};

    // Debug form data
    console.log('🔍 Form validation - Current formData:', {
      homeTeamId: formData.homeTeamId,
      awayTeamId: formData.awayTeamId,
      leagueId: formData.leagueId,
      date: formData.date,
      time: formData.time,
      timeLength: formData.time?.length,
      timeType: typeof formData.time
    });

    if (!formData.homeTeamId) newErrors.homeTeamId = 'Home team is required';
    if (!formData.awayTeamId) newErrors.awayTeamId = 'Away team is required';
    if (!formData.leagueId) newErrors.leagueId = 'League is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.time || formData.time.trim() === '') {
      newErrors.time = 'Time is required';
      console.log('❌ Time validation failed:', { time: formData.time, isEmpty: !formData.time });
    }

    if (formData.homeTeamId === formData.awayTeamId) {
      newErrors.awayTeamId = 'Away team must be different from home team';
    }

    console.log('🔍 Validation errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    // Combine date and time and ensure UTC conversion
    // Parse date and time components
    const [year, month, day] = formData.date.split('-').map(Number);
    const [hours, minutes] = formData.time.split(':').map(Number);

    // Create Date object in local timezone first
    const localDateTime = new Date(year, month - 1, day, hours, minutes, 0, 0);

    // Convert to UTC by creating a new Date with UTC methods
    const utcDateTime = new Date(Date.UTC(year, month - 1, day, hours, minutes, 0, 0));

    console.log('🕐 DateTime conversion:', {
      inputDate: formData.date,
      inputTime: formData.time,
      localDateTime: localDateTime.toISOString(),
      utcDateTime: utcDateTime.toISOString(),
      userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timezoneOffset: localDateTime.getTimezoneOffset()
    });

    // Helper function to get status long description
    const getStatusLong = (status: string): string => {
      const statusMap: Record<string, string> = {
        'TBD': 'Time To Be Defined',
        'NS': 'Not Started',
        'ST': 'Scheduled',
        '1H': 'First Half',
        'HT': 'Halftime',
        '2H': 'Second Half',
        'ET': 'Extra Time',
        'BT': 'Break Time',
        'P': 'Penalty In Progress',
        'SUSP': 'Match Suspended',
        'INT': 'Match Interrupted',
        'FT': 'Match Finished',
        'AET': 'Match Finished After Extra Time',
        'PEN': 'Match Finished After Penalty',
        'PST': 'Match Postponed',
        'CANC': 'Match Cancelled',
        'ABD': 'Match Abandoned',
        'AWD': 'Technical Loss',
        'WO': 'WalkOver',
        'LIVE': 'In Progress',
      };
      return statusMap[status] || status;
    };

    // Get selected entities to extract externalId and names
    const selectedLeagueData = leagueOptions.find(league => league.value === formData.leagueId);
    const selectedHomeTeamData = homeTeamOptions.find(team => team.value === formData.homeTeamId);
    const selectedAwayTeamData = awayTeamOptions.find(team => team.value === formData.awayTeamId);

    if (!selectedLeagueData || !selectedHomeTeamData || !selectedAwayTeamData) {
      toast.error('Please ensure all teams and league are properly selected');
      return;
    }

    // Prepare data for API - Using externalId and required fields
    const submitData = {
      leagueId: selectedLeagueData.externalId,           // ← ExternalId của League
      season: selectedLeagueData.season || 2024,         // ← Bắt buộc
      homeTeamId: selectedHomeTeamData.externalId,       // ← ExternalId của Team
      awayTeamId: selectedAwayTeamData.externalId,       // ← ExternalId của Team
      date: utcDateTime.toISOString(),
      round: formData.round || null,
      venueName: formData.venueName || null,
      venueCity: formData.venueCity || null,
      referee: formData.referee || null,
      isHot: formData.isHot,
      data: {
        homeTeamName: selectedHomeTeamData.label,        // ← Bắt buộc
        awayTeamName: selectedAwayTeamData.label,        // ← Bắt buộc
        status: formData.status,
        statusLong: getStatusLong(formData.status),
        statusExtra: 0,
        elapsed: formData.elapsed ? parseInt(formData.elapsed) : 0,
        goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : 0,
        goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : 0,
      }
    };

    console.log('🚀 Fixture Create Payload:', JSON.stringify(submitData, null, 2));
    createMutation.mutate(submitData);
  };

  const updateFormData = (field: keyof FixtureFormData, value: string) => {
    console.log(`📝 Updating ${field}:`, { oldValue: formData[field], newValue: value });
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Preview component for selected values
  const SelectedValuePreview = ({
    label,
    selectedOption,
    placeholder = "Not selected"
  }: {
    label: string;
    selectedOption?: { value: any; label: string; logo?: string; subtitle?: string; externalId?: number; season?: number } | null;
    placeholder?: string;
  }) => {
    const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://116.203.125.65';

    return (
      <div className="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div className="text-sm font-medium text-gray-700 mb-2">{label}</div>
        {selectedOption ? (
          <div className="flex items-center space-x-3">
            {selectedOption.logo && (
              <img
                src={`${CDN_URL}/${selectedOption.logo}`}
                alt={selectedOption.label}
                className="w-8 h-8 object-contain rounded"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <div className="flex-1">
              <div className="font-medium text-gray-900">{selectedOption.label}</div>
              <div className="text-xs text-gray-500 space-y-1">
                <div>Internal ID: {selectedOption.value}</div>
                {selectedOption.externalId && (
                  <div className="text-green-600">🔗 External ID: {selectedOption.externalId}</div>
                )}
                {selectedOption.season && (
                  <div className="text-purple-600">📅 Season: {selectedOption.season}</div>
                )}
                {selectedOption.subtitle && (
                  <div className="text-blue-600">📍 {selectedOption.subtitle}</div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-gray-500 italic">{placeholder}</div>
        )}
      </div>
    );
  };

  // Show loading state if any required data is loading
  const isDataLoading = leaguesLoading || teamsLoading;

  // Show error state if any critical data failed to load
  if (leaguesError || teamsError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              {!!leaguesError && <p className="text-red-600 mb-4">Failed to load leagues</p>}
              {!!teamsError && <p className="text-red-600 mb-4">Failed to load teams</p>}
              <Button onClick={() => router.push('/dashboard/fixtures')}>
                Return to Fixtures
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <FixtureNavigation
          variant="create"
          isLoading={createMutation.isLoading}
        />
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create New Fixture</h1>
          <p className="text-gray-600 mt-1">Add a new football fixture to the system</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Fixture Details
          </CardTitle>
          <CardDescription>
            Fill in the details for the new fixture
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDataLoading ? (
            <div className="space-y-6">
              <div className="space-y-4">
                <Skeleton className="h-6 w-48" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div className="space-y-3">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
                <div className="space-y-3">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-6 w-32" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <FormSection title="Teams & Competition" description="Select the teams and league">
                {/* Teams Selection with Preview */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <SelectedValuePreview
                      label="Selected Home Team"
                      selectedOption={selectedHomeTeam}
                      placeholder="No home team selected"
                    />
                    <SearchableSelectField
                      label="Home Team"
                      placeholder={teamsLoading ? "Loading teams..." : "Select home team"}
                      searchPlaceholder="Search teams..."
                      required
                      value={formData.homeTeamId}
                      onValueChange={(value) => updateFormData('homeTeamId', value)}
                      options={homeTeamOptions}
                      error={errors.homeTeamId}
                      disabled={teamsLoading}
                      onSearch={handleHomeTeamSearch}
                      isLoading={teamsLoading}
                    />
                  </div>

                  <div>
                    <SelectedValuePreview
                      label="Selected Away Team"
                      selectedOption={selectedAwayTeam}
                      placeholder="No away team selected"
                    />
                    <SearchableSelectField
                      label="Away Team"
                      placeholder={teamsLoading ? "Loading teams..." : "Select away team"}
                      searchPlaceholder="Search teams..."
                      required
                      value={formData.awayTeamId}
                      onValueChange={(value) => updateFormData('awayTeamId', value)}
                      options={awayTeamOptions.filter(team => team.value !== formData.homeTeamId)}
                      error={errors.awayTeamId}
                      disabled={teamsLoading}
                      onSearch={handleAwayTeamSearch}
                      isLoading={teamsLoading}
                    />
                  </div>
                </div>

                {/* League Selection with Preview */}
                <div>
                  <SelectedValuePreview
                    label="Selected League"
                    selectedOption={selectedLeague}
                    placeholder="No league selected"
                  />
                  <SearchableSelectField
                    label="League"
                    placeholder={leaguesLoading ? "Loading leagues..." : "Select league"}
                    searchPlaceholder="Search leagues..."
                    required
                    value={formData.leagueId}
                    onValueChange={(value) => updateFormData('leagueId', value)}
                    options={leagueOptions}
                    error={errors.leagueId}
                    disabled={leaguesLoading}
                    onSearch={handleLeagueSearch}
                    isLoading={leaguesLoading}
                  />
                </div>
              </FormSection>

              <FormSection title="Schedule" description="Set the date and time">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InputField
                    label="Date *"
                    type="date"
                    required
                    value={formData.date}
                    onChange={(e) => updateFormData('date', e.target.value)}
                    error={errors.date}
                    description="Match date"
                  />

                  <InputField
                    label="Time *"
                    type="time"
                    required
                    value={formData.time}
                    onChange={(e) => updateFormData('time', e.target.value)}
                    error={errors.time}
                    description={`Local time (${Intl.DateTimeFormat().resolvedOptions().timeZone}) - will be converted to UTC`}
                  />
                </div>

                <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <p className="flex items-center mb-2">
                    <span className="text-blue-600 mr-2">🌍</span>
                    <strong>Timezone Conversion:</strong> Enter time in your local timezone ({Intl.DateTimeFormat().resolvedOptions().timeZone}).
                  </p>
                  <p className="flex items-center text-xs">
                    <span className="text-green-600 mr-2">✅</span>
                    The system will automatically convert to UTC for API storage.
                    The asterisk (*) indicates required fields.
                  </p>
                </div>
              </FormSection>

              <FormSection title="Match Status" description="Set initial match status and score">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <SelectField
                    label="Status"
                    placeholder="Select status"
                    required
                    value={formData.status}
                    onValueChange={(value) => updateFormData('status', value)}
                    options={statusOptions}
                    error={errors.status}
                  />

                  <InputField
                    label="Home Goals"
                    type="number"
                    min="0"
                    value={formData.goalsHome}
                    onChange={(e) => updateFormData('goalsHome', e.target.value)}
                    description="Leave empty for scheduled matches"
                  />

                  <InputField
                    label="Away Goals"
                    type="number"
                    min="0"
                    value={formData.goalsAway}
                    onChange={(e) => updateFormData('goalsAway', e.target.value)}
                    description="Leave empty for scheduled matches"
                  />
                </div>

                <InputField
                  label="Elapsed Time (minutes)"
                  type="number"
                  min="0"
                  max="120"
                  value={formData.elapsed}
                  onChange={(e) => updateFormData('elapsed', e.target.value)}
                  description="Minutes played in the match (for live/finished matches)"
                />
              </FormSection>

              <FormSection title="Fixture Settings" description="Additional fixture settings">
                <ToggleSwitch
                  checked={formData.isHot}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isHot: checked }))}
                  label="Hot Fixture"
                  description="Mark this fixture as hot/featured"
                  variant="danger"
                />
              </FormSection>

              <FormSection title="Venue & Match Information" description="Venue details and match context">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InputField
                    label="Venue Name"
                    placeholder="Stadium name"
                    value={formData.venueName}
                    onChange={(e) => updateFormData('venueName', e.target.value)}
                  />

                  <InputField
                    label="Venue City"
                    placeholder="City"
                    value={formData.venueCity}
                    onChange={(e) => updateFormData('venueCity', e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InputField
                    label="Round"
                    placeholder="e.g., Matchday 1, Quarter-final"
                    value={formData.round}
                    onChange={(e) => updateFormData('round', e.target.value)}
                  />

                  <InputField
                    label="Referee"
                    placeholder="Referee name"
                    value={formData.referee || ''}
                    onChange={(e) => updateFormData('referee', e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <InputField
                    label="Temperature (°C)"
                    type="number"
                    placeholder="e.g., 22"
                    value={formData.temperature || ''}
                    onChange={(e) => updateFormData('temperature', e.target.value)}
                  />

                  <InputField
                    label="Weather"
                    placeholder="e.g., Sunny, Rainy"
                    value={formData.weather || ''}
                    onChange={(e) => updateFormData('weather', e.target.value)}
                  />

                  <InputField
                    label="Attendance"
                    type="number"
                    placeholder="Number of spectators"
                    value={formData.attendance || ''}
                    onChange={(e) => updateFormData('attendance', e.target.value)}
                  />
                </div>
              </FormSection>

              <FormActions>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={createMutation.isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMutation.isLoading}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {createMutation.isLoading ? 'Creating...' : 'Create Fixture'}
                </Button>
              </FormActions>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
