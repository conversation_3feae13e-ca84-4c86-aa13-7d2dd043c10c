import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
        try {
                const body = await request.json();
                const { leagueId, season } = body;

                // Validate required parameters
                if (!leagueId || !season) {
                        return NextResponse.json(
                                { error: 'League ID and season are required' },
                                { status: 400 }
                        );
                }

                console.log('🔄 Starting players sync for league:', leagueId, 'season:', season);

                // Fetch all players from all pages
                let allPlayers: any[] = [];
                let currentPage = 1;
                let totalPages = 1;
                let totalItems = 0;

                do {
                        const url = `${API_BASE_URL}/football/players?league=${leagueId}&season=${season}&limit=50&page=${currentPage}`;
                        console.log('🔄 Fetching players page:', currentPage, 'URL:', url);

                        const response = await fetch(url, {
                                method: 'GET',
                                headers: {
                                        'Content-Type': 'application/json',
                                        ...(request.headers.get('authorization') && {
                                                'Authorization': request.headers.get('authorization')!
                                        })
                                }
                        });

                        if (!response.ok) {
                                console.error('❌ Players API Error:', response.status, response.statusText);
                                return NextResponse.json(
                                        {
                                                error: 'Failed to fetch players',
                                                status: response.status,
                                                message: response.statusText
                                        },
                                        { status: response.status }
                                );
                        }

                        const pageData = await response.json();

                        // Extract players from current page
                        if (pageData.data && Array.isArray(pageData.data)) {
                                allPlayers = allPlayers.concat(pageData.data);
                        }

                        // Update pagination info
                        if (pageData.meta) {
                                totalPages = pageData.meta.totalPages || 1;
                                totalItems = pageData.meta.totalItems || 0;
                        }

                        console.log(`✅ Page ${currentPage}/${totalPages} fetched: ${pageData.data?.length || 0} players`);
                        currentPage++;

                } while (currentPage <= totalPages);

                console.log('✅ Players sync completed successfully:', {
                        totalPlayers: allPlayers.length,
                        totalPages: totalPages,
                        leagueId,
                        season
                });

                // Return summary of sync operation
                const syncResult = {
                        success: true,
                        count: allPlayers.length,
                        totalPages: totalPages,
                        leagueId,
                        season,
                        players: allPlayers, // Include all players data
                        message: `Successfully synced ${allPlayers.length} players from ${totalPages} pages`
                };

                return NextResponse.json(syncResult);
        } catch (error: any) {
                console.error('❌ Players sync proxy error:', error);
                return NextResponse.json(
                        {
                                error: 'Internal server error',
                                message: error.message
                        },
                        { status: 500 }
                );
        }
}
