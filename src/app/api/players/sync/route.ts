import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
        try {
                const body = await request.json();
                const { leagueId, season } = body;

                // Validate required parameters
                if (!leagueId || !season) {
                        return NextResponse.json(
                                { error: 'League ID and season are required' },
                                { status: 400 }
                        );
                }

                console.log('🔄 Starting players sync for league:', leagueId, 'season:', season);

                // Call the new sync endpoint - it handles all sync logic internally
                const url = `${API_BASE_URL}/football/players/sync?league=${leagueId}&season=${season}&limit=100&page=1`;
                console.log('🔄 Calling players sync endpoint:', url);

                const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                                'Content-Type': 'application/json',
                                ...(request.headers.get('authorization') && {
                                        'Authorization': request.headers.get('authorization')!
                                })
                        }
                });

                if (!response.ok) {
                        console.error('❌ Players Sync API Error:', response.status, response.statusText);
                        return NextResponse.json(
                                {
                                        error: 'Failed to sync players',
                                        status: response.status,
                                        message: response.statusText
                                },
                                { status: response.status }
                        );
                }

                const syncData = await response.json();

                console.log('✅ Players sync completed successfully:', {
                        status: syncData.status,
                        totalPlayers: syncData.data?.totalPlayers || 0,
                        newPlayers: syncData.data?.newPlayers || 0,
                        updatedPlayers: syncData.data?.updatedPlayers || 0,
                        leagueId,
                        season
                });

                // Return summary of sync operation in consistent format
                const syncResult = {
                        success: syncData.status === 'success',
                        count: syncData.data?.totalPlayers || 0,
                        newPlayers: syncData.data?.newPlayers || 0,
                        updatedPlayers: syncData.data?.updatedPlayers || 0,
                        leagueId,
                        season,
                        message: syncData.message || `Successfully synced ${syncData.data?.totalPlayers || 0} players`,
                        details: syncData.data
                };

                return NextResponse.json(syncResult);
        } catch (error: any) {
                console.error('❌ Players sync proxy error:', error);
                return NextResponse.json(
                        {
                                error: 'Internal server error',
                                message: error.message
                        },
                        { status: 500 }
                );
        }
}
