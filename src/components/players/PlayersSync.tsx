'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { usePlayerSync } from '@/lib/hooks/usePlayers';
import { leaguesApi } from '@/lib/api/leagues';
import { buildLeagueLogoUrl } from '@/lib/utils/image';
import {
        RotateCcw,
        Trophy,
        Calendar,
        Users,
        Target,
        AlertCircle,
        CheckCircle,
        Loader2,
        ExternalLink
} from 'lucide-react';

interface PlayersSyncProps {
        open: boolean;
        onClose: () => void;
}

export const PlayersSync: React.FC<PlayersSyncProps> = ({ open, onClose }) => {
        const [selectedLeague, setSelectedLeague] = useState<string>('');
        const [season, setSeason] = useState<number>(new Date().getFullYear());
        const [step, setStep] = useState<'select' | 'confirm' | 'syncing' | 'complete'>('select');
        const [syncResult, setSyncResult] = useState<any>(null);

        const { syncPlayers, isSyncing } = usePlayerSync();

        // Fetch leagues for selection
        const { data: leagues, isLoading: leaguesLoading } = useQuery({
                queryKey: ['leagues', 'all'],
                queryFn: () => leaguesApi.getLeagues({ limit: 100, active: true }),
        });

        // Get selected league details
        const selectedLeagueData = leagues?.data?.find(
                league => league.externalId.toString() === selectedLeague
        );

        const handleNext = () => {
                if (step === 'select' && selectedLeague && season) {
                        setStep('confirm');
                }
        };

        const handleSync = () => {
                if (!selectedLeague || !season) return;

                setStep('syncing');

                syncPlayers(
                        { leagueId: parseInt(selectedLeague), season },
                        {
                                onSuccess: (data) => {
                                        setSyncResult(data);
                                        setStep('complete');
                                },
                                onError: () => {
                                        setStep('select');
                                }
                        }
                );
        };

        const handleClose = () => {
                setStep('select');
                setSelectedLeague('');
                setSeason(new Date().getFullYear());
                setSyncResult(null);
                onClose();
        };

        const renderStepContent = () => {
                switch (step) {
                        case 'select':
                                return (
                                        <div className="space-y-6">
                                                <div className="space-y-2">
                                                        <Label htmlFor="league">Select League</Label>
                                                        {leaguesLoading ? (
                                                                <div className="flex items-center space-x-2 p-3 border rounded-md">
                                                                        <Loader2 className="w-4 h-4 animate-spin" />
                                                                        <span>Loading leagues...</span>
                                                                </div>
                                                        ) : (
                                                                <select
                                                                        id="league"
                                                                        value={selectedLeague}
                                                                        onChange={(e) => setSelectedLeague(e.target.value)}
                                                                        className="w-full h-10 px-3 rounded-md border border-input bg-background text-sm"
                                                                >
                                                                        <option value="">Choose a league</option>
                                                                        {leagues?.data?.map((league) => (
                                                                                <option key={league.externalId} value={league.externalId.toString()}>
                                                                                        {league.name} ({league.country})
                                                                                </option>
                                                                        ))}
                                                                </select>
                                                        )}
                                                </div>

                                                <div className="space-y-2">
                                                        <Label htmlFor="season">Season</Label>
                                                        <Input
                                                                id="season"
                                                                type="number"
                                                                value={season}
                                                                onChange={(e) => setSeason(parseInt(e.target.value) || new Date().getFullYear())}
                                                                min="2000"
                                                                max={new Date().getFullYear() + 1}
                                                                placeholder="Enter season year"
                                                        />
                                                        <p className="text-sm text-muted-foreground">
                                                                Current year: {new Date().getFullYear()}
                                                        </p>
                                                </div>

                                                {selectedLeagueData && (
                                                        <div className="border rounded-lg p-4 bg-muted/50">
                                                                <div className="flex items-center space-x-3 mb-3">
                                                                        {selectedLeagueData.logo && (
                                                                                <img
                                                                                        src={buildLeagueLogoUrl(selectedLeagueData.logo) || '/images/default-league.png'}
                                                                                        alt={selectedLeagueData.name}
                                                                                        className="w-8 h-8 object-contain"
                                                                                        onError={(e) => {
                                                                                                e.currentTarget.style.display = 'none';
                                                                                        }}
                                                                                />
                                                                        )}
                                                                        <div>
                                                                                <h4 className="font-medium">{selectedLeagueData.name}</h4>
                                                                                <p className="text-sm text-muted-foreground">{selectedLeagueData.country}</p>
                                                                        </div>
                                                                </div>
                                                                <div className="flex items-center space-x-4 text-sm">
                                                                        <Badge variant="outline">
                                                                                <Trophy className="w-3 h-3 mr-1" />
                                                                                League ID: {selectedLeagueData.externalId}
                                                                        </Badge>
                                                                        <Badge variant="outline">
                                                                                <Calendar className="w-3 h-3 mr-1" />
                                                                                Season: {season}
                                                                        </Badge>
                                                                </div>
                                                        </div>
                                                )}

                                                <Alert>
                                                        <AlertCircle className="h-4 w-4" />
                                                        <AlertDescription>
                                                                This will sync top scorers from the external API for the selected league and season.
                                                                The process may take a few moments depending on the amount of data.
                                                        </AlertDescription>
                                                </Alert>
                                        </div>
                                );

                        case 'confirm':
                                return (
                                        <div className="space-y-6">
                                                <div className="text-center">
                                                        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                                                <Target className="w-8 h-8 text-blue-600" />
                                                        </div>
                                                        <h3 className="text-lg font-semibold mb-2">Confirm Player Sync</h3>
                                                        <p className="text-muted-foreground">
                                                                You are about to sync top scorers data for:
                                                        </p>
                                                </div>

                                                <div className="border rounded-lg p-4 bg-muted/50">
                                                        <div className="flex items-center space-x-3 mb-3">
                                                                {selectedLeagueData?.logo && (
                                                                        <img
                                                                                src={buildLeagueLogoUrl(selectedLeagueData.logo) || '/images/default-league.png'}
                                                                                alt={selectedLeagueData.name}
                                                                                className="w-10 h-10 object-contain"
                                                                        />
                                                                )}
                                                                <div>
                                                                        <h4 className="font-medium">{selectedLeagueData?.name}</h4>
                                                                        <p className="text-sm text-muted-foreground">
                                                                                {selectedLeagueData?.country} • Season {season}
                                                                        </p>
                                                                </div>
                                                        </div>

                                                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                                                <ExternalLink className="w-4 h-4" />
                                                                <span>API Endpoint: /football/players?league={selectedLeague}&season={season}</span>
                                                        </div>
                                                </div>

                                                <Alert>
                                                        <AlertCircle className="h-4 w-4" />
                                                        <AlertDescription>
                                                                <strong>Note:</strong> This will fetch and update player statistics for top scorers.
                                                                Existing data may be updated with the latest information from the external API.
                                                        </AlertDescription>
                                                </Alert>
                                        </div>
                                );

                        case 'syncing':
                                return (
                                        <div className="space-y-6 text-center">
                                                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                                                        <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
                                                </div>

                                                <div>
                                                        <h3 className="text-lg font-semibold mb-2">Syncing Players...</h3>
                                                        <p className="text-muted-foreground mb-4">
                                                                Fetching top scorers data from external API
                                                        </p>
                                                        <Progress value={undefined} className="w-full" />
                                                </div>

                                                <div className="text-sm text-muted-foreground">
                                                        <p>League: {selectedLeagueData?.name}</p>
                                                        <p>Season: {season}</p>
                                                        <p>Please wait while we sync the data...</p>
                                                </div>
                                        </div>
                                );

                        case 'complete':
                                return (
                                        <div className="space-y-6 text-center">
                                                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                                        <CheckCircle className="w-8 h-8 text-green-600" />
                                                </div>

                                                <div>
                                                        <h3 className="text-lg font-semibold mb-2">Sync Complete!</h3>
                                                        <p className="text-muted-foreground">
                                                                Successfully synced player data
                                                        </p>
                                                </div>

                                                {syncResult && (
                                                        <div className="border rounded-lg p-4 bg-green-50">
                                                                <div className="flex items-center justify-between mb-2">
                                                                        <span className="font-medium">Sync Results</span>
                                                                        <Badge variant="default" className="bg-green-100 text-green-800">
                                                                                <Users className="w-3 h-3 mr-1" />
                                                                                {syncResult.count || 0} players
                                                                        </Badge>
                                                                </div>
                                                                <div className="text-sm text-muted-foreground space-y-1">
                                                                        <p>League: {selectedLeagueData?.name}</p>
                                                                        <p>Season: {season}</p>
                                                                        <p>Players synced: {syncResult.count || 0}</p>
                                                                        {syncResult.updated && <p>Players updated: {syncResult.updated}</p>}
                                                                        {syncResult.created && <p>New players: {syncResult.created}</p>}
                                                                </div>
                                                        </div>
                                                )}

                                                <Alert>
                                                        <CheckCircle className="h-4 w-4" />
                                                        <AlertDescription>
                                                                The players data has been successfully updated. You can now view the latest top scorers
                                                                in the players management page.
                                                        </AlertDescription>
                                                </Alert>
                                        </div>
                                );

                        default:
                                return null;
                }
        };

        return (
                <Dialog open={open} onOpenChange={handleClose}>
                        <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                        <DialogTitle className="flex items-center space-x-2">
                                                <RotateCcw className="w-5 h-5" />
                                                <span>Sync Players</span>
                                        </DialogTitle>
                                        <DialogDescription>
                                                {step === 'select' && 'Select league and season to sync top scorers'}
                                                {step === 'confirm' && 'Confirm the sync operation'}
                                                {step === 'syncing' && 'Syncing players data...'}
                                                {step === 'complete' && 'Sync operation completed'}
                                        </DialogDescription>
                                </DialogHeader>

                                {renderStepContent()}

                                <DialogFooter>
                                        <div className="flex space-x-2 w-full">
                                                {step === 'select' && (
                                                        <>
                                                                <Button variant="outline" onClick={handleClose} className="flex-1">
                                                                        Cancel
                                                                </Button>
                                                                <Button
                                                                        onClick={handleNext}
                                                                        disabled={!selectedLeague || !season}
                                                                        className="flex-1"
                                                                >
                                                                        Next
                                                                </Button>
                                                        </>
                                                )}

                                                {step === 'confirm' && (
                                                        <>
                                                                <Button
                                                                        variant="outline"
                                                                        onClick={() => setStep('select')}
                                                                        className="flex-1"
                                                                >
                                                                        Back
                                                                </Button>
                                                                <Button
                                                                        onClick={handleSync}
                                                                        disabled={isSyncing}
                                                                        className="flex-1"
                                                                >
                                                                        <RotateCcw className="w-4 h-4 mr-2" />
                                                                        Start Sync
                                                                </Button>
                                                        </>
                                                )}

                                                {step === 'syncing' && (
                                                        <Button variant="outline" disabled className="w-full">
                                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                                Syncing...
                                                        </Button>
                                                )}

                                                {step === 'complete' && (
                                                        <Button onClick={handleClose} className="w-full">
                                                                <CheckCircle className="w-4 h-4 mr-2" />
                                                                Done
                                                        </Button>
                                                )}
                                        </div>
                                </DialogFooter>
                        </DialogContent>
                </Dialog>
        );
};
