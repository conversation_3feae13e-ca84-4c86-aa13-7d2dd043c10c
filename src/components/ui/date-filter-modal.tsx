"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, X, Check, RotateCcw } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface DateFilterModalProps {
  isOpen: boolean
  onClose: () => void
  selectedDate?: Date
  onDateSelect: (date: Date | undefined) => void
  onApplyFilter: (date: Date | undefined) => void
  onResetFilter: () => void
}

export function DateFilterModal({
  isOpen,
  onClose,
  selectedDate,
  onDateSelect,
  onApplyFilter,
  onResetFilter,
}: DateFilterModalProps) {
  const [tempDate, setTempDate] = React.useState<Date | undefined>(selectedDate)

  React.useEffect(() => {
    setTempDate(selectedDate)
  }, [selectedDate, isOpen])

  const handleApply = () => {
    try {
      onApplyFilter(tempDate)
      onDateSelect(tempDate)
      onClose()
    } catch (error) {
      console.error('Error applying date filter:', error)
    }
  }

  const handleReset = () => {
    try {
      setTempDate(undefined)
      onResetFilter()
      onDateSelect(undefined)
      onClose()
    } catch (error) {
      console.error('Error resetting date filter:', error)
    }
  }

  const handleCancel = () => {
    try {
      setTempDate(selectedDate) // Reset to original value
      onClose()
    } catch (error) {
      console.error('Error canceling date filter:', error)
      onClose()
    }
  }

  const handleDateSelect = (date: Date | undefined) => {
    try {
      setTempDate(date)
    } catch (error) {
      console.error('Error selecting date:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px] p-0 overflow-hidden bg-white rounded-xl shadow-2xl">
        <DialogHeader className="px-6 py-5 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <DialogTitle className="flex items-center gap-3 text-xl font-bold">
            <CalendarIcon className="h-6 w-6" />
            Select Date Filter
          </DialogTitle>
          <DialogDescription className="text-blue-100 mt-2 text-sm">
            Choose a specific date to filter fixtures, or reset to show today's fixtures.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 py-6">
          {/* Selected Date Display */}
          {tempDate && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-blue-800 mb-1">Selected Date:</div>
                  <div className="text-xl font-bold text-blue-900">
                    {format(tempDate, "EEEE, MMMM d, yyyy")}
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    {tempDate < new Date() ? "Past date" : tempDate.toDateString() === new Date().toDateString() ? "Today" : "Future date"}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDateSelect(undefined)}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-lg"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          )}

          {/* Calendar */}
          <div className="flex justify-center mb-6">
            <div className="bg-white rounded-xl border border-gray-200 shadow-lg p-2">
              <Calendar
                mode="single"
                selected={tempDate}
                onSelect={handleDateSelect}
                initialFocus
                className="rounded-lg"
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
            <div className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Quick Actions
            </div>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateSelect(new Date())}
                className="text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const tomorrow = new Date()
                  tomorrow.setDate(tomorrow.getDate() + 1)
                  handleDateSelect(tomorrow)
                }}
                className="text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
              >
                Tomorrow
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const nextWeek = new Date()
                  nextWeek.setDate(nextWeek.getDate() + 7)
                  handleDateSelect(nextWeek)
                }}
                className="text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
              >
                Next Week
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-5 bg-gray-50 border-t border-gray-200 flex-col sm:flex-row gap-3">
          <div className="flex gap-3 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleReset}
              className="flex-1 sm:flex-none hover:bg-gray-100 border-gray-300"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Today
            </Button>
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1 sm:flex-none hover:bg-gray-100 border-gray-300"
            >
              Cancel
            </Button>
          </div>
          <Button
            onClick={handleApply}
            disabled={!tempDate}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Check className="h-4 w-4 mr-2" />
            Apply Filter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
