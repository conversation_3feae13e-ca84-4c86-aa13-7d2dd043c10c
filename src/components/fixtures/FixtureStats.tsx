'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Fixture, FixtureStatisticsResponse } from '@/lib/types/api';
import { fixturesApi } from '@/lib/api/fixtures';
import { BarChart3, Target, Clock, Flag, AlertCircle } from 'lucide-react';

interface FixtureStatsProps {
  fixture: Fixture;
}

export function FixtureStats({ fixture }: FixtureStatsProps) {
  // Fetch real statistics from API
  const { data: statisticsData, isLoading, error } = useQuery({
    queryKey: ['fixture-statistics', fixture.externalId],
    queryFn: () => fixturesApi.getFixtureStatistics(fixture.externalId),
    enabled: !!fixture.externalId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Parse API data or use mock data as fallback
  const parseStatistics = () => {
    if (!statisticsData?.data || statisticsData.data.length < 2) {
      // Use mock data when API fails or no data
      return {
        possession: { home: 65, away: 35 },
        shots: { home: 12, away: 8 },
        shotsOnTarget: { home: 6, away: 3 },
        corners: { home: 7, away: 4 },
        fouls: { home: 11, away: 14 },
        yellowCards: { home: 2, away: 3 },
        redCards: { home: 0, away: 1 },
        offsides: { home: 3, away: 2 },
        isRealData: false
      };
    }

    const homeTeamStats = statisticsData.data[0]?.statistics || [];
    const awayTeamStats = statisticsData.data[1]?.statistics || [];

    const getStatValue = (stats: any[], type: string): number => {
      const stat = stats.find(s => s.type === type);
      if (!stat) return 0;

      // Handle percentage values
      if (typeof stat.value === 'string' && stat.value.includes('%')) {
        return parseInt(stat.value.replace('%', ''));
      }

      return parseInt(stat.value) || 0;
    };

    return {
      possession: {
        home: getStatValue(homeTeamStats, 'Ball Possession'),
        away: getStatValue(awayTeamStats, 'Ball Possession')
      },
      shots: {
        home: getStatValue(homeTeamStats, 'Total Shots'),
        away: getStatValue(awayTeamStats, 'Total Shots')
      },
      shotsOnTarget: {
        home: getStatValue(homeTeamStats, 'Shots on Goal'),
        away: getStatValue(awayTeamStats, 'Shots on Goal')
      },
      corners: {
        home: getStatValue(homeTeamStats, 'Corner Kicks'),
        away: getStatValue(awayTeamStats, 'Corner Kicks')
      },
      fouls: {
        home: getStatValue(homeTeamStats, 'Fouls'),
        away: getStatValue(awayTeamStats, 'Fouls')
      },
      yellowCards: {
        home: getStatValue(homeTeamStats, 'Yellow Cards'),
        away: getStatValue(awayTeamStats, 'Yellow Cards')
      },
      redCards: {
        home: getStatValue(homeTeamStats, 'Red Cards'),
        away: getStatValue(awayTeamStats, 'Red Cards')
      },
      offsides: {
        home: getStatValue(homeTeamStats, 'Offsides'),
        away: getStatValue(awayTeamStats, 'Offsides')
      },
      isRealData: true
    };
  };

  const stats = parseStatistics();

  const StatRow = ({
    label,
    homeValue,
    awayValue,
    icon: Icon,
    isPercentage = false
  }: {
    label: string;
    homeValue: number;
    awayValue: number;
    icon: React.ElementType;
    isPercentage?: boolean;
  }) => {
    const total = homeValue + awayValue;
    const homePercentage = total > 0 ? (homeValue / total) * 100 : 50;
    const awayPercentage = total > 0 ? (awayValue / total) * 100 : 50;

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="font-medium text-right w-12">
            {homeValue}{isPercentage ? '%' : ''}
          </span>
          <div className="flex items-center space-x-2 flex-1 justify-center">
            <Icon className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{label}</span>
          </div>
          <span className="font-medium text-left w-12">
            {awayValue}{isPercentage ? '%' : ''}
          </span>
        </div>

        {/* Progress bar */}
        <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="bg-blue-500 transition-all duration-300"
            style={{ width: `${homePercentage}%` }}
          />
          <div
            className="bg-red-500 transition-all duration-300"
            style={{ width: `${awayPercentage}%` }}
          />
        </div>
      </div>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Match Statistics</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-8" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-8" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Match Statistics</span>
          {!stats.isRealData && (
            <div className="flex items-center space-x-1 text-orange-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-xs">Demo Data</span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Team Names Header */}
        <div className="flex items-center justify-between text-sm font-medium text-gray-600">
          <span className="w-12 text-right">{fixture.homeTeamName}</span>
          <span className="flex-1 text-center">Statistics</span>
          <span className="w-12 text-left">{fixture.awayTeamName}</span>
        </div>

        {/* Stats */}
        <div className="space-y-4">
          <StatRow
            label="Possession"
            homeValue={stats.possession.home}
            awayValue={stats.possession.away}
            icon={BarChart3}
            isPercentage={true}
          />

          <StatRow
            label="Shots"
            homeValue={stats.shots.home}
            awayValue={stats.shots.away}
            icon={Target}
          />

          <StatRow
            label="Shots on Target"
            homeValue={stats.shotsOnTarget.home}
            awayValue={stats.shotsOnTarget.away}
            icon={Target}
          />

          <StatRow
            label="Corners"
            homeValue={stats.corners.home}
            awayValue={stats.corners.away}
            icon={Flag}
          />

          <StatRow
            label="Fouls"
            homeValue={stats.fouls.home}
            awayValue={stats.fouls.away}
            icon={Clock}
          />

          <StatRow
            label="Yellow Cards"
            homeValue={stats.yellowCards.home}
            awayValue={stats.yellowCards.away}
            icon={Flag}
          />

          <StatRow
            label="Red Cards"
            homeValue={stats.redCards.home}
            awayValue={stats.redCards.away}
            icon={Flag}
          />

          <StatRow
            label="Offsides"
            homeValue={stats.offsides.home}
            awayValue={stats.offsides.away}
            icon={Flag}
          />
        </div>

        {/* Note */}
        <div className="text-xs text-gray-500 text-center pt-4 border-t">
          * Statistics are updated in real-time during the match
        </div>
      </CardContent>
    </Card>
  );
}
