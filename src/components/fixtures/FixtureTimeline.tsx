'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Fixture } from '@/lib/types/api';
import { Clock, Goal, AlertTriangle, RotateCcw, UserX, Users, AlertCircle } from 'lucide-react';

interface FixtureTimelineProps {
  fixture: Fixture;
}

interface TimelineEvent {
  id: number;
  minute: number;
  type: 'goal' | 'yellow_card' | 'red_card' | 'substitution' | 'penalty' | 'own_goal';
  team: 'home' | 'away';
  player: string;
  description: string;
  additionalInfo?: string;
}

export function FixtureTimeline({ fixture }: FixtureTimelineProps) {
  // Mock timeline data - in real app, this would come from API
  const events: TimelineEvent[] = [
    {
      id: 1,
      minute: 15,
      type: 'goal',
      team: 'home',
      player: '<PERSON>',
      description: 'Goal',
      additionalInfo: 'Assist: <PERSON>'
    },
    {
      id: 2,
      minute: 23,
      type: 'yellow_card',
      team: 'away',
      player: '<PERSON>',
      description: 'Yellow Card',
      additionalInfo: 'Foul'
    },
    {
      id: 3,
      minute: 45,
      type: 'substitution',
      team: 'home',
      player: 'Anthony Martial',
      description: 'Substitution',
      additionalInfo: 'Out: Jadon Sancho'
    },
    {
      id: 4,
      minute: 67,
      type: 'goal',
      team: 'away',
      player: 'Mohamed Salah',
      description: 'Goal',
      additionalInfo: 'Assist: Sadio Mané'
    },
    {
      id: 5,
      minute: 89,
      type: 'goal',
      team: 'home',
      player: 'Mason Greenwood',
      description: 'Goal',
      additionalInfo: 'Penalty'
    }
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'goal':
      case 'penalty':
      case 'own_goal':
        return <Goal className="h-4 w-4" />;
      case 'yellow_card':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'red_card':
        return <UserX className="h-4 w-4 text-red-500" />;
      case 'substitution':
        return <RotateCcw className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'goal':
      case 'penalty':
        return 'bg-green-100 text-green-800';
      case 'own_goal':
        return 'bg-orange-100 text-orange-800';
      case 'yellow_card':
        return 'bg-yellow-100 text-yellow-800';
      case 'red_card':
        return 'bg-red-100 text-red-800';
      case 'substitution':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEventTitle = (type: string) => {
    switch (type) {
      case 'goal':
        return 'Goal';
      case 'penalty':
        return 'Penalty Goal';
      case 'own_goal':
        return 'Own Goal';
      case 'yellow_card':
        return 'Yellow Card';
      case 'red_card':
        return 'Red Card';
      case 'substitution':
        return 'Substitution';
      default:
        return type;
    }
  };

  if (events.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Match Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No events recorded for this match yet.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Match Timeline</span>
          <div className="flex items-center space-x-1 text-orange-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-xs">Demo Data</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {events.map((event, index) => (
            <div key={event.id} className="relative">
              {/* Timeline line */}
              {index < events.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
              )}

              <div className={`flex items-start space-x-4 ${event.team === 'away' ? 'flex-row-reverse space-x-reverse' : ''
                }`}>
                {/* Time badge */}
                <div className="flex-shrink-0">
                  <Badge variant="outline" className="font-mono">
                    {event.minute}'
                  </Badge>
                </div>

                {/* Event icon */}
                <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${event.team === 'home' ? 'bg-blue-100' : 'bg-red-100'
                  }`}>
                  {getEventIcon(event.type)}
                </div>

                {/* Event details */}
                <div className={`flex-1 ${event.team === 'away' ? 'text-right' : ''}`}>
                  <div className="flex items-center space-x-2">
                    <Badge className={getEventColor(event.type)}>
                      {getEventTitle(event.type)}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {event.team === 'home' ? fixture.homeTeamName : fixture.awayTeamName}
                    </span>
                  </div>

                  <p className="font-medium text-gray-900 mt-1">
                    {event.player}
                  </p>

                  {event.additionalInfo && (
                    <p className="text-sm text-gray-600 mt-1">
                      {event.additionalInfo}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Match periods */}
        <div className="mt-8 pt-6 border-t">
          <div className="flex items-center justify-center space-x-8 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full" />
              <span>1st Half: 0-45'</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full" />
              <span>2nd Half: 45-90'</span>
            </div>
            {fixture.elapsed && fixture.elapsed > 90 && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                <span>Extra Time: 90'+</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
