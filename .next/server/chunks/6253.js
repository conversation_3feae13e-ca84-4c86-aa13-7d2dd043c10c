"use strict";exports.id=6253,exports.ids=[6253],exports.modules={55085:(t,e,a)=>{a.d(e,{Z:()=>c});var s=a(95344),o=a(5094),r=a(63024),i=a(33733),n=a(8428);let c=({variant:t,fixtureId:e,onRefresh:a,isLoading:c=!1,className:u=""})=>{let l=(0,n.useRouter)();return(0,s.jsxs)("div",{className:`flex items-center space-x-4 ${u}`,children:[(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>{switch(t){case"detail":case"create":l.push("/dashboard/fixtures");break;case"edit":e?l.push(`/dashboard/fixtures/${e}`):l.back();break;default:l.back()}},disabled:c,children:[s.jsx(r.Z,{className:"mr-2 h-4 w-4"}),(()=>{switch(t){case"detail":case"create":return"Back to Fixtures";case"edit":return"Back to Detail";default:return"Back"}})()]}),"detail"===t&&a&&(0,s.jsxs)(o.z,{variant:"outline",onClick:a,disabled:c,children:[s.jsx(i.Z,{className:`mr-2 h-4 w-4 ${c?"animate-spin":""}`}),"Refresh"]})]})}},20255:(t,e,a)=>{a.d(e,{L:()=>o});var s=a(50053);let o={getFixtures:async(t={})=>{let e=new URLSearchParams;Object.entries(t).forEach(([t,a])=>{void 0!==a&&e.append(t,a.toString())});let a=await fetch(`/api/fixtures?${e.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`Failed to fetch fixtures: ${a.statusText}`);return await a.json()},getFixtureById:async t=>{let e=await fetch(`/api/fixtures/${t}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Failed to fetch fixture: ${e.statusText}`);return await e.json()},getUpcomingAndLive:async(t={})=>{let e=new URLSearchParams;Object.entries(t).forEach(([t,a])=>{void 0!==a&&e.append(t,a.toString())});let a=await fetch(`/api/fixtures/live?${e.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`Failed to fetch live fixtures: ${a.statusText}`);return await a.json()},getTeamSchedule:async(t,e={})=>{let a=new URLSearchParams;return Object.entries(e).forEach(([t,e])=>{void 0!==e&&a.append(t,e.toString())}),await s.x.get(`/football/fixtures/schedules/${t}?${a.toString()}`)},getFixtureStatistics:async t=>await s.x.get(`/football/fixtures/statistics/${t}`),triggerSeasonSync:async()=>{let t=(console.warn("❌ Season sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Season sync request via proxy");let e=await fetch("/api/fixtures/sync",{method:"POST",headers:t,body:JSON.stringify({type:"season"})});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Season sync failed:",e.status,e.statusText,t),Error(t.message||`Failed to trigger season sync: ${e.statusText}`)}let a=await e.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let t=(console.warn("❌ Daily sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Daily sync request via proxy");let e=await fetch("/api/fixtures/sync",{method:"POST",headers:t,body:JSON.stringify({type:"daily"})});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",e.status,e.statusText,t),Error(t.message||`Failed to trigger daily sync: ${e.statusText}`)}let a=await e.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let t=(console.warn("❌ Sync status - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Sync status request via proxy");let e=await fetch("/api/fixtures/sync",{method:"GET",headers:t});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Sync status failed:",e.status,e.statusText,t),Error(t.message||`Failed to get sync status: ${e.statusText}`)}let a=await e.json();return console.log("✅ Sync status successful"),a},createFixture:async t=>{let e=(console.warn("❌ Create fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!e.Authorization,data:t});let a=await fetch("/api/fixtures",{method:"POST",headers:e,body:JSON.stringify(t)});if(!a.ok){let t=await a.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",a.status,a.statusText,t),Error(t.message||`Failed to create fixture: ${a.statusText}`)}let s=await a.json();return console.log("✅ Create fixture successful:",s.data?.id),s.data||s},updateFixture:async(t,e)=>{let a=(console.warn("❌ Update fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Update fixture request:",{externalId:t,hasAuth:!!a.Authorization,data:e});let s=await fetch(`/api/fixtures/${t}`,{method:"PUT",headers:a,body:JSON.stringify(e)});if(!s.ok){let t=await s.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",s.status,s.statusText,t),Error(t.message||`Failed to update fixture: ${s.statusText}`)}let o=await s.json();return console.log("✅ Update fixture successful:",t),o.data||o},deleteFixture:async t=>{let e=(console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Delete fixture request:",{externalId:t,hasAuth:!!e.Authorization});let a=await fetch(`/api/fixtures/${t}`,{method:"DELETE",headers:e});if(!a.ok){let t=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,t),Error(t.message||`Failed to delete fixture: ${a.statusText}`)}console.log("✅ Delete fixture successful:",t)},getFixtureStatistics:async t=>{let e=await fetch(`/api/fixtures/${t}/statistics`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Failed to fetch fixture statistics: ${e.statusText}`);return await e.json()},getFixtureEvents:async t=>{let e=await fetch(`/api/fixtures/${t}/events`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Failed to fetch fixture events: ${e.statusText}`);return await e.json()},getFixture:async t=>(await o.getFixtureById(t)).data}}};