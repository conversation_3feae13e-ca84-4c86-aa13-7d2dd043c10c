"use strict";exports.id=958,exports.ids=[958],exports.modules={79:(e,a,t)=>{t.d(a,{U:()=>U,Z:()=>R});var r=t(95344),s=t(3729),l=t.n(s),i=t(11526),o=t(3380),n=t(14513),d=t(56389),c=t(10232),u=t(20016),m=t(95269),p=t(5094),f=t(46540),h=t(7361),x=t(51467),g=t(23673),v=t(19591),y=t(11453),b=t(34755);let j=()=>null,w=e=>{if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;let a=e;return a.startsWith("/")&&(a=a.slice(1)),`http://*************/${a}`},N={uploadFile:async(e,a="general",t)=>{let r;let s=j();if(!s)throw Error("Authentication required for file upload");let l=new FormData;l.append("file",e),l.append("category",a),t&&l.append("description",t);let i=await fetch("/api/upload/file",{method:"POST",headers:{Authorization:`Bearer ${s}`},body:l});if(!i.ok){let e="Upload failed";try{e=(await i.json()).message||e}catch(a){console.warn("Failed to parse error response:",a),e=`Upload failed with status ${i.status}`}throw Error(e)}try{r=await i.json()}catch(e){throw console.error("Failed to parse upload response:",e),Error("Invalid response from upload service")}let o=r.data||r;if(!o||!o.path)throw console.error("Invalid upload response:",r),Error("Upload response missing required data");let n=w(o.path);if(!n)throw Error("Failed to generate CDN URL from upload response");return{...o,url:n}},uploadFromUrl:async e=>{let a=j();if(!a)throw Error("Authentication required for URL upload");let t=await fetch("/api/upload/url",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Upload failed"}))).message||`Upload failed with status ${t.status}`);let r=await t.json(),s=r.data||r,l=w(s.path);return{...s,url:l}},getUploadedImages:async(e=1,a=20,t)=>{let r=j();if(!r)throw Error("Authentication required");let s=new URLSearchParams({page:e.toString(),limit:a.toString()});t&&s.append("category",t);let l=await fetch(`/api/upload?${s.toString()}`,{method:"GET",headers:{Authorization:`Bearer ${r}`}});if(!l.ok)throw Error(`Failed to fetch images: ${l.status}`);let i=await l.json(),o=i.data.map(e=>({...e,url:w(e.path)}));return{...i,data:o}},deleteUploadedImage:async e=>{let a=j();if(!a)throw Error("Authentication required");let t=await fetch(`/api/upload/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${a}`}});if(!t.ok)throw Error(`Failed to delete image: ${t.status}`)},buildDirectCdnUrl:w},U=l().forwardRef(({label:e,description:a,value:t,onChange:l,onFileSelect:j,accept:w="image/*",maxSize:U=5,className:R,error:L,required:k,preview:E=!0,placeholder:F="Enter image URL or drag & drop a file",category:C="general",uploadDescription:I},P)=>{let[S,z]=(0,s.useState)(!1),[$,D]=(0,s.useState)("url"),[Z,B]=(0,s.useState)(t||""),[T,A]=(0,s.useState)(null),[M,O]=(0,s.useState)(!1),[W,q]=(0,s.useState)(0),G=(0,s.useRef)(null),V=(0,s.useRef)(null);(0,s.useEffect)(()=>()=>{if(V.current&&clearInterval(V.current),Z&&Z.startsWith("blob:"))try{URL.revokeObjectURL(Z)}catch(e){console.warn("Failed to revoke object URL on unmount:",e)}},[Z]),(0,s.useEffect)(()=>{t!==Z&&B(t||"")},[t]);let J=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?z(!0):"dragleave"===e.type&&z(!1)},[]),Y=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),z(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&Q(e.dataTransfer.files[0])},[]),Q=(0,s.useCallback)(async e=>{if(!e.type.startsWith("image/")){b.toast.error("Please select a valid image file");return}if(e.size>1048576*U){b.toast.error(`File size must be less than ${U}MB`);return}A(e),O(!0),q(0);let a=URL.createObjectURL(e);B(a);try{V.current=setInterval(()=>{q(e=>Math.min(e+10,90))},200);let t=await N.uploadFile(e,C,I);if(V.current&&(clearInterval(V.current),V.current=null),q(100),!t||!t.url)throw Error("Invalid upload response - missing URL");let r=t.url;if(!r||!r.startsWith("http://")&&!r.startsWith("https://"))throw Error("Invalid CDN URL format received");B(r),l(r),j&&j(e),b.toast.success("Image uploaded successfully!");try{URL.revokeObjectURL(a)}catch(e){console.warn("Failed to revoke object URL:",e)}}catch(r){console.error("Upload failed:",r),V.current&&(clearInterval(V.current),V.current=null);let e=r?.message||"Upload failed. Please try again.";b.toast.error(e),B(t||""),A(null);try{URL.revokeObjectURL(a)}catch(e){console.warn("Failed to revoke object URL:",e)}}finally{V.current&&(clearInterval(V.current),V.current=null),O(!1),q(0)}},[U,l,j,C,I,t]),_=(0,s.useCallback)(e=>{e.target.files&&e.target.files[0]&&Q(e.target.files[0])},[Q]),K=(0,s.useCallback)(e=>{try{if(B(e),A(null),e&&e.trim())try{new URL(e)}catch(a){console.warn("Invalid URL format:",e),b.toast.error("Please enter a valid URL");return}l(e)}catch(e){console.error("Error handling URL change:",e),b.toast.error("Failed to update image URL")}},[l]),H=(0,s.useCallback)(()=>{B(""),A(null),l(""),G.current&&(G.current.value="")},[l]),X=e=>{if(0===e)return"0 Bytes";let a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["Bytes","KB","MB","GB"][a]};return(0,r.jsxs)("div",{ref:P,className:(0,y.cn)("space-y-3",R),children:[e&&(0,r.jsxs)(h._,{className:(0,y.cn)("text-sm font-medium",L&&"text-red-600"),children:[e,k&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)(x.mQ,{value:$,onValueChange:e=>D(e),className:"w-full",children:[(0,r.jsxs)(x.dr,{className:"grid w-full grid-cols-2",children:[(0,r.jsxs)(x.SP,{value:"url",className:"flex items-center gap-2",children:[r.jsx(i.Z,{className:"w-4 h-4"}),"URL"]}),(0,r.jsxs)(x.SP,{value:"upload",className:"flex items-center gap-2",children:[r.jsx(o.Z,{className:"w-4 h-4"}),"Upload"]})]}),r.jsx(x.nU,{value:"url",className:"space-y-3",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(f.I,{type:"url",placeholder:F,value:T?`File: ${T.name}`:Z,onChange:e=>K(e.target.value),disabled:!!T,className:(0,y.cn)(L&&"border-red-500 focus:border-red-500")}),(Z||T)&&r.jsx(p.z,{type:"button",variant:"ghost",size:"sm",onClick:H,className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0",children:r.jsx(n.Z,{className:"w-4 h-4"})})]})}),r.jsx(x.nU,{value:"upload",className:"space-y-3",children:(0,r.jsxs)("div",{className:(0,y.cn)("relative border-2 border-dashed rounded-lg p-6 transition-colors",S?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400",L&&"border-red-500","cursor-pointer"),onDragEnter:J,onDragLeave:J,onDragOver:J,onDrop:Y,onClick:()=>G.current?.click(),children:[r.jsx("input",{ref:G,type:"file",accept:w,onChange:_,className:"hidden"}),r.jsx("div",{className:"flex flex-col items-center justify-center text-center",children:M?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.Z,{className:"w-12 h-12 text-blue-500 mb-3 animate-spin"}),r.jsx("p",{className:"text-sm font-medium text-blue-700",children:"Uploading image..."}),(0,r.jsxs)("div",{className:"w-full max-w-xs mt-3",children:[r.jsx("div",{className:"bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${W}%`}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[W,"% complete"]})]})]}):T?(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.Z,{className:"w-12 h-12 text-green-500 mb-3"}),r.jsx("p",{className:"text-sm font-medium text-green-700",children:T.name}),r.jsx("p",{className:"text-xs text-gray-500",children:X(T.size)}),(0,r.jsxs)(p.z,{type:"button",variant:"outline",size:"sm",onClick:e=>{e.stopPropagation(),H()},className:"mt-3",disabled:M,children:[r.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Remove"]})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.Z,{className:(0,y.cn)("w-12 h-12 mb-3",S?"text-blue-500":"text-gray-400")}),r.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Drag & drop an image here, or click to select"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Supports PNG, JPG, JPEG (max ",U,"MB)"]}),r.jsx("p",{className:"text-xs text-blue-600 mt-1 font-medium",children:"Images will be uploaded to CDN automatically"})]})})]})})]}),E&&(Z||T)&&r.jsx(g.Zb,{className:"overflow-hidden",children:r.jsx(g.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"flex-shrink-0",children:(0,r.jsxs)("div",{className:"relative w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border",children:[r.jsx("img",{src:Z,alt:"Preview",className:"w-full h-full object-cover",onError:e=>{let a=e.target;a.src="",a.style.display="none",console.warn("Failed to load image preview:",Z)},onLoad:()=>{console.log("Image preview loaded successfully")}}),!Z&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:r.jsx(u.Z,{className:"w-6 h-6 text-gray-400"})})]})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"text-sm font-medium truncate",children:T?T.name:"Image Preview"}),T&&r.jsx("p",{className:"text-xs text-gray-500",children:X(T.size)}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[r.jsx(v.C,{variant:"secondary",className:"text-xs",children:T?"Local File":"Remote URL"}),T&&r.jsx(v.C,{variant:"outline",className:"text-xs",children:T.type})]})]})]})})}),a&&!L&&r.jsx("p",{className:"text-sm text-gray-500",children:a}),L&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[r.jsx(m.Z,{className:"w-4 h-4"}),L]})]})});U.displayName="ImageUpload";let R=U},7361:(e,a,t)=>{t.d(a,{_:()=>d});var r=t(95344),s=t(3729),l=t(14217),i=t(49247),o=t(11453);let n=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...a},t)=>r.jsx(l.f,{ref:t,className:(0,o.cn)(n(),e),...a}));d.displayName=l.f.displayName},68065:(e,a,t)=>{t.d(a,{E:()=>n,m:()=>d});var r=t(95344),s=t(3729),l=t(9913),i=t(82958),o=t(11453);let n=s.forwardRef(({className:e,...a},t)=>r.jsx(l.fC,{className:(0,o.cn)("grid gap-2",e),...a,ref:t}));n.displayName=l.fC.displayName;let d=s.forwardRef(({className:e,...a},t)=>r.jsx(l.ck,{ref:t,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:r.jsx(l.z$,{className:"flex items-center justify-center",children:r.jsx(i.Z,{className:"h-3.5 w-3.5 fill-primary"})})}));d.displayName=l.ck.displayName},51467:(e,a,t)=>{t.d(a,{SP:()=>d,dr:()=>n,mQ:()=>o,nU:()=>c});var r=t(95344),s=t(3729),l=t(89128),i=t(11453);let o=l.fC,n=s.forwardRef(({className:e,...a},t)=>r.jsx(l.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));n.displayName=l.aV.displayName;let d=s.forwardRef(({className:e,...a},t)=>r.jsx(l.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));d.displayName=l.xz.displayName;let c=s.forwardRef(({className:e,...a},t)=>r.jsx(l.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=l.VY.displayName},2690:(e,a,t)=>{t.d(a,{g:()=>i});var r=t(95344),s=t(3729),l=t(11453);let i=s.forwardRef(({className:e,...a},t)=>r.jsx("textarea",{className:(0,l.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));i.displayName="Textarea"}};