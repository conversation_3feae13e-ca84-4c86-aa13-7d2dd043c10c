exports.id=6317,exports.ids=[6317],exports.modules={97342:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},53622:(e,r,t)=>{Promise.resolve().then(t.bind(t,34755)),Promise.resolve().then(t.bind(t,64792)),Promise.resolve().then(t.bind(t,89284)),Promise.resolve().then(t.bind(t,11503))},5094:(e,r,t)=>{"use strict";t.d(r,{d:()=>l,z:()=>c});var s=t(95344),a=t(3729),o=t(32751),i=t(49247),n=t(11453);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...i},c)=>{let d=a?o.g7:"button";return s.jsx(d,{className:(0,n.cn)(l({variant:r,size:t,className:e})),ref:c,...i})});c.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>n,SZ:()=>c,Zb:()=>i,aY:()=>d,ll:()=>l});var s=t(95344),a=t(3729),o=t(11453);let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));i.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},64792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DefaultErrorFallback:()=>u,ErrorBoundary:()=>d,useErrorHandler:()=>h});var s=t(95344),a=t(3729),o=t.n(a),i=t(5094),n=t(23673),l=t(65719),c=t(33733);class d extends o().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return s.jsx(e,{error:this.state.error,resetError:this.resetError})}return s.jsx(u,{error:this.state.error,resetError:this.resetError})}return this.props.children}}let u=({error:e,resetError:r})=>s.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(n.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(n.Ol,{className:"text-center",children:[s.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:s.jsx(l.Z,{className:"h-6 w-6 text-red-600"})}),s.jsx(n.ll,{className:"text-red-900",children:"Something went wrong"}),s.jsx(n.SZ,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,s.jsxs)(n.aY,{className:"space-y-4",children:[!1,(0,s.jsxs)(i.z,{onClick:r,className:"w-full",variant:"outline",children:[s.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}),h=()=>{let[e,r]=o().useState(null),t=o().useCallback(()=>{r(null)},[]),s=o().useCallback(e=>{r(e)},[]);return o().useEffect(()=>{if(e)throw e},[e]),{captureError:s,resetError:t}}},52962:(e,r,t)=>{"use strict";t.d(r,{SX:()=>i,TK:()=>o});var s=t(95344);t(3729),t(86688),t(23673);var a=t(56389);let o=({size:e="md",className:r=""})=>s.jsx(a.Z,{className:`animate-spin ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${r}`}),i=({message:e="Loading..."})=>(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[s.jsx(o,{size:"lg"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:e})]})},86688:(e,r,t)=>{"use strict";t.d(r,{Od:()=>o,hM:()=>n,q4:()=>i});var s=t(95344),a=t(11453);function o({className:e,...r}){return s.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",e),...r})}let i=({className:e})=>(0,s.jsxs)("div",{className:(0,a.cn)("border rounded-lg p-6 space-y-4",e),children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(o,{className:"h-4 w-3/4"}),s.jsx(o,{className:"h-4 w-1/2"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(o,{className:"h-3 w-full"}),s.jsx(o,{className:"h-3 w-full"}),s.jsx(o,{className:"h-3 w-2/3"})]})]}),n=({rows:e=5,columns:r=4,className:t})=>s.jsx("div",{className:(0,a.cn)("space-y-4",t),children:(0,s.jsxs)("div",{className:"border rounded-lg",children:[s.jsx("div",{className:"border-b p-4",children:s.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${r}, 1fr)`},children:Array.from({length:r}).map((e,r)=>s.jsx(o,{className:"h-4 w-20"},r))})}),Array.from({length:e}).map((e,t)=>s.jsx("div",{className:"border-b last:border-b-0 p-4",children:s.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${r}, 1fr)`},children:Array.from({length:r}).map((e,r)=>s.jsx(o,{className:"h-4 w-full"},r))})},t))]})})},59358:(e,r,t)=>{"use strict";t.d(r,{i:()=>o});var s=t(50053),a=t(54074);let o={login:async e=>{console.log("\uD83D\uDD10 Attempting login via proxy...");try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){let e=await r.json();throw Error(e.message||"Login failed")}let t=await r.json();console.log("✅ Login successful via proxy");let s=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t.accessToken}`}});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to fetch profile")}return{user:await s.json(),accessToken:t.accessToken,refreshToken:t.refreshToken}}catch(r){if(console.error("❌ Login failed via proxy:",r.message),(r.message.includes("fetch")||r.message.includes("network"))&&(console.warn("⚠️ Network error, using mock data"),"admin"===e.username&&"admin123456"===e.password)){let e={user:{id:1,username:"admin",email:"<EMAIL>",fullName:"System Administrator",role:"admin",isActive:!0,lastLoginAt:new Date().toISOString(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now()};return await new Promise(e=>setTimeout(e,500)),e}throw r}},logout:async e=>{let r=await fetch("/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!r.ok)throw Error((await r.json()).message||"Logout failed");return await r.json()},logoutFromAllDevices:async()=>await s.x.post("/system-auth/logout-all"),refreshToken:async e=>{let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!r.ok)throw Error((await r.json()).message||"Token refresh failed");return await r.json()},getProfile:async()=>{let e=a.t.getState(),r=e.accessToken,t=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}}});if(!t.ok){if(401===t.status)throw console.warn("⚠️ Token expired, forcing logout..."),e.clearAuth(),window.location.href="/auth/login",Error("Token expired, please login again");throw Error((await t.json()).message||"Failed to fetch profile")}return await t.json()},updateProfile:async e=>await s.x.put("/system-auth/profile",e),changePassword:async e=>await s.x.post("/system-auth/change-password",e),createUser:async e=>await s.x.post("/system-auth/users",e),updateUser:async(e,r)=>await s.x.put(`/system-auth/users/${e}`,r)}},50053:(e,r,t)=>{"use strict";t.d(r,{x:()=>i});var s=t(47665),a=t(54074);class o{constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}setupInterceptors(){this.client.interceptors.request.use(e=>{let r=this.getAuthToken();return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let r=e.config;if(e.response?.status===401&&!r._retry){if(this.isRefreshing)return new Promise((e,r)=>{this.failedQueue.push({resolve:e,reject:r})}).then(e=>(r.headers.Authorization=`Bearer ${e}`,this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization=`Bearer ${t}`,this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){return null}async refreshToken(){let e=a.t.getState(),r=e.refreshToken;if(!r)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:r})});if(!t.ok)throw Error("Token refresh failed");let{accessToken:s}=await t.json(),a=e.user;if(a)return e.setAuth(a,s,r),this.setAuthToken(s),console.log("✅ Token refreshed successfully"),s}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e,r=null){this.failedQueue.forEach(({resolve:t,reject:s})=>{e?s(e):t(r)}),this.failedQueue=[]}handleUnauthorized(){}setAuthToken(e){}removeAuthToken(){}async get(e,r){return(await this.client.get(e,r)).data}async post(e,r,t){return(await this.client.post(e,r,t)).data}async put(e,r,t){return(await this.client.put(e,r,t)).data}async patch(e,r,t){return(await this.client.patch(e,r,t)).data}async delete(e,r){return(await this.client.delete(e,r)).data}}let i=new o},48333:(e,r,t)=>{"use strict";t.d(r,{a:()=>c});var s=t(11494),a=t(14373),o=t(19738),i=t(59358),n=t(54074),l=t(50053);let c=()=>{let e=(0,s.NL)(),{setAuth:r,clearAuth:t,setLoading:c,user:d,isAuthenticated:u}=(0,n.t)(),h=(0,a.D)({mutationFn:i.i.login,onMutate:()=>{c(!0)},onSuccess:t=>{r(t.user,t.accessToken,t.refreshToken),l.x.setAuthToken(t.accessToken),e.invalidateQueries({queryKey:["auth","profile"]})},onError:e=>{console.error("Login failed:",e),c(!1)}}),m=(0,a.D)({mutationFn:e=>i.i.logout(e),onSuccess:()=>{t(),l.x.removeAuthToken(),e.clear()},onError:()=>{t(),l.x.removeAuthToken(),e.clear()}}),f=(0,a.D)({mutationFn:i.i.logoutFromAllDevices,onSuccess:()=>{t(),l.x.removeAuthToken(),e.clear()}}),p=(0,o.a)({queryKey:["auth","profile"],queryFn:i.i.getProfile,enabled:u,staleTime:6e5}),g=(0,a.D)({mutationFn:e=>i.i.updateProfile(e),onSuccess:r=>{e.setQueryData(["auth","profile"],r),n.t.getState().updateUser(r)}}),y=(0,a.D)({mutationFn:i.i.changePassword,onSuccess:()=>{}}),x=(0,a.D)({mutationFn:e=>i.i.refreshToken(e),onSuccess:e=>{let t=n.t.getState().user,s=n.t.getState().refreshToken;t&&s&&(r(t,e.accessToken,s),l.x.setAuthToken(e.accessToken))},onError:()=>{t(),l.x.removeAuthToken(),e.clear()}});return{user:d,isAuthenticated:u,isLoading:(0,n.t)(e=>e.isLoading),profile:p.data,isProfileLoading:p.isLoading,profileError:p.error,login:h.mutate,logout:e=>m.mutate(e),logoutAll:f.mutate,updateProfile:g.mutate,changePassword:y.mutate,refreshToken:x.mutate,isLoginLoading:h.isLoading,loginError:h.error,isLogoutLoading:m.isLoading,isUpdateProfileLoading:g.isLoading,updateProfileError:g.error,isChangePasswordLoading:y.isLoading,changePasswordError:y.error}}},89284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryProvider:()=>n});var s=t(95344),a=t(86166),o=t(11494),i=t(3729);let n=({children:e})=>{let[r]=(0,i.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,s.jsxs)(o.aH,{client:r,children:[e,!1]})}},11503:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ThemeProvider:()=>i,useTheme:()=>n});var s=t(95344),a=t(3729);let o=(0,a.createContext)({theme:"system",setTheme:()=>null});function i({children:e,defaultTheme:r="system",storageKey:t="cms-theme",...i}){let[n,l]=(0,a.useState)(r),[c,d]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{d(!0);let e=localStorage?.getItem(t);e&&l(e)},[t]),(0,a.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===n){let r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(r);return}e.classList.add(n)},[n]),c)?s.jsx(o.Provider,{...i,value:{theme:n,setTheme:e=>{localStorage?.setItem(t,e),l(e)}},children:e}):null}let n=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},54074:(e,r,t)=>{"use strict";t.d(r,{t:()=>i});var s=t(19369),a=t(67023);let o={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},i=(0,s.U)()((0,a.tJ)((e,r)=>({...o,setAuth:(r,t,s)=>{e({user:r,accessToken:t,refreshToken:s,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(o)},setLoading:r=>{e({isLoading:r})},updateUser:t=>{let s=r().user;s&&e({user:{...s,...t}})},hasPermission:e=>{let t=r().user;if(!t)return!1;let s=Array.isArray(e)?e:[e];return"admin"===t.role||(s.includes("editor")?["admin","editor"].includes(t.role):s.includes("moderator")?["admin","editor","moderator"].includes(t.role):s.includes(t.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},11453:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(56815),a=t(79377);function o(...e){return(0,a.m6)((0,s.W)(e))}},32417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j,metadata:()=>v});var s=t(25036),a=t(42195),o=t.n(a);t(5023);var i=t(86843);let n=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx`),{__esModule:l,$$typeof:c}=n;n.default;let d=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx#QueryProvider`),u=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx`),{__esModule:h,$$typeof:m}=u;u.default;let f=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#ThemeProvider`);(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#useTheme`);let p=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx`),{__esModule:g,$$typeof:y}=p;p.default;let x=(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#ErrorBoundary`);(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#DefaultErrorFallback`),(0,i.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#useErrorHandler`);var w=t(27171);let v={title:"APISportsGame CMS",description:"Content Management System for APISportsGame API"};function j({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:o().className,children:s.jsx(f,{defaultTheme:"system",children:s.jsx(x,{children:(0,s.jsxs)(d,{children:[e,s.jsx(w.x7,{position:"top-right",richColors:!0})]})})})})})}},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(70337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};