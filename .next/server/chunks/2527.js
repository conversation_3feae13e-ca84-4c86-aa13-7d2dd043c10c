"use strict";exports.id=2527,exports.ids=[2527],exports.modules={46327:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},38271:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},43231:(e,t,n)=>{/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(3729),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useSyncExternalStore,i=r.useRef,u=r.useEffect,a=r.useMemo,s=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=l(e,(d=a(function(){function e(e){if(!u){if(u=!0,l=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,o(l,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(l=e,t):(l=e,i=n)}var l,i,u=!1,a=void 0===n?null:n;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,n,r,c]))[0],d[1]);return u(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},40687:(e,t,n)=>{e.exports=n(43231)},13659:(e,t,n)=>{n.d(t,{Vq:()=>tt});var r,o,l,i,u,a,s,c,d,f,p,m,v,h,g,b=n(3729),E=((r=E||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r),y=n(20313);function w(e,t,n,r){let o=(0,y.E)(n);(0,b.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class S extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var k=n(42107),F=Object.defineProperty,P=(e,t,n)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,T=(e,t,n)=>(P(e,"symbol"!=typeof t?t+"":t,n),n),C=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},O=(e,t,n)=>(C(e,t,"read from private field"),n?n.call(e):t.get(e)),x=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},A=(e,t,n,r)=>(C(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class L{constructor(e){x(this,v,{}),x(this,h,new S(()=>new Set)),x(this,g,new Set),T(this,"disposables",(0,k.k)()),A(this,v,e)}dispose(){this.disposables.dispose()}get state(){return O(this,v)}subscribe(e,t){let n={selector:e,callback:t,current:e(O(this,v))};return O(this,g).add(n),this.disposables.add(()=>{O(this,g).delete(n)})}on(e,t){return O(this,h).get(e).add(t),this.disposables.add(()=>{O(this,h).get(e).delete(t)})}send(e){let t=this.reduce(O(this,v),e);if(t!==O(this,v)){for(let e of(A(this,v,t),O(this,g))){let t=e.selector(O(this,v));N(e.current,t)||(e.current=t,e.callback(t))}for(let t of O(this,h).get(e.type))t(O(this,v),e)}}}function N(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&M(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&M(e.entries(),t.entries()):!!(R(e)&&R(t))&&M(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function M(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function R(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}v=new WeakMap,h=new WeakMap,g=new WeakMap;var D=n(46070),j=Object.defineProperty,I=(e,t,n)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,V=(e,t,n)=>(I(e,"symbol"!=typeof t?t+"":t,n),n),z=((o=z||{})[o.Push=0]="Push",o[o.Pop=1]="Pop",o);let H={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class W extends L{constructor(){super(...arguments),V(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),V(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new W({stack:[]})}reduce(e,t){return(0,D.E)(t.type,H,e,t)}}let $=new S(()=>W.new());var U=n(40687),_=n(70621);function Y(e,t,n=N){return(0,U.useSyncExternalStoreWithSelector)((0,_.z)(t=>e.subscribe(Z,t)),(0,_.z)(()=>e.state),(0,_.z)(()=>e.state),(0,_.z)(t),n)}function Z(e){return e}var q=n(33783);function B(e,t){let n=(0,b.useId)(),r=$.get(t),[o,l]=Y(r,(0,b.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,q.e)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!l||o)}var G=n(62670);function K(e){var t,n;return G.O.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let X=new Map,J=new Map;function Q(e){var t;let n=null!=(t=J.get(e))?t:0;return J.set(e,n+1),0!==n||(X.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=J.get(e))?t:1;if(1===n?J.delete(e):J.set(e,n-1),1!==n)return;let r=X.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,X.delete(e))})(e)}function ee(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function et(e){return ee(e)&&"tagName"in e}function en(e){return et(e)&&"accessKey"in e}function er(e){return et(e)&&"tabIndex"in e}let eo=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),el=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var ei=((l=ei||{})[l.First=1]="First",l[l.Previous=2]="Previous",l[l.Next=4]="Next",l[l.Last=8]="Last",l[l.WrapAround=16]="WrapAround",l[l.NoScroll=32]="NoScroll",l[l.AutoFocus=64]="AutoFocus",l),eu=((i=eu||{})[i.Error=0]="Error",i[i.Overflow=1]="Overflow",i[i.Success=2]="Success",i[i.Underflow=3]="Underflow",i),ea=((u=ea||{})[u.Previous=-1]="Previous",u[u.Next=1]="Next",u),es=((a=es||{})[a.Strict=0]="Strict",a[a.Loose=1]="Loose",a),ec=((s=ec||{})[s.Keyboard=0]="Keyboard",s[s.Mouse=1]="Mouse",s);function ed(e){null==e||e.focus({preventScroll:!0})}function ef(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var l,i,u;let a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(el)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(eo)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:a.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,m=s.length,v;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(v=s[e])||v.focus(f),p+=c}while(v!==a.activeElement);return 6&t&&null!=(u=null==(i=null==(l=v)?void 0:l.matches)?void 0:i.call(l,"textarea,input"))&&u&&v.select(),2}function ep(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function em(){return ep()||/Android/gi.test(window.navigator.userAgent)}function ev(e,t,n,r){let o=(0,y.E)(n);(0,b.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function eh(e,t,n,r){let o=(0,y.E)(n);(0,b.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function eg(...e){return(0,b.useMemo)(()=>K(...e),[...e])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var eb=n(84653),eE=((c=eE||{})[c.None=1]="None",c[c.Focusable=2]="Focusable",c[c.Hidden=4]="Hidden",c);let ey=(0,eb.yV)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,eb.L6)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),ew=(0,b.createContext)(null);function eS({children:e,node:t}){let[n,r]=(0,b.useState)(null),o=ek(null!=t?t:n);return b.createElement(ew.Provider,{value:o},e,null===o&&b.createElement(ey,{features:eE.Hidden,ref:e=>{var t,n;if(e){for(let o of null!=(n=null==(t=K(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(o!==document.body&&o!==document.head&&et(o)&&null!=o&&o.contains(e)){r(o);break}}}}))}function ek(e=null){var t;return null!=(t=(0,b.useContext)(ew))?t:e}let eF=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let l=t[e].call(n,...o);l&&(n=l,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,k.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},l=[ep()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,k.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",t=>{if(er(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),i=e.querySelector(o);er(i)&&!r(i)&&(l=i)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var n;if(er(e.target)&&et(n=e.target)&&"style"in n){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(er(e.target)){var t;if(!(en(t=e.target)&&"INPUT"===t.nodeName)){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth)},after({doc:e,d:t}){let n=e.documentElement,o=Math.max(0,n.clientWidth-n.offsetWidth),l=Math.max(0,r-o);t.style(n,"paddingRight",`${l}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];l.forEach(({before:e})=>null==e?void 0:e(o)),l.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});eF.subscribe(()=>{let e=eF.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&eF.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&eF.dispatch("TEARDOWN",n)}});var eP=n(50296),eT=n(3159);let eC=(0,b.createContext)(()=>{});function eO({value:e,children:t}){return b.createElement(eC.Provider,{value:e},t)}var ex=n(11786);let eA=(0,b.createContext)(!1);function eL(e){return b.createElement(eA.Provider,{value:e.force},e.children)}let eN=(0,b.createContext)(void 0),eM=(0,b.createContext)(null);eM.displayName="DescriptionContext";let eR=Object.assign((0,eb.yV)(function(e,t){let n=(0,b.useId)(),r=(0,b.useContext)(eN),{id:o=`headlessui-description-${n}`,...l}=e,i=function e(){let t=(0,b.useContext)(eM);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=(0,eT.T)(t);(0,q.e)(()=>i.register(o),[o,i.register]);let a=r||!1,s=(0,b.useMemo)(()=>({...i.slot,disabled:a}),[i.slot,a]),c={ref:u,...i.props,id:o};return(0,eb.L6)()({ourProps:c,theirProps:l,slot:s,defaultTag:"p",name:i.name||"Description"})}),{});var eD=n(70053),ej=n(89788),eI=n(17209);function eV(e){let t=(0,_.z)(e),n=(0,b.useRef)(!1);(0,b.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,eI.Y)(()=>{n.current&&t()})}),[t])}var ez=((d=ez||{})[d.Forwards=0]="Forwards",d[d.Backwards=1]="Backwards",d);function eH(e,t){let n=(0,b.useRef)([]),r=(0,_.z)(e);(0,b.useEffect)(()=>{let e=[...n.current];for(let[o,l]of t.entries())if(n.current[o]!==l){let o=r(t,e);return n.current=t,o}},[r,...t])}let eW=[];function e$(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)et(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!er(e.target)||e.target===document.body||eW[0]===e.target)return;let t=e.target;t=t.closest(eo),eW.unshift(null!=t?t:e.target),(eW=eW.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eU=((f=eU||{})[f.None=0]="None",f[f.InitialFocus=1]="InitialFocus",f[f.TabLock=2]="TabLock",f[f.FocusLock=4]="FocusLock",f[f.RestoreFocus=8]="RestoreFocus",f[f.AutoFocus=16]="AutoFocus",f);let e_=Object.assign((0,eb.yV)(function(e,t){let n,r=(0,b.useRef)(null),o=(0,eT.T)(r,t),{initialFocus:l,initialFocusFallback:i,containers:u,features:a=15,...s}=e;(0,eP.H)()||(a=0);let c=eg(r);(function(e,{ownerDocument:t}){let n=!!(8&e),r=function(e=!0){let t=(0,b.useRef)(eW.slice());return eH(([e],[n])=>{!0===n&&!1===e&&(0,eI.Y)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=eW.slice())},[e,eW,t]),(0,_.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(n);eH(()=>{n||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&ed(r())},[n]),eV(()=>{n&&ed(r())})})(a,{ownerDocument:c});let d=function(e,{ownerDocument:t,container:n,initialFocus:r,initialFocusFallback:o}){let l=(0,b.useRef)(null),i=B(!!(1&e),"focus-trap#initial-focus"),u=(0,ej.t)();return eH(()=>{if(0===e)return;if(!i){null!=o&&o.current&&ed(o.current);return}let a=n.current;a&&(0,eI.Y)(()=>{if(!u.current)return;let n=null==t?void 0:t.activeElement;if(null!=r&&r.current){if((null==r?void 0:r.current)===n){l.current=n;return}}else if(a.contains(n)){l.current=n;return}if(null!=r&&r.current)ed(r.current);else{if(16&e){if(ef(a,ei.First|ei.AutoFocus)!==eu.Error)return}else if(ef(a,ei.First)!==eu.Error)return;if(null!=o&&o.current&&(ed(o.current),(null==t?void 0:t.activeElement)===o.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}l.current=null==t?void 0:t.activeElement})},[o,i,e]),l}(a,{ownerDocument:c,container:r,initialFocus:l,initialFocusFallback:i});(function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:o}){let l=(0,ej.t)(),i=!!(4&e);w(null==t?void 0:t.defaultView,"focus",e=>{if(!i||!l.current)return;let t=e$(r);en(n.current)&&t.add(n.current);let u=o.current;if(!u)return;let a=e.target;en(a)?eY(t,a)?(o.current=a,ed(a)):(e.preventDefault(),e.stopPropagation(),ed(u)):ed(o.current)},!0)})(a,{ownerDocument:c,container:r,containers:u,previousActiveElement:d});let f=(n=(0,b.useRef)(0),eh(!0,"keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),p=(0,_.z)(e=>{if(!en(r.current))return;let t=r.current;(0,D.E)(f.current,{[ez.Forwards]:()=>{ef(t,ei.First,{skipElements:[e.relatedTarget,i]})},[ez.Backwards]:()=>{ef(t,ei.Last,{skipElements:[e.relatedTarget,i]})}})}),m=B(!!(2&a),"focus-trap#tab-lock"),v=(0,eD.G)(),h=(0,b.useRef)(!1),g=(0,eb.L6)();return b.createElement(b.Fragment,null,m&&b.createElement(ey,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eE.Focusable}),g({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(h.current=!0,v.requestAnimationFrame(()=>{h.current=!1}))},onBlur(e){if(!(4&a))return;let t=e$(u);en(r.current)&&t.add(r.current);let n=e.relatedTarget;er(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(eY(t,n)||(h.current?ef(r.current,(0,D.E)(f.current,{[ez.Forwards]:()=>ei.Next,[ez.Backwards]:()=>ei.Previous})|ei.WrapAround,{relativeTo:e.target}):er(e.target)&&ed(e.target)))}},theirProps:s,defaultTag:"div",name:"FocusTrap"}),m&&b.createElement(ey,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eE.Focusable}))}),{features:eU});function eY(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var eZ=n(81202);let eq=b.Fragment,eB=(0,eb.yV)(function(e,t){let{ownerDocument:n=null,...r}=e,o=(0,b.useRef)(null),l=(0,eT.T)((0,eT.h)(e=>{o.current=e}),t),i=eg(o),u=null!=n?n:i,a=function(e){let t=(0,b.useContext)(eA),n=(0,b.useContext)(eK),[r,o]=(0,b.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(G.O.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,b.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,b.useEffect)(()=>{t||null!==n&&o(n.current)},[n,o,t]),r}(u),[s]=(0,b.useState)(()=>{var e;return G.O.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),c=(0,b.useContext)(eX),d=(0,eP.H)();(0,q.e)(()=>{!a||!s||a.contains(s)||(s.setAttribute("data-headlessui-portal",""),a.appendChild(s))},[a,s]),(0,q.e)(()=>{if(s&&c)return c.register(s)},[c,s]),eV(()=>{var e;a&&s&&(ee(s)&&a.contains(s)&&a.removeChild(s),a.childNodes.length<=0&&(null==(e=a.parentElement)||e.removeChild(a)))});let f=(0,eb.L6)();return d&&a&&s?(0,eZ.createPortal)(f({ourProps:{ref:l},theirProps:r,slot:{},defaultTag:eq,name:"Portal"}),s):null}),eG=b.Fragment,eK=(0,b.createContext)(null),eX=(0,b.createContext)(null),eJ=(0,eb.yV)(function(e,t){let n=(0,eT.T)(t),{enabled:r=!0,ownerDocument:o,...l}=e,i=(0,eb.L6)();return r?b.createElement(eB,{...l,ownerDocument:o,ref:n}):i({ourProps:{ref:n},theirProps:l,slot:{},defaultTag:eq,name:"Portal"})}),eQ=(0,eb.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,eT.T)(t)},l=(0,eb.L6)();return b.createElement(eK.Provider,{value:n},l({ourProps:o,theirProps:r,defaultTag:eG,name:"Popover.Group"}))}),e0=Object.assign(eJ,{Group:eQ});var e1=n(81021),e2=((p=e2||{})[p.Open=0]="Open",p[p.Closed=1]="Closed",p),e3=((m=e3||{})[m.SetTitleId=0]="SetTitleId",m);let e7={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},e4=(0,b.createContext)(null);function e6(e){let t=(0,b.useContext)(e4);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,e6),t}return t}function e8(e,t){return(0,D.E)(t.type,e7,e,t)}e4.displayName="DialogContext";let e9=(0,eb.yV)(function(e,t){let n,r,o,l,i,u,a,s,c,d,f=(0,b.useId)(),{id:p=`headlessui-dialog-${f}`,open:m,onClose:v,initialFocus:h,role:g="dialog",autoFocus:S=!0,__demoMode:F=!1,unmount:P=!1,...T}=e,C=(0,b.useRef)(!1);g="dialog"===g||"alertdialog"===g?g:(C.current||(C.current=!0,console.warn(`Invalid role [${g}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let O=(0,ex.oJ)();void 0===m&&null!==O&&(m=(O&ex.ZM.Open)===ex.ZM.Open);let x=(0,b.useRef)(null),A=(0,eT.T)(x,t),L=eg(x),N=m?0:1,[M,R]=(0,b.useReducer)(e8,{titleId:null,descriptionId:null,panelRef:(0,b.createRef)()}),j=(0,_.z)(()=>v(!1)),I=(0,_.z)(e=>R({type:0,id:e})),V=!!(0,eP.H)()&&0===N,[z,H]=(n=(0,b.useContext)(eX),r=(0,b.useRef)([]),o=(0,_.z)(e=>(r.current.push(e),n&&n.register(e),()=>l(e))),l=(0,_.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),i=(0,b.useMemo)(()=>({register:o,unregister:l,portals:r}),[o,l,r]),[r,(0,b.useMemo)(()=>function({children:e}){return b.createElement(eX.Provider,{value:i},e)},[i])]),W=ek(),{resolveContainers:U}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=eg(n),o=(0,_.z)(()=>{var o,l;let i=[];for(let t of e)null!==t&&(et(t)?i.push(t):"current"in t&&et(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&et(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:o,contains:(0,_.z)(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:W,portals:z,defaultContainers:[{get current(){var Z;return null!=(Z=M.panelRef.current)?Z:x.current}}]}),G=null!==O&&(O&ex.ZM.Closing)===ex.ZM.Closing;!function(e,{allowed:t,disallowed:n}={}){let r=B(e,"inert-others");(0,q.e)(()=>{var e,o;if(!r)return;let l=(0,k.k)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(Q(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let e of i){if(!e)continue;let t=K(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(Q(e));n=n.parentElement}}return l.dispose},[r,t,n])}(!F&&!G&&V,{allowed:(0,_.z)(()=>{var e,t;return[null!=(t=null==(e=x.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,_.z)(()=>{var e;return[null!=(e=null==W?void 0:W.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let X=$.get(null);(0,q.e)(()=>{if(V)return X.actions.push(p),()=>X.actions.pop(p)},[X,p,V]);let J=Y(X,(0,b.useCallback)(e=>X.selectors.isTop(e,p),[X,p]));u=(0,y.E)(e=>{e.preventDefault(),j()}),a=(0,b.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(U))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e,t=0){var n;return e!==(null==(n=K(e))?void 0:n.body)&&(0,D.E)(t,{0:()=>e.matches(eo),1(){let t=e;for(;null!==t;){if(t.matches(eo))return!0;t=t.parentElement}return!1}})}(n,es.Loose)||-1===n.tabIndex||e.preventDefault(),u.current(e,n)}},[u,U]),s=(0,b.useRef)(null),ev(J,"pointerdown",e=>{var t,n;em()||(s.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ev(J,"pointerup",e=>{if(em()||!s.current)return;let t=s.current;return s.current=null,a(e,()=>t)},!0),c=(0,b.useRef)({x:0,y:0}),ev(J,"touchstart",e=>{c.current.x=e.touches[0].clientX,c.current.y=e.touches[0].clientY},!0),ev(J,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-c.current.x)>=30||Math.abs(t.y-c.current.y)>=30))return a(e,()=>er(e.target)?e.target:null)},!0),eh(J,"blur",e=>a(e,()=>{var e;return en(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e,t="undefined"!=typeof document?document.defaultView:null,n){let r=B(e,"escape");w(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===E.Escape&&n(e))})}(J,null==L?void 0:L.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),j()}),function(e,t,n=()=>[document.body]){!function(e,t,n=()=>({containers:[]})){let r=(0,b.useSyncExternalStore)(eF.subscribe,eF.getSnapshot,eF.getSnapshot),o=t?r.get(t):void 0;o&&o.count,(0,q.e)(()=>{if(!(!t||!e))return eF.dispatch("PUSH",t,n),()=>eF.dispatch("POP",t,n)},[e,t])}(B(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!F&&!G&&V,L,U),d=(0,y.E)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&j()}),(0,b.useEffect)(()=>{if(!V)return;let e=null===x?null:en(x)?x:x.current;if(!e)return;let t=(0,k.k)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>d.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>d.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[x,d,V]);let[ee,el]=function(){let[e,t]=(0,b.useState)([]);return[e.length>0?e.join(" "):void 0,(0,b.useMemo)(()=>function(e){let n=(0,_.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,b.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return b.createElement(eM.Provider,{value:r},e.children)},[t])]}(),ei=(0,b.useMemo)(()=>[{dialogState:N,close:j,setTitleId:I,unmount:P},M],[N,M,j,I,P]),eu=(0,b.useMemo)(()=>({open:0===N}),[N]),ea={ref:A,id:p,role:g,tabIndex:-1,"aria-modal":F?void 0:0===N||void 0,"aria-labelledby":M.titleId,"aria-describedby":ee,unmount:P},ec=!function(){var e;let[t]=(0,b.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,b.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,q.e)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),ed=eU.None;V&&!F&&(ed|=eU.RestoreFocus,ed|=eU.TabLock,S&&(ed|=eU.AutoFocus),ec&&(ed|=eU.InitialFocus));let ef=(0,eb.L6)();return b.createElement(ex.uu,null,b.createElement(eL,{force:!0},b.createElement(e0,null,b.createElement(e4.Provider,{value:ei},b.createElement(eQ,{target:x},b.createElement(eL,{force:!1},b.createElement(el,{slot:eu},b.createElement(H,null,b.createElement(e_,{initialFocus:h,initialFocusFallback:x,containers:U,features:ed},b.createElement(eO,{value:j},ef({ourProps:ea,theirProps:T,slot:eu,defaultTag:e5,features:te,visible:0===N,name:"Dialog"})))))))))))}),e5="div",te=eb.VN.RenderStrategy|eb.VN.Static,tt=Object.assign((0,eb.yV)(function(e,t){let{transition:n=!1,open:r,...o}=e,l=(0,ex.oJ)(),i=e.hasOwnProperty("open")||null!==l,u=e.hasOwnProperty("onClose");if(!i&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!i)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!l&&"boolean"!=typeof e.open)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(void 0!==r||n)&&!o.static?b.createElement(eS,null,b.createElement(e1.u,{show:r,transition:n,unmount:o.unmount},b.createElement(e9,{ref:t,...o}))):b.createElement(eS,null,b.createElement(e9,{ref:t,open:r,...o}))}),{Panel:(0,eb.yV)(function(e,t){let n=(0,b.useId)(),{id:r=`headlessui-dialog-panel-${n}`,transition:o=!1,...l}=e,[{dialogState:i,unmount:u},a]=e6("Dialog.Panel"),s=(0,eT.T)(t,a.panelRef),c=(0,b.useMemo)(()=>({open:0===i}),[i]),d=(0,_.z)(e=>{e.stopPropagation()}),f=o?e1.x:b.Fragment,p=(0,eb.L6)();return b.createElement(f,{...o?{unmount:u}:{}},p({ourProps:{ref:s,id:r,onClick:d},theirProps:l,slot:c,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,eb.yV)(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:o,unmount:l}]=e6("Dialog.Backdrop"),i=(0,b.useMemo)(()=>({open:0===o}),[o]),u=n?e1.x:b.Fragment,a=(0,eb.L6)();return b.createElement(u,{...n?{unmount:l}:{}},a({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:i,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,eb.yV)(function(e,t){let n=(0,b.useId)(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:l,setTitleId:i}]=e6("Dialog.Title"),u=(0,eT.T)(t);(0,b.useEffect)(()=>(i(r),()=>i(null)),[r,i]);let a=(0,b.useMemo)(()=>({open:0===l}),[l]);return(0,eb.L6)()({ourProps:{ref:u,id:r},theirProps:o,slot:a,defaultTag:"h2",name:"Dialog.Title"})})),Description:eR})},81021:(e,t,n)=>{n.d(t,{u:()=>N,x:()=>L});var r,o,l,i,u=n(3729),a=n(70053),s=n(70621),c=n(89788),d=n(33783),f=n(20313),p=n(50296),m=n(3159),v=n(42107);"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(l=null==process?void 0:process.env)?void 0:l.NODE_ENV)==="test"&&void 0===(null==(i=null==Element?void 0:Element.prototype)?void 0:i.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var h=((r=h||{})[r.None=0]="None",r[r.Closed=1]="Closed",r[r.Enter=2]="Enter",r[r.Leave=4]="Leave",r),g=n(11786),b=n(28341),E=n(46070),y=n(84653);function w(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:C)!==u.Fragment||1===u.Children.count(e.children)}let S=(0,u.createContext)(null);S.displayName="TransitionContext";var k=((o=k||{}).Visible="visible",o.Hidden="hidden",o);let F=(0,u.createContext)(null);function P(e){return"children"in e?P(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function T(e,t){let n=(0,f.E)(e),r=(0,u.useRef)([]),o=(0,c.t)(),l=(0,a.G)(),i=(0,s.z)((e,t=y.l4.Hidden)=>{let i=r.current.findIndex(({el:t})=>t===e);-1!==i&&((0,E.E)(t,{[y.l4.Unmount](){r.current.splice(i,1)},[y.l4.Hidden](){r.current[i].state="hidden"}}),l.microTask(()=>{var e;!P(r)&&o.current&&(null==(e=n.current)||e.call(n))}))}),d=(0,s.z)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>i(e,y.l4.Unmount)}),p=(0,u.useRef)([]),m=(0,u.useRef)(Promise.resolve()),v=(0,u.useRef)({enter:[],leave:[]}),h=(0,s.z)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(v.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?m.current=m.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),g=(0,s.z)((e,t,n)=>{Promise.all(v.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,u.useMemo)(()=>({children:r,register:d,unregister:i,onStart:h,onStop:g,wait:m,chains:v}),[d,i,r,h,g,v,m])}F.displayName="NestingContext";let C=u.Fragment,O=y.VN.RenderStrategy,x=(0,y.yV)(function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...l}=e,i=(0,u.useRef)(null),a=w(e),c=(0,m.T)(...a?[i,t]:null===t?[]:[t]);(0,p.H)();let f=(0,g.oJ)();if(void 0===n&&null!==f&&(n=(f&g.ZM.Open)===g.ZM.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,h]=(0,u.useState)(n?"visible":"hidden"),b=T(()=>{n||h("hidden")}),[E,k]=(0,u.useState)(!0),C=(0,u.useRef)([n]);(0,d.e)(()=>{!1!==E&&C.current[C.current.length-1]!==n&&(C.current.push(n),k(!1))},[C,n]);let x=(0,u.useMemo)(()=>({show:n,appear:r,initial:E}),[n,r,E]);(0,d.e)(()=>{n?h("visible"):P(b)||null===i.current||h("hidden")},[n,b]);let L={unmount:o},N=(0,s.z)(()=>{var t;E&&k(!1),null==(t=e.beforeEnter)||t.call(e)}),M=(0,s.z)(()=>{var t;E&&k(!1),null==(t=e.beforeLeave)||t.call(e)}),R=(0,y.L6)();return u.createElement(F.Provider,{value:b},u.createElement(S.Provider,{value:x},R({ourProps:{...L,as:u.Fragment,children:u.createElement(A,{ref:c,...L,...l,beforeEnter:N,beforeLeave:M})},theirProps:{},defaultTag:u.Fragment,features:O,visible:"visible"===v,name:"Transition"})))}),A=(0,y.yV)(function(e,t){var n,r;let{transition:o=!0,beforeEnter:l,afterEnter:i,beforeLeave:c,afterLeave:f,enter:h,enterFrom:k,enterTo:x,entered:A,leave:L,leaveFrom:N,leaveTo:M,...R}=e,[D,j]=(0,u.useState)(null),I=(0,u.useRef)(null),V=w(e),z=(0,m.T)(...V?[I,t,j]:null===t?[]:[t]),H=null==(n=R.unmount)||n?y.l4.Unmount:y.l4.Hidden,{show:W,appear:$,initial:U}=function(){let e=(0,u.useContext)(S);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[_,Y]=(0,u.useState)(W?"visible":"hidden"),Z=function(){let e=(0,u.useContext)(F);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:q,unregister:B}=Z;(0,d.e)(()=>q(I),[q,I]),(0,d.e)(()=>{if(H===y.l4.Hidden&&I.current){if(W&&"visible"!==_){Y("visible");return}return(0,E.E)(_,{hidden:()=>B(I),visible:()=>q(I)})}},[_,I,q,B,W,H]);let G=(0,p.H)();(0,d.e)(()=>{if(V&&G&&"visible"===_&&null===I.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[I,_,G,V]);let K=U&&!$,X=$&&W&&U,J=(0,u.useRef)(!1),Q=T(()=>{J.current||(Y("hidden"),B(I))},Z),ee=(0,s.z)(e=>{J.current=!0,Q.onStart(I,e?"enter":"leave",e=>{"enter"===e?null==l||l():"leave"===e&&(null==c||c())})}),et=(0,s.z)(e=>{let t=e?"enter":"leave";J.current=!1,Q.onStop(I,t,e=>{"enter"===e?null==i||i():"leave"===e&&(null==f||f())}),"leave"!==t||P(Q)||(Y("hidden"),B(I))});(0,u.useEffect)(()=>{V&&o||(ee(W),et(W))},[W,V,o]);let[,en]=function(e,t,n,r){let[o,l]=(0,u.useState)(n),{hasFlag:i,addFlag:s,removeFlag:c}=function(e=0){let[t,n]=(0,u.useState)(e),r=(0,u.useCallback)(e=>n(e),[t]),o=(0,u.useCallback)(e=>n(t=>t|e),[t]),l=(0,u.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:l,removeFlag:(0,u.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,u.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),f=(0,u.useRef)(!1),p=(0,u.useRef)(!1),m=(0,a.G)();return(0,d.e)(()=>{var o;if(e){if(n&&l(!0),!t){n&&s(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,{prepare:t,run:n,done:r,inFlight:o}){let l=(0,v.k)();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current){n();return}let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:o}),l.nextFrame(()=>{n(),l.requestAnimationFrame(()=>{l.add(function(e,t){var n,r;let o=(0,v.k)();if(!e)return o.dispose;let l=!1;o.add(()=>{l=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{l||t()}),o.dispose}(e,r))})}),l.dispose}(t,{inFlight:f,prepare(){p.current?p.current=!1:p.current=f.current,f.current=!0,p.current||(n?(s(3),c(4)):(s(4),c(2)))},run(){p.current?n?(c(3),s(4)):(c(4),s(3)):n?c(1):s(1)},done(){var e;p.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,c(7),n||l(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,m]),e?[o,{closed:i(1),enter:i(2),leave:i(4),transition:i(2)||i(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!o||!V||!G||K),D,W,{start:ee,end:et}),er=(0,y.oA)({ref:z,className:(null==(r=(0,b.A)(R.className,X&&h,X&&k,en.enter&&h,en.enter&&en.closed&&k,en.enter&&!en.closed&&x,en.leave&&L,en.leave&&!en.closed&&N,en.leave&&en.closed&&M,!en.transition&&W&&A))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}(en)}),eo=0;"visible"===_&&(eo|=g.ZM.Open),"hidden"===_&&(eo|=g.ZM.Closed),W&&"hidden"===_&&(eo|=g.ZM.Opening),W||"visible"!==_||(eo|=g.ZM.Closing);let el=(0,y.L6)();return u.createElement(F.Provider,{value:Q},u.createElement(g.up,{value:eo},el({ourProps:er,theirProps:R,defaultTag:C,features:O,visible:"visible"===_,name:"Transition.Child"})))}),L=(0,y.yV)(function(e,t){let n=null!==(0,u.useContext)(S),r=null!==(0,g.oJ)();return u.createElement(u.Fragment,null,!n&&r?u.createElement(x,{ref:t,...e}):u.createElement(A,{ref:t,...e}))}),N=Object.assign(x,{Child:L,Root:x})},70053:(e,t,n)=>{n.d(t,{G:()=>l});var r=n(3729),o=n(42107);function l(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},70621:(e,t,n)=>{n.d(t,{z:()=>l});var r=n(3729),o=n(20313);let l=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},89788:(e,t,n)=>{n.d(t,{t:()=>l});var r=n(3729),o=n(33783);function l(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},33783:(e,t,n)=>{n.d(t,{e:()=>l});var r=n(3729),o=n(62670);let l=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},20313:(e,t,n)=>{n.d(t,{E:()=>l});var r=n(3729),o=n(33783);function l(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},50296:(e,t,n)=>{n.d(t,{H:()=>i});var r,o=n(3729),l=n(62670);function i(){let e;let t=(e="undefined"==typeof document,"useSyncExternalStore"in(r||(r=n.t(o,2)))&&(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,u]=o.useState(l.O.isHandoffComplete);return i&&!1===l.O.isHandoffComplete&&u(!1),o.useEffect(()=>{!0!==i&&u(!0)},[i]),o.useEffect(()=>l.O.handoff(),[]),!t&&i}},3159:(e,t,n)=>{n.d(t,{T:()=>u,h:()=>i});var r=n(3729),o=n(70621);let l=Symbol();function i(e,t=!0){return Object.assign(e,{[l]:t})}function u(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[l]))?void 0:n}},11786:(e,t,n)=>{n.d(t,{ZM:()=>i,oJ:()=>u,up:()=>a,uu:()=>s});var r,o=n(3729);let l=(0,o.createContext)(null);l.displayName="OpenClosedContext";var i=((r=i||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function u(){return(0,o.useContext)(l)}function a({value:e,children:t}){return o.createElement(l.Provider,{value:e},t)}function s({children:e}){return o.createElement(l.Provider,{value:null},e)}},28341:(e,t,n)=>{n.d(t,{A:()=>r});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},42107:(e,t,n)=>{n.d(t,{k:()=>function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(17209)},62670:(e,t,n)=>{n.d(t,{O:()=>u});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let u=new i},46070:(e,t,n)=>{n.d(t,{E:()=>r});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}},17209:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},84653:(e,t,n)=>{n.d(t,{L6:()=>c,VN:()=>a,l4:()=>s,oA:()=>v,yV:()=>m});var r,o,l=n(3729),i=n(28341),u=n(46070),a=((r=a||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c(){let e,t;let n=(e=(0,l.useRef)([]),t=(0,l.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),(...n)=>{if(!n.every(e=>null==e))return e.current=n,t});return(0,l.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=null!=a?a:f;let s=p(t,e);if(l)return d(s,n,r,i,a);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,i,a)}if(1&c){let{unmount:e=!0,...t}=s;return(0,u.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,i,a)})}return d(s,n,r,i,a)})({mergeRefs:n,...e}),[n])}function d(e,t={},n,r,o){let{as:u=n,children:a,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof a?a(t):a;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let m={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(m["data-headlessui-state"]=n.join(" "),n))m[`data-${e}`]=""}if(u===l.Fragment&&(Object.keys(v(c)).length>0||Object.keys(v(m)).length>0)){if(!(0,l.isValidElement)(f)||Array.isArray(f)&&f.length>1){if(Object.keys(v(c)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(v(c)).concat(Object.keys(v(m))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{let e=f.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>(0,i.A)(t(...e),c.className):(0,i.A)(t,c.className),r=p(f.props,v(h(c,["ref"])));for(let e in m)e in r&&delete m[e];return(0,l.cloneElement)(f,Object.assign({},r,m,d,{ref:o(l.version.split(".")[0]>="19"?f.props.ref:f.ref,d.ref)},n?{className:n}:{}))}}return(0,l.createElement)(u,Object.assign({},h(c,["ref"]),u!==l.Fragment&&d,u!==l.Fragment&&m),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function p(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let e in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(n[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function m(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}}};