"use strict";exports.id=155,exports.ids=[155],exports.modules={4138:(e,t,a)=>{a.d(t,{A:()=>w});var s=a(95344),i=a(3729),l=a(11494),r=a(19738),n=a(14373),d=a(5094),o=a(46540),c=a(7361),u=a(19591),m=a(23673),x=a(81137),h=a(14513),g=a(51838),j=a(31498),p=a(35851),y=a(79200),v=a(2768),D=a(46327),f=a(38271),b=a(54074);let N=()=>{let e=b.t.getState(),t=e.accessToken;if(console.log("\uD83D\uDD11 Auth Debug:",{isAuthenticated:e.isAuthenticated,hasToken:!!t,tokenLength:t?.length||0,tokenPreview:t?.substring(0,20)+"..."}),!t){console.error("❌ No access token found in auth store!");let e=localStorage.getItem("accessToken");if(e)return console.log("\uD83D\uDD04 Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:`Bearer ${e}`}}return{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}}},k={getBroadcastLinks:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/broadcast-links?${t.toString()}`,{method:"GET",headers:N()});if(!a.ok)throw Error(`Failed to fetch broadcast links: ${a.statusText}`);return await a.json()},getBroadcastLinksByFixture:async e=>{let t=await fetch(`/api/broadcast-links/fixture/${e}`,{method:"GET",headers:N()});if(!t.ok)throw Error(`Failed to fetch broadcast links for fixture: ${t.statusText}`);return await t.json()},getBroadcastLinkById:async e=>{let t=await fetch(`/api/broadcast-links/${e}`,{method:"GET",headers:N()});if(!t.ok)throw Error(`Failed to fetch broadcast link: ${t.statusText}`);return await t.json()},createBroadcastLink:async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:N(),body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create broadcast link: ${t.statusText}`);return await t.json()},updateBroadcastLink:async(e,t)=>{let a=await fetch(`/api/broadcast-links/${e}`,{method:"PUT",headers:N(),body:JSON.stringify(t)});if(!a.ok)throw Error(`Failed to update broadcast link: ${a.statusText}`);return await a.json()},deleteBroadcastLink:async e=>{let t=await fetch(`/api/broadcast-links/${e}`,{method:"DELETE",headers:N()});if(!t.ok)throw Error(`Failed to delete broadcast link: ${t.statusText}`)}};var C=a(86688);let w=({isOpen:e,onClose:t,fixture:a})=>{let[b,N]=(0,i.useState)(!1),[w,F]=(0,i.useState)(null),[E,T]=(0,i.useState)({title:"",url:"",comment:"",language:"English",quality:"HD"}),z=(0,l.NL)(),{data:L,isLoading:S,error:A}=(0,r.a)({queryKey:["broadcast-links",a.externalId||a.id],queryFn:()=>k.getBroadcastLinksByFixture(a.externalId||a.id),enabled:e}),B=L?.data||[],q=(0,n.D)({mutationFn:e=>k.createBroadcastLink(e),onSuccess:()=>{z.invalidateQueries({queryKey:["broadcast-links"]}),N(!1),Z()},onError:e=>{console.error("Failed to create broadcast link:",e.message)}}),M=(0,n.D)({mutationFn:({id:e,data:t})=>k.updateBroadcastLink(e,t),onSuccess:()=>{z.invalidateQueries({queryKey:["broadcast-links"]}),F(null),Z()},onError:e=>{console.error("Failed to update broadcast link:",e.message)}}),$=(0,n.D)({mutationFn:e=>k.deleteBroadcastLink(e),onSuccess:()=>{z.invalidateQueries({queryKey:["broadcast-links"]})},onError:e=>{console.error("Failed to delete broadcast link:",e.message)}}),Z=()=>{T({title:"",url:"",comment:"",language:"English",quality:"HD"})},H=e=>{F(e),T({title:e.linkName,url:e.linkUrl,comment:e.linkComment||"",language:e.language||"English",quality:e.quality||"HD"}),N(!0)},U=e=>{confirm(`Are you sure you want to delete "${e.linkName}"?`)&&$.mutate(e.id)},I=()=>{N(!1),F(null),Z()},O=e=>{switch(e.toLowerCase()){case"4k":case"uhd":return"bg-purple-100 text-purple-800";case"hd":case"1080p":return"bg-blue-100 text-blue-800";case"sd":case"720p":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},P=e=>({en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",it:"\uD83C\uDDEE\uD83C\uDDF9",pt:"\uD83C\uDDF5\uD83C\uDDF9",ar:"\uD83C\uDDF8\uD83C\uDDE6"})[e]||"\uD83C\uDF10";return e?s.jsx("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[s.jsx(x.Z,{className:"mr-2 h-5 w-5"}),"Broadcast Links - ",a.homeTeamName," vs ",a.awayTeamName]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Manage streaming links for this fixture"})]}),s.jsx(d.z,{variant:"outline",size:"sm",onClick:t,children:s.jsx(h.Z,{className:"h-4 w-4"})})]})}),(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[!b&&(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[B.length," broadcast link",1!==B.length?"s":""," available"]}),(0,s.jsxs)(d.z,{onClick:()=>N(!0),children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Add Link"]})]}),b&&(0,s.jsxs)(m.Zb,{children:[s.jsx(m.Ol,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx(m.ll,{className:"text-lg",children:w?"Edit Broadcast Link":"Add New Broadcast Link"}),s.jsx(d.z,{type:"button",variant:"outline",size:"sm",onClick:I,children:s.jsx(h.Z,{className:"h-4 w-4"})})]})}),s.jsx(m.aY,{children:(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!E.title.trim()||!E.url.trim()||!E.comment.trim())return;let t={fixtureId:a.externalId||a.id,linkName:E.title.trim(),linkUrl:E.url.trim(),linkComment:E.comment.trim(),language:E.language,quality:E.quality};w?M.mutate({id:w.id,data:t}):q.mutate(t)},className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx(c._,{htmlFor:"title",children:"Title"}),s.jsx(o.I,{id:"title",value:E.title,onChange:e=>T({...E,title:e.target.value}),placeholder:"e.g., ESPN HD Stream",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx(c._,{htmlFor:"url",children:"URL"}),s.jsx(o.I,{id:"url",type:"url",value:E.url,onChange:e=>T({...E,url:e.target.value}),placeholder:"https://...",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx(c._,{htmlFor:"language",children:"Language"}),(0,s.jsxs)("select",{id:"language",value:E.language,onChange:e=>T({...E,language:e.target.value}),className:"w-full p-2 border rounded",children:[s.jsx("option",{value:"en",children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),s.jsx("option",{value:"es",children:"\uD83C\uDDEA\uD83C\uDDF8 Spanish"}),s.jsx("option",{value:"fr",children:"\uD83C\uDDEB\uD83C\uDDF7 French"}),s.jsx("option",{value:"de",children:"\uD83C\uDDE9\uD83C\uDDEA German"}),s.jsx("option",{value:"it",children:"\uD83C\uDDEE\uD83C\uDDF9 Italian"}),s.jsx("option",{value:"pt",children:"\uD83C\uDDF5\uD83C\uDDF9 Portuguese"}),s.jsx("option",{value:"ar",children:"\uD83C\uDDF8\uD83C\uDDE6 Arabic"})]})]}),(0,s.jsxs)("div",{children:[s.jsx(c._,{htmlFor:"quality",children:"Quality"}),(0,s.jsxs)("select",{id:"quality",value:E.quality,onChange:e=>T({...E,quality:e.target.value}),className:"w-full p-2 border rounded",children:[s.jsx("option",{value:"4K",children:"4K Ultra HD"}),s.jsx("option",{value:"HD",children:"HD (1080p)"}),s.jsx("option",{value:"720p",children:"HD (720p)"}),s.jsx("option",{value:"SD",children:"SD (480p)"})]})]})]}),(0,s.jsxs)("div",{children:[s.jsx(c._,{htmlFor:"comment",children:"Comment"}),s.jsx(o.I,{id:"comment",value:E.comment,onChange:e=>T({...E,comment:e.target.value}),placeholder:"e.g., Official HD stream with English commentary",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[s.jsx(d.z,{type:"button",variant:"outline",onClick:I,children:"Cancel"}),(0,s.jsxs)(d.z,{type:"submit",disabled:q.isLoading||M.isLoading,children:[s.jsx(j.Z,{className:"h-4 w-4 mr-2"}),w?"Update":"Add"," Link"]})]})]})})]}),s.jsx("div",{className:"space-y-4",children:S?s.jsx(C.hM,{rows:3,columns:1}):A?(0,s.jsxs)("div",{className:"text-center py-8",children:[s.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load broadcast links"}),s.jsx(d.z,{onClick:()=>console.log("Mock: Retry loading"),children:"Try Again"})]}):0===B.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[s.jsx(x.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),s.jsx("p",{className:"mt-2 text-gray-600",children:"No broadcast links added yet"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Add a link to get started"}),!b&&(0,s.jsxs)(d.z,{onClick:()=>N(!0),className:"mt-4",children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Add First Link"]})]}):B.map(e=>s.jsx(m.Zb,{children:s.jsx(m.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.linkName.toLowerCase().includes("comment")||e.linkName.toLowerCase().includes("chat")?s.jsx(p.Z,{className:"h-4 w-4 text-blue-600"}):s.jsx(x.Z,{className:"h-4 w-4 text-green-600"}),s.jsx("span",{className:"font-medium",children:e.linkName})]}),(0,s.jsxs)(u.C,{className:O(e.quality||"HD"),children:[s.jsx(y.Z,{className:"mr-1 h-3 w-3"}),e.quality||"HD"]}),(0,s.jsxs)(u.C,{variant:"outline",children:[P(e.language||"English")," ",e.language||"English"]}),s.jsx(u.C,{variant:"outline",className:"bg-green-50 text-green-700",children:"Active"})]}),s.jsx("div",{className:"text-sm text-gray-600 flex items-center space-x-2",children:(0,s.jsxs)("a",{href:e.linkUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]",children:[s.jsx("span",{className:"truncate",children:e.linkUrl}),s.jsx(v.Z,{className:"h-3 w-3 flex-shrink-0"})]})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(d.z,{size:"sm",variant:"outline",onClick:()=>H(e),disabled:w?.id===e.id,children:s.jsx(D.Z,{className:"h-4 w-4"})}),s.jsx(d.z,{size:"sm",variant:"outline",onClick:()=>U(e),disabled:$.isLoading,children:s.jsx(f.Z,{className:"h-4 w-4"})})]})]})})},e.id))})]})]})}):null}},85499:(e,t,a)=>{a.d(t,{U:()=>m});var s=a(95344),i=a(3729),l=a(53330),r=a(11453);let n=l.zt,d=l.fC,o=l.xz,c=i.forwardRef(({className:e,sideOffset:t=4,...a},i)=>s.jsx(l.VY,{ref:i,sideOffset:t,className:(0,r.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));c.displayName=l.VY.displayName;var u=a(82912);let m=({dateTime:e,className:t="",showDate:a=!0,showTime:i=!0,format:l,onClick:r,isClickable:m=!1})=>{let x=(0,u.Bv)(e,"dd/MM/yyyy"),h=(0,u.Bv)(e,"HH:mm"),g=(0,u.Yh)(e,"dd/MM/yyyy"),j=(0,u.Yh)(e,"HH:mm"),p=(0,u.vV)(),y=`
    text-center cursor-help transition-all duration-200
    ${m?"hover:bg-blue-50 hover:shadow-sm rounded-md p-2 cursor-pointer":""}
    ${t}
  `.trim(),v=(0,s.jsxs)("div",{className:y,onClick:m?r:void 0,children:[a&&s.jsx("div",{className:"font-semibold text-sm text-gray-900 leading-tight",children:x}),i&&s.jsx("div",{className:"text-gray-600 text-xs font-medium mt-1 leading-tight",children:h}),m&&s.jsx("div",{className:"text-xs text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:"Click to filter"})]});return s.jsx(n,{children:(0,s.jsxs)(d,{children:[s.jsx(o,{asChild:!0,children:s.jsx("div",{className:m?"group":"",children:v})}),s.jsx(c,{side:"top",className:"max-w-xs bg-gray-900 text-white",children:(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("div",{className:"font-medium text-sm",children:"UTC Time:"}),(0,s.jsxs)("div",{className:"text-sm",children:[g," ",j," (GMT+0)"]}),(0,s.jsxs)("div",{className:"text-xs text-gray-300 mt-2 border-t border-gray-700 pt-2",children:["Local: ",p]}),m&&s.jsx("div",{className:"text-xs text-blue-300 border-t border-gray-700 pt-2",children:"\uD83D\uDCA1 Click to filter by this date"})]})})]})})}},73875:(e,t,a)=>{a.d(t,{sm:()=>m,uB:()=>x,u_:()=>u});var s=a(95344),i=a(3729),l=a(81021),r=a(13659),n=a(14513),d=a(5094),o=a(11453);let c={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:t,title:a,description:u,children:m,size:x="md",showCloseButton:h=!0,closeOnOverlayClick:g=!0,className:j})=>s.jsx(l.u,{appear:!0,show:e,as:i.Fragment,children:(0,s.jsxs)(r.Vq,{as:"div",className:"relative z-50",onClose:g?t:()=>{},children:[s.jsx(l.u.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),s.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:s.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:s.jsx(l.u.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(r.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",c[x],j),children:[(a||h)&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{children:[a&&s.jsx(r.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:a}),u&&s.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),h&&s.jsx(d.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:s.jsx(n.Z,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"mt-2",children:m})]})})})})]})}),m=({isOpen:e,onClose:t,onConfirm:a,title:i="Confirm Action",message:l="Are you sure you want to proceed?",confirmText:r="Confirm",cancelText:n="Cancel",variant:o="default",loading:c=!1})=>s.jsx(u,{isOpen:e,onClose:t,title:i,size:"sm",closeOnOverlayClick:!c,children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("p",{className:"text-sm text-gray-600",children:l}),(0,s.jsxs)("div",{className:"flex space-x-2 justify-end",children:[s.jsx(d.z,{variant:"outline",onClick:t,disabled:c,children:n}),s.jsx(d.z,{variant:"destructive"===o?"destructive":"default",onClick:a,disabled:c,children:c?"Processing...":r})]})]})}),x=({isOpen:e,onClose:t,title:a,description:i,children:l,onSubmit:r,submitText:n="Save",cancelText:o="Cancel",loading:c=!1,size:m="md"})=>s.jsx(u,{isOpen:e,onClose:t,title:a,description:i,size:m,closeOnOverlayClick:!c,children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r?.()},className:"space-y-4",children:[l,(0,s.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[s.jsx(d.z,{type:"button",variant:"outline",onClick:t,disabled:c,children:o}),r&&s.jsx(d.z,{type:"submit",disabled:c,children:c?"Saving...":n})]})]})})},82912:(e,t,a)=>{a.d(t,{Bv:()=>d,PM:()=>m,Yh:()=>o,vV:()=>u});var s=a(86997),i=a(80471),l=a(3281),r=a(54054);let n=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,d=(e,t="dd/MM/yyyy HH:mm")=>{try{let a="string"==typeof e?(0,s.D)(e):e;if(!(0,i.J)(a))return"Invalid Date";let l=n();return(0,r.CV)(a,l,t)}catch(e){return console.error("Error formatting date to local time:",e),"Invalid Date"}},o=(e,t="dd/MM/yyyy HH:mm")=>{try{let a="string"==typeof e?(0,s.D)(e):e;if(!(0,i.J)(a))return"Invalid Date";return(0,r.CV)(a,"UTC",t)}catch(e){return console.error("Error formatting date to UTC:",e),"Invalid Date"}},c=()=>{let e=new Date().getTimezoneOffset();return`GMT${e<=0?"+":"-"}${Math.floor(Math.abs(e)/60).toString().padStart(2,"0")}:${(Math.abs(e)%60).toString().padStart(2,"0")}`},u=()=>{let e=n(),t=c(),a=new Intl.DateTimeFormat("en",{timeZoneName:"short",timeZone:e}).formatToParts(new Date).find(e=>"timeZoneName"===e.type)?.value||"";return`${a} (${t})`},m=e=>{try{return(0,l.WU)(e,"yyyy-MM-dd")}catch(t){return console.error("Error converting local date to UTC:",t),(0,l.WU)(e,"yyyy-MM-dd")}}}};