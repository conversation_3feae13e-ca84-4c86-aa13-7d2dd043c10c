"use strict";exports.id=6419,exports.ids=[6419],exports.modules={63024:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31498:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},85684:(e,t,r)=>{r.d(t,{fC:()=>w,z$:()=>R});var a=r(3729),n=r(31405),o=r(98462),d=r(85222),s=r(33183),l=r(92062),i=r(63085),c=r(43234),u=r(62409),p=r(95344),f="Checkbox",[h,v]=(0,o.b)(f),[b,k]=h(f);function y(e){let{__scopeCheckbox:t,checked:r,children:n,defaultChecked:o,disabled:d,form:l,name:i,onCheckedChange:c,required:u,value:h="on",internal_do_not_use_render:v}=e,[k,y]=(0,s.T)({prop:r,defaultProp:o??!1,onChange:c,caller:f}),[m,x]=a.useState(null),[w,j]=a.useState(null),R=a.useRef(!1),E=!m||!!l||!!m.closest("form"),C={checked:k,disabled:d,setChecked:y,control:m,setControl:x,name:i,form:l,value:h,hasConsumerStoppedPropagationRef:R,required:u,defaultChecked:!g(o)&&o,isFormControl:E,bubbleInput:w,setBubbleInput:j};return(0,p.jsx)(b,{scope:t,...C,children:"function"==typeof v?v(C):n})}var m="CheckboxTrigger",x=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...o},s)=>{let{control:l,value:i,disabled:c,checked:f,required:h,setControl:v,setChecked:b,hasConsumerStoppedPropagationRef:y,isFormControl:x,bubbleInput:w}=k(m,e),j=(0,n.e)(s,v),R=a.useRef(f);return a.useEffect(()=>{let e=l?.form;if(e){let t=()=>b(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,b]),(0,p.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":g(f)?"mixed":f,"aria-required":h,"data-state":M(f),"data-disabled":c?"":void 0,disabled:c,value:i,...o,ref:j,onKeyDown:(0,d.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.M)(r,e=>{b(e=>!!g(e)||!e),w&&x&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});x.displayName=m;var w=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:o,required:d,disabled:s,value:l,onCheckedChange:i,form:c,...u}=e;return(0,p.jsx)(y,{__scopeCheckbox:r,checked:n,defaultChecked:o,disabled:s,required:d,onCheckedChange:i,name:a,form:c,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(x,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(C,{__scopeCheckbox:r})]})})});w.displayName=f;var j="CheckboxIndicator",R=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,o=k(j,r);return(0,p.jsx)(c.z,{present:a||g(o.checked)||!0===o.checked,children:(0,p.jsx)(u.WV.span,{"data-state":M(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});R.displayName=j;var E="CheckboxBubbleInput",C=a.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:o,hasConsumerStoppedPropagationRef:d,checked:s,defaultChecked:c,required:f,disabled:h,name:v,value:b,form:y,bubbleInput:m,setBubbleInput:x}=k(E,e),w=(0,n.e)(r,x),j=(0,l.D)(s),R=(0,i.t)(o);a.useEffect(()=>{if(!m)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!d.current;if(j!==s&&e){let r=new Event("click",{bubbles:t});m.indeterminate=g(s),e.call(m,!g(s)&&s),m.dispatchEvent(r)}},[m,j,s,d]);let C=a.useRef(!g(s)&&s);return(0,p.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??C.current,required:f,disabled:h,name:v,value:b,form:y,...t,tabIndex:-1,ref:w,style:{...t.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function g(e){return"indeterminate"===e}function M(e){return g(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=E},14217:(e,t,r)=>{r.d(t,{f:()=>s});var a=r(3729),n=r(62409),o=r(95344),d=a.forwardRef((e,t)=>(0,o.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var s=d},9913:(e,t,r)=>{r.d(t,{ck:()=>H,fC:()=>z,z$:()=>O});var a=r(3729),n=r(85222),o=r(31405),d=r(98462),s=r(62409),l=r(34504),i=r(33183),c=r(3975),u=r(63085),p=r(92062),f=r(43234),h=r(95344),v="Radio",[b,k]=(0,d.b)(v),[y,m]=b(v),x=a.forwardRef((e,t)=>{let{__scopeRadio:r,name:d,checked:l=!1,required:i,disabled:c,value:u="on",onCheck:p,form:f,...v}=e,[b,k]=a.useState(null),m=(0,o.e)(t,e=>k(e)),x=a.useRef(!1),w=!b||f||!!b.closest("form");return(0,h.jsxs)(y,{scope:r,checked:l,disabled:c,children:[(0,h.jsx)(s.WV.button,{type:"button",role:"radio","aria-checked":l,"data-state":E(l),"data-disabled":c?"":void 0,disabled:c,value:u,...v,ref:m,onClick:(0,n.M)(e.onClick,e=>{l||p?.(),w&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),w&&(0,h.jsx)(R,{control:b,bubbles:!x.current,name:d,value:u,checked:l,required:i,disabled:c,form:f,style:{transform:"translateX(-100%)"}})]})});x.displayName=v;var w="RadioIndicator",j=a.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:a,...n}=e,o=m(w,r);return(0,h.jsx)(f.z,{present:a||o.checked,children:(0,h.jsx)(s.WV.span,{"data-state":E(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})})});j.displayName=w;var R=a.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:n=!0,...d},l)=>{let i=a.useRef(null),c=(0,o.e)(i,l),f=(0,p.D)(r),v=(0,u.t)(t);return a.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==r&&t){let a=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(a)}},[f,r,n]),(0,h.jsx)(s.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...d,tabIndex:-1,ref:c,style:{...d.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}R.displayName="RadioBubbleInput";var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g="RadioGroup",[M,P]=(0,d.b)(g,[l.Pc,k]),D=(0,l.Pc)(),N=k(),[V,I]=M(g),L=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:a,defaultValue:n,value:o,required:d=!1,disabled:u=!1,orientation:p,dir:f,loop:v=!0,onValueChange:b,...k}=e,y=D(r),m=(0,c.gm)(f),[x,w]=(0,i.T)({prop:o,defaultProp:n??null,onChange:b,caller:g});return(0,h.jsx)(V,{scope:r,name:a,required:d,disabled:u,value:x,onValueChange:w,children:(0,h.jsx)(l.fC,{asChild:!0,...y,orientation:p,dir:m,loop:v,children:(0,h.jsx)(s.WV.div,{role:"radiogroup","aria-required":d,"aria-orientation":p,"data-disabled":u?"":void 0,dir:m,...k,ref:t})})})});L.displayName=g;var S="RadioGroupItem",W=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:d,...s}=e,i=I(S,r),c=i.disabled||d,u=D(r),p=N(r),f=a.useRef(null),v=(0,o.e)(t,f),b=i.value===s.value,k=a.useRef(!1);return a.useEffect(()=>{let e=e=>{C.includes(e.key)&&(k.current=!0)},t=()=>k.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(l.ck,{asChild:!0,...u,focusable:!c,active:b,children:(0,h.jsx)(x,{disabled:c,required:i.required,checked:b,...p,...s,name:i.name,ref:v,onCheck:()=>i.onValueChange(s.value),onKeyDown:(0,n.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.M)(s.onFocus,()=>{k.current&&f.current?.click()})})})});W.displayName=S;var T=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...a}=e,n=N(r);return(0,h.jsx)(j,{...n,...a,ref:t})});T.displayName="RadioGroupIndicator";var z=L,H=W,O=T},19655:(e,t,r)=>{r.d(t,{bU:()=>R,fC:()=>j});var a=r(3729),n=r(85222),o=r(31405),d=r(98462),s=r(33183),l=r(92062),i=r(63085),c=r(62409),u=r(95344),p="Switch",[f,h]=(0,d.b)(p),[v,b]=f(p),k=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:d,checked:l,defaultChecked:i,required:f,disabled:h,value:b="on",onCheckedChange:k,form:y,...m}=e,[j,R]=a.useState(null),E=(0,o.e)(t,e=>R(e)),C=a.useRef(!1),g=!j||y||!!j.closest("form"),[M,P]=(0,s.T)({prop:l,defaultProp:i??!1,onChange:k,caller:p});return(0,u.jsxs)(v,{scope:r,checked:M,disabled:h,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":M,"aria-required":f,"data-state":w(M),"data-disabled":h?"":void 0,disabled:h,value:b,...m,ref:E,onClick:(0,n.M)(e.onClick,e=>{P(e=>!e),g&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),g&&(0,u.jsx)(x,{control:j,bubbles:!C.current,name:d,value:b,checked:M,required:f,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});k.displayName=p;var y="SwitchThumb",m=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=b(y,r);return(0,u.jsx)(c.WV.span,{"data-state":w(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});m.displayName=y;var x=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...d},s)=>{let c=a.useRef(null),p=(0,o.e)(c,s),f=(0,l.D)(r),h=(0,i.t)(t);return a.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==r&&t){let a=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(a)}},[f,r,n]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...d,tabIndex:-1,ref:p,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var j=k,R=m}};