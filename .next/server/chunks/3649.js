"use strict";exports.id=3649,exports.ids=[3649],exports.modules={45904:(e,t,n)=>{n.d(t,{Ry:()=>c});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(s.get(e)||0)+1;r.set(e,l),s.set(e,a),f.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),l++,function(){f.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},62312:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25390:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},82958:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},71210:(e,t,n)=>{n.d(t,{Z:()=>U});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(3729)),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i=(void 0===t&&(t=d),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return i.options=o({async:!0,ssr:!1},e),i}(),m=function(){},h=l.forwardRef(function(e,t){var n,r,a,u,d=l.useRef(null),h=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=h[0],g=h[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,S=e.sideCar,A=e.noRelative,C=e.noIsolation,T=e.inert,L=e.allowPinchZoom,M=e.as,P=e.gapMode,N=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[d,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,u=a.facade,s(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),k=o(o({},N),v);return l.createElement(l.Fragment,null,E&&l.createElement(S,{sideCar:p,removeScrollBar:x,shards:R,noRelative:A,noIsolation:C,inert:T,setCallbacks:g,allowPinchZoom:!!L,lockRef:d,gapMode:P}),y?l.cloneElement(l.Children.only(w),o(o({},k),{ref:O})):l.createElement(void 0===M?"div":M,o({},k,{className:b,ref:O}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:a};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=w(),A="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},L=function(){l.useEffect(function(){return document.body.setAttribute(A,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var i=l.useMemo(function(){return R(o)},[o]);return l.createElement(S,{styles:C(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){P=!1}var O=!!P&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{var m=F(e,u),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&W(e,u)&&(d+=v,p+=h),u=u.parentNode.host||u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},j=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},$=0,_=[];let V=(p.useMedium(function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState($++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=j(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=D(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=D(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return I(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(_.length&&_[_.length-1]===i){var n="deltaY"in e?B(e):j(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=l.useCallback(function(e){n.current=j(e),r.current=void 0},[]),d=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,j(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return _.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,O),document.addEventListener("touchmove",c,O),document.addEventListener("touchstart",f,O),function(){_=_.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,O),document.removeEventListener("touchmove",c,O),document.removeEventListener("touchstart",f,O)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var z=l.forwardRef(function(e,t){return l.createElement(h,o({},e,{ref:t,sideCar:V}))});z.classNames=h.classNames;let U=z},85222:(e,t,n)=>{n.d(t,{M:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},77411:(e,t,n)=>{n.d(t,{B:()=>u});var r=n(3729),o=n(98462),i=n(31405),l=n(32751),a=n(95344);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=(0,l.Z8)(d),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),l=(0,i.e)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:r})});m.displayName=d;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,l.Z8)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,u=r.useRef(null),c=(0,i.e)(t,u),f=s(h,n);return r.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,a.jsx)(g,{[v]:"",ref:c,children:o})});return y.displayName=h,[{Provider:f,Slot:m,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},98462:(e,t,n)=>{n.d(t,{b:()=>l,k:()=>i});var r=n(3729),o=n(95344);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},3975:(e,t,n)=>{n.d(t,{gm:()=>i});var r=n(3729);n(95344);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},44155:(e,t,n)=>{n.d(t,{XB:()=>d});var r,o=n(3729),i=n(85222),l=n(62409),a=n(31405),u=n(2256),c=n(95344),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(f),[x,E]=o.useState(null),R=x?.ownerDocument??globalThis?.document,[,S]=o.useState({}),A=(0,a.e)(t,e=>E(e)),C=Array.from(b.layers),[T]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),L=C.indexOf(T),M=x?C.indexOf(x):-1,P=b.layersWithOutsidePointerEventsDisabled.size>0,N=M>=L,O=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!N||n||(h?.(e),g?.(e),e.defaultPrevented||y?.())},R),k=function(e,t=globalThis?.document){let n=(0,u.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},R);return function(e,t=globalThis?.document){let n=(0,u.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M!==b.layers.size-1||(d?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},R),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[x,R,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.WV.div,{...w,ref:A,style:{pointerEvents:P?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,O.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,l.jH)(o,i):o.dispatchEvent(i)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},1106:(e,t,n)=>{n.d(t,{EW:()=>i});var r=n(3729),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},27386:(e,t,n)=>{n.d(t,{M:()=>f});var r=n(3729),o=n(31405),i=n(62409),l=n(2256),a=n(95344),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.W)(v),E=(0,l.W)(g),R=r.useRef(null),S=(0,o.e)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,A.paused]),r.useEffect(()=>{if(w){h.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(c,E),h.remove(A)},0)}}},[w,x,E,A]);let C=r.useCallback(e=>{if(!n&&!f||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,A.paused]);return(0,a.jsx)(i.WV.div,{tabIndex:-1,...y,ref:S,onKeyDown:C})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},99048:(e,t,n)=>{n.d(t,{M:()=>u});var r,o=n(3729),i=n(16069),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},37574:(e,t,n)=>{n.d(t,{ee:()=>eq,Eh:()=>eJ,VY:()=>eG,fC:()=>eX,D7:()=>ek});var r=n(3729);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=h(g(t)),u=v(a),c=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(c,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=b(m),v=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},R=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-R.top+h.top)/E.y,bottom:(R.bottom-g.bottom+h.bottom)/E.y,left:(g.left-R.left+h.left)/E.x,right:(R.right-g.right+h.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=m(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function L(){return"undefined"!=typeof window}function M(e){return O(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(O(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function O(e){return!!L()&&(e instanceof Node||e instanceof P(e).Node)}function k(e){return!!L()&&(e instanceof Element||e instanceof P(e).Element)}function D(e){return!!L()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function W(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=$(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function j(e){let t=B(),n=k(e)?$(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(M(e))}function $(e){return P(e).getComputedStyle(e)}function _(e){return k(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===M(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||N(e);return W(t)?t.host:t}function z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&F(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=P(o);if(i){let e=U(l);return t.concat(l,l.visualViewport||[],F(o)?o:[],e&&n?z(e):[])}return t.concat(o,z(o,[],n))}function U(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=$(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function Y(e){return k(e)?e:e.contextElement}function Z(e){let t=Y(e);if(!D(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let X=c(0);function q(e){let t=P(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function G(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=Y(e),a=c(1);t&&(r?k(r)&&(a=Z(r)):a=Z(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(l))&&o)?q(l):c(0),s=(i.left+u.x)/a.x,f=(i.top+u.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=r&&k(r)?P(r):r,n=e,o=U(n);for(;o&&r&&t!==n;){let e=Z(o),t=o.getBoundingClientRect(),r=$(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=U(n=P(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=_(e).scrollLeft;return t?t.left+n:G(N(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=N(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=B();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=_(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===$(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(N(e));else if(k(t))r=function(e,t){let n=G(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?Z(e):c(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===$(e).position}function en(e,t){if(!D(e)||"fixed"===$(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(I(e))return n;if(!D(e)){let t=V(e);for(;t&&!H(t);){if(k(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(M(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!j(r)?n:r||function(e){let t=V(e);for(;D(t)&&!H(t);){if(j(t))return t;if(I(t))break;t=V(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=N(t),i="fixed"===n,l=G(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i){if(("body"!==M(t)||F(o))&&(a=_(t)),r){let e=G(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o))}i&&!r&&o&&(u.x=J(o));let s=!o||r||i?c(0):Q(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=N(r),a=!!t&&I(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=D(r);if((d||!d&&!i)&&(("body"!==M(r)||F(l))&&(u=_(r)),D(r))){let e=G(r);s=Z(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?c(0):Q(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=z(e,[],!1).filter(e=>k(e)&&"body"!==M(e)),o=null,i="fixed"===$(e).position,l=i?V(e):e;for(;k(l)&&!H(l);){let t=$(l),n=j(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||F(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!k(r)||H(r))&&("fixed"===$(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:Z,isElement:k,isRTL:function(e){return"rtl"===$(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=h(g(o)),E=v(x),R=await u.getDimensions(f),S="y"===x,A=S?"clientHeight":"clientWidth",C=a.reference[E]+a.reference[x]-w[x]-a.floating[E],T=w[x]-a.reference[x],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),M=L?L[A]:0;M&&await (null==u.isElement?void 0:u.isElement(L))||(M=c.floating[A]||a.floating[E]);let P=M/2-R[E]/2-1,N=i(y[S?"top":"left"],P),O=i(y[S?"bottom":"right"],P),k=M-R[E]-O,D=M/2-R[E]/2+(C/2-T/2),W=l(N,i(D,k)),F=!s.arrow&&null!=m(o)&&D!==W&&a.reference[E]/2-(D<N?N:O)-R[E]/2<0,I=F?D<N?D-N:D-k:0;return{[x]:w[x]+I,data:{[x]:W,centerOffset:D-W-I,...F&&{alignmentOffset:I}},reset:F}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var ec=n(81202),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await T(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},m=await S(t,s),v=g(p(o)),y=h(v),w=f[y],b=f[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=l(n,i(b,r))}let x=c.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=g(o),m=h(f),v=s[m],y=s[f],w=d(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[f]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l,a;let{placement:u,middlewareData:c,rects:s,initialPlacement:f,platform:b,elements:x}=t,{mainAxis:E=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:L=!0,...M}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let P=p(u),N=g(f),O=p(f)===f,k=await (null==b.isRTL?void 0:b.isRTL(x.floating)),D=A||(O||!L?[w(f)]:function(e){let t=w(e);return[y(e),t,y(t)]}(f)),W="none"!==T;!A&&W&&D.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(f,L,T,k));let F=[f,...D],I=await S(t,M),j=[],B=(null==(r=c.flip)?void 0:r.overflows)||[];if(E&&j.push(I[P]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(g(e)),i=v(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(u,s,k);j.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:u,overflows:j}],!j.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=F[e];if(t){let n="alignment"===R&&N!==g(t),r=(null==(l=B[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:B},reset:{placement:t}}}let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(C){case"bestFit":{let e=null==(a=B.filter(e=>{if(W){let t=g(e.placement);return t===N||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=f}if(u!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a;let{placement:u,rects:c,platform:s,elements:f}=t,{apply:h=()=>{},...v}=d(e,t),y=await S(t,v),w=p(u),b=m(u),x="y"===g(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let A=R-y.top-y.bottom,C=E-y.left-y.right,T=i(R-y[o],A),L=i(E-y[a],C),M=!t.middlewareData.shift,P=T,N=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=A),M&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await h({...t,availableWidth:N,availableHeight:P});let O=await s.getDimensions(f.floating);return E!==O.width||R!==O.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=A(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=A(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eR=n(62409),eS=n(95344),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eS.jsx)(eR.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eS.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eC=n(31405),eT=n(98462),eL=n(2256),eM=n(16069),eP=n(63085),eN="Popper",[eO,ek]=(0,eT.b)(eN),[eD,eW]=eO(eN),eF=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eS.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eF.displayName=eN;var eI="PopperAnchor",ej=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eW(eI,n),a=r.useRef(null),u=(0,eC.e)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eS.jsx)(eR.WV.div,{...i,ref:u})});ej.displayName=eI;var eB="PopperContent",[eH,e$]=eO(eB),e_=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eW(eB,n),[x,E]=r.useState(null),R=(0,eC.e)(t,e=>E(e)),[S,A]=r.useState(null),C=(0,eP.t)(S),T=C?.width??0,L=C?.height??0,M="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},P=Array.isArray(p)?p:[p],O=P.length>0,k={padding:M,boundary:P.filter(eK),altBoundary:O},{refs:D,floatingStyles:W,placement:F,isPositioned:I,middlewareData:j}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);ef(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=l||h,E=a||g,R=r.useRef(null),S=r.useRef(null),A=r.useRef(f),C=null!=c,T=em(c),L=em(i),M=em(s),P=r.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),eu(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};N.current&&!ef(A.current,t)&&(A.current=t,ec.flushSync(()=>{d(t)}))})},[p,t,n,L,M]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);es(()=>(N.current=!0,()=>{N.current=!1}),[]),es(()=>{if(x&&(R.current=x),E&&(S.current=E),x&&E){if(T.current)return T.current(x,E,P);P()}},[x,E,P,T,C]);let O=r.useMemo(()=>({reference:R,floating:S,setReference:w,setFloating:b}),[w,b]),k=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=ep(k.floating,f.x),r=ep(k.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:P,refs:O,elements:k,floatingStyles:D}),[f,P,O,k,D])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=Y(e),m=a||c?[...p?z(p):[],...z(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,o=N(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:l(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?G(e):null;return d&&function t(){let r=G(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:a+L,alignmentAxis:s}),d&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ey():void 0,...k}),d&&ew({...k}),eb({...k,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&eE({element:S,padding:f}),eY({arrowWidth:T,arrowHeight:L}),v&&ex({strategy:"referenceHidden",...k})]}),[B,H]=eZ(F),$=(0,eL.W)(y);(0,eM.b)(()=>{I&&$?.()},[I,$]);let _=j.arrow?.x,V=j.arrow?.y,U=j.arrow?.centerOffset!==0,[K,Z]=r.useState();return(0,eM.b)(()=>{x&&Z(window.getComputedStyle(x).zIndex)},[x]),(0,eS.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:I?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[j.transformOrigin?.x,j.transformOrigin?.y].join(" "),...j.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eS.jsx)(eH,{scope:n,placedSide:B,onArrowChange:A,arrowX:_,arrowY:V,shouldHideArrow:U,children:(0,eS.jsx)(eR.WV.div,{"data-side":B,"data-align":H,...w,ref:R,style:{...w.style,animation:I?void 0:"none"}})})})});e_.displayName=eB;var eV="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eU=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e$(eV,n),i=ez[o.placedSide];return(0,eS.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eS.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}eU.displayName=eV;var eY=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=eZ(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===u?(p=i?s:`${f}px`,m=`${-a}px`):"top"===u?(p=i?s:`${f}px`,m=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,m=i?s:`${d}px`):"left"===u&&(p=`${r.floating.width+a}px`,m=i?s:`${d}px`),{data:{x:p,y:m}}}});function eZ(e){let[t,n="center"]=e.split("-");return[t,n]}var eX=eF,eq=ej,eG=e_,eJ=eU},31179:(e,t,n)=>{n.d(t,{h:()=>u});var r=n(3729),o=n(81202),i=n(62409),l=n(16069),a=n(95344),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,l.b)(()=>s(!0),[]);let f=n||c&&globalThis?.document?.body;return f?o.createPortal((0,a.jsx)(i.WV.div,{...u,ref:t}),f):null});u.displayName="Portal"},43234:(e,t,n)=>{n.d(t,{z:()=>l});var r=n(3729),o=n(31405),i=n(16069),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===f?e:"none"},[f]),(0,i.b)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=a(u.current).includes(n.animationName);if(n.target===o&&r&&(d("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.e)(l.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return e?.animationName||"none"}l.displayName="Presence"},62409:(e,t,n)=>{n.d(t,{WV:()=>a,jH:()=>u});var r=n(3729),o=n(81202),i=n(32751),l=n(95344),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},34504:(e,t,n)=>{n.d(t,{Pc:()=>x,ck:()=>N,fC:()=>P});var r=n(3729),o=n(85222),i=n(77411),l=n(31405),a=n(98462),u=n(99048),c=n(62409),s=n(2256),f=n(33183),d=n(3975),p=n(95344),m="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.B)(v),[b,x]=(0,a.b)(v,[w]),[E,R]=b(v),S=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(A,{...e,ref:t})})}));S.displayName=v;var A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:a=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:R=!1,...S}=e,A=r.useRef(null),C=(0,l.e)(t,A),T=(0,d.gm)(u),[L,P]=(0,f.T)({prop:g,defaultProp:w??null,onChange:b,caller:v}),[N,O]=r.useState(!1),k=(0,s.W)(x),D=y(n),W=r.useRef(!1),[F,I]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(m,k),()=>e.removeEventListener(m,k)},[k]),(0,p.jsx)(E,{scope:n,orientation:i,dir:T,loop:a,currentTabStopId:L,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>O(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:N||0===F?-1:0,"data-orientation":i,...S,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{W.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!W.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(m,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===L),...e].filter(Boolean).map(e=>e.ref.current),R)}}W.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>O(!1))})})}),C="RovingFocusGroupItem",T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,children:s,...f}=e,d=(0,u.M)(),m=a||d,h=R(C,n),v=h.currentTabStopId===m,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=h;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:m,focusable:i,active:l,children:(0,p.jsx)(c.WV.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?h.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>M(n))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=E}):s})})});T.displayName=C;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var P=S,N=T},2256:(e,t,n)=>{n.d(t,{W:()=>o});var r=n(3729);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},33183:(e,t,n)=>{n.d(t,{T:()=>a});var r,o=n(3729),i=n(16069),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},16069:(e,t,n)=>{n.d(t,{b:()=>o});var r=n(3729),o=globalThis?.document?r.useLayoutEffect:()=>{}},63085:(e,t,n)=>{n.d(t,{t:()=>i});var r=n(3729),o=n(16069);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};