"use strict";exports.id=6278,exports.ids=[6278],exports.modules={90187:(e,a,s)=>{s.d(a,{L:()=>m});var t=s(95344),r=s(3729),l=s.n(r),n=s(25390),i=s(28765),d=s(56389),o=s(11453);let c=l().forwardRef(({label:e,placeholder:a="Select option",value:s,onValueChange:l,options:c=[],error:m,disabled:u=!1,required:f=!1,onSearch:x,isLoading:p=!1,searchPlaceholder:g="Search...",...h},y)=>{let[b,j]=(0,r.useState)(!1),[w,N]=(0,r.useState)(""),v=(0,r.useRef)(null),$=(0,r.useRef)(null),k="http://*************";(0,r.useEffect)(()=>{console.log("\uD83D\uDD0D SearchableSelectField state change:",{isOpen:b,searchQuery:w,optionsLength:c.length,placeholder:a})},[b,w,c.length,a]),(0,r.useEffect)(()=>{console.log("\uD83D\uDCCB Options changed for:",{placeholder:a,newOptionsLength:c.length,firstFewOptions:c.slice(0,3).map(e=>e.label)})},[c,a]);let[C,L]=(0,r.useState)(!1);(0,r.useEffect)(()=>{w.trim()&&c.length>0&&(console.log("\uD83D\uDD04 Keeping dropdown open due to search results:",{searchQuery:w,optionsLength:c.length,currentIsOpen:b,placeholder:a}),L(!0),b||j(!0))},[c.length,w,b,a]),(0,r.useEffect)(()=>{let e=e=>{v.current&&!v.current.contains(e.target)&&(console.log("\uD83D\uDDB1️ Click outside detected - closing dropdown"),j(!1),L(!1))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,r.useEffect)(()=>{b&&$.current&&$.current.focus()},[b]),(0,r.useEffect)(()=>{x&&(w.trim()?x(w):x(""))},[w,x]);let R=c.find(e=>e.value===s),S=e=>{console.log("✅ Option selected - closing dropdown:",e),l?.(e),j(!1),N(""),L(!1)};return(0,t.jsxs)("div",{className:"space-y-2",ref:y,children:[e&&(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700",children:[e,f&&t.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",ref:v,children:[(0,t.jsxs)("button",{type:"button",onClick:()=>{u||(console.log("\uD83D\uDD18 Dropdown button clicked:",{currentIsOpen:b,willBeOpen:!b}),j(!b))},disabled:u,className:(0,o.cn)("w-full flex items-center justify-between px-3 py-2 text-left bg-white border rounded-md shadow-sm","focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",u&&"bg-gray-50 text-gray-500 cursor-not-allowed",m&&"border-red-500 focus:ring-red-500 focus:border-red-500",!m&&!u&&"border-gray-300 hover:border-gray-400"),children:[t.jsx("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:R?(0,t.jsxs)(t.Fragment,{children:[R.logo&&t.jsx("img",{src:`${k}/${R.logo}`,alt:R.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[t.jsx("div",{className:"truncate",children:R.label}),R.subtitle&&t.jsx("div",{className:"text-xs text-gray-500 truncate",children:R.subtitle})]})]}):t.jsx("span",{className:"text-gray-500",children:a})}),t.jsx(n.Z,{className:(0,o.cn)("w-4 h-4 text-gray-400 transition-transform",b&&"transform rotate-180")})]}),b&&(0,t.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden",children:[t.jsx("div",{className:"p-2 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx(i.Z,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),t.jsx("input",{ref:$,type:"text",placeholder:g,value:w,onChange:e=>N(e.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]})}),t.jsx("div",{className:"max-h-60 overflow-y-auto",children:0!==c.length||p?(0,t.jsxs)(t.Fragment,{children:[c.map((e,a)=>(0,t.jsxs)("button",{type:"button",onClick:()=>S(e.value),className:(0,o.cn)("w-full flex items-center space-x-2 px-3 py-2 text-left text-sm hover:bg-gray-50",s===e.value&&"bg-blue-50 text-blue-700"),children:[e.logo&&t.jsx("img",{src:`${k}/${e.logo}`,alt:e.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[t.jsx("div",{className:"truncate",children:e.label}),e.subtitle&&t.jsx("div",{className:"text-xs text-gray-500 truncate",children:e.subtitle})]})]},e.uniqueKey||`${e.value}-${a}`)),p&&(0,t.jsxs)("div",{className:"w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-gray-500",children:[t.jsx(d.Z,{className:"w-4 h-4 animate-spin"}),t.jsx("span",{children:"Searching..."})]})]}):t.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:"No options found"})})]})]}),m&&t.jsx("p",{className:"text-sm text-red-600",children:m})]})});c.displayName="SearchableSelectField";let m=l().memo(c)},25179:(e,a,s)=>{s.d(a,{ji:()=>y,iN:()=>j,hj:()=>b,UP:()=>p,mg:()=>h,XL:()=>g});var t=s(95344),r=s(3729),l=s(7361),n=s(46540),i=s(2690),d=s(38157),o=s(85684),c=s(62312),m=s(11453);let u=r.forwardRef(({className:e,...a},s)=>t.jsx(o.fC,{ref:s,className:(0,m.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...a,children:t.jsx(o.z$,{className:(0,m.cn)("flex items-center justify-center text-current"),children:t.jsx(c.Z,{className:"h-4 w-4"})})}));u.displayName=o.fC.displayName;var f=s(68065);let x=(0,r.forwardRef)(({label:e,description:a,error:s,required:r,className:n,children:i},d)=>(0,t.jsxs)("div",{ref:d,className:(0,m.cn)("space-y-2",n),children:[e&&(0,t.jsxs)(l._,{className:(0,m.cn)("text-sm font-medium",s&&"text-red-600"),children:[e,r&&t.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),i,a&&!s&&t.jsx("p",{className:"text-sm text-gray-500",children:a}),s&&t.jsx("p",{className:"text-sm text-red-600",children:s})]}));x.displayName="FormField";let p=(0,r.forwardRef)(({label:e,description:a,error:s,required:r,className:l,...i},d)=>t.jsx(x,{label:e,description:a,error:s,required:r,children:t.jsx(n.I,{ref:d,className:(0,m.cn)(s&&"border-red-500 focus:border-red-500",l),...i})}));p.displayName="InputField";let g=(0,r.forwardRef)(({label:e,description:a,error:s,required:r,className:l,...n},d)=>t.jsx(x,{label:e,description:a,error:s,required:r,children:t.jsx(i.g,{ref:d,className:(0,m.cn)(s&&"border-red-500 focus:border-red-500",l),...n})}));g.displayName="TextareaField";let h=(0,r.forwardRef)(({label:e,description:a,error:s,required:r,placeholder:l,value:n,onValueChange:i,options:o,className:c,disabled:u},f)=>{let p=o.find(e=>e.value===n),g="http://*************";return t.jsx(x,{label:e,description:a,error:s,required:r,children:(0,t.jsxs)(d.Ph,{value:n,onValueChange:i,disabled:u,children:[t.jsx(d.i4,{ref:f,className:(0,m.cn)(s&&"border-red-500 focus:border-red-500",c),children:t.jsx("div",{className:"flex items-center justify-between w-full",children:t.jsx("div",{className:"flex items-center space-x-2 flex-1",children:p?p?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[p.logo&&t.jsx("img",{src:`${g}/${p.logo}`,alt:p.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),t.jsx("span",{children:p.label})]}):l:t.jsx("span",{className:"text-muted-foreground",children:l})})})}),t.jsx(d.Bw,{children:o.map(e=>t.jsx(d.Ql,{value:e.value,disabled:e.disabled,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&t.jsx("img",{src:`${g}/${e.logo}`,alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),t.jsx("span",{children:e.label})]})},e.value))})]})})});h.displayName="SelectField";let y=(0,r.forwardRef)(({label:e,description:a,error:s,checked:r,onCheckedChange:n,className:i},d)=>t.jsx(x,{description:a,error:s,className:i,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(u,{ref:d,checked:r,onCheckedChange:n,className:(0,m.cn)(s&&"border-red-500")}),e&&t.jsx(l._,{className:(0,m.cn)("text-sm font-normal cursor-pointer",s&&"text-red-600"),children:e})]})}));y.displayName="CheckboxField",(0,r.forwardRef)(({label:e,description:a,error:s,required:r,value:n,onValueChange:i,options:d,orientation:o="vertical",className:c},u)=>t.jsx(x,{label:e,description:a,error:s,required:r,className:c,children:t.jsx(f.E,{ref:u,value:n,onValueChange:i,className:(0,m.cn)("horizontal"===o?"flex flex-row space-x-4":"space-y-2"),children:d.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(f.m,{value:e.value,disabled:e.disabled,className:(0,m.cn)(s&&"border-red-500")}),t.jsx(l._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})).displayName="RadioField";let b=({title:e,description:a,children:s,className:r})=>(0,t.jsxs)("div",{className:(0,m.cn)("space-y-4",r),children:[(e||a)&&(0,t.jsxs)("div",{className:"space-y-1",children:[e&&t.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e}),a&&t.jsx("p",{className:"text-sm text-gray-600",children:a})]}),t.jsx("div",{className:"space-y-4",children:s})]}),j=({children:e,className:a,align:s="right"})=>t.jsx("div",{className:(0,m.cn)("flex space-x-2 pt-4 border-t","left"===s&&"justify-start","center"===s&&"justify-center","right"===s&&"justify-end",a),children:e})},7361:(e,a,s)=>{s.d(a,{_:()=>o});var t=s(95344),r=s(3729),l=s(14217),n=s(49247),i=s(11453);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...a},s)=>t.jsx(l.f,{ref:s,className:(0,i.cn)(d(),e),...a}));o.displayName=l.f.displayName},68065:(e,a,s)=>{s.d(a,{E:()=>d,m:()=>o});var t=s(95344),r=s(3729),l=s(9913),n=s(82958),i=s(11453);let d=r.forwardRef(({className:e,...a},s)=>t.jsx(l.fC,{className:(0,i.cn)("grid gap-2",e),...a,ref:s}));d.displayName=l.fC.displayName;let o=r.forwardRef(({className:e,...a},s)=>t.jsx(l.ck,{ref:s,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:t.jsx(l.z$,{className:"flex items-center justify-center",children:t.jsx(n.Z,{className:"h-3.5 w-3.5 fill-primary"})})}));o.displayName=l.ck.displayName},38157:(e,a,s)=>{s.d(a,{Bw:()=>p,Ph:()=>c,Ql:()=>g,i4:()=>u,ki:()=>m});var t=s(95344),r=s(3729),l=s(32116),n=s(25390),i=s(12704),d=s(62312),o=s(11453);let c=l.fC;l.ZA;let m=l.B4,u=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(l.xz,{ref:r,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,t.jsx(l.JO,{asChild:!0,children:t.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=l.xz.displayName;let f=r.forwardRef(({className:e,...a},s)=>t.jsx(l.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(i.Z,{className:"h-4 w-4"})}));f.displayName=l.u_.displayName;let x=r.forwardRef(({className:e,...a},s)=>t.jsx(l.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(n.Z,{className:"h-4 w-4"})}));x.displayName=l.$G.displayName;let p=r.forwardRef(({className:e,children:a,position:s="popper",...r},n)=>t.jsx(l.h_,{children:(0,t.jsxs)(l.VY,{ref:n,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[t.jsx(f,{}),t.jsx(l.l_,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),t.jsx(x,{})]})}));p.displayName=l.VY.displayName,r.forwardRef(({className:e,...a},s)=>t.jsx(l.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",e),...a})).displayName=l.__.displayName;let g=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(l.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[t.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(l.wU,{children:t.jsx(d.Z,{className:"h-4 w-4"})})}),t.jsx(l.eT,{children:a})]}));g.displayName=l.ck.displayName,r.forwardRef(({className:e,...a},s)=>t.jsx(l.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=l.Z0.displayName},13611:(e,a,s)=>{s.d(a,{r:()=>i});var t=s(95344),r=s(3729),l=s(19655),n=s(11453);let i=r.forwardRef(({className:e,...a},s)=>t.jsx(l.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:s,children:t.jsx(l.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=l.fC.displayName},2690:(e,a,s)=>{s.d(a,{g:()=>n});var t=s(95344),r=s(3729),l=s(11453);let n=r.forwardRef(({className:e,...a},s)=>t.jsx("textarea",{className:(0,l.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...a}));n.displayName="Textarea"},67999:(e,a,s)=>{s.d(a,{Z:()=>n});var t=s(95344);s(3729);var r=s(13611),l=s(7361);function n({checked:e,onCheckedChange:a,label:s,description:n,disabled:i=!1,size:d="md",variant:o="default"}){return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(r.r,{id:s,checked:e,onCheckedChange:a,disabled:i}),(0,t.jsxs)("div",{className:"flex flex-col",children:[t.jsx(l._,{htmlFor:s,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[d]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[o]} ${i?"opacity-50":""}`,children:s}),n&&t.jsx("span",{className:`text-xs text-gray-500 ${i?"opacity-50":""}`,children:n})]})]})}},59836:(e,a,s)=>{s.d(a,{A:()=>l});var t=s(50053);let r=()=>null,l={getLeagues:async(e={})=>{let a=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&a.append(e,s.toString())});let s=await fetch(`/api/leagues?${a.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,a)=>{let s=a?`${e}-${a}`:e.toString(),t=await fetch(`/api/leagues/${s}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||`Failed to fetch league ${e}`);return await t.json()},createLeague:async e=>await t.x.post("/football/leagues",e),updateLeague:async(e,a,s)=>{let t=r(),n={"Content-Type":"application/json"};t&&(n.Authorization=`Bearer ${t}`);let i=await l.getLeagueById(e,s);if(!i||!i.id)throw Error(`League not found: ${e}${s?`-${s}`:""}`);let d=await fetch(`/api/leagues/${i.id}`,{method:"PATCH",headers:n,body:JSON.stringify(a)});if(!d.ok)throw Error((await d.json()).message||`Failed to update league ${e}`);return await d.json()},deleteLeague:async(e,a)=>{let s=await l.getLeagueById(e,a);if(!s||!s.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);await t.x.delete(`/football/leagues/${s.id}`)},getActiveLeagues:async()=>l.getLeagues({active:!0}),getLeaguesByCountry:async e=>l.getLeagues({country:e}),toggleLeagueStatus:async(e,a,s)=>l.updateLeague(e,{active:a},s)}},73286:(e,a,s)=>{s.d(a,{k:()=>r});var t=s(50053);let r={getTeams:async(e={})=>{let a=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&a.append(e,s.toString())});let s=await fetch(`/api/teams?${a.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch teams");return await s.json()},getTeamById:async e=>await t.x.get(`/football/teams/${e}`),getTeamStatistics:async(e,a,s)=>{let r=new URLSearchParams({league:e.toString(),season:a.toString(),team:s.toString()});return await t.x.get(`/football/teams/statistics?${r.toString()}`)},getTeamsByLeague:async(e,a)=>{let s={league:e};return a&&(s.season=a),r.getTeams(s)},getTeamsByCountry:async e=>r.getTeams({country:e}),searchTeams:async(e,a={})=>{let s=await r.getTeams(a),t=s.data.filter(a=>a.name.toLowerCase().includes(e.toLowerCase())||a.code?.toLowerCase().includes(e.toLowerCase()));return{data:t,meta:{...s.meta,totalItems:t.length,totalPages:Math.ceil(t.length/(a.limit||10))}}},deleteTeam:async e=>{await t.x.delete(`/football/teams/${e}`)}}}};