"use strict";exports.id=7211,exports.ids=[7211],exports.modules={74763:(e,t,a)=>{a.d(t,{V:()=>d});var r=a(95344),s=a(3729),i=a(14513),l=a(25390),o=a(28765),n=a(56389),c=a(11453);let d=({value:e,onChange:t,onSearch:a,placeholder:d="Search...",label:u,description:f,error:m,disabled:g=!1,className:h,emptyMessage:b="No results found",loadingMessage:p="Searching...",clearable:y=!0})=>{let[x,w]=(0,s.useState)(!1),[v,j]=(0,s.useState)(""),[N,$]=(0,s.useState)([]),[C,I]=(0,s.useState)(!1),[S,F]=(0,s.useState)(null),k=(0,s.useRef)(null),T=(0,s.useRef)(null),E=(0,s.useRef)();(0,s.useEffect)(()=>(E.current&&clearTimeout(E.current),v.length>=2?E.current=setTimeout(async()=>{I(!0);try{let e=await a(v);$(e)}catch(e){console.error("Search error:",e),$([])}finally{I(!1)}},300):$([]),()=>{E.current&&clearTimeout(E.current)}),[v,a]),(0,s.useEffect)(()=>{let e=e=>{k.current&&!k.current.contains(e.target)&&w(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,s.useEffect)(()=>{if(e&&N.length>0){let t=N.find(t=>t.value===e);t&&F(t)}else e||F(null)},[e,N]);let L=e=>{F(e),t(e.value),w(!1),j("")},A=()=>{F(null),t(""),j("")};return(0,r.jsxs)("div",{className:(0,c.cn)("space-y-2",h),children:[u&&r.jsx("label",{className:"text-sm font-semibold text-slate-700",children:u}),(0,r.jsxs)("div",{ref:k,className:"relative",children:[S&&!x&&(0,r.jsxs)("div",{className:(0,c.cn)("flex items-center gap-3 p-3 border rounded-lg bg-white",m?"border-red-300":"border-slate-300",g?"bg-slate-50 cursor-not-allowed":"cursor-pointer hover:border-slate-400"),onClick:()=>!g&&w(!0),children:[S.image&&r.jsx("img",{src:S.image,alt:S.label,className:"w-6 h-6 rounded object-cover"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"font-medium text-slate-900 truncate",children:S.label}),S.subtitle&&r.jsx("p",{className:"text-sm text-slate-500 truncate",children:S.subtitle})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[y&&r.jsx("button",{type:"button",onClick:e=>{e.stopPropagation(),A()},className:"p-1 hover:bg-slate-100 rounded",disabled:g,children:r.jsx(i.Z,{className:"w-4 h-4 text-slate-400"})}),r.jsx(l.Z,{className:"w-4 h-4 text-slate-400"})]})]}),(!S||x)&&(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o.Z,{className:"h-4 w-4 text-slate-400"})}),r.jsx("input",{ref:T,type:"text",value:v,onChange:e=>j(e.target.value),onFocus:()=>{w(!0),T.current&&T.current.select()},placeholder:d,disabled:g,className:(0,c.cn)("w-full pl-10 pr-10 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",m?"border-red-300 focus:border-red-500":"border-slate-300 focus:border-blue-500",g&&"bg-slate-50 cursor-not-allowed")}),y&&(S||v)&&r.jsx("button",{type:"button",onClick:A,className:"absolute inset-y-0 right-0 pr-3 flex items-center",disabled:g,children:r.jsx(i.Z,{className:"h-4 w-4 text-slate-400 hover:text-slate-600"})})]}),x&&r.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white border border-slate-200 rounded-lg shadow-lg max-h-60 overflow-auto",children:C?(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[r.jsx(n.Z,{className:"w-4 h-4 animate-spin mr-2"}),r.jsx("span",{className:"text-sm text-slate-500",children:p})]}):N.length>0?r.jsx("div",{className:"py-1",children:N.map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>L(e),className:"w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-slate-50 focus:bg-slate-50 focus:outline-none",children:[e.image&&r.jsx("img",{src:e.image,alt:e.label,className:"w-6 h-6 rounded object-cover flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"font-medium text-slate-900 truncate",children:e.label}),e.subtitle&&r.jsx("p",{className:"text-sm text-slate-500 truncate",children:e.subtitle})]})]},e.value))}):v.length>=2?r.jsx("div",{className:"py-4 text-center text-sm text-slate-500",children:b}):r.jsx("div",{className:"py-4 text-center text-sm text-slate-500",children:"Type at least 2 characters to search"})})]}),f&&!m&&r.jsx("p",{className:"text-sm text-slate-500",children:f}),m&&r.jsx("p",{className:"text-sm text-red-600",children:m})]})}},97700:(e,t,a)=>{a.d(t,{Z:()=>F});var r=a(95344),s=a(47983),i=a.n(s),l=a(3729),o=a(11453),n=a(20862),c=a(8561),d=a(39669),u=a(65554),f=a(34826),m=a(20439),g=a(15600),h=a(58968),b=a(45613),p=a(41272),y=a(77094),x=a(93772),w=a(51360),v=a(94730),j=a(39272),N=a(6911),$=a(20016),C=a(70009),I=a(82424),S=a(5094);function F({value:e="",onChange:t,placeholder:a="Start writing...",className:s,error:F,minHeight:k=300,showCharCount:T=!1,maxLength:E}){let L=(0,l.useRef)(null),[A,D]=(0,l.useState)(!1),P=L.current&&L.current.textContent?.length||0,q=!!E&&P>E,z=()=>{if(L.current){let e=L.current;e.style.height="auto";let t=Math.max(e.scrollHeight,k);e.style.height=`${t}px`}};(0,l.useEffect)(()=>{L.current&&L.current.innerHTML!==e&&(L.current.innerHTML=e,setTimeout(z,0))},[e,k]),(0,l.useEffect)(()=>{let e=new ResizeObserver(()=>{z()});return L.current&&(e.observe(L.current),setTimeout(z,100)),()=>{e.disconnect()}},[]);let U=()=>{L.current&&t&&(t(L.current.innerHTML),setTimeout(z,0))},H=(e,t)=>{document.execCommand(e,!1,t),L.current?.focus(),U()},B=e=>{H("formatBlock",e)},Z=[{icon:n.Z,command:"bold",title:"Bold"},{icon:c.Z,command:"italic",title:"Italic"},{icon:d.Z,command:"underline",title:"Underline"},{icon:u.Z,command:"strikeThrough",title:"Strikethrough"},{icon:f.Z,command:"formatBlock",value:"pre",title:"Code Block"},{icon:m.Z,command:"insertUnorderedList",title:"Bullet List"},{icon:g.Z,command:"insertOrderedList",title:"Numbered List"},{icon:h.Z,command:"formatBlock",value:"blockquote",title:"Quote"},{icon:b.Z,command:"justifyLeft",title:"Align Left"},{icon:p.Z,command:"justifyCenter",title:"Align Center"},{icon:y.Z,command:"justifyRight",title:"Align Right"},{icon:x.Z,command:"subscript",title:"Subscript"},{icon:w.Z,command:"superscript",title:"Superscript"},{icon:v.Z,command:"undo",title:"Undo"},{icon:j.Z,command:"redo",title:"Redo"}];return(0,r.jsxs)("div",{className:"jsx-fa9b18c3ff7e3fd3 "+((0,o.cn)("w-full",s)||""),children:[(0,r.jsxs)("div",{className:"jsx-fa9b18c3ff7e3fd3 border border-slate-300 border-b-0 rounded-t-lg bg-slate-50 p-2 flex flex-wrap gap-1",children:[(0,r.jsxs)("select",{onChange:e=>B(e.target.value),defaultValue:"",className:"jsx-fa9b18c3ff7e3fd3 px-2 py-1 text-sm border border-slate-200 rounded bg-white",children:[r.jsx("option",{value:"",className:"jsx-fa9b18c3ff7e3fd3",children:"Normal"}),r.jsx("option",{value:"h1",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 1"}),r.jsx("option",{value:"h2",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 2"}),r.jsx("option",{value:"h3",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 3"}),r.jsx("option",{value:"h4",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 4"}),r.jsx("option",{value:"h5",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 5"}),r.jsx("option",{value:"h6",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 6"}),r.jsx("option",{value:"p",className:"jsx-fa9b18c3ff7e3fd3",children:"Paragraph"})]}),r.jsx("div",{className:"jsx-fa9b18c3ff7e3fd3 w-px h-6 bg-slate-300 mx-1"}),Z.map(({icon:e,command:t,value:a,title:s})=>r.jsx(S.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>a?H(t,a):H(t),title:s,children:r.jsx(e,{className:"jsx-fa9b18c3ff7e3fd3 h-4 w-4"})},t)),r.jsx("div",{className:"jsx-fa9b18c3ff7e3fd3 w-px h-6 bg-slate-300 mx-1"}),r.jsx(S.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter URL:");e&&H("createLink",e)},title:"Insert Link",children:r.jsx(N.Z,{className:"h-4 w-4"})}),r.jsx(S.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter image URL:");e&&H("insertImage",e)},title:"Insert Image",children:r.jsx($.Z,{className:"h-4 w-4"})}),r.jsx(S.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter color (hex, rgb, or color name):");e&&H("foreColor",e)},title:"Text Color",children:r.jsx(C.Z,{className:"h-4 w-4"})}),r.jsx(S.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter background color (hex, rgb, or color name):");e&&H("hiliteColor",e)},title:"Background Color",children:r.jsx(I.Z,{className:"h-4 w-4"})})]}),r.jsx("div",{ref:L,contentEditable:!0,onInput:U,onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text/html");if(t){let e=document.createElement("div");e.innerHTML=t,e.querySelectorAll("script, style, meta, link").forEach(e=>e.remove()),e.querySelectorAll("*").forEach(e=>{let t=["href","src","alt","title"];Array.from(e.attributes).forEach(a=>{t.includes(a.name)||e.removeAttribute(a.name)})}),t=e.innerHTML}else t=e.clipboardData.getData("text/plain");t&&document.execCommand("insertHTML",!1,t),setTimeout(()=>{U()},0)},onKeyDown:e=>{"Enter"===e.key&&setTimeout(z,0)},onFocus:()=>{D(!0),setTimeout(z,0)},onBlur:()=>D(!1),style:{minHeight:`${k}px`,height:`${k}px`,transition:"height 0.2s ease-in-out"},"data-placeholder":a,suppressContentEditableWarning:!0,className:"jsx-fa9b18c3ff7e3fd3 "+((0,o.cn)("w-full px-4 py-3 border border-slate-300 rounded-b-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500","prose prose-sm max-w-none overflow-hidden resize-none",F&&"border-red-300 focus:border-red-500 focus:ring-red-500",!e&&"text-slate-500")||"")}),!e&&!A&&r.jsx("div",{style:{top:"52px"},className:"jsx-fa9b18c3ff7e3fd3 absolute pointer-events-none text-slate-500 px-4 py-3",children:a}),F&&r.jsx("p",{className:"jsx-fa9b18c3ff7e3fd3 text-sm text-red-600 mt-1",children:F}),T&&r.jsx("div",{className:"jsx-fa9b18c3ff7e3fd3 flex justify-end mt-2",children:(0,r.jsxs)("p",{className:"jsx-fa9b18c3ff7e3fd3 "+((0,o.cn)("text-xs",q?"text-red-600":"text-slate-500")||""),children:[P,E?` / ${E}`:""," characters"]})}),r.jsx(i(),{id:"fa9b18c3ff7e3fd3",children:'[contenteditable].jsx-fa9b18c3ff7e3fd3:empty:before{content:attr(data-placeholder);color:#94a3b8}[contenteditable].jsx-fa9b18c3ff7e3fd3:focus:before{content:""}[contenteditable].jsx-fa9b18c3ff7e3fd3 h1.jsx-fa9b18c3ff7e3fd3{font-size:2rem;font-weight:bold;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h2.jsx-fa9b18c3ff7e3fd3{font-size:1.5rem;font-weight:bold;margin:.875rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h3.jsx-fa9b18c3ff7e3fd3{font-size:1.25rem;font-weight:bold;margin:.75rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h4.jsx-fa9b18c3ff7e3fd3{font-size:1.125rem;font-weight:bold;margin:.625rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h5.jsx-fa9b18c3ff7e3fd3{font-size:1rem;font-weight:bold;margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h6.jsx-fa9b18c3ff7e3fd3{font-size:.875rem;font-weight:bold;margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 p.jsx-fa9b18c3ff7e3fd3{margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 blockquote.jsx-fa9b18c3ff7e3fd3{border-left:4px solid#e2e8f0;padding-left:1rem;margin:1rem 0;font-style:italic;color:#64748b}[contenteditable].jsx-fa9b18c3ff7e3fd3 pre.jsx-fa9b18c3ff7e3fd3{background-color:#f1f5f9;padding:1rem;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-family:"Courier New",monospace;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 ul.jsx-fa9b18c3ff7e3fd3,[contenteditable].jsx-fa9b18c3ff7e3fd3 ol.jsx-fa9b18c3ff7e3fd3{margin:.5rem 0;padding-left:2rem}[contenteditable].jsx-fa9b18c3ff7e3fd3 li.jsx-fa9b18c3ff7e3fd3{margin:.25rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 img.jsx-fa9b18c3ff7e3fd3{max-width:100%;height:auto;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 a.jsx-fa9b18c3ff7e3fd3{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-fa9b18c3ff7e3fd3 a.jsx-fa9b18c3ff7e3fd3:hover{color:#1d4ed8}'})]})}},13611:(e,t,a)=>{a.d(t,{r:()=>o});var r=a(95344),s=a(3729),i=a(19655),l=a(11453);let o=s.forwardRef(({className:e,...t},a)=>r.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:r.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));o.displayName=i.fC.displayName},87279:(e,t,a)=>{a.d(t,{Kf:()=>c,PP:()=>d,sz:()=>n,up:()=>l,zf:()=>o});let r="http://172.31.213.61",s=e=>{if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return t.startsWith("public/")?`${r}/${t}`:t.startsWith("images/")?`${r}/public/${t}`:`${r}/public/images/${t}`},i=()=>null,l=async e=>{try{let t=i();if(!t)throw Error("Authentication required");let a=await fetch(`/api/leagues?search=${encodeURIComponent(e)}&limit=20`,{headers:{Authorization:`Bearer ${t}`}});if(!a.ok)throw Error("Failed to search leagues");return((await a.json()).data||[]).map(e=>({value:e.externalId.toString(),label:e.name,subtitle:`${e.country} • ${e.season}`,image:s(e.logo)}))}catch(e){return console.error("Search leagues error:",e),[]}},o=async e=>{try{let t=i();if(!t)throw Error("Authentication required");let a=await fetch(`/api/teams?search=${encodeURIComponent(e)}&limit=20`,{headers:{Authorization:`Bearer ${t}`}});if(!a.ok)throw Error("Failed to search teams");return((await a.json()).data||[]).map(e=>({value:e.externalId.toString(),label:e.name,subtitle:e.country||"Team",image:s(e.logo)}))}catch(e){return console.error("Search teams error:",e),[]}},n=async e=>{try{let t=i();if(!t)throw Error("Authentication required");console.log("\uD83D\uDD0D Searching players with query:",e);let a=await fetch(`/api/players?search=${encodeURIComponent(e)}&limit=20`,{headers:{Authorization:`Bearer ${t}`}});if(!a.ok){if(console.error("❌ Players search API error:",a.status,a.statusText),404===a.status||a.status>=500)return console.warn("⚠️ Using fallback mock data for players search"),[{value:"154",label:"Erling Haaland",subtitle:"Manchester City • Forward",image:s("public/images/players/154.png")},{value:"276",label:"Mohamed Salah",subtitle:"Liverpool • Forward",image:s("public/images/players/276.png")},{value:"524",label:"Kevin De Bruyne",subtitle:"Manchester City • Midfielder",image:s("public/images/players/524.png")},{value:"1100",label:"Cristiano Ronaldo",subtitle:"Al Nassr • Forward",image:s("public/images/players/1100.png")},{value:"1101",label:"Lionel Messi",subtitle:"Inter Miami • Forward",image:s("public/images/players/1101.png")}].filter(t=>t.label.toLowerCase().includes(e.toLowerCase())||t.subtitle.toLowerCase().includes(e.toLowerCase()));throw Error("Failed to search players")}let r=(await a.json()).data||[];return console.log("✅ Players search successful:",r.length,"players found"),r.map(e=>{let t=e.player;return{value:t.id?.toString(),label:t.name,subtitle:`${t.nationality||"Unknown"} • Player`,image:s(t.photo)}})}catch(t){return console.error("❌ Search players error:",t),console.warn("⚠️ Using fallback mock data due to error"),[{value:"154",label:"Erling Haaland",subtitle:"Manchester City • Forward",image:s("public/images/players/154.png")},{value:"276",label:"Mohamed Salah",subtitle:"Liverpool • Forward",image:s("public/images/players/276.png")},{value:"524",label:"Kevin De Bruyne",subtitle:"Manchester City • Midfielder",image:s("public/images/players/524.png")}].filter(t=>t.label.toLowerCase().includes(e.toLowerCase())||t.subtitle.toLowerCase().includes(e.toLowerCase()))}},c=async e=>{try{let t=i();if(!t)throw Error("Authentication required");let a=await fetch(`/api/fixtures?search=${encodeURIComponent(e)}&limit=20`,{headers:{Authorization:`Bearer ${t}`}});if(!a.ok)throw Error("Failed to search fixtures");return((await a.json()).data||[]).map(e=>{let t=e.homeTeam?.name||"Home Team",a=e.awayTeam?.name||"Away Team",r=new Date(e.date).toLocaleDateString(),s=e.league?.name||"League";return{value:e.externalId?.toString()||e.id?.toString(),label:`${t} vs ${a}`,subtitle:`${s} • ${r}`,image:void 0}})}catch(e){return console.error("Search fixtures error:",e),[]}},d=async(e,t)=>{try{let a=i();if(!a)throw Error("Authentication required");let r="";switch(e){case"league":r=`/api/leagues/${t}`;break;case"team":r=`/api/teams/${t}`;break;case"player":r=`/api/players/${t}`;break;case"fixture":r=`/api/fixtures/${t}`;break;default:return null}let l=await fetch(r,{headers:{Authorization:`Bearer ${a}`}});if(!l.ok)return null;let o=await l.json(),n=o.data||o;switch(e){case"league":return{value:n.externalId?.toString()||n.id?.toString(),label:n.name,subtitle:`${n.country} • ${n.season}`,image:s(n.logo)};case"team":return{value:n.externalId?.toString()||n.id?.toString(),label:n.name,subtitle:n.country||"Team",image:s(n.logo)};case"player":let c=n.player||n;return{value:c.id?.toString(),label:c.name,subtitle:`${c.nationality||"Unknown"} • Player`,image:s(c.photo)};case"fixture":let d=n.homeTeam?.name||"Home Team",u=n.awayTeam?.name||"Away Team",f=new Date(n.date).toLocaleDateString(),m=n.league?.name||"League";return{value:n.externalId?.toString()||n.id?.toString(),label:`${d} vs ${u}`,subtitle:`${m} • ${f}`,image:void 0};default:return null}}catch(t){return console.error(`Get ${e} by ID error:`,t),null}}},23549:(e,t,a)=>{a.d(t,{L_:()=>u,b5:()=>f,HL:()=>g,P9:()=>b,dk:()=>m,fu:()=>p,Ny:()=>h});var r=a(19738),s=a(11494),i=a(14373),l=a(50053);class o{async getCategories(e={}){try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString(),r=a?`${this.baseUrl}?${a}`:this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let s=await l.x.get(r);if(console.log("✅ Categories fetched successfully:",s),s.data&&Array.isArray(s.data))return s;if(Array.isArray(s)){let t=e.page||1,a=e.limit||20,r=s.length,i=(t-1)*a;return{data:s.slice(i,i+a),meta:{currentPage:t,totalPages:Math.ceil(r/a),totalItems:r,limit:a}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await l.x.get(`${this.baseUrl}/${e}`);return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return l.x.post(this.baseUrl,e)}async updateCategory(e,t){return l.x.patch(`${this.baseUrl}/${e}`,t)}async deleteCategory(e){return l.x.delete(`${this.baseUrl}/${e}`)}async toggleCategoryStatus(e,t){return l.x.patch(`${this.baseUrl}/${e}`,{isActive:t})}async reorderCategories(e){return l.x.post(`${this.baseUrl}/reorder`,{categoryIds:e})}async getCategoryStats(){return l.x.get(`${this.baseUrl}/stats`)}constructor(){this.baseUrl="/api/news/categories"}}let n=new o;var c=a(34755);let d={all:["categories"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),e],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],public:()=>[...d.all,"public"]};function u(e={}){return(0,r.a)({queryKey:d.list(e),queryFn:()=>n.getCategories(e),staleTime:3e5})}function f(e){return(0,r.a)({queryKey:d.detail(e),queryFn:()=>n.getCategoryById(e),enabled:!!e,staleTime:6e5})}function m(){return(0,r.a)({queryKey:d.public(),queryFn:()=>n.getPublicCategories(),staleTime:9e5})}function g(){let e=(0,s.NL)();return(0,i.D)({mutationFn:e=>n.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),c.toast.success(`Category "${t.name}" created successfully`)},onError:e=>{c.toast.error("Failed to create category: "+e.message)}})}function h(){let e=(0,s.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>n.updateCategory(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),c.toast.success(`Category "${t.name}" updated successfully`)},onError:e=>{c.toast.error("Failed to update category: "+e.message)}})}function b(){let e=(0,s.NL)();return(0,i.D)({mutationFn:e=>n.deleteCategory(e),onSuccess:(t,a)=>{e.removeQueries({queryKey:d.detail(a)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),c.toast.success("Category deleted successfully")},onError:e=>{c.toast.error("Failed to delete category: "+e.message)}})}function p(){let e=(0,s.NL)();return(0,i.D)({mutationFn:({id:e,isActive:t})=>n.toggleCategoryStatus(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()});let a=t.isActive?"activated":"deactivated";c.toast.success(`Category "${t.name}" ${a} successfully`)},onError:e=>{c.toast.error("Failed to toggle category status: "+e.message)}})}},52721:(e,t,a)=>{a.d(t,{dR:()=>f,po:()=>g,Kg:()=>d,ZZ:()=>u,FF:()=>b,CC:()=>h,rj:()=>m});var r=a(19738),s=a(11494),i=a(14373);function l(e){return{...e,author:`Author ${e.authorId}`,summary:e.excerpt,imageUrl:e.featuredImage,isPublished:"published"===e.status,isHot:e.isFeatured,publishDate:e.publishedAt||e.createdAt}}let o=()=>null,n={getNews:async(e={})=>{let t=function(e){let t={...e};return void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft",delete t.isPublished),void 0!==e.isHot&&(t.isFeatured=e.isHot,delete t.isHot),t}(e),a=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&(Array.isArray(t)?t.forEach(t=>a.append(e,t.toString())):a.append(e,t.toString()))});let r=o(),s={"Content-Type":"application/json"};r&&(s.Authorization=`Bearer ${r}`);let i=await fetch(`/api/news?${a.toString()}`,{method:"GET",headers:s});if(!i.ok)throw Error((await i.json()).message||"Failed to fetch news");let n=await i.json();return n.data&&(n.data=n.data.map(l)),n},getNewsById:async e=>{let t=o(),a={"Content-Type":"application/json"};t&&(a.Authorization=`Bearer ${t}`);let r=await fetch(`/api/news/${e}`,{method:"GET",headers:a});if(!r.ok)throw Error((await r.json()).message||`Failed to fetch news ${e}`);return l(await r.json())},createNews:async e=>{let t=o(),a={"Content-Type":"application/json"};t&&(a.Authorization=`Bearer ${t}`);let r=function(e){return{title:e.title,content:e.content,categoryId:e.categoryId,...e.slug&&{slug:e.slug},...e.excerpt&&{excerpt:e.excerpt},...e.featuredImage&&{featuredImage:e.featuredImage},...e.tags&&{tags:e.tags},...e.status&&{status:e.status},...e.publishedAt&&{publishedAt:e.publishedAt},...e.metaTitle&&{metaTitle:e.metaTitle},...e.metaDescription&&{metaDescription:e.metaDescription},...e.relatedLeagueId&&{relatedLeagueId:e.relatedLeagueId},...e.relatedTeamId&&{relatedTeamId:e.relatedTeamId},...e.relatedPlayerId&&{relatedPlayerId:e.relatedPlayerId},...e.relatedFixtureId&&{relatedFixtureId:e.relatedFixtureId},...void 0!==e.isFeatured&&{isFeatured:e.isFeatured},...void 0!==e.priority&&{priority:e.priority},...e.summary&&!e.excerpt&&{excerpt:e.summary},...e.imageUrl&&!e.featuredImage&&{featuredImage:e.imageUrl},...void 0!==e.isPublished&&!e.status&&{status:e.isPublished?"published":"draft"},...void 0!==e.isHot&&void 0===e.isFeatured&&{isFeatured:e.isHot}}}(e),s=await fetch("/api/news",{method:"POST",headers:a,body:JSON.stringify(r)});if(!s.ok)throw Error((await s.json()).message||"Failed to create news");return l(await s.json())},updateNews:async(e,t)=>{let a=o(),r={"Content-Type":"application/json"};a&&(r.Authorization=`Bearer ${a}`);let s=function(e){let t={};return void 0!==e.title&&(t.title=e.title),void 0!==e.content&&(t.content=e.content),void 0!==e.excerpt&&(t.excerpt=e.excerpt),void 0!==e.featuredImage&&(t.featuredImage=e.featuredImage),void 0!==e.tags&&(t.tags=e.tags),void 0!==e.status&&(t.status=e.status),void 0!==e.categoryId&&(t.categoryId="string"==typeof e.categoryId?parseInt(e.categoryId):e.categoryId),void 0!==e.metaTitle&&(t.metaTitle=e.metaTitle),void 0!==e.metaDescription&&(t.metaDescription=e.metaDescription),void 0!==e.relatedLeagueId&&(t.relatedLeagueId=e.relatedLeagueId),void 0!==e.relatedTeamId&&(t.relatedTeamId=e.relatedTeamId),void 0!==e.relatedPlayerId&&(t.relatedPlayerId=e.relatedPlayerId),void 0!==e.relatedFixtureId&&(t.relatedFixtureId=e.relatedFixtureId),void 0!==e.isFeatured&&(t.isFeatured=e.isFeatured),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.publishedAt&&(t.publishedAt=e.publishedAt),void 0!==e.summary&&(t.excerpt=e.summary),void 0!==e.imageUrl&&(t.featuredImage=e.imageUrl),void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft"),void 0!==e.isHot&&(t.isFeatured=e.isHot),t}(t),i=await fetch(`/api/news/${e}`,{method:"PATCH",headers:r,body:JSON.stringify(s)});if(!i.ok)throw Error((await i.json()).message||`Failed to update news ${e}`);return l(await i.json())},deleteNews:async e=>{let t=o(),a={"Content-Type":"application/json"};t&&(a.Authorization=`Bearer ${t}`);let r=await fetch(`/api/news/${e}`,{method:"DELETE",headers:a});if(!r.ok){let t=`Failed to delete news ${e}`;try{t=(await r.json()).message||t}catch(a){t=`Failed to delete news ${e} (Status: ${r.status})`}throw Error(t)}},getPublishedNews:async(e={})=>n.getNews({...e,status:"published"}),getHotNews:async(e={})=>n.getNews({...e,isFeatured:!0}),getNewsByAuthor:async(e,t={})=>n.getNews({...t,author:e}),toggleNewsStatus:async(e,t)=>n.updateNews(e,{status:t?"published":"draft"}),toggleHotStatus:async(e,t)=>n.updateNews(e,{isFeatured:t})};var c=a(34755);let d=(e={})=>(0,r.a)({queryKey:["news",e],queryFn:()=>n.getNews(e),staleTime:3e5}),u=(e,t=!0)=>(0,r.a)({queryKey:["news",e],queryFn:()=>n.getNewsById(e),enabled:!!e&&t,staleTime:6e5}),f=()=>{let e=(0,s.NL)();return(0,i.D)({mutationFn:e=>n.createNews(e),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News created successfully")},onError:e=>{c.toast.error(`Failed to create news: ${e.message}`)}})},m=()=>{let e=(0,s.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>n.updateNews(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success("News updated successfully")},onError:e=>{c.toast.error(`Failed to update news: ${e.message}`)}})},g=()=>{let e=(0,s.NL)();return(0,i.D)({mutationFn:e=>n.deleteNews(e),onSuccess:()=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News deleted successfully")},onError:e=>{c.toast.error(`Failed to delete news: ${e.message}`)}})},h=()=>{let e=(0,s.NL)();return(0,i.D)({mutationFn:({id:e,isPublished:t})=>n.toggleNewsStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success(`News ${"published"===t.status?"published":"unpublished"} successfully`)},onError:e=>{c.toast.error(`Failed to toggle news status: ${e.message}`)}})},b=()=>{let e=(0,s.NL)();return(0,i.D)({mutationFn:({id:e,isHot:t})=>n.toggleHotStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success(`News ${t.isFeatured?"marked as featured":"unmarked as featured"} successfully`)},onError:e=>{c.toast.error(`Failed to toggle hot status: ${e.message}`)}})}}};