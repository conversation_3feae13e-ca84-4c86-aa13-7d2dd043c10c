"use strict";exports.id=6614,exports.ids=[6614],exports.modules={2768:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35851:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},51838:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},64989:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},31498:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},79200:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(97075).Z)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},14217:(e,t,n)=>{n.d(t,{f:()=>u});var r=n(3729),a=n(62409),i=n(95344),o=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var u=o},53330:(e,t,n)=>{n.d(t,{VY:()=>Q,fC:()=>$,xz:()=>q,zt:()=>j});var r=n(3729),a=n(85222),i=n(31405),o=n(98462),u=n(44155),l=n(99048),s=n(37574),c=(n(31179),n(43234)),d=n(62409),f=n(32751),h=n(33183),g=n(87298),m=n(95344),[w,p]=(0,o.b)("Tooltip",[s.D7]),x=(0,s.D7)(),y="TooltipProvider",b="tooltip.open",[D,T]=w(y),v=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:a=300,disableHoverableContent:i=!1,children:o}=e,u=r.useRef(!0),l=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(D,{scope:t,isOpenDelayedRef:u,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),u.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.current=!0,a)},[a]),isPointerInTransitRef:l,onPointerInTransitChange:r.useCallback(e=>{l.current=e},[]),disableHoverableContent:i,children:o})};v.displayName=y;var M="Tooltip",[N,C]=w(M),Y=e=>{let{__scopeTooltip:t,children:n,open:a,defaultOpen:i,onOpenChange:o,disableHoverableContent:u,delayDuration:c}=e,d=T(M,e.__scopeTooltip),f=x(t),[g,w]=r.useState(null),p=(0,l.M)(),y=r.useRef(0),D=u??d.disableHoverableContent,v=c??d.delayDuration,C=r.useRef(!1),[Y,k]=(0,h.T)({prop:a,defaultProp:i??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),o?.(e)},caller:M}),E=r.useMemo(()=>Y?C.current?"delayed-open":"instant-open":"closed",[Y]),S=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,C.current=!1,k(!0)},[k]),P=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,k(!1)},[k]),H=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{C.current=!0,k(!0),y.current=0},v)},[v,k]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,m.jsx)(s.fC,{...f,children:(0,m.jsx)(N,{scope:t,contentId:p,open:Y,stateAttribute:E,trigger:g,onTriggerChange:w,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?H():S()},[d.isOpenDelayedRef,H,S]),onTriggerLeave:r.useCallback(()=>{D?P():(window.clearTimeout(y.current),y.current=0)},[P,D]),onOpen:S,onClose:P,disableHoverableContent:D,children:n})})};Y.displayName=M;var k="TooltipTrigger",E=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...o}=e,u=C(k,n),l=T(k,n),c=x(n),f=r.useRef(null),h=(0,i.e)(t,f,u.onTriggerChange),g=r.useRef(!1),w=r.useRef(!1),p=r.useCallback(()=>g.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,m.jsx)(s.ee,{asChild:!0,...c,children:(0,m.jsx)(d.WV.button,{"aria-describedby":u.open?u.contentId:void 0,"data-state":u.stateAttribute,...o,ref:h,onPointerMove:(0,a.M)(e.onPointerMove,e=>{"touch"===e.pointerType||w.current||l.isPointerInTransitRef.current||(u.onTriggerEnter(),w.current=!0)}),onPointerLeave:(0,a.M)(e.onPointerLeave,()=>{u.onTriggerLeave(),w.current=!1}),onPointerDown:(0,a.M)(e.onPointerDown,()=>{u.open&&u.onClose(),g.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,a.M)(e.onFocus,()=>{g.current||u.onOpen()}),onBlur:(0,a.M)(e.onBlur,u.onClose),onClick:(0,a.M)(e.onClick,u.onClose)})})});E.displayName=k;var[S,P]=w("TooltipPortal",{forceMount:void 0}),H="TooltipContent",Z=r.forwardRef((e,t)=>{let n=P(H,e.__scopeTooltip),{forceMount:r=n.forceMount,side:a="top",...i}=e,o=C(H,e.__scopeTooltip);return(0,m.jsx)(c.z,{present:r||o.open,children:o.disableHoverableContent?(0,m.jsx)(F,{side:a,...i,ref:t}):(0,m.jsx)(L,{side:a,...i,ref:t})})}),L=r.forwardRef((e,t)=>{let n=C(H,e.__scopeTooltip),a=T(H,e.__scopeTooltip),o=r.useRef(null),u=(0,i.e)(t,o),[l,s]=r.useState(null),{trigger:c,onClose:d}=n,f=o.current,{onPointerInTransitChange:h}=a,g=r.useCallback(()=>{s(null),h(!1)},[h]),w=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},a=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,a,i)){case i:return"left";case a:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,a),...function(e){let{top:t,right:n,bottom:r,left:a}=e;return[{x:a,y:t},{x:n,y:t},{x:n,y:r},{x:a,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>g(),[g]),r.useEffect(()=>{if(c&&f){let e=e=>w(e,f),t=e=>w(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,w,g]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=c?.contains(t)||f?.contains(t),a=!function(e,t){let{x:n,y:r}=e,a=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let o=t[e],u=t[i],l=o.x,s=o.y,c=u.x,d=u.y;s>r!=d>r&&n<(c-l)*(r-s)/(d-s)+l&&(a=!a)}return a}(n,l);r?g():a&&(g(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,g]),(0,m.jsx)(F,{...e,ref:u})}),[O,z]=w(M,{isInside:!1}),I=(0,f.sA)("TooltipContent"),F=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:a,"aria-label":i,onEscapeKeyDown:o,onPointerDownOutside:l,...c}=e,d=C(H,n),f=x(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(b,h),()=>document.removeEventListener(b,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,m.jsx)(u.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,m.jsxs)(s.VY,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(I,{children:a}),(0,m.jsx)(O,{scope:n,isInside:!0,children:(0,m.jsx)(g.fC,{id:d.contentId,role:"tooltip",children:i||a})})]})})});Z.displayName=H;var U="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,a=x(n);return z(U,n).isInside?null:(0,m.jsx)(s.Eh,{...a,...r,ref:t})}).displayName=U;var j=v,$=Y,q=E,Q=Z},54054:(e,t,n)=>{n.d(t,{CV:()=>P});var r=n(3281),a=n(55395);function i(e,t,n){var r,i;let o=Object.assign({},(0,a.j)()),u=(r=n.timeZone,i=n.locale??o.locale,new Intl.DateTimeFormat(i?[i.code,"en-US"]:void 0,{timeZone:r,timeZoneName:e}));return"formatToParts"in u?function(e,t){let n=e.formatToParts(t);for(let e=n.length-1;e>=0;--e)if("timeZoneName"===n[e].type)return n[e].value}(u,t):function(e,t){let n=e.format(t).replace(/\u200E/g,""),r=/ [\w-+ ]+$/.exec(n);return r?r[0].substr(1):""}(u,t)}let o={year:0,month:1,day:2,hour:3,minute:4,second:5},u={},l=new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:"America/New_York",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date("2014-06-25T04:00:00.123Z")),s="06/25/2014, 00:00:00"===l||"‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00"===l;function c(e,t,n,r,a,i,o){let u=new Date(0);return u.setUTCFullYear(e,t,n),u.setUTCHours(r,a,i,o),u}function d(e,t,n){let r,a;if(!e)return 0;let i=/^(Z)$/.exec(e);if(i)return 0;if(i=/^([+-]\d{2})$/.exec(e))return h(r=parseInt(i[1],10))?-(36e5*r):NaN;if(i=/^([+-])(\d{2}):?(\d{2})$/.exec(e)){r=parseInt(i[2],10);let e=parseInt(i[3],10);return h(r,e)?(a=36e5*Math.abs(r)+6e4*e,"+"===i[1]?-a:a):NaN}if(function(e){if(g[e])return!0;try{return new Intl.DateTimeFormat(void 0,{timeZone:e}),g[e]=!0,!0}catch(e){return!1}}(e)){var o;t=new Date(t||Date.now());let r=f(n?t:c((o=t).getFullYear(),o.getMonth(),o.getDate(),o.getHours(),o.getMinutes(),o.getSeconds(),o.getMilliseconds()),e);return-(n?r:function(e,t,n){let r=e.getTime()-t,a=f(new Date(r),n);if(t===a)return t;let i=f(new Date(r-=a-t),n);return a===i?a:Math.max(a,i)}(t,r,e))}return NaN}function f(e,t){let n=function(e,t){let n=(u[t]||(u[t]=s?new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:t,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})),u[t]);return"formatToParts"in n?function(e,t){try{let n=e.formatToParts(t),r=[];for(let e=0;e<n.length;e++){let t=o[n[e].type];void 0!==t&&(r[t]=parseInt(n[e].value,10))}return r}catch(e){if(e instanceof RangeError)return[NaN];throw e}}(n,e):function(e,t){let n=e.format(t),r=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(n);return[parseInt(r[3],10),parseInt(r[1],10),parseInt(r[2],10),parseInt(r[4],10),parseInt(r[5],10),parseInt(r[6],10)]}(n,e)}(e,t),r=c(n[0],n[1]-1,n[2],n[3]%24,n[4],n[5],0).getTime(),a=e.getTime(),i=a%1e3;return r-(a-=i>=0?i:1e3+i)}function h(e,t){return -23<=e&&e<=23&&(null==t||0<=t&&t<=59)}let g={},m={X:function(e,t,n){let r=w(n.timeZone,e);if(0===r)return"Z";switch(t){case"X":return y(r);case"XXXX":case"XX":return x(r);default:return x(r,":")}},x:function(e,t,n){let r=w(n.timeZone,e);switch(t){case"x":return y(r);case"xxxx":case"xx":return x(r);default:return x(r,":")}},O:function(e,t,n){let r=w(n.timeZone,e);switch(t){case"O":case"OO":case"OOO":return"GMT"+function(e,t=""){let n=e>0?"-":"+",r=Math.abs(e),a=Math.floor(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+t+p(i,2)}(r,":");default:return"GMT"+x(r,":")}},z:function(e,t,n){switch(t){case"z":case"zz":case"zzz":return i("short",e,n);default:return i("long",e,n)}}};function w(e,t){let n=e?d(e,t,!0)/6e4:t?.getTimezoneOffset()??0;if(Number.isNaN(n))throw RangeError("Invalid time zone specified: "+e);return n}function p(e,t){let n=Math.abs(e).toString();for(;n.length<t;)n="0"+n;return(e<0?"-":"")+n}function x(e,t=""){let n=Math.abs(e);return(e>0?"-":"+")+p(Math.floor(n/60),2)+t+p(Math.floor(n%60),2)}function y(e,t){return e%60==0?(e>0?"-":"+")+p(Math.abs(e)/60,2):x(e,t)}function b(e){let t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+e-+t}let D={dateTimePattern:/^([0-9W+-]+)(T| )(.*)/,datePattern:/^([0-9W+-]+)(.*)/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timeZone:/(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/};function T(e,t={}){if(arguments.length<1)throw TypeError("1 argument required, but only "+arguments.length+" present");if(null===e)return new Date(NaN);let n=null==t.additionalDigits?2:Number(t.additionalDigits);if(2!==n&&1!==n&&0!==n)throw RangeError("additionalDigits must be 0, 1 or 2");if(e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e))return new Date(e.getTime());if("number"==typeof e||"[object Number]"===Object.prototype.toString.call(e))return new Date(e);if("[object String]"!==Object.prototype.toString.call(e))return new Date(NaN);let r=function(e){let t;let n={},r=D.dateTimePattern.exec(e);if(r?(n.date=r[1],t=r[3]):(r=D.datePattern.exec(e))?(n.date=r[1],t=r[2]):(n.date=null,t=e),t){let e=D.timeZone.exec(t);e?(n.time=t.replace(e[1],""),n.timeZone=e[1].trim()):n.time=t}return n}(e),{year:a,restDateString:i}=function(e,t){if(e){let n=D.YYY[t],r=D.YYYYY[t],a=D.YYYY.exec(e)||r.exec(e);if(a){let t=a[1];return{year:parseInt(t,10),restDateString:e.slice(t.length)}}if(a=D.YY.exec(e)||n.exec(e)){let t=a[1];return{year:100*parseInt(t,10),restDateString:e.slice(t.length)}}}return{year:null}}(r.date,n),o=function(e,t){let n,r,a;if(null===t)return null;if(!e||!e.length)return(n=new Date(0)).setUTCFullYear(t),n;let i=D.MM.exec(e);if(i)return(n=new Date(0),Y(t,r=parseInt(i[1],10)-1))?(n.setUTCFullYear(t,r),n):new Date(NaN);if(i=D.DDD.exec(e)){n=new Date(0);let e=parseInt(i[1],10);return!function(e,t){if(t<1)return!1;let n=C(e);return(!n||!(t>366))&&(!!n||!(t>365))}(t,e)?new Date(NaN):(n.setUTCFullYear(t,0,e),n)}if(i=D.MMDD.exec(e)){n=new Date(0),r=parseInt(i[1],10)-1;let e=parseInt(i[2],10);return Y(t,r,e)?(n.setUTCFullYear(t,r,e),n):new Date(NaN)}if(i=D.Www.exec(e))return k(a=parseInt(i[1],10)-1)?v(t,a):new Date(NaN);if(i=D.WwwD.exec(e)){a=parseInt(i[1],10)-1;let e=parseInt(i[2],10)-1;return k(a,e)?v(t,a,e):new Date(NaN)}return null}(i,a);if(null===o||isNaN(o.getTime())||!o)return new Date(NaN);{let e;let n=o.getTime(),a=0;if(r.time&&(null===(a=function(e){let t,n;let r=D.HH.exec(e);if(r)return E(t=parseFloat(r[1].replace(",",".")))?t%24*36e5:NaN;if(r=D.HHMM.exec(e))return E(t=parseInt(r[1],10),n=parseFloat(r[2].replace(",",".")))?t%24*36e5+6e4*n:NaN;if(r=D.HHMMSS.exec(e)){t=parseInt(r[1],10),n=parseInt(r[2],10);let e=parseFloat(r[3].replace(",","."));return E(t,n,e)?t%24*36e5+6e4*n+1e3*e:NaN}return null}(r.time))||isNaN(a)))return new Date(NaN);if(r.timeZone||t.timeZone){if(isNaN(e=d(r.timeZone||t.timeZone,new Date(n+a))))return new Date(NaN)}else e=b(new Date(n+a)),e=b(new Date(n+a+e));return new Date(n+a+e)}}function v(e,t,n){t=t||0,n=n||0;let r=new Date(0);r.setUTCFullYear(e,0,4);let a=7*t+n+1-(r.getUTCDay()||7);return r.setUTCDate(r.getUTCDate()+a),r}let M=[31,28,31,30,31,30,31,31,30,31,30,31],N=[31,29,31,30,31,30,31,31,30,31,30,31];function C(e){return e%400==0||e%4==0&&e%100!=0}function Y(e,t,n){if(t<0||t>11)return!1;if(null!=n){if(n<1)return!1;let r=C(e);if(r&&n>N[t]||!r&&n>M[t])return!1}return!0}function k(e,t){return!(e<0)&&!(e>52)&&(null==t||!(t<0)&&!(t>6))}function E(e,t,n){return!(e<0)&&!(e>=25)&&(null==t||!(t<0)&&!(t>=60))&&(null==n||!(n<0)&&!(n>=60))}let S=/([xXOz]+)|''|'(''|[^'])+('|$)/g;function P(e,t,n,a){return a={...a,timeZone:t,originalDate:e},function(e,t,n={}){let a=(t=String(t)).match(S);if(a){let r=T(n.originalDate||e,n);t=a.reduce(function(e,t){if("'"===t[0])return e;let a=e.indexOf(t),i="'"===e[a-1],o=e.replace(t,"'"+m[t[0]](r,t,n)+"'");return i?o.substring(0,a-1)+o.substring(a+1):o},t)}return(0,r.WU)(e,t,n)}(function(e,t,n){let r=d(t,e=T(e,n),!0),a=new Date(e.getTime()-r),i=new Date(0);return i.setFullYear(a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate()),i.setHours(a.getUTCHours(),a.getUTCMinutes(),a.getUTCSeconds(),a.getUTCMilliseconds()),i}(e,t,{timeZone:a.timeZone}),n,a)}},16061:(e,t,n)=>{n.d(t,{w:()=>u});var r=n(74649),a=n(84951),i=n(88731),o=n(27087);function u(e,t,n){let[u,l]=(0,a.d)(n?.in,e,t),s=(0,o.b)(u),c=(0,o.b)(l);return Math.round((+s-(0,r.D)(s)-(+c-(0,r.D)(c)))/i.dP)}},3281:(e,t,n)=>{n.d(t,{WU:()=>P});var r=n(44246),a=n(55395),i=n(16061),o=n(1707),u=n(98037),l=n(92594),s=n(48385),c=n(29474),d=n(82261);function f(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let h={y(e,t){let n=e.getFullYear(),r=n>0?n:1-n;return f("yy"===t?r%100:r,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):f(n+1,2)},d:(e,t)=>f(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>f(e.getHours()%12||12,t.length),H:(e,t)=>f(e.getHours(),t.length),m:(e,t)=>f(e.getMinutes(),t.length),s:(e,t)=>f(e.getSeconds(),t.length),S(e,t){let n=t.length;return f(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},g={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},m={G:function(e,t,n){let r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return h.y(e,t)},Y:function(e,t,n,r){let a=(0,d.c)(e,r),i=a>0?a:1-a;return"YY"===t?f(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):f(i,t.length)},R:function(e,t){return f((0,s.L)(e),t.length)},u:function(e,t){return f(e.getFullYear(),t.length)},Q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return f(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return f(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){let r=e.getMonth();switch(t){case"M":case"MM":return h.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){let r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return f(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){let a=(0,c.Q)(e,r);return"wo"===t?n.ordinalNumber(a,{unit:"week"}):f(a,t.length)},I:function(e,t,n){let r=(0,l.l)(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):f(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):h.d(e,t)},D:function(e,t,n){let r=function(e,t){let n=(0,u.Q)(e,void 0);return(0,i.w)(n,(0,o.e)(n))+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):f(r,t.length)},E:function(e,t,n){let r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){let a=e.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return f(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){let a=e.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return f(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){let r=e.getDay(),a=0===r?7:r;switch(t){case"i":return String(a);case"ii":return f(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){let r;let a=e.getHours();switch(r=12===a?g.noon:0===a?g.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){let r;let a=e.getHours();switch(r=a>=17?g.evening:a>=12?g.afternoon:a>=4?g.morning:g.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return h.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):h.H(e,t)},K:function(e,t,n){let r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):f(r,t.length)},k:function(e,t,n){let r=e.getHours();return(0===r&&(r=24),"ko"===t)?n.ordinalNumber(r,{unit:"hour"}):f(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):h.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):h.s(e,t)},S:function(e,t){return h.S(e,t)},X:function(e,t,n){let r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return p(r);case"XXXX":case"XX":return x(r);default:return x(r,":")}},x:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"x":return p(r);case"xxxx":case"xx":return x(r);default:return x(r,":")}},O:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+w(r,":");default:return"GMT"+x(r,":")}},z:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+w(r,":");default:return"GMT"+x(r,":")}},t:function(e,t,n){return f(Math.trunc(+e/1e3),t.length)},T:function(e,t,n){return f(+e,t.length)}};function w(e,t=""){let n=e>0?"-":"+",r=Math.abs(e),a=Math.trunc(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+t+f(i,2)}function p(e,t){return e%60==0?(e>0?"-":"+")+f(Math.abs(e)/60,2):x(e,t)}function x(e,t=""){let n=Math.abs(e);return(e>0?"-":"+")+f(Math.trunc(n/60),2)+t+f(n%60,2)}let y=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},b=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},D={p:b,P:(e,t)=>{let n;let r=e.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return y(e,t);switch(a){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",y(a,t)).replace("{{time}}",b(i,t))}},T=/^D+$/,v=/^Y+$/,M=["D","DD","YY","YYYY"];var N=n(80471);let C=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Y=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,k=/^'([^]*?)'?$/,E=/''/g,S=/[a-zA-Z]/;function P(e,t,n){let i=(0,a.j)(),o=n?.locale??i.locale??r._,l=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,s=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??i.weekStartsOn??i.locale?.options?.weekStartsOn??0,c=(0,u.Q)(e,n?.in);if(!(0,N.J)(c))throw RangeError("Invalid time value");let d=t.match(Y).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,D[t])(e,o.formatLong):e}).join("").match(C).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(k);return t?t[1].replace(E,"'"):e}(e)};if(m[t])return{isToken:!0,value:e};if(t.match(S))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});o.localize.preprocessor&&(d=o.localize.preprocessor(c,d));let f={firstWeekContainsDate:l,weekStartsOn:s,locale:o};return d.map(r=>{if(!r.isToken)return r.value;let a=r.value;return(!n?.useAdditionalWeekYearTokens&&v.test(a)||!n?.useAdditionalDayOfYearTokens&&T.test(a))&&function(e,t,n){let r=function(e,t,n){let r="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(console.warn(r),M.includes(e))throw RangeError(r)}(a,t,String(e)),(0,m[a[0]])(c,a,o.localize,f)}).join("")}},92594:(e,t,n)=>{n.d(t,{l:()=>l});var r=n(88731),a=n(49953),i=n(10077),o=n(48385),u=n(98037);function l(e,t){let n=(0,u.Q)(e,t?.in);return Math.round((+(0,a.T)(n)-+function(e,t){let n=(0,o.L)(e,t),r=(0,i.L)(t?.in||e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),(0,a.T)(r)}(n))/r.jE)+1}},48385:(e,t,n)=>{n.d(t,{L:()=>o});var r=n(10077),a=n(49953),i=n(98037);function o(e,t){let n=(0,i.Q)(e,t?.in),o=n.getFullYear(),u=(0,r.L)(n,0);u.setFullYear(o+1,0,4),u.setHours(0,0,0,0);let l=(0,a.T)(u),s=(0,r.L)(n,0);s.setFullYear(o,0,4),s.setHours(0,0,0,0);let c=(0,a.T)(s);return n.getTime()>=l.getTime()?o+1:n.getTime()>=c.getTime()?o:o-1}},29474:(e,t,n)=>{n.d(t,{Q:()=>s});var r=n(88731),a=n(67700),i=n(55395),o=n(10077),u=n(82261),l=n(98037);function s(e,t){let n=(0,l.Q)(e,t?.in);return Math.round((+(0,a.z)(n,t)-+function(e,t){let n=(0,i.j)(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,l=(0,u.c)(e,t),s=(0,o.L)(t?.in||e,0);return s.setFullYear(l,0,r),s.setHours(0,0,0,0),(0,a.z)(s,t)}(n,t))/r.jE)+1}},82261:(e,t,n)=>{n.d(t,{c:()=>u});var r=n(55395),a=n(10077),i=n(67700),o=n(98037);function u(e,t){let n=(0,o.Q)(e,t?.in),u=n.getFullYear(),l=(0,r.j)(),s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??l.firstWeekContainsDate??l.locale?.options?.firstWeekContainsDate??1,c=(0,a.L)(t?.in||e,0);c.setFullYear(u+1,0,s),c.setHours(0,0,0,0);let d=(0,i.z)(c,t),f=(0,a.L)(t?.in||e,0);f.setFullYear(u,0,s),f.setHours(0,0,0,0);let h=(0,i.z)(f,t);return+n>=+d?u+1:+n>=+h?u:u-1}},6283:(e,t,n)=>{function r(e){return e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)}n.d(t,{J:()=>r})},80471:(e,t,n)=>{n.d(t,{J:()=>i});var r=n(6283),a=n(98037);function i(e){return!(!(0,r.J)(e)&&"number"!=typeof e||isNaN(+(0,a.Q)(e)))}},86997:(e,t,n)=>{n.d(t,{D:()=>o});var r=n(88731),a=n(10077),i=n(98037);function o(e,t){let n,o;let m=()=>(0,a.L)(t?.in,NaN),w=t?.additionalDigits??2,p=function(e){let t;let n={},r=e.split(u.dateTimeDelimiter);if(r.length>2)return n;if(/:/.test(r[0])?t=r[0]:(n.date=r[0],t=r[1],u.timeZoneDelimiter.test(n.date)&&(n.date=e.split(u.timeZoneDelimiter)[0],t=e.substr(n.date.length,e.length))),t){let e=u.timezone.exec(t);e?(n.time=t.replace(e[1],""),n.timezone=e[1]):n.time=t}return n}(e);if(p.date){let e=function(e,t){let n=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};let a=r[1]?parseInt(r[1]):null,i=r[2]?parseInt(r[2]):null;return{year:null===i?a:100*i,restDateString:e.slice((r[1]||r[2]).length)}}(p.date,w);n=function(e,t){if(null===t)return new Date(NaN);let n=e.match(l);if(!n)return new Date(NaN);let r=!!n[4],a=d(n[1]),i=d(n[2])-1,o=d(n[3]),u=d(n[4]),s=d(n[5])-1;if(r)return u>=1&&u<=53&&s>=0&&s<=6?function(e,t,n){let r=new Date(0);r.setUTCFullYear(e,0,4);let a=r.getUTCDay()||7;return r.setUTCDate(r.getUTCDate()+((t-1)*7+n+1-a)),r}(t,u,s):new Date(NaN);{let e=new Date(0);return i>=0&&i<=11&&o>=1&&o<=(h[i]||(g(t)?29:28))&&a>=1&&a<=(g(t)?366:365)?(e.setUTCFullYear(t,i,Math.max(a,o)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!n||isNaN(+n))return m();let x=+n,y=0;if(p.time&&isNaN(y=function(e){let t=e.match(s);if(!t)return NaN;let n=f(t[1]),a=f(t[2]),i=f(t[3]);return(24===n?0===a&&0===i:i>=0&&i<60&&a>=0&&a<60&&n>=0&&n<25)?n*r.vh+a*r.yJ+1e3*i:NaN}(p.time)))return m();if(p.timezone){if(isNaN(o=function(e){if("Z"===e)return 0;let t=e.match(c);if(!t)return 0;let n="+"===t[1]?-1:1,a=parseInt(t[2]),i=t[3]&&parseInt(t[3])||0;return i>=0&&i<=59?n*(a*r.vh+i*r.yJ):NaN}(p.timezone)))return m()}else{let e=new Date(x+y),n=(0,i.Q)(0,t?.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return(0,i.Q)(x+y+o,t?.in)}let u={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},l=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,s=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,c=/^([+-])(\d{2})(?::?(\d{2}))?$/;function d(e){return e?parseInt(e):1}function f(e){return e&&parseFloat(e.replace(",","."))||0}let h=[31,null,31,30,31,30,31,31,30,31,30,31];function g(e){return e%400==0||e%4==0&&e%100!=0}},27087:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(98037);function a(e,t){let n=(0,r.Q)(e,t?.in);return n.setHours(0,0,0,0),n}},49953:(e,t,n)=>{n.d(t,{T:()=>a});var r=n(67700);function a(e,t){return(0,r.z)(e,{...t,weekStartsOn:1})}},67700:(e,t,n)=>{n.d(t,{z:()=>i});var r=n(55395),a=n(98037);function i(e,t){let n=(0,r.j)(),i=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,o=(0,a.Q)(e,t?.in),u=o.getDay();return o.setDate(o.getDate()-((u<i?7:0)+u-i)),o.setHours(0,0,0,0),o}},1707:(e,t,n)=>{n.d(t,{e:()=>a});var r=n(98037);function a(e,t){let n=(0,r.Q)(e,t?.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}}};