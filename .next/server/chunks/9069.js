"use strict";exports.id=9069,exports.ids=[9069],exports.modules={80958:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},95269:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96885:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},30304:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},73875:(e,t,a)=>{a.d(t,{sm:()=>g,uB:()=>h,u_:()=>u});var r=a(95344),s=a(3729),i=a(81021),n=a(13659),l=a(14513),o=a(5094),d=a(11453);let c={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:t,title:a,description:u,children:g,size:h="md",showCloseButton:p=!0,closeOnOverlayClick:y=!0,className:x})=>r.jsx(i.u,{appear:!0,show:e,as:s.Fragment,children:(0,r.jsxs)(n.Vq,{as:"div",className:"relative z-50",onClose:y?t:()=>{},children:[r.jsx(i.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),r.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:r.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:r.jsx(i.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(n.Vq.Panel,{className:(0,d.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",c[h],x),children:[(a||p)&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[a&&r.jsx(n.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:a}),u&&r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),p&&r.jsx(o.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:r.jsx(l.Z,{className:"h-4 w-4"})})]}),r.jsx("div",{className:"mt-2",children:g})]})})})})]})}),g=({isOpen:e,onClose:t,onConfirm:a,title:s="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:n="Confirm",cancelText:l="Cancel",variant:d="default",loading:c=!1})=>r.jsx(u,{isOpen:e,onClose:t,title:s,size:"sm",closeOnOverlayClick:!c,children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,r.jsxs)("div",{className:"flex space-x-2 justify-end",children:[r.jsx(o.z,{variant:"outline",onClick:t,disabled:c,children:l}),r.jsx(o.z,{variant:"destructive"===d?"destructive":"default",onClick:a,disabled:c,children:c?"Processing...":n})]})]})}),h=({isOpen:e,onClose:t,title:a,description:s,children:i,onSubmit:n,submitText:l="Save",cancelText:d="Cancel",loading:c=!1,size:g="md"})=>r.jsx(u,{isOpen:e,onClose:t,title:a,description:s,size:g,closeOnOverlayClick:!c,children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n?.()},className:"space-y-4",children:[i,(0,r.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[r.jsx(o.z,{type:"button",variant:"outline",onClick:t,disabled:c,children:d}),n&&r.jsx(o.z,{type:"submit",disabled:c,children:c?"Saving...":l})]})]})})},13611:(e,t,a)=>{a.d(t,{r:()=>l});var r=a(95344),s=a(3729),i=a(19655),n=a(11453);let l=s.forwardRef(({className:e,...t},a)=>r.jsx(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:r.jsx(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));l.displayName=i.fC.displayName},67999:(e,t,a)=>{a.d(t,{Z:()=>n});var r=a(95344);a(3729);var s=a(13611),i=a(7361);function n({checked:e,onCheckedChange:t,label:a,description:n,disabled:l=!1,size:o="md",variant:d="default"}){return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(s.r,{id:a,checked:e,onCheckedChange:t,disabled:l}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(i._,{htmlFor:a,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[o]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[d]} ${l?"opacity-50":""}`,children:a}),n&&r.jsx("span",{className:`text-xs text-gray-500 ${l?"opacity-50":""}`,children:n})]})]})}},59836:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(50053);let s=()=>null,i={getLeagues:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/leagues?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?`${e}-${t}`:e.toString(),r=await fetch(`/api/leagues/${a}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||`Failed to fetch league ${e}`);return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let r=s(),n={"Content-Type":"application/json"};r&&(n.Authorization=`Bearer ${r}`);let l=await i.getLeagueById(e,a);if(!l||!l.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);let o=await fetch(`/api/leagues/${l.id}`,{method:"PATCH",headers:n,body:JSON.stringify(t)});if(!o.ok)throw Error((await o.json()).message||`Failed to update league ${e}`);return await o.json()},deleteLeague:async(e,t)=>{let a=await i.getLeagueById(e,t);if(!a||!a.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);await r.x.delete(`/football/leagues/${a.id}`)},getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async e=>i.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>i.updateLeague(e,{active:t},a)}},69142:(e,t,a)=>{a.d(t,{HK:()=>o,My:()=>d,sF:()=>l});var r=a(19738),s=a(11494),i=a(14373),n=a(59836);let l=(e={})=>{let t=(0,r.a)({queryKey:["leagues",e],queryFn:()=>n.A.getLeagues(e),staleTime:6e5});return{leagues:t.data?.data||[],leaguesMeta:t.data?.meta,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},o=(e,t)=>{let a=(0,r.a)({queryKey:["leagues",e,t],queryFn:()=>n.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},d=()=>{let e=(0,s.NL)(),t=(0,i.D)({mutationFn:e=>n.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,i.D)({mutationFn:({externalId:e,data:t,season:a})=>n.A.updateLeague(e,t,a),onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId,a.season],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,i.D)({mutationFn:({id:e,active:t})=>n.A.toggleLeagueStatus(e,t),onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isLoading,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isLoading,updateError:a.error,updateData:a.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},11723:(e,t,a)=>{function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${t}`}function s(e){return r(e)}function i(e){return r(e)}function n(e){return r(e)}a.d(t,{Bf:()=>s,Fc:()=>n,Sc:()=>r,ou:()=>i})},14217:(e,t,a)=>{a.d(t,{f:()=>l});var r=a(3729),s=a(62409),i=a(95344),n=r.forwardRef((e,t)=>(0,i.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},19655:(e,t,a)=>{a.d(t,{bU:()=>k,fC:()=>j});var r=a(3729),s=a(85222),i=a(31405),n=a(98462),l=a(33183),o=a(92062),d=a(63085),c=a(62409),u=a(95344),g="Switch",[h,p]=(0,n.b)(g),[y,x]=h(g),m=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:n,checked:o,defaultChecked:d,required:h,disabled:p,value:x="on",onCheckedChange:m,form:f,...v}=e,[j,k]=r.useState(null),L=(0,i.e)(t,e=>k(e)),C=r.useRef(!1),N=!j||f||!!j.closest("form"),[S,$]=(0,l.T)({prop:o,defaultProp:d??!1,onChange:m,caller:g});return(0,u.jsxs)(y,{scope:a,checked:S,disabled:p,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":h,"data-state":w(S),"data-disabled":p?"":void 0,disabled:p,value:x,...v,ref:L,onClick:(0,s.M)(e.onClick,e=>{$(e=>!e),N&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),N&&(0,u.jsx)(b,{control:j,bubbles:!C.current,name:n,value:x,checked:S,required:h,disabled:p,form:f,style:{transform:"translateX(-100%)"}})]})});m.displayName=g;var f="SwitchThumb",v=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=x(f,a);return(0,u.jsx)(c.WV.span,{"data-state":w(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});v.displayName=f;var b=r.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:s=!0,...n},l)=>{let c=r.useRef(null),g=(0,i.e)(c,l),h=(0,o.D)(a),p=(0,d.t)(t);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[h,a,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:g,style:{...n.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var j=m,k=v}};