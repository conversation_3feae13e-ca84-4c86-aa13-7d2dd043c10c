exports.id=7833,exports.ids=[7833],exports.modules={74627:(e,s,a)=>{Promise.resolve().then(a.bind(a,39372))},39372:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>O});var t=a(95344),r=a(3729),n=a(36487),i=a(98200),l=a(28765),d=a(33037),o=a(5094),c=a(46540),m=a(23485),h=a(18822),x=a(13746),u=a(48120),f=a(47585),p=a(14871),g=a(19591),j=a(48333),b=a(54074),N=a(52962);let y=()=>{var e,s;let{user:a,logout:r,isLogoutLoading:n}=(0,j.a)(),{refreshToken:i}=(0,b.t)();return a?(0,t.jsxs)(f.h_,{children:[t.jsx(f.$F,{asChild:!0,children:t.jsx(o.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:t.jsx(p.qE,{className:"h-8 w-8",children:t.jsx(p.Q5,{className:"text-xs",children:(e=a.fullName,s=a.username,e?e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):s?.slice(0,2).toUpperCase()||"U")})})})}),(0,t.jsxs)(f.AW,{className:"w-56",align:"end",forceMount:!0,children:[t.jsx(f.Ju,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("p",{className:"text-sm font-medium leading-none",children:a.fullName||a.username}),t.jsx(g.C,{className:`text-xs ${(e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"editor":return"bg-blue-100 text-blue-800";case"moderator":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(a.role)}`,children:(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"admin":return t.jsx(m.Z,{className:"h-3 w-3"});case"editor":default:return t.jsx(h.Z,{className:"h-3 w-3"});case"moderator":return t.jsx(x.Z,{className:"h-3 w-3"})}})(a.role),t.jsx("span",{children:a.role})]})})]}),t.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:a.email}),a.lastLoginAt&&(0,t.jsxs)("p",{className:"text-xs leading-none text-muted-foreground",children:["Last login: ",new Date(a.lastLoginAt).toLocaleDateString()]})]})}),t.jsx(f.VD,{}),(0,t.jsxs)(f.Xi,{className:"cursor-pointer",children:[t.jsx(h.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Profile"})]}),(0,t.jsxs)(f.Xi,{className:"cursor-pointer",children:[t.jsx(x.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Settings"})]}),t.jsx(f.VD,{}),t.jsx(f.Xi,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:()=>{if(console.log("\uD83D\uDD04 Logout clicked, refreshToken:",i?"Present":"Missing"),i)r(i);else{console.log("⚠️ No refresh token, forcing logout...");let{clearAuth:e}=b.t.getState();e(),window.location.href="/auth/login"}},disabled:n,children:n?(0,t.jsxs)(t.Fragment,{children:[t.jsx(N.TK,{size:"sm",className:"mr-2"}),t.jsx("span",{children:"Signing out..."})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Log out"})]})})]})]}):null};var v=a(8428),w=a(56506),Z=a(97751),k=a(89486),C=a(11453);let R={dashboard:"Dashboard",fixtures:"Fixtures",leagues:"Leagues",teams:"Teams",users:"Users",settings:"Settings",broadcast:"Broadcast Links",system:"System Users",registered:"Registered Users",tiers:"Tier Statistics",live:"Live & Upcoming",sync:"Data Sync",auth:"Authentication",login:"Login"},z=({items:e,className:s})=>{let a=(0,v.usePathname)(),r=e||function(e){let s=e.split("/").filter(Boolean),a=[];a.push({label:"Dashboard",href:"/dashboard",icon:k.Z});let t="";return s.forEach((e,r)=>{if(t+=`/${e}`,"dashboard"===e)return;let n=R[e]||e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),i=r===s.length-1;a.push({label:n,href:i?void 0:t})}),a}(a);return r.length<=1?null:t.jsx("nav",{className:(0,C.cn)("flex items-center space-x-1 text-sm text-gray-500",s),children:r.map((e,s)=>{let a=s===r.length-1,n=e.icon;return(0,t.jsxs)("div",{className:"flex items-center",children:[s>0&&t.jsx(Z.Z,{className:"h-4 w-4 mx-1 text-gray-400"}),a?(0,t.jsxs)("span",{className:"font-medium text-gray-900 flex items-center",children:[n&&t.jsx(n,{className:"h-4 w-4 mr-1"}),e.label]}):(0,t.jsxs)(w.default,{href:e.href||"#",className:"hover:text-gray-700 transition-colors flex items-center",children:[n&&t.jsx(n,{className:"h-4 w-4 mr-1"}),e.label]})]},s)})})};var S=a(98714),A=a(47180),D=a(73101),L=a(11503);let U=()=>{let{setTheme:e,theme:s}=(0,L.useTheme)();return(0,t.jsxs)(f.h_,{children:[t.jsx(f.$F,{asChild:!0,children:(0,t.jsxs)(o.z,{variant:"ghost",size:"sm",className:"h-8 w-8 px-0",children:[t.jsx(S.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),t.jsx(A.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),t.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,t.jsxs)(f.AW,{align:"end",children:[(0,t.jsxs)(f.Xi,{onClick:()=>e("light"),className:"light"===s?"bg-accent":"",children:[t.jsx(S.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Light"})]}),(0,t.jsxs)(f.Xi,{onClick:()=>e("dark"),className:"dark"===s?"bg-accent":"",children:[t.jsx(A.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Dark"})]}),(0,t.jsxs)(f.Xi,{onClick:()=>e("system"),className:"system"===s?"bg-accent":"",children:[t.jsx(D.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"System"})]})]})]})},F=({onMenuClick:e,showBreadcrumb:s=!0})=>(0,t.jsxs)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-30",children:[t.jsx("div",{className:"px-4 sm:px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("button",{className:"md:hidden p-2 hover:bg-gray-100 relative z-50 rounded-md transition-colors",onClick:e,"aria-label":"Open navigation menu",type:"button",children:t.jsx(i.Z,{className:"h-5 w-5"})}),t.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"APISportsGame CMS"})]}),t.jsx("div",{className:"hidden sm:flex flex-1 max-w-md mx-8",children:(0,t.jsxs)("div",{className:"relative w-full",children:[t.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),t.jsx(c.I,{type:"text",placeholder:"Search...",className:"pl-10 pr-4 w-full"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[t.jsx(o.z,{variant:"ghost",size:"sm",className:"sm:hidden",children:t.jsx(l.Z,{className:"h-5 w-5"})}),t.jsx(U,{}),(0,t.jsxs)(o.z,{variant:"ghost",size:"sm",className:"relative",children:[t.jsx(d.Z,{className:"h-5 w-5"}),t.jsx("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),t.jsx(y,{})]})]})}),s&&t.jsx("div",{className:"px-4 sm:px-6 py-2 bg-gray-50 border-t border-gray-100",children:t.jsx(z,{})})]});var T=a(2273),E=a(55794),q=a(81137),_=a(33733),P=a(30782),X=a(89895),M=a(15366),$=a(68219),V=a(25390),Y=a(14513);let I=[{title:"Dashboard",href:"/dashboard",icon:T.Z},{title:"Fixtures",icon:E.Z,children:[{title:"All Fixtures",href:"/dashboard/fixtures",icon:E.Z},{title:"Live & Upcoming",href:"/dashboard/fixtures/live",icon:q.Z},{title:"Sync Data",href:"/dashboard/fixtures/sync",icon:_.Z,requiredRole:"admin"}]},{title:"Leagues",href:"/dashboard/leagues",icon:P.Z},{title:"Teams",href:"/dashboard/teams",icon:X.Z},{title:"Players",href:"/dashboard/players",icon:M.Z},{title:"News",icon:q.Z,children:[{title:"All News",href:"/dashboard/news",icon:q.Z},{title:"Categories",href:"/dashboard/news/categories",icon:$.Z}]},{title:"Broadcast Links",href:"/dashboard/broadcast",icon:q.Z,requiredRole:"editor"},{title:"User Management",icon:M.Z,requiredRole:"admin",children:[{title:"System Users",href:"/dashboard/users/system",icon:M.Z},{title:"Registered Users",href:"/dashboard/users/registered",icon:X.Z},{title:"Tier Statistics",href:"/dashboard/users/tiers",icon:P.Z}]},{title:"Settings",href:"/dashboard/settings",icon:x.Z},{title:"API Test",href:"/dashboard/api-test",icon:_.Z,requiredRole:"admin"},{title:"Components Demo",href:"/dashboard/components-demo",icon:x.Z,requiredRole:"admin"},{title:"Test Token",href:"/dashboard/test-token",icon:_.Z,requiredRole:"admin"}],W=({isOpen:e=!1,onClose:s,className:a})=>{let i=(0,v.usePathname)(),{hasRole:l}=(0,n.TE)(),[d,c]=(0,r.useState)([]),[m,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{h(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let x=e=>{c(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},u=e=>i===e||i.startsWith(e+"/"),f=e=>!e.requiredRole||l(e.requiredRole),p=(e,s=0)=>{if(!f(e))return null;let a=e.children&&e.children.length>0,r=d.includes(e.title),n=e.icon;return a?(0,t.jsxs)("div",{children:[(0,t.jsxs)("button",{onClick:()=>x(e.title),className:(0,C.cn)("w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors","text-gray-700 hover:text-gray-900 hover:bg-gray-100",s>0&&"ml-4"),children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(n,{className:"mr-3 h-4 w-4"}),t.jsx("span",{children:e.title}),e.badge&&t.jsx(g.C,{variant:"secondary",className:"ml-2 text-xs",children:e.badge})]}),r?t.jsx(V.Z,{className:"h-4 w-4"}):t.jsx(Z.Z,{className:"h-4 w-4"})]}),r&&t.jsx("div",{className:"mt-1 space-y-1",children:e.children?.map(e=>p(e,s+1))})]},e.title):(0,t.jsxs)(w.default,{href:e.href,className:(0,C.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s>0&&"ml-4",u(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"),children:[t.jsx(n,{className:"mr-3 h-4 w-4"}),t.jsx("span",{children:e.title}),e.badge&&t.jsx(g.C,{variant:"secondary",className:"ml-2 text-xs",children:e.badge})]},e.title)};return m?(console.log("Rendering mobile sidebar, isOpen:",e),(0,t.jsxs)(t.Fragment,{children:[e&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:s,"aria-hidden":"true"}),(0,t.jsxs)("aside",{className:(0,C.cn)("fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out md:hidden",e?"translate-x-0":"-translate-x-full",a),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[t.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Menu"}),t.jsx(o.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:t.jsx(Y.Z,{className:"h-4 w-4"})})]}),t.jsx("nav",{className:"p-4 space-y-2 overflow-y-auto",style:{height:"calc(100vh - 73px)"},children:I.map(e=>p(e))})]})]})):t.jsx("aside",{className:(0,C.cn)("hidden md:block w-64 bg-white border-r border-gray-200 min-h-screen fixed left-0 z-20","top-0 pt-28 shadow-sm",a),children:t.jsx("nav",{className:"p-4 space-y-2 overflow-y-auto",style:{height:"calc(100vh - 7rem)"},children:I.map(e=>p(e))})})};function O({children:e}){let[s,a]=(0,r.useState)(!1);return t.jsx(n.a1,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t.jsx(F,{onMenuClick:()=>{a(!0)}}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(W,{isOpen:s,onClose:()=>{a(!1)}}),t.jsx("main",{className:"p-4 sm:p-6 md:ml-64 min-h-screen transition-all duration-300",children:e})]})]})})}},14871:(e,s,a)=>{"use strict";a.d(s,{F$:()=>d,Q5:()=>o,qE:()=>l});var t=a(95344),r=a(3729),n=a(15480),i=a(11453);let l=r.forwardRef(({className:e,...s},a)=>t.jsx(n.fC,{ref:a,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...s}));l.displayName=n.fC.displayName;let d=r.forwardRef(({className:e,...s},a)=>t.jsx(n.Ee,{ref:a,className:(0,i.cn)("aspect-square h-full w-full",e),...s}));d.displayName=n.Ee.displayName;let o=r.forwardRef(({className:e,...s},a)=>t.jsx(n.NY,{ref:a,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...s}));o.displayName=n.NY.displayName},19591:(e,s,a)=>{"use strict";a.d(s,{C:()=>l});var t=a(95344);a(3729);var r=a(49247),n=a(11453);let i=(0,r.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...a}){return t.jsx("div",{className:(0,n.cn)(i({variant:s}),e),...a})}},47585:(e,s,a)=>{"use strict";a.d(s,{$F:()=>m,AW:()=>h,Ju:()=>u,VD:()=>f,Xi:()=>x,h_:()=>c});var t=a(95344),r=a(3729),n=a(28473),i=a(97751),l=a(62312),d=a(82958),o=a(11453);let c=n.fC,m=n.xz;n.ZA,n.Uv,n.Tr,n.Ee,r.forwardRef(({className:e,inset:s,children:a,...r},l)=>(0,t.jsxs)(n.fF,{ref:l,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",e),...r,children:[a,t.jsx(i.Z,{className:"ml-auto"})]})).displayName=n.fF.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(n.tu,{ref:a,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...s})).displayName=n.tu.displayName;let h=r.forwardRef(({className:e,sideOffset:s=4,...a},r)=>t.jsx(n.Uv,{children:t.jsx(n.VY,{ref:r,sideOffset:s,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...a})}));h.displayName=n.VY.displayName;let x=r.forwardRef(({className:e,inset:s,...a},r)=>t.jsx(n.ck,{ref:r,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",s&&"pl-8",e),...a}));x.displayName=n.ck.displayName,r.forwardRef(({className:e,children:s,checked:a,...r},i)=>(0,t.jsxs)(n.oC,{ref:i,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...r,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(n.wU,{children:t.jsx(l.Z,{className:"h-4 w-4"})})}),s]})).displayName=n.oC.displayName,r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(n.Rk,{ref:r,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(n.wU,{children:t.jsx(d.Z,{className:"h-2 w-2 fill-current"})})}),s]})).displayName=n.Rk.displayName;let u=r.forwardRef(({className:e,inset:s,...a},r)=>t.jsx(n.__,{ref:r,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",e),...a}));u.displayName=n.__.displayName;let f=r.forwardRef(({className:e,...s},a)=>t.jsx(n.Z0,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s}));f.displayName=n.Z0.displayName},46540:(e,s,a)=>{"use strict";a.d(s,{I:()=>i});var t=a(95344),r=a(3729),n=a(11453);let i=r.forwardRef(({className:e,type:s,...a},r)=>t.jsx("input",{type:s,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));i.displayName="Input"},36487:(e,s,a)=>{"use strict";a.d(s,{TE:()=>o,a1:()=>d});var t=a(95344),r=a(3729),n=a(8428),i=a(48333),l=a(52962);let d=({children:e,requiredRole:s,fallbackUrl:a="/auth/login"})=>{let d=(0,n.useRouter)(),{isAuthenticated:o,user:c,isLoading:m}=(0,i.a)();if((0,r.useEffect)(()=>{if(!m){if(!o||!c){d.push(a);return}if(s&&!(Array.isArray(s)?s:[s]).includes(c.role)){d.push("/dashboard?error=unauthorized");return}}},[o,c,m,s,d,a]),m)return t.jsx(l.SX,{message:"Verifying authentication..."});if(!o||!c)return t.jsx(l.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(c.role))return t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",c.role]})]})})}return t.jsx(t.Fragment,{children:e})},o=()=>{let{user:e}=(0,i.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),a=()=>s("admin"),t=()=>s(["admin","editor"]),r=()=>s(["admin","editor","moderator"]),n=()=>a(),l=()=>t(),d=()=>r(),o=()=>a();return{user:e,hasRole:s,isAdmin:a,isEditor:t,isModerator:r,canManageUsers:n,canManageContent:l,canModerate:d,canSync:o,can:e=>{switch(e){case"manage-users":return n();case"manage-content":return l();case"moderate":return d();case"sync":return o();default:return!1}}}}},94699:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx`),{__esModule:r,$$typeof:n}=t,i=t.default}};