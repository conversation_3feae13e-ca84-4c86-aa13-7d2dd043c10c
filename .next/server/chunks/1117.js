exports.id=1117,exports.ids=[1117],exports.modules={41272:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},45613:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},77094:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},20862:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},25545:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},34826:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},53148:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37121:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8561:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},6911:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},15600:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},20439:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},70009:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},58968:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},39272:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},30473:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},65554:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},93772:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("subscript",[["path",{d:"m4 5 8 8",key:"1eunvl"}],["path",{d:"m12 5-8 8",key:"1ah0jp"}],["path",{d:"M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07",key:"e8ta8j"}]])},51360:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("superscript",[["path",{d:"m4 19 8-8",key:"hr47gm"}],["path",{d:"m12 19-8-8",key:"1dhhmo"}],["path",{d:"M20 12h-4c0-1.5.442-2 1.5-2.5S20 8.334 20 7.002c0-.472-.17-.93-.484-1.29a2.105 2.105 0 0 0-2.617-.436c-.42.239-.738.614-.899 1.06",key:"1dfcux"}]])},36341:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},82424:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},39669:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]])},94730:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97075).Z)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},29408:()=>{},18630:(e,t,r)=>{"use strict";r(29408);var s=r(3729),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s);function n(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}var l="undefined"!=typeof process&&process.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,a=t.optimizeForSpeed,n=void 0===a?l:a;u(i(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",u("boolean"==typeof n,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=n,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;u(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return u(i(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){l||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&u(i(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var a=document.head||document.getElementsByTagName("head")[0];return r?a.insertBefore(s,r):a.appendChild(s),s},n(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t&&n(e,t),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},o={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return o[s]||(o[s]="jsx-"+d(e+"-"+r)),o[s]}function p(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return o[r]||(o[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),o[r]}var y=s.createContext(null);y.displayName="StyleSheetContext",a.default.useInsertionEffect||a.default.useLayoutEffect;var k=void 0;function f(e){var t=k||s.useContext(y);return t&&t.add(e),null}f.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=f},47983:(e,t,r)=>{"use strict";e.exports=r(18630).style},19655:(e,t,r)=>{"use strict";r.d(t,{bU:()=>S,fC:()=>Z});var s=r(3729),a=r(85222),n=r(31405),l=r(98462),i=r(33183),c=r(92062),u=r(63085),d=r(62409),o=r(95344),h="Switch",[p,y]=(0,l.b)(h),[k,f]=p(h),v=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:c,defaultChecked:u,required:p,disabled:y,value:f="on",onCheckedChange:v,form:m,...M}=e,[Z,S]=s.useState(null),b=(0,n.e)(t,e=>S(e)),_=s.useRef(!1),j=!Z||m||!!Z.closest("form"),[R,z]=(0,i.T)({prop:c,defaultProp:u??!1,onChange:v,caller:h});return(0,o.jsxs)(k,{scope:r,checked:R,disabled:y,children:[(0,o.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":R,"aria-required":p,"data-state":x(R),"data-disabled":y?"":void 0,disabled:y,value:f,...M,ref:b,onClick:(0,a.M)(e.onClick,e=>{z(e=>!e),j&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})}),j&&(0,o.jsx)(g,{control:Z,bubbles:!_.current,name:l,value:f,checked:R,required:p,disabled:y,form:m,style:{transform:"translateX(-100%)"}})]})});v.displayName=h;var m="SwitchThumb",M=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=f(m,r);return(0,o.jsx)(d.WV.span,{"data-state":x(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});M.displayName=m;var g=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:a=!0,...l},i)=>{let d=s.useRef(null),h=(0,n.e)(d,i),p=(0,c.D)(r),y=(0,u.t)(t);return s.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let s=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(s)}},[p,r,a]),(0,o.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...l,tabIndex:-1,ref:h,style:{...l.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var Z=v,S=M},92062:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var s=r(3729);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};