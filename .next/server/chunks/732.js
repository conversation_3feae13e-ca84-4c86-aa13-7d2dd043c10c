"use strict";exports.id=732,exports.ids=[732],exports.modules={33037:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},55794:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},97751:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},68219:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},89486:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},2273:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},48120:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},98200:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},73101:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},47180:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},81137:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("radio",[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]])},28765:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},13746:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},23485:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},98714:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},30782:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},15366:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},18822:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},89895:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},14513:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97075).Z)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},56506:(e,t,r)=>{r.d(t,{default:()=>o.a});var n=r(61476),o=r.n(n)},41314:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(19847);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73055:(e,t,r)=>{function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(19847),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return v}});let n=r(39694),o=r(95344),a=n._(r(3729)),l=r(26656),u=r(76737),i=r(92421),s=r(10853),c=r(41314),d=r(66150),f=r(46860),p=r(3470),h=r(73055),m=r(88928),g=r(8085);function y(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}let v=a.default.forwardRef(function(e,t){let r,n;let{href:i,as:v,children:b,prefetch:w=null,passHref:x,replace:M,shallow:k,scroll:j,locale:R,onClick:_,onMouseEnter:C,onTouchStart:P,legacyBehavior:O=!1,...E}=e;r=b,O&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let S=a.default.useContext(d.RouterContext),N=a.default.useContext(f.AppRouterContext),I=null!=S?S:N,D=!S,T=!1!==w,L=null===w?g.PrefetchKind.AUTO:g.PrefetchKind.FULL,{href:A,as:Z}=a.default.useMemo(()=>{if(!S){let e=y(i);return{href:e,as:v?y(v):e}}let[e,t]=(0,l.resolveHref)(S,i,!0);return{href:e,as:v?(0,l.resolveHref)(S,v):t||e}},[S,i,v]),W=a.default.useRef(A),U=a.default.useRef(Z);O&&(n=a.default.Children.only(r));let F=O?n&&"object"==typeof n&&n.ref:t,[K,V,z]=(0,p.useIntersection)({rootMargin:"200px"}),q=a.default.useCallback(e=>{(U.current!==Z||W.current!==A)&&(z(),U.current=Z,W.current=A),K(e),F&&("function"==typeof F?F(e):"object"==typeof F&&(F.current=e))},[Z,F,A,z,K]);a.default.useEffect(()=>{},[Z,A,V,R,T,null==S?void 0:S.locale,I,D,L]);let H={ref:q,onClick(e){O||"function"!=typeof _||_(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),I&&!e.defaultPrevented&&function(e,t,r,n,o,l,i,s,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,u.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==i||i;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:l,locale:s,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};c?a.default.startTransition(f):f()}(e,I,A,Z,M,k,j,R,D)},onMouseEnter(e){O||"function"!=typeof C||C(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart(e){O||"function"!=typeof P||P(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,s.isAbsoluteUrl)(Z))H.href=Z;else if(!O||x||"a"===n.type&&!("href"in n.props)){let e=void 0!==R?R:null==S?void 0:S.locale,t=(null==S?void 0:S.isLocaleDomain)&&(0,h.getDomainLocale)(Z,e,null==S?void 0:S.locales,null==S?void 0:S.domainLocales);H.href=t||(0,m.addBasePath)((0,c.addLocale)(Z,e,null==S?void 0:S.defaultLocale))}return O?a.default.cloneElement(n,H):(0,o.jsx)("a",{...E,...H,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66252:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{requestIdleCallback:function(){return r},cancelIdleCallback:function(){return n}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26656:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(77043),o=r(92421),a=r(60663),l=r(10853),u=r(19847),i=r(76737),s=r(44831),c=r(78729);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,i.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,u.normalizePathTrailingSlash)(e.pathname);let t="";if((0,s.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:l,params:u}=(0,c.interpolateAs)(e.pathname,e.pathname,r);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,a.omit)(r,u)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[l,t||l]:l}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3470:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return i}});let n=r(3729),o=r(66252),a="function"==typeof IntersectionObserver,l=new Map,u=[];function i(e){let{rootRef:t,rootMargin:r,disabled:i}=e,s=i||!a,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(a){if(s||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:a}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=u.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=l.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},u.push(r),l.set(r,t),t}(r);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(n);let e=u.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&u.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66150:(e,t,r)=>{e.exports=r(16372).vendored.contexts.RouterContext},77866:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},92421:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},urlObjectKeys:function(){return l},formatWithValidation:function(){return u}});let n=r(17824)._(r(77043)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",u=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return a(e)}},44831:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(46177),o=r(25508)},78729:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(82694),o=r(76603);function a(e,t,r){let a="",l=(0,o.getRouteRegex)(e),u=l.groups,i=(t!==e?(0,n.getRouteMatcher)(l)(t):"")||r;a=e;let s=Object.keys(u);return s.every(e=>{let t=i[e]||"",{repeat:r,optional:n}=u[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in i)&&(a=a.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:s,result:a}}},25508:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a}});let n=r(45767),o=/\/\[[^/]+?\](?=\/|$)/;function a(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},76737:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(10853),o=r(96411);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},60663:(e,t)=>{function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},77043:(e,t)=>{function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o},assign:function(){return a}})},82694:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(10853);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},l={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(l[e]=~n.indexOf("/")?n.split("/").map(e=>a(e)):t.repeat?[a(n)]:a(n))}),l}}},76603:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRouteRegex:function(){return i},getNamedRouteRegex:function(){return d},getNamedMiddlewareRegex:function(){return f}});let n=r(45767),o=r(77866),a=r(74310);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},u=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:n,repeat:i}=l(a[1]);return r[e]={pos:u++,repeat:i,optional:n},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=l(a[1]);return r[e]={pos:u++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function i(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function s(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:u}=e,{key:i,optional:s,repeat:c}=l(n),d=i.replace(/\W/g,"");u&&(d=""+u+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),u?a[d]=""+u+i:a[d]=i;let p=t?(0,o.escapeStringRegexp)(t):"";return c?s?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function c(e,t){let r;let l=(0,a.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),i={};return{namedParameterizedRoute:l.map(e=>{let r=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return s({getSafeRouteKey:u,interceptionMarker:r,segment:a[1],routeKeys:i,keyPrefix:t?"nxtI":void 0})}return a?s({getSafeRouteKey:u,segment:a[1],routeKeys:i,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:i}}function d(e,t){let r=c(e,t);return{...i(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=c(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},46177:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),l=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),l=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function a(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');a(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');a(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},10853:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{WEB_VITALS:function(){return r},execOnce:function(){return n},isAbsoluteUrl:function(){return a},getLocationOrigin:function(){return l},getURL:function(){return u},getDisplayName:function(){return i},isResSent:function(){return s},normalizeRepeatedSlashes:function(){return c},loadGetInitialProps:function(){return d},SP:function(){return f},ST:function(){return p},DecodeError:function(){return h},NormalizeError:function(){return m},PageNotFoundError:function(){return g},MissingStaticPage:function(){return y},MiddlewareNotFoundError:function(){return v},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=l();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},15480:(e,t,r)=>{r.d(t,{NY:()=>j,Ee:()=>k,fC:()=>M});var n=r(3729),o=r(98462),a=r(2256),l=r(16069),u=r(62409),i=r(8145);function s(){return()=>{}}var c=r(95344),d="Avatar",[f,p]=(0,o.b)(d),[h,m]=f(d),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,c.jsx)(h,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,c.jsx)(u.WV.span,{...o,ref:t})})});g.displayName=d;var y="AvatarImage",v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(y,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,i.useSyncExternalStore)(s,()=>!0,()=>!1),a=n.useRef(null),u=o?(a.current||(a.current=new window.Image),a.current):null,[c,d]=n.useState(()=>x(u,e));return(0,l.b)(()=>{d(x(u,e))},[u,e]),(0,l.b)(()=>{let e=e=>()=>{d(e)};if(!u)return;let n=e("loaded"),o=e("error");return u.addEventListener("load",n),u.addEventListener("error",o),t&&(u.referrerPolicy=t),"string"==typeof r&&(u.crossOrigin=r),()=>{u.removeEventListener("load",n),u.removeEventListener("error",o)}},[u,r,t]),c}(o,f),g=(0,a.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==h&&g(h)},[h,g]),"loaded"===h?(0,c.jsx)(u.WV.img,{...f,ref:t,src:o}):null});v.displayName=y;var b="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=m(b,r),[i,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),i&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(u.WV.span,{...a,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var M=g,k=v,j=w},28473:(e,t,r)=>{r.d(t,{oC:()=>e3,VY:()=>e7,ZA:()=>e4,ck:()=>e6,wU:()=>te,__:()=>e5,Uv:()=>e2,Ee:()=>e9,Rk:()=>e8,fC:()=>e0,Z0:()=>tt,Tr:()=>tr,tu:()=>to,fF:()=>tn,xz:()=>e1});var n=r(3729),o=r(85222),a=r(31405),l=r(98462),u=r(33183),i=r(62409),s=r(77411),c=r(3975),d=r(44155),f=r(1106),p=r(27386),h=r(99048),m=r(37574),g=r(31179),y=r(43234),v=r(34504),b=r(32751),w=r(2256),x=r(45904),M=r(71210),k=r(95344),j=["Enter"," "],R=["ArrowUp","PageDown","End"],_=["ArrowDown","PageUp","Home",...R],C={ltr:[...j,"ArrowRight"],rtl:[...j,"ArrowLeft"]},P={ltr:["ArrowLeft"],rtl:["ArrowRight"]},O="Menu",[E,S,N]=(0,s.B)(O),[I,D]=(0,l.b)(O,[N,m.D7,v.Pc]),T=(0,m.D7)(),L=(0,v.Pc)(),[A,Z]=I(O),[W,U]=I(O),F=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=T(t),[s,d]=n.useState(null),f=n.useRef(!1),p=(0,w.W)(l),h=(0,c.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,k.jsx)(m.fC,{...i,children:(0,k.jsx)(A,{scope:t,open:r,onOpenChange:p,content:s,onContentChange:d,children:(0,k.jsx)(W,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:u,children:o})})})};F.displayName=O;var K=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=T(r);return(0,k.jsx)(m.ee,{...o,...n,ref:t})});K.displayName="MenuAnchor";var V="MenuPortal",[z,q]=I(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=Z(V,t);return(0,k.jsx)(z,{scope:t,forceMount:r,children:(0,k.jsx)(y.z,{present:r||a.open,children:(0,k.jsx)(g.h,{asChild:!0,container:o,children:n})})})};H.displayName=V;var B="MenuContent",[Y,G]=I(B),$=n.forwardRef((e,t)=>{let r=q(B,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=Z(B,e.__scopeMenu),l=U(B,e.__scopeMenu);return(0,k.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(y.z,{present:n||a.open,children:(0,k.jsx)(E.Slot,{scope:e.__scopeMenu,children:l.modal?(0,k.jsx)(X,{...o,ref:t}):(0,k.jsx)(Q,{...o,ref:t})})})})}),X=n.forwardRef((e,t)=>{let r=Z(B,e.__scopeMenu),l=n.useRef(null),u=(0,a.e)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,x.Ry)(e)},[]),(0,k.jsx)(ee,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=Z(B,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.Z8)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:w,onDismiss:x,disableOutsideScroll:j,...C}=e,P=Z(B,r),O=U(B,r),E=T(r),N=L(r),I=S(r),[D,A]=n.useState(null),W=n.useRef(null),F=(0,a.e)(t,W,P.onContentChange),K=n.useRef(0),V=n.useRef(""),z=n.useRef(0),q=n.useRef(null),H=n.useRef("right"),G=n.useRef(0),$=j?M.Z:n.Fragment,X=e=>{let t=V.current+e,r=I().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===a)?.ref.current;(function e(t){V.current=t,window.clearTimeout(K.current),""!==t&&(K.current=window.setTimeout(()=>e(""),1e3))})(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(K.current),[]),(0,f.EW)();let Q=n.useCallback(e=>H.current===q.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],u=t[a],i=l.x,s=l.y,c=u.x,d=u.y;s>n!=d>n&&r<(c-i)*(n-s)/(d-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,q.current?.area),[]);return(0,k.jsx)(Y,{scope:r,searchRef:V,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(W.current?.focus(),A(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:z,onPointerGraceIntentChange:n.useCallback(e=>{q.current=e},[]),children:(0,k.jsx)($,{...j?{as:J,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.M)(i,e=>{e.preventDefault(),W.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,k.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:w,onDismiss:x,children:(0,k.jsx)(v.fC,{asChild:!0,...N,dir:O.dir,orientation:"vertical",loop:l,currentTabStopId:D,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.M)(h,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eP(P.open),"data-radix-menu-content":"",dir:O.dir,...E,...C,ref:F,style:{outline:"none",...C.style},onKeyDown:(0,o.M)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&X(e.key));let o=W.current;if(e.target!==o||!_.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(K.current),V.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eS(e=>{let t=e.target,r=G.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>G.current?"right":"left";H.current=t,G.current=e.clientX}}))})})})})})})});$.displayName=B;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(i.WV.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(i.WV.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...u}=e,s=n.useRef(null),c=U(en,e.__scopeMenu),d=G(en,e.__scopeMenu),f=(0,a.e)(t,s),p=n.useRef(!1);return(0,k.jsx)(el,{...u,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>l?.(e),{once:!0}),(0,i.jH)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&j.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var el=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:u,...s}=e,c=G(en,r),d=L(r),f=n.useRef(null),p=(0,a.e)(t,f),[h,m]=n.useState(!1),[g,y]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&y((e.textContent??"").trim())},[s.children]),(0,k.jsx)(E.ItemSlot,{scope:r,disabled:l,textValue:u??g,children:(0,k.jsx)(v.ck,{asChild:!0,...d,focusable:!l,children:(0,k.jsx)(i.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eS(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eS(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),eu=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eO(r)?"mixed":r,...a,ref:t,"data-state":eE(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eO(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ec]=I(ei,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,w.W)(n);return(0,k.jsx)(es,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,k.jsx)(et,{...o,ref:t})})});ed.displayName=ei;var ef="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ec(ef,e.__scopeMenu),l=r===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eE(l),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[em,eg]=I(eh,{checked:!1}),ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eg(eh,r);return(0,k.jsx)(y.z,{present:n||eO(a.checked)||!0===a.checked,children:(0,k.jsx)(i.WV.span,{...o,ref:t,"data-state":eE(a.checked)})})});ey.displayName=eh;var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(i.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ev.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=T(r);return(0,k.jsx)(m.Eh,{...o,...n,ref:t})});eb.displayName="MenuArrow";var ew="MenuSub",[ex,eM]=I(ew),ek=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,l=Z(ew,t),u=T(t),[i,s]=n.useState(null),[c,d]=n.useState(null),f=(0,w.W)(a);return n.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,k.jsx)(m.fC,{...u,children:(0,k.jsx)(A,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,k.jsx)(ex,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:i,onTriggerChange:s,children:r})})})};ek.displayName=ew;var ej="MenuSubTrigger",eR=n.forwardRef((e,t)=>{let r=Z(ej,e.__scopeMenu),l=U(ej,e.__scopeMenu),u=eM(ej,e.__scopeMenu),i=G(ej,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,k.jsx)(K,{asChild:!0,...f,children:(0,k.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":u.contentId,"data-state":eP(r.open),...e,ref:(0,a.F)(t,u.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eS(t=>{i.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eS(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],l=t[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==i.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&C[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eR.displayName=ej;var e_="MenuSubContent",eC=n.forwardRef((e,t)=>{let r=q(B,e.__scopeMenu),{forceMount:l=r.forceMount,...u}=e,i=Z(B,e.__scopeMenu),s=U(B,e.__scopeMenu),c=eM(e_,e.__scopeMenu),d=n.useRef(null),f=(0,a.e)(t,d);return(0,k.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(y.z,{present:l||i.open,children:(0,k.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...u,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=P[s.dir].includes(e.key);t&&r&&(i.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eP(e){return e?"open":"closed"}function eO(e){return"indeterminate"===e}function eE(e){return eO(e)?"indeterminate":e?"checked":"unchecked"}function eS(e){return t=>"mouse"===t.pointerType?e(t):void 0}eC.displayName=e_;var eN="DropdownMenu",[eI,eD]=(0,l.b)(eN,[D]),eT=D(),[eL,eA]=eI(eN),eZ=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=eT(t),d=n.useRef(null),[f,p]=(0,u.T)({prop:a,defaultProp:l??!1,onChange:i,caller:eN});return(0,k.jsx)(eL,{scope:t,triggerId:(0,h.M)(),triggerRef:d,contentId:(0,h.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,k.jsx)(F,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eZ.displayName=eN;var eW="DropdownMenuTrigger",eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,u=eA(eW,r),s=eT(r);return(0,k.jsx)(K,{asChild:!0,...s,children:(0,k.jsx)(i.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.F)(t,u.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eW;var eF=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eT(t);return(0,k.jsx)(H,{...n,...r})};eF.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eA(eK,r),u=eT(r),i=n.useRef(!1);return(0,k.jsx)($,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{i.current||l.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eK;var ez=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(et,{...o,...n,ref:t})});ez.displayName="DropdownMenuGroup";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(er,{...o,...n,ref:t})});eq.displayName="DropdownMenuLabel";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ea,{...o,...n,ref:t})});eH.displayName="DropdownMenuItem";var eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eu,{...o,...n,ref:t})});eB.displayName="DropdownMenuCheckboxItem";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ed,{...o,...n,ref:t})});eY.displayName="DropdownMenuRadioGroup";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ep,{...o,...n,ref:t})});eG.displayName="DropdownMenuRadioItem";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ey,{...o,...n,ref:t})});e$.displayName="DropdownMenuItemIndicator";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ev,{...o,...n,ref:t})});eX.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eb,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eR,{...o,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eC,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var e0=eZ,e1=eU,e2=eF,e7=eV,e4=ez,e5=eq,e6=eH,e3=eB,e9=eY,e8=eG,te=e$,tt=eX,tr=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,l=eT(t),[i,s]=(0,u.T)({prop:n,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,k.jsx)(ek,{...l,open:i,onOpenChange:s,children:r})},tn=eQ,to=eJ}};