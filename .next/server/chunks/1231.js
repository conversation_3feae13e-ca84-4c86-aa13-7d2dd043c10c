"use strict";exports.id=1231,exports.ids=[1231],exports.modules={47210:(e,s,r)=>{r.d(s,{E:()=>n});var t=r(95344),a=r(3729),i=r(11453);let n=a.forwardRef(({className:e,value:s=0,max:r=100,showValue:a=!1,size:n="md",variant:u="default",...o},c)=>{let l=Math.min(Math.max(s/r*100,0),100);return(0,t.jsxs)("div",{ref:c,className:(0,i.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[n],e),...o,children:[t.jsx("div",{className:(0,i.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[u]),style:{width:`${l}%`}}),a&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(l),"%"]})})]})});n.displayName="Progress"},54578:(e,s,r)=>{r.d(s,{Il:()=>d,Xn:()=>y,Ow:()=>c,ie:()=>g,zL:()=>l});var t=r(19738),a=r(11494),i=r(14373),n=r(50053);let u={register:async e=>await n.x.post("/users/register",e),login:async e=>await n.x.post("/users/login",e),verifyEmail:async e=>await n.x.post("/users/verify-email",{token:e}),resendVerification:async e=>await n.x.post("/users/resend-verification",{email:e}),forgotPassword:async e=>await n.x.post("/users/forgot-password",{email:e}),resetPassword:async(e,s)=>await n.x.post("/users/reset-password",{token:e,newPassword:s}),getProfile:async()=>await n.x.get("/users/profile"),updateProfile:async e=>await n.x.put("/users/profile",e),changePassword:async e=>await n.x.post("/users/change-password",e),getApiUsage:async()=>await n.x.get("/users/api-usage"),getAllUsers:async(e={})=>{let s=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{void 0!==r&&s.append(e,r.toString())}),await n.x.get(`/admin/users?${s.toString()}`)},getTierStatistics:async()=>await n.x.get("/admin/tiers/statistics"),getUsersApproachingLimits:async(e=80)=>await n.x.get(`/admin/users/approaching-limits?threshold=${e}`),upgradeTier:async(e,s,r)=>{let t={newTier:s};return r&&(t.subscriptionMonths=r),await n.x.post(`/admin/users/${e}/upgrade-tier`,t)},downgradeTier:async(e,s)=>await n.x.post(`/admin/users/${e}/downgrade-tier`,{newTier:s}),extendSubscription:async(e,s)=>await n.x.post(`/admin/users/${e}/extend-subscription`,{additionalMonths:s}),getSubscriptionInfo:async e=>await n.x.get(`/admin/users/${e}/subscription`),resetMonthlyApiUsage:async()=>await n.x.post("/admin/reset-api-usage"),checkApiUsageWarnings:async()=>await n.x.post("/admin/check-usage-warnings")};var o=r(34755);let c=(e={})=>(0,t.a)({queryKey:["registered-users",e],queryFn:()=>u.getAllUsers(e),staleTime:3e5}),l=(e=80)=>(0,t.a)({queryKey:["users-approaching-limits",e],queryFn:()=>u.getUsersApproachingLimits(e),staleTime:9e5}),d=()=>{let e=(0,a.NL)(),s=(0,i.D)({mutationFn:({userId:e,newTier:s,subscriptionMonths:r})=>u.upgradeTier(e,s,r),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r.userId]}),e.invalidateQueries({queryKey:["tier-statistics"]}),o.toast.success(`User upgraded to ${r.newTier} tier successfully`)},onError:e=>{o.toast.error(`Failed to upgrade user: ${e.message}`)}}),r=(0,i.D)({mutationFn:({userId:e,newTier:s})=>u.downgradeTier(e,s),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r.userId]}),e.invalidateQueries({queryKey:["tier-statistics"]}),o.toast.success(`User downgraded to ${r.newTier} tier successfully`)},onError:e=>{o.toast.error(`Failed to downgrade user: ${e.message}`)}}),t=(0,i.D)({mutationFn:({userId:e,additionalMonths:s})=>u.extendSubscription(e,s),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-user",r.userId]}),e.invalidateQueries({queryKey:["user-subscription",r.userId]}),o.toast.success(`Subscription extended by ${r.additionalMonths} months`)},onError:e=>{o.toast.error(`Failed to extend subscription: ${e.message}`)}});return{upgradeTier:s,downgradeTier:r,extendSubscription:t,resetApiUsage:(0,i.D)({mutationFn:u.resetMonthlyApiUsage,onSuccess:()=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["users-approaching-limits"]}),o.toast.success("Monthly API usage reset successfully")},onError:e=>{o.toast.error(`Failed to reset API usage: ${e.message}`)}}),suspendUser:(0,i.D)({mutationFn:async e=>(console.log("Suspending user:",e),{message:"User suspended successfully"}),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),o.toast.success("User suspended successfully")},onError:e=>{o.toast.error(`Failed to suspend user: ${e.message}`)}}),reactivateUser:(0,i.D)({mutationFn:async e=>(console.log("Reactivating user:",e),{message:"User reactivated successfully"}),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),o.toast.success("User reactivated successfully")},onError:e=>{o.toast.error(`Failed to reactivate user: ${e.message}`)}})}},g=()=>(0,t.a)({queryKey:["user-analytics"],queryFn:async()=>({totalUsers:1247,activeUsers:1156,newUsersThisMonth:45,tierDistribution:{free:856,premium:312,enterprise:79},monthlyRevenue:34567,apiCallsThisMonth:2847392,apiUsageStats:{totalCallsThisMonth:2847392,averageCallsPerUser:188,peakUsageDay:"2024-01-15",topUsers:[{id:1,username:"api_user_premium_1",calls:9567},{id:2,username:"enterprise_client",calls:8234},{id:3,username:"mobile_app_backend",calls:7456}]},revenueMetrics:{monthlyRecurringRevenue:34567,averageRevenuePerUser:11.67,lifetimeValue:186.45}}),staleTime:3e5}),y={useList:c,useGetById:e=>(0,t.a)({queryKey:["registered-user",e],queryFn:async()=>({id:e,name:"John Doe",email:"<EMAIL>",company:"TechCorp Inc.",phone:"+****************",website:"https://techcorp.com",tier:"premium",status:"active",emailVerified:!0,apiCallsUsed:7250,apiCallsLimit:1e4,monthlySpend:29,avatar:"",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-12-15T14:22:00Z",lastLogin:"2024-12-20T09:15:00Z",lastApiCall:"2024-12-22T16:45:00Z",subscriptionStartDate:"2024-01-15T10:30:00Z",subscriptionEndDate:"2025-01-15T10:30:00Z",notes:"Premium customer with good API usage patterns"}),enabled:!!e,staleTime:3e5}),useUpdate:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Updating user:",e),{...e,updatedAt:new Date().toISOString()}),onSuccess:s=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",s.id]})}})},useDelete:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["registered-users"]})}})},useSuspend:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Suspending user:",e),{message:"User suspended successfully"}),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]})}})},useReactivate:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Reactivating user:",e),{message:"User reactivated successfully"}),onSuccess:(s,r)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]})}})},useUpgradeTier:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async({userId:e,newTier:s})=>(console.log("Upgrading user tier:",e,s),{message:`User upgraded to ${s} tier successfully`}),onSuccess:(s,{userId:r})=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),e.invalidateQueries({queryKey:["tier-stats"]})}})},useDowngradeTier:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async({userId:e,newTier:s})=>(console.log("Downgrading user tier:",e,s),{message:`User downgraded to ${s} tier successfully`}),onSuccess:(s,{userId:r})=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),e.invalidateQueries({queryKey:["tier-stats"]})}})},useUsageStats:e=>(0,t.a)({queryKey:["usage-stats",e],queryFn:async()=>({today:125,thisWeek:890,thisMonth:3420,total:45600}),enabled:!!e,staleTime:3e5}),useApiCalls:e=>(0,t.a)({queryKey:["api-calls",e],queryFn:async()=>[{id:"1",endpoint:"/api/fixtures",status:200,responseTime:145,timestamp:"2024-12-22T16:45:00Z"},{id:"2",endpoint:"/api/teams",status:200,responseTime:98,timestamp:"2024-12-22T16:30:00Z"},{id:"3",endpoint:"/api/leagues",status:429,responseTime:50,timestamp:"2024-12-22T16:15:00Z"}],enabled:!!e,staleTime:6e4}),useTierStats:(e="30d")=>(0,t.a)({queryKey:["tier-stats",e],queryFn:async()=>({totalUsers:1247,totalUsersChange:8.5,conversionRate:15.2,conversionRateChange:2.1,tierDistribution:[{tier:"free",count:856,percentage:68.6,monthlyGrowth:5.2,avgApiUsage:650},{tier:"premium",count:312,percentage:25,monthlyGrowth:12.8,avgApiUsage:7500},{tier:"enterprise",count:79,percentage:6.3,monthlyGrowth:18.5,avgApiUsage:45e3}],growthTrends:[{tier:"free",change:5.2},{tier:"premium",change:12.8},{tier:"enterprise",change:18.5}],apiUsageByTier:[{tier:"free",avgUsage:650,usagePercentage:65},{tier:"premium",avgUsage:7500,usagePercentage:75},{tier:"enterprise",avgUsage:45e3,usagePercentage:45}]}),staleTime:9e5}),useRevenueStats:(e="30d")=>(0,t.a)({queryKey:["revenue-stats",e],queryFn:async()=>({monthlyRevenue:34567,revenueChange:15.3,arpu:27.72,arpuChange:3.2,mrr:34567,churnRate:3.5,revenueByTier:[{tier:"free",amount:0,percentage:0},{tier:"premium",amount:9048,percentage:26.2},{tier:"enterprise",amount:25519,percentage:73.8}]}),staleTime:9e5}),useTierMigration:(e="30d")=>(0,t.a)({queryKey:["tier-migration",e],queryFn:async()=>({migrations:[{fromTier:"free",toTier:"premium",count:42,percentage:65.6},{fromTier:"premium",toTier:"enterprise",count:15,percentage:23.4},{fromTier:"premium",toTier:"free",count:7,percentage:10.9}],totalMigrations:64}),staleTime:18e5})}}};