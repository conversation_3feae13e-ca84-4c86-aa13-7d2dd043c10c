(()=>{var e={};e.id=6716,e.ids=[6716],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},76173:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(50482),a=t(69108),i=t(62563),n=t.n(i),o=t(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,41904)),"/home/<USER>/FECMS-sport/src/app/auth/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,86637)),"/home/<USER>/FECMS-sport/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/auth/login/page.tsx"],u="/auth/login/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35292:(e,s,t)=>{Promise.resolve().then(t.bind(t,5215))},35303:()=>{},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5215:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(95344),a=t(3729),i=t(8428),n=t(60708),o=t(85453),l=t(3389),d=t(1222),c=t(53148);let u=(0,t(97075).Z)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);var p=t(5094),m=t(46540),x=t(7361),h=t(23673),g=t(48333),y=t(52962);let f=l.z.object({username:l.z.string().min(1,"Username is required"),password:l.z.string().min(1,"Password is required")}),j=({onSuccess:e,redirectTo:s="/dashboard"})=>{let[t,i]=(0,a.useState)(!1),{login:l,isLoginLoading:j,loginError:b}=(0,g.a)(),{register:v,handleSubmit:w,formState:{errors:N},reset:q}=(0,n.cI)({resolver:(0,o.F)(f)}),P=async t=>{try{await l(t),q(),e?e():window.location.href=s}catch(e){console.error("Login failed:",e)}};return(0,r.jsxs)(h.Zb,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(h.Ol,{className:"space-y-1",children:[r.jsx(h.ll,{className:"text-2xl font-bold text-center",children:"Sign In"}),r.jsx(h.SZ,{className:"text-center",children:"Enter your credentials to access the CMS"})]}),(0,r.jsxs)(h.aY,{children:[(0,r.jsxs)("form",{onSubmit:w(P),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(x._,{htmlFor:"username",children:"Username"}),r.jsx(m.I,{id:"username",type:"text",placeholder:"Enter your username",...v("username"),className:N.username?"border-red-500":""}),N.username?r.jsx("p",{className:"text-sm text-red-500",children:String(N.username.message||"Username is required")}):null]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(x._,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(m.I,{id:"password",type:t?"text":"password",placeholder:"Enter your password",...v("password"),className:N.password?"border-red-500 pr-10":"pr-10"}),r.jsx(p.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>i(!t),children:t?r.jsx(d.Z,{className:"h-4 w-4 text-gray-400"}):r.jsx(c.Z,{className:"h-4 w-4 text-gray-400"})})]}),N.password?r.jsx("p",{className:"text-sm text-red-500",children:String(N.password.message||"Password is required")}):null]}),b?r.jsx("div",{className:"rounded-md bg-red-50 p-3",children:r.jsx("p",{className:"text-sm text-red-800",children:b instanceof Error?b.message:"Login failed. Please try again."})}):null,r.jsx(p.z,{type:"submit",className:"w-full",disabled:j,children:j?(0,r.jsxs)(r.Fragment,{children:[r.jsx(y.TK,{size:"sm",className:"mr-2"}),"Signing in..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(u,{className:"mr-2 h-4 w-4"}),"Sign In"]})})]}),r.jsx("div",{className:"mt-4 text-center text-sm text-gray-600",children:r.jsx("p",{children:"System Administrator Access Only"})})]})]})};function b(){let e=(0,i.useRouter)(),{isAuthenticated:s,isLoading:t}=(0,g.a)();return((0,a.useEffect)(()=>{s&&!t&&e.push("/dashboard")},[s,t,e]),t)?r.jsx(y.SX,{message:"Checking authentication..."}):s?r.jsx(y.SX,{message:"Redirecting to dashboard..."}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"APISportsGame CMS"}),r.jsx("p",{className:"text-gray-600",children:"Content Management System"})]}),r.jsx(j,{onSuccess:()=>e.push("/dashboard")}),r.jsx("div",{className:"text-center text-sm text-gray-500",children:r.jsx("p",{children:"\xa9 2025 APISportsGame. All rights reserved."})})]})})}},46540:(e,s,t)=>{"use strict";t.d(s,{I:()=>n});var r=t(95344),a=t(3729),i=t(11453);let n=a.forwardRef(({className:e,type:s,...t},a)=>r.jsx("input",{type:s,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},7361:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var r=t(95344),a=t(3729),i=t(14217),n=t(49247),o=t(11453);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},t)=>r.jsx(i.f,{ref:t,className:(0,o.cn)(l(),e),...s}));d.displayName=i.f.displayName},86637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(25036);function a({children:e}){return r.jsx("div",{className:"auth-layout",children:e})}},41904:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/auth/login/page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},62409:(e,s,t)=>{"use strict";t.d(s,{WV:()=>o,jH:()=>l});var r=t(3729),a=t(81202),i=t(32751),n=t(95344),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,i.Z8)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...i}=e,o=a?t:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(o,{...i,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function l(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,6126,337,2609,4932,6317],()=>t(76173));module.exports=r})();