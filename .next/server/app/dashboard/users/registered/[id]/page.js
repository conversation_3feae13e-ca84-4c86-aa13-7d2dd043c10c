(()=>{var e={};e.id=2336,e.ids=[2336],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},90811:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var t=a(50482),r=a(69108),i=a(62563),l=a.n(i),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["dashboard",{children:["users",{children:["registered",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,12713)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/page.tsx"],x="/dashboard/users/registered/[id]/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/users/registered/[id]/page",pathname:"/dashboard/users/registered/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40120:(e,s,a)=>{Promise.resolve().then(a.bind(a,7802))},88534:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63024:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},85674:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48411:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},30304:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},71206:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},46327:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},38271:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},7802:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>L});var t=a(95344),r=a(3729),i=a(8428),l=a(63024),n=a(65719),c=a(23485),d=a(46327),o=a(38271),x=a(89895),m=a(71206),h=a(30304);let u=(0,a(97075).Z)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var p=a(55794),j=a(88534),g=a(46064),v=a(85674),y=a(48411),f=a(5094),N=a(23673),b=a(19591),w=a(14871),C=a(51467),k=a(47210),Z=a(50909),S=a(79770),A=a(60339),P=a(54578),D=a(36487);function L(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:a}=(0,A.p)(),{canManageUsers:L}=(0,D.TE)(),M=e.id,{data:E,isLoading:q,error:T}=P.Xn.useGetById(M),{mutate:z,isLoading:U}=P.Xn.useSuspend(),{mutate:R,isLoading:I}=P.Xn.useReactivate(),{mutate:V,isLoading:_}=P.Xn.useDelete(),{data:F}=P.Xn.useUsageStats(M),{data:O=[]}=P.Xn.useApiCalls(M),[Y,$]=(0,r.useState)(!1),[W,G]=(0,r.useState)(!1),H=e=>{switch(e){case"enterprise":return"default";case"premium":return"secondary";default:return"outline"}};if(q)return t.jsx("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[t.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[t.jsx("div",{className:"h-64 bg-gray-200 rounded"}),t.jsx("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"h-32 bg-gray-200 rounded"}),t.jsx("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})});if(T||!E)return t.jsx("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,t.jsxs)(f.z,{onClick:()=>s.push("/dashboard/users/registered"),children:[t.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});let X=E.tier?Math.round(E.apiCallsUsed/E.apiCallsLimit*100):0;return(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(f.z,{variant:"ghost",size:"sm",onClick:()=>s.push("/dashboard/users/registered"),children:[t.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:E.name}),t.jsx("p",{className:"text-gray-600",children:E.email})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[L()&&(0,t.jsxs)(t.Fragment,{children:["active"===E.status?(0,t.jsxs)(S.aR,{open:W,onOpenChange:G,children:[t.jsx(S.vW,{asChild:!0,children:(0,t.jsxs)(f.z,{variant:"outline",children:[t.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Suspend"]})}),(0,t.jsxs)(S._T,{children:[(0,t.jsxs)(S.fY,{children:[t.jsx(S.f$,{children:"Suspend User"}),t.jsx(S.yT,{children:"This will suspend the user's access to the API. They will not be able to make API calls until reactivated."})]}),(0,t.jsxs)(S.xo,{children:[t.jsx(S.le,{children:"Cancel"}),t.jsx(S.OL,{onClick:()=>{z(M,{onSuccess:()=>{a({title:"User suspended",description:"User has been successfully suspended."}),G(!1)},onError:e=>{a({title:"Error",description:e?.message||"Failed to suspend user.",variant:"destructive"})}})},disabled:U,children:U?"Suspending...":"Suspend User"})]})]})]}):"suspended"===E.status?(0,t.jsxs)(f.z,{variant:"outline",onClick:()=>{R(M,{onSuccess:()=>{a({title:"User reactivated",description:"User has been successfully reactivated."})},onError:e=>{a({title:"Error",description:e?.message||"Failed to reactivate user.",variant:"destructive"})}})},disabled:I,children:[t.jsx(c.Z,{className:"w-4 h-4 mr-2"}),I?"Reactivating...":"Reactivate"]}):null,(0,t.jsxs)(f.z,{variant:"outline",onClick:()=>s.push(`/dashboard/users/registered/${M}/edit`),children:[t.jsx(d.Z,{className:"w-4 h-4 mr-2"}),"Edit"]})]}),L()&&(0,t.jsxs)(S.aR,{open:Y,onOpenChange:$,children:[t.jsx(S.vW,{asChild:!0,children:(0,t.jsxs)(f.z,{variant:"destructive",children:[t.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})}),(0,t.jsxs)(S._T,{children:[(0,t.jsxs)(S.fY,{children:[t.jsx(S.f$,{children:"Are you sure?"}),t.jsx(S.yT,{children:"This action cannot be undone. This will permanently delete the user account and remove all associated data including API keys and usage history."})]}),(0,t.jsxs)(S.xo,{children:[t.jsx(S.le,{children:"Cancel"}),t.jsx(S.OL,{onClick:()=>{V(M,{onSuccess:()=>{a({title:"User deleted",description:"User has been successfully deleted."}),s.push("/dashboard/users/registered")},onError:e=>{a({title:"Error",description:e?.message||"Failed to delete user.",variant:"destructive"})}})},className:"bg-red-600 hover:bg-red-700",disabled:_,children:_?"Deleting...":"Delete User"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[t.jsx("div",{className:"lg:col-span-2",children:(0,t.jsxs)(C.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,t.jsxs)(C.dr,{children:[t.jsx(C.SP,{value:"overview",children:"Overview"}),t.jsx(C.SP,{value:"usage",children:"API Usage"}),t.jsx(C.SP,{value:"subscription",children:"Subscription"}),t.jsx(C.SP,{value:"activity",children:"Activity"})]}),(0,t.jsxs)(C.nU,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)(N.Zb,{children:[t.jsx(N.Ol,{children:(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(x.Z,{className:"w-5 h-5 mr-2"}),"Profile Information"]})}),(0,t.jsxs)(N.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(w.qE,{className:"w-16 h-16",children:[t.jsx(w.F$,{src:E.avatar,alt:E.name}),t.jsx(w.Q5,{children:E.name.split(" ").map(e=>e.charAt(0)).join("")})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(b.C,{variant:H(E.tier),children:E.tier?.charAt(0).toUpperCase()+E.tier?.slice(1)}),t.jsx(b.C,{variant:(e=>{switch(e){case"active":return"default";case"inactive":return"secondary";case"suspended":return"destructive";default:return"outline"}})(E.status),children:E.status.charAt(0).toUpperCase()+E.status.slice(1)}),E.emailVerified&&t.jsx(b.C,{variant:"secondary",children:"Verified"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",E.id]})]})]}),t.jsx(Z.Z,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(m.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Email"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:E.email})]}),E.company&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(h.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Company"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:E.company})]}),E.phone&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(u,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Phone"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:E.phone})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(p.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Last Login"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:E.lastLogin?new Date(E.lastLogin).toLocaleString():"Never"})]})]})]})]}),(0,t.jsxs)(N.Zb,{children:[t.jsx(N.Ol,{children:(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(j.Z,{className:"w-5 h-5 mr-2"}),"API Usage Overview"]})}),(0,t.jsxs)(N.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-2xl font-bold text-blue-600",children:E.apiCallsUsed?.toLocaleString()}),t.jsx("p",{className:"text-sm text-gray-600",children:"Calls Used"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-2xl font-bold text-green-600",children:E.apiCallsLimit?.toLocaleString()}),t.jsx("p",{className:"text-sm text-gray-600",children:"Calls Limit"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[X,"%"]}),t.jsx("p",{className:"text-sm text-gray-600",children:"Usage"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"API Calls"}),(0,t.jsxs)("span",{children:[E.apiCallsUsed," / ",E.apiCallsLimit]})]}),t.jsx(k.E,{value:X,variant:X>=90?"error":X>=75?"warning":"success",className:"h-2"})]}),X>=90&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[t.jsx(n.Z,{className:"w-4 h-4 text-red-500"}),t.jsx("p",{className:"text-sm text-red-700",children:"User is approaching their API limit. Consider upgrading their tier."})]})]})]})]}),(0,t.jsxs)(C.nU,{value:"usage",className:"space-y-6",children:[(0,t.jsxs)(N.Zb,{children:[(0,t.jsxs)(N.Ol,{children:[(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(g.Z,{className:"w-5 h-5 mr-2"}),"API Usage Statistics"]}),t.jsx(N.SZ,{children:"Detailed breakdown of API usage"})]}),t.jsx(N.aY,{children:F?t.jsx("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[t.jsx("p",{className:"text-xl font-bold text-blue-600",children:F.today}),t.jsx("p",{className:"text-sm text-gray-600",children:"Today"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[t.jsx("p",{className:"text-xl font-bold text-green-600",children:F.thisWeek}),t.jsx("p",{className:"text-sm text-gray-600",children:"This Week"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[t.jsx("p",{className:"text-xl font-bold text-yellow-600",children:F.thisMonth}),t.jsx("p",{className:"text-sm text-gray-600",children:"This Month"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[t.jsx("p",{className:"text-xl font-bold text-purple-600",children:F.total}),t.jsx("p",{className:"text-sm text-gray-600",children:"Total"})]})]})}):t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No usage statistics available"})})]}),(0,t.jsxs)(N.Zb,{children:[(0,t.jsxs)(N.Ol,{children:[t.jsx(N.ll,{children:"Recent API Calls"}),t.jsx(N.SZ,{children:"Latest API requests made by this user"})]}),t.jsx(N.aY,{children:t.jsx("div",{className:"space-y-3",children:O.length>0?O.slice(0,10).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium",children:e.endpoint}),t.jsx("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(b.C,{variant:200===e.status?"default":"destructive",children:e.status}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.responseTime,"ms"]})]})]},e.id)):t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No API calls recorded"})})})]})]}),t.jsx(C.nU,{value:"subscription",className:"space-y-6",children:(0,t.jsxs)(N.Zb,{children:[t.jsx(N.Ol,{children:(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(v.Z,{className:"w-5 h-5 mr-2"}),"Subscription Details"]})}),t.jsx(N.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(y.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Current Tier"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:t.jsx(b.C,{variant:H(E.tier),children:E.tier?.charAt(0).toUpperCase()+E.tier?.slice(1)})})]}),E.subscriptionStartDate&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(p.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Subscription Start"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:new Date(E.subscriptionStartDate).toLocaleDateString()})]}),E.subscriptionEndDate&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(p.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Subscription End"})]}),t.jsx("p",{className:"text-sm text-gray-600 ml-6",children:new Date(E.subscriptionEndDate).toLocaleDateString()})]}),E.monthlySpend&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(y.Z,{className:"w-4 h-4 text-gray-500"}),t.jsx("span",{className:"text-sm font-medium",children:"Monthly Spend"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 ml-6",children:["$",E.monthlySpend.toFixed(2)]})]})]})})]})}),t.jsx(C.nU,{value:"activity",className:"space-y-6",children:(0,t.jsxs)(N.Zb,{children:[(0,t.jsxs)(N.Ol,{children:[(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(j.Z,{className:"w-5 h-5 mr-2"}),"Account Activity"]}),t.jsx(N.SZ,{children:"Recent account-related activities"})]}),t.jsx(N.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm font-medium",children:"Account created"}),t.jsx("p",{className:"text-xs text-gray-500",children:new Date(E.createdAt).toLocaleString()})]})]}),E.lastLogin&&(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm font-medium",children:"Last login"}),t.jsx("p",{className:"text-xs text-gray-500",children:new Date(E.lastLogin).toLocaleString()})]})]}),E.emailVerified&&(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm font-medium",children:"Email verified"}),t.jsx("p",{className:"text-xs text-gray-500",children:"Email address confirmed"})]})]})]})})]})})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(N.Zb,{children:[t.jsx(N.Ol,{children:(0,t.jsxs)(N.ll,{className:"flex items-center",children:[t.jsx(g.Z,{className:"w-5 h-5 mr-2"}),"Quick Stats"]})}),(0,t.jsxs)(N.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"API Calls Today"}),t.jsx("span",{className:"font-medium",children:F?.today||0})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Days as Member"}),t.jsx("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(E.createdAt).getTime())/864e5)})]}),E.lastLogin&&(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Days Since Last Login"}),t.jsx("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(E.lastLogin).getTime())/864e5)})]})]})]}),L()&&(0,t.jsxs)(N.Zb,{children:[t.jsx(N.Ol,{children:t.jsx(N.ll,{children:"Quick Actions"})}),(0,t.jsxs)(N.aY,{className:"space-y-2",children:[(0,t.jsxs)(f.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>s.push(`/dashboard/users/registered/${M}/edit`),children:[t.jsx(d.Z,{className:"w-4 h-4 mr-2"}),"Edit Profile"]}),(0,t.jsxs)(f.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{a({title:"Coming Soon",description:"Tier upgrade functionality will be implemented."})},children:[t.jsx(g.Z,{className:"w-4 h-4 mr-2"}),"Upgrade Tier"]})]})]})]})]})]})}},79770:(e,s,a)=>{"use strict";a.d(s,{OL:()=>u,_T:()=>d,aR:()=>c,f$:()=>x,fY:()=>o,le:()=>p,vW:()=>j,xo:()=>h,yT:()=>m});var t=a(95344),r=a(3729),i=a(81202),l=a(11453),n=a(5094);let c=({open:e,onOpenChange:s,children:a})=>(r.useEffect(()=>{let a=a=>{"Escape"===a.key&&e&&s?.(!1)};return e&&(document.addEventListener("keydown",a),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",a),document.body.style.overflow="unset"}},[e,s]),e)?(0,i.createPortal)((0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[t.jsx("div",{className:"absolute inset-0 bg-black/50",onClick:()=>s?.(!1)}),t.jsx("div",{className:"relative z-10",children:a})]}),document.body):null,d=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6",e),...a,children:s}));d.displayName="AlertDialogContent";let o=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left mb-4",e),...a,children:s}));o.displayName="AlertDialogHeader";let x=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx("h2",{ref:r,className:(0,l.cn)("text-lg font-semibold text-gray-900",e),...a,children:s}));x.displayName="AlertDialogTitle";let m=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx("p",{ref:r,className:(0,l.cn)("text-sm text-gray-600",e),...a,children:s}));m.displayName="AlertDialogDescription";let h=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",e),...a,children:s}));h.displayName="AlertDialogFooter";let u=r.forwardRef(({className:e,variant:s="default",children:a,...r},i)=>t.jsx(n.z,{ref:i,className:(0,l.cn)("destructive"===s&&"bg-red-600 hover:bg-red-700 text-white",e),...r,children:a}));u.displayName="AlertDialogAction";let p=r.forwardRef(({className:e,children:s,...a},r)=>t.jsx(n.z,{ref:r,variant:"outline",className:(0,l.cn)("mt-2 sm:mt-0",e),...a,children:s}));p.displayName="AlertDialogCancel";let j=r.forwardRef(({className:e,children:s,asChild:a=!1,...i},l)=>a?r.cloneElement(s,{ref:l,...i}):t.jsx("button",{ref:l,className:e,...i,children:s}));j.displayName="AlertDialogTrigger"},50909:(e,s,a)=>{"use strict";a.d(s,{Z:()=>o});var t=a(95344),r=a(3729),i=a(62409),l="horizontal",n=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:a,orientation:r=l,...c}=e,d=n.includes(r)?r:l;return(0,t.jsx)(i.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=a(11453);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},i)=>t.jsx(c,{ref:i,decorative:a,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=c.displayName},51467:(e,s,a)=>{"use strict";a.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var t=a(95344),r=a(3729),i=a(89128),l=a(11453);let n=i.fC,c=r.forwardRef(({className:e,...s},a)=>t.jsx(i.aV,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.aV.displayName;let d=r.forwardRef(({className:e,...s},a)=>t.jsx(i.xz,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.xz.displayName;let o=r.forwardRef(({className:e,...s},a)=>t.jsx(i.VY,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=i.VY.displayName},60339:(e,s,a)=>{"use strict";a.d(s,{p:()=>r});var t=a(34755);let r=()=>({toast:e=>{"destructive"===e.variant?t.toast.error(e.title||e.description||"Error occurred"):t.toast.success(e.title||e.description||"Success")}})},12713:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/page.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},89128:(e,s,a)=>{"use strict";a.d(s,{VY:()=>L,aV:()=>P,fC:()=>A,xz:()=>D});var t=a(3729),r=a(85222),i=a(98462),l=a(34504),n=a(43234),c=a(62409),d=a(3975),o=a(33183),x=a(99048),m=a(95344),h="Tabs",[u,p]=(0,i.b)(h,[l.Pc]),j=(0,l.Pc)(),[g,v]=u(h),y=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:u="automatic",...p}=e,j=(0,d.gm)(n),[v,y]=(0,o.T)({prop:t,onChange:r,defaultProp:i??"",caller:h});return(0,m.jsx)(g,{scope:a,baseId:(0,x.M)(),value:v,onValueChange:y,orientation:l,dir:j,activationMode:u,children:(0,m.jsx)(c.WV.div,{dir:j,"data-orientation":l,...p,ref:s})})});y.displayName=h;var f="TabsList",N=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...r}=e,i=v(f,a),n=j(a);return(0,m.jsx)(l.fC,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:t,children:(0,m.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});N.displayName=f;var b="TabsTrigger",w=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:i=!1,...n}=e,d=v(b,a),o=j(a),x=Z(d.baseId,t),h=S(d.baseId,t),u=t===d.value;return(0,m.jsx)(l.ck,{asChild:!0,...o,focusable:!i,active:u,children:(0,m.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:x,...n,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||i||!e||d.onValueChange(t)})})})});w.displayName=b;var C="TabsContent",k=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:i,children:l,...d}=e,o=v(C,a),x=Z(o.baseId,r),h=S(o.baseId,r),u=r===o.value,p=t.useRef(u);return t.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(n.z,{present:i||u,children:({present:a})=>(0,m.jsx)(c.WV.div,{"data-state":u?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function Z(e,s){return`${e}-trigger-${s}`}function S(e,s){return`${e}-content-${s}`}k.displayName=C;var A=y,P=N,D=w,L=k}};var s=require("../../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,6317,7833,1231],()=>a(90811));module.exports=t})();