(()=>{var e={};e.id=2578,e.ids=[2578],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},85911:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>c});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["dashboard",{children:["users",{children:["registered",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98333)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/edit/page.tsx"],m="/dashboard/users/registered/[id]/edit/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/users/registered/[id]/edit/page",pathname:"/dashboard/users/registered/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},45071:(e,s,t)=>{Promise.resolve().then(t.bind(t,82285))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},85674:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},82285:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>_});var a=t(95344),r=t(3729),i=t(8428),l=t(60708),n=t(85453),d=t(3389),c=t(63024),o=t(31498),m=t(18822),u=t(85674),p=t(65719),x=t(5094),h=t(23673),f=t(46540),j=t(7361),g=t(38157),y=t(13611),b=t(2690),v=t(50909),N=t(19591),w=t(47210),C=t(60339),S=t(54578),k=t(36487);let P=d.z.object({name:d.z.string().min(1,"Name is required"),email:d.z.string().email("Invalid email address"),company:d.z.string().optional(),phone:d.z.string().optional(),website:d.z.string().url("Invalid website URL").optional().or(d.z.literal("")),tier:d.z.enum(["free","premium","enterprise"],{required_error:"Tier is required"}),status:d.z.enum(["active","inactive","suspended"]),emailVerified:d.z.boolean(),apiCallsLimit:d.z.number().min(0,"API calls limit must be non-negative"),notes:d.z.string().optional()});function _(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,C.p)(),{can:d}=(0,k.TE)(),_=e.id,{data:z,isLoading:Z,error:E}=S.Xn.useGetById(_),{mutate:q,isLoading:L}=S.Xn.useUpdate(),{mutate:A,isLoading:I}=S.Xn.useUpgradeTier(),{mutate:F,isLoading:V}=S.Xn.useDowngradeTier(),[R,U]=(0,r.useState)(!1),[M,T]=(0,r.useState)(""),D=(0,l.cI)({resolver:(0,n.F)(P),defaultValues:{name:"",email:"",company:"",phone:"",website:"",tier:"free",status:"active",emailVerified:!1,apiCallsLimit:1e3,notes:""}});(0,r.useEffect)(()=>{z&&(T(z.tier||"free"),D.reset({name:z.name||"",email:z.email||"",company:z.company||"",phone:z.phone||"",website:z.website||"",tier:z.tier||"free",status:z.status||"active",emailVerified:z.emailVerified||!1,apiCallsLimit:z.apiCallsLimit||1e3,notes:z.notes||""}))},[z,D]),(0,r.useEffect)(()=>{let e=D.watch(()=>{U(!0)});return()=>e.unsubscribe()},[D]);let $=e=>{if(M!==e.tier){let a=B(e.tier)>B(M);(a?A:F)({userId:_,newTier:e.tier},{onSuccess:()=>{q({id:_,...e},{onSuccess:()=>{t({title:"User updated",description:`User has been successfully updated and tier ${a?"upgraded":"downgraded"} to ${e.tier}.`}),U(!1),s.push(`/dashboard/users/registered/${_}`)},onError:e=>{t({title:"Partial Success",description:`Tier was changed but other updates failed: ${e?.message||"Unknown error"}`,variant:"destructive"})}})},onError:e=>{t({title:"Error",description:e?.message||"Failed to change user tier.",variant:"destructive"})}})}else q({id:_,...e},{onSuccess:()=>{t({title:"User updated",description:"User has been successfully updated."}),U(!1),s.push(`/dashboard/users/registered/${_}`)},onError:e=>{t({title:"Error",description:e?.message||"Failed to update user.",variant:"destructive"})}})},B=e=>{switch(e){case"free":return 1;case"premium":return 2;case"enterprise":return 3;default:return 0}},G=e=>{switch(e){case"enterprise":return"default";case"premium":return"secondary";default:return"outline"}},O=()=>{R?confirm("You have unsaved changes. Are you sure you want to leave?")&&s.push(`/dashboard/users/registered/${_}`):s.push(`/dashboard/users/registered/${_}`)};if(!d("users:update"))return a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"You don't have permission to edit users."}),(0,a.jsxs)(x.z,{onClick:()=>s.push("/dashboard/users/registered"),children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});if(Z)return a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-6",children:[a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[a.jsx("div",{className:"h-64 bg-gray-200 rounded"}),a.jsx("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"h-32 bg-gray-200 rounded"}),a.jsx("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})});if(E||!z)return a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,a.jsxs)(x.z,{onClick:()=>s.push("/dashboard/users/registered"),children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});let Y=z.tier?Math.round(z.apiCallsUsed/z.apiCallsLimit*100):0,Q=(e=>{switch(e){case"free":return{apiCalls:1e3,features:["Basic API access","Email support"]};case"premium":return{apiCalls:1e4,features:["Enhanced API access","Priority support","Analytics"]};case"enterprise":return{apiCalls:1e5,features:["Full API access","24/7 support","Custom integration","SLA"]};default:return{apiCalls:1e3,features:[]}}})(D.watch("tier"));return(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(x.z,{variant:"ghost",size:"sm",onClick:O,children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit User"}),a.jsx("p",{className:"text-gray-600",children:z.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(x.z,{variant:"outline",onClick:O,children:"Cancel"}),(0,a.jsxs)(x.z,{onClick:D.handleSubmit($),disabled:L||I||V||!R,children:[a.jsx(o.Z,{className:"w-4 h-4 mr-2"}),L||I||V?"Saving...":"Save Changes"]})]})]}),a.jsx("form",{onSubmit:D.handleSubmit($),className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center",children:[a.jsx(m.Z,{className:"w-5 h-5 mr-2"}),"Basic Information"]}),a.jsx(h.SZ,{children:"Update the user's personal and contact information"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"name",children:"Full Name *"}),a.jsx(f.I,{id:"name",...D.register("name")}),D.formState.errors.name&&a.jsx("p",{className:"text-sm text-red-600",children:D.formState.errors.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"email",children:"Email Address *"}),a.jsx(f.I,{id:"email",type:"email",...D.register("email")}),D.formState.errors.email&&a.jsx("p",{className:"text-sm text-red-600",children:D.formState.errors.email.message})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"company",children:"Company"}),a.jsx(f.I,{id:"company",...D.register("company"),placeholder:"Company name"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"phone",children:"Phone Number"}),a.jsx(f.I,{id:"phone",type:"tel",...D.register("phone"),placeholder:"+****************"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"website",children:"Website"}),a.jsx(f.I,{id:"website",type:"url",...D.register("website"),placeholder:"https://example.com"}),D.formState.errors.website&&a.jsx("p",{className:"text-sm text-red-600",children:D.formState.errors.website.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"notes",children:"Notes"}),a.jsx(b.g,{id:"notes",...D.register("notes"),placeholder:"Additional notes about this user...",rows:3})]})]})]}),(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center",children:[a.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"Subscription & Tier"]}),a.jsx(h.SZ,{children:"Manage the user's subscription tier and API limits"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"tier",children:"Subscription Tier *"}),(0,a.jsxs)(g.Ph,{value:D.watch("tier"),onValueChange:e=>D.setValue("tier",e),children:[a.jsx(g.i4,{children:a.jsx(g.ki,{placeholder:"Select a tier"})}),(0,a.jsxs)(g.Bw,{children:[a.jsx(g.Ql,{value:"free",children:"Free"}),a.jsx(g.Ql,{value:"premium",children:"Premium"}),a.jsx(g.Ql,{value:"enterprise",children:"Enterprise"})]})]}),D.formState.errors.tier&&a.jsx("p",{className:"text-sm text-red-600",children:D.formState.errors.tier.message})]}),M!==D.watch("tier")&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[a.jsx(p.Z,{className:"w-4 h-4 text-yellow-500"}),(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:["Tier will be changed from"," ",a.jsx(N.C,{variant:G(M),children:M})," ","to"," ",a.jsx(N.C,{variant:G(D.watch("tier")),children:D.watch("tier")})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"apiCallsLimit",children:"API Calls Limit"}),a.jsx(f.I,{id:"apiCallsLimit",type:"number",min:"0",...D.register("apiCallsLimit",{valueAsNumber:!0})}),D.formState.errors.apiCallsLimit&&a.jsx("p",{className:"text-sm text-red-600",children:D.formState.errors.apiCallsLimit.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Recommended limit for ",D.watch("tier"),": ",Q.apiCalls.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(v.Z,{}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Tier Features"}),a.jsx("div",{className:"text-sm text-gray-600",children:a.jsx("ul",{className:"space-y-1",children:Q.features.map((e,s)=>(0,a.jsxs)("li",{children:["• ",e]},s))})})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[a.jsx(h.ll,{children:"Account Status"}),a.jsx(h.SZ,{children:"Manage the user's account status"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(j._,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(g.Ph,{value:D.watch("status"),onValueChange:e=>D.setValue("status",e),children:[a.jsx(g.i4,{children:a.jsx(g.ki,{})}),(0,a.jsxs)(g.Bw,{children:[a.jsx(g.Ql,{value:"active",children:"Active"}),a.jsx(g.Ql,{value:"inactive",children:"Inactive"}),a.jsx(g.Ql,{value:"suspended",children:"Suspended"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(j._,{htmlFor:"emailVerified",className:"text-sm font-medium",children:"Email Verified"}),a.jsx(y.r,{id:"emailVerified",checked:D.watch("emailVerified"),onCheckedChange:e=>D.setValue("emailVerified",e)})]})]})]}),(0,a.jsxs)(h.Zb,{children:[a.jsx(h.Ol,{children:a.jsx(h.ll,{children:"Current Usage"})}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{children:"API Calls"}),(0,a.jsxs)("span",{children:[z.apiCallsUsed," / ",z.apiCallsLimit]})]}),a.jsx(w.E,{value:Y,className:"h-2"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[Y,"% used"]})]}),a.jsx(v.Z,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Monthly Spend"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",z.monthlySpend?.toFixed(2)||"0.00"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Last API Call"}),a.jsx("span",{className:"font-medium",children:z.lastApiCall?new Date(z.lastApiCall).toLocaleDateString():"Never"})]})]})]})]}),(0,a.jsxs)(h.Zb,{children:[a.jsx(h.Ol,{children:a.jsx(h.ll,{children:"Account Information"})}),(0,a.jsxs)(h.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"User ID"}),a.jsx("span",{className:"font-medium",children:z.id})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Created"}),a.jsx("span",{className:"font-medium",children:new Date(z.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Last Updated"}),a.jsx("span",{className:"font-medium",children:new Date(z.updatedAt).toLocaleDateString()})]}),z.lastLogin&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Last Login"}),a.jsx("span",{className:"font-medium",children:new Date(z.lastLogin).toLocaleDateString()})]})]})]})]})]})})]})}},7361:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var a=t(95344),r=t(3729),i=t(14217),l=t(49247),n=t(11453);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>a.jsx(i.f,{ref:t,className:(0,n.cn)(d(),e),...s}));c.displayName=i.f.displayName},38157:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>h,Ph:()=>o,Ql:()=>f,i4:()=>u,ki:()=>m});var a=t(95344),r=t(3729),i=t(32116),l=t(25390),n=t(12704),d=t(62312),c=t(11453);let o=i.fC;i.ZA;let m=i.B4,u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.xz,{ref:r,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(i.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.xz.displayName;let p=r.forwardRef(({className:e,...s},t)=>a.jsx(i.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=i.u_.displayName;let x=r.forwardRef(({className:e,...s},t)=>a.jsx(i.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(l.Z,{className:"h-4 w-4"})}));x.displayName=i.$G.displayName;let h=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(i.h_,{children:(0,a.jsxs)(i.VY,{ref:l,className:(0,c.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(p,{}),a.jsx(i.l_,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(x,{})]})}));h.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.__,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let f=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.ck,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(i.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(i.eT,{children:s})]}));f.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},50909:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),r=t(3729),i=t(62409),l="horizontal",n=["horizontal","vertical"],d=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=l,...d}=e,c=n.includes(r)?r:l;return(0,a.jsx)(i.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:s})});d.displayName="Separator";var c=t(11453);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>a.jsx(d,{ref:i,decorative:t,orientation:s,className:(0,c.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=d.displayName},13611:(e,s,t)=>{"use strict";t.d(s,{r:()=>n});var a=t(95344),r=t(3729),i=t(19655),l=t(11453);let n=r.forwardRef(({className:e,...s},t)=>a.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},2690:(e,s,t)=>{"use strict";t.d(s,{g:()=>l});var a=t(95344),r=t(3729),i=t(11453);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));l.displayName="Textarea"},60339:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(34755);let r=()=>({toast:e=>{"destructive"===e.variant?a.toast.error(e.title||e.description||"Error occurred"):a.toast.success(e.title||e.description||"Success")}})},98333:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/[id]/edit/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},19655:(e,s,t)=>{"use strict";t.d(s,{bU:()=>w,fC:()=>N});var a=t(3729),r=t(85222),i=t(31405),l=t(98462),n=t(33183),d=t(92062),c=t(63085),o=t(62409),m=t(95344),u="Switch",[p,x]=(0,l.b)(u),[h,f]=p(u),j=a.forwardRef((e,s)=>{let{__scopeSwitch:t,name:l,checked:d,defaultChecked:c,required:p,disabled:x,value:f="on",onCheckedChange:j,form:g,...y}=e,[N,w]=a.useState(null),C=(0,i.e)(s,e=>w(e)),S=a.useRef(!1),k=!N||g||!!N.closest("form"),[P,_]=(0,n.T)({prop:d,defaultProp:c??!1,onChange:j,caller:u});return(0,m.jsxs)(h,{scope:t,checked:P,disabled:x,children:[(0,m.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":p,"data-state":v(P),"data-disabled":x?"":void 0,disabled:x,value:f,...y,ref:C,onClick:(0,r.M)(e.onClick,e=>{_(e=>!e),k&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),k&&(0,m.jsx)(b,{control:N,bubbles:!S.current,name:l,value:f,checked:P,required:p,disabled:x,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=u;var g="SwitchThumb",y=a.forwardRef((e,s)=>{let{__scopeSwitch:t,...a}=e,r=f(g,t);return(0,m.jsx)(o.WV.span,{"data-state":v(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:s})});y.displayName=g;var b=a.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:r=!0,...l},n)=>{let o=a.useRef(null),u=(0,i.e)(o,n),p=(0,d.D)(t),x=(0,c.t)(s);return a.useEffect(()=>{let e=o.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==t&&s){let a=new Event("click",{bubbles:r});s.call(e,t),e.dispatchEvent(a)}},[p,t,r]),(0,m.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:u,style:{...l.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var N=j,w=y}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,7966,4932,6317,7833,1231],()=>t(85911));module.exports=a})();