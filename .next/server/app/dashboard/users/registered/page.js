(()=>{var e={};e.id=5305,e.ids=[5305],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},97860:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(50482),a=r(69108),i=r(62563),l=r.n(i),n=r(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c=["",{children:["dashboard",{children:["users",{children:["registered",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76036)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/page.tsx"],x="/dashboard/users/registered/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/users/registered/page",pathname:"/dashboard/users/registered",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},62289:(e,s,r)=>{Promise.resolve().then(r.bind(r,57305))},48411:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97075).Z)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},46064:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},57305:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>Z});var t=r(95344),a=r(3729),i=r(36487),l=r(54578),n=r(23673),d=r(19591),c=r(5094),o=r(46540),x=r(38157),u=r(47585),m=r(79770),h=r(77022),p=r(14871),j=r(47210),g=r(46327),f=r(87957),v=r(46064),y=r(32817),N=r(15366),w=r(89895),b=r(88534),A=r(48411),C=r(65719),P=r(28765),k=r(56506),S=r(63185);function Z(){let{canManageUsers:e}=(0,i.TE)(),[s,r]=(0,a.useState)({search:"",tier:void 0,isActive:void 0,isEmailVerified:void 0,page:1,limit:10}),{data:Z,isLoading:U}=(0,l.Ow)(s),{data:E}=(0,l.ie)(),{data:T}=(0,l.zL)(85),{upgradeTier:q,downgradeTier:D,suspendUser:M,reactivateUser:_}=(0,l.Il)(),[L,O]=(0,a.useState)({isOpen:!1,type:null,user:null}),R=e=>{r(s=>({...s,search:e,page:1}))},$=e=>{switch(e){case"free":default:return"bg-gray-100 text-gray-800 border-gray-200";case"premium":return"bg-blue-100 text-blue-800 border-blue-200";case"enterprise":return"bg-purple-100 text-purple-800 border-purple-200"}},I=e=>e>=90?"error":e>=75?"warning":"success",F=(e,s,r)=>{O({isOpen:!0,type:e,user:s,newTier:r})},z=async()=>{if(L.user&&L.type)try{switch(L.type){case"suspend":await M.mutateAsync(L.user.id);break;case"reactivate":await _.mutateAsync(L.user.id);break;case"upgrade":L.newTier&&await q.mutateAsync({userId:L.user.id,newTier:L.newTier,subscriptionMonths:12});break;case"downgrade":L.newTier&&await D.mutateAsync({userId:L.user.id,newTier:L.newTier})}}catch(e){console.error("Action failed:",e)}finally{O({isOpen:!1,type:null,user:null})}},Q=[{key:"username",title:"User",render:(e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(p.qE,{className:"h-8 w-8",children:t.jsx(p.Q5,{className:"text-xs",children:s.username.slice(0,2).toUpperCase()})}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:s.username}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["ID: ",s.id]})]})]})},{key:"email",title:"Email & Status",render:(e,s)=>(0,t.jsxs)("div",{className:"space-y-1",children:[t.jsx("div",{className:"font-mono text-sm",children:s.email}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[s.isEmailVerified?t.jsx(d.C,{variant:"outline",className:"text-xs text-green-600 border-green-200",children:"Verified"}):t.jsx(d.C,{variant:"outline",className:"text-xs text-red-600 border-red-200",children:"Unverified"}),t.jsx(d.C,{variant:"outline",className:`text-xs ${s.isActive?"text-green-600 border-green-200":"text-red-600 border-red-200"}`,children:s.isActive?"Active":"Suspended"})]})]})},{key:"tier",title:"Tier",render:e=>t.jsx(d.C,{className:`${$(e)} capitalize`,children:e})},{key:"apiUsage",title:"API Usage",render:(e,s)=>{let r=s.apiCallsUsed||0,a=s.apiCallsLimit;if(!a)return(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{className:"font-medium",children:[r.toLocaleString()," calls"]}),t.jsx("div",{className:"text-muted-foreground",children:"Unlimited"})]});let i=Math.round(r/a*100);return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs",children:[t.jsx("span",{children:r.toLocaleString()}),t.jsx("span",{children:a.toLocaleString()})]}),t.jsx(j.E,{value:i,className:"h-2",variant:I(i)}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[i,"% used"]})]})}},{key:"subscription",title:"Subscription",render:(e,s)=>s.hasActiveSubscription?(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx(d.C,{variant:"outline",className:"text-green-600 border-green-200 mb-1",children:"Active"}),s.subscriptionEndDate&&(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Expires ",(0,S.Q)(new Date(s.subscriptionEndDate),{addSuffix:!0})]})]}):t.jsx(d.C,{variant:"outline",className:"text-gray-600 border-gray-200",children:"No subscription"})},{key:"lastLoginAt",title:"Last Login",render:e=>e?t.jsx("div",{className:"text-sm",children:(0,S.Q)(new Date(e),{addSuffix:!0})}):t.jsx("span",{className:"text-muted-foreground text-sm",children:"Never"})},{key:"actions",title:"Actions",render:(s,r)=>e?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(c.z,{variant:"outline",size:"sm",asChild:!0,children:t.jsx(k.default,{href:`/dashboard/users/registered/${r.id}`,children:t.jsx(g.Z,{className:"h-3 w-3"})})}),(0,t.jsxs)(u.h_,{children:[t.jsx(u.$F,{asChild:!0,children:t.jsx(c.z,{variant:"outline",size:"sm",children:t.jsx(f.Z,{className:"h-3 w-3"})})}),(0,t.jsxs)(u.AW,{align:"end",children:[t.jsx(u.Xi,{asChild:!0,children:(0,t.jsxs)(k.default,{href:`/dashboard/users/registered/${r.id}`,children:[t.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),"free"===r.tier&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(u.Xi,{onClick:()=>F("upgrade",r,"premium"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Premium"]}),(0,t.jsxs)(u.Xi,{onClick:()=>F("upgrade",r,"enterprise"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Enterprise"]})]}),"premium"===r.tier&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(u.Xi,{onClick:()=>F("upgrade",r,"enterprise"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Enterprise"]}),(0,t.jsxs)(u.Xi,{onClick:()=>F("downgrade",r,"free"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Free"]})]}),"enterprise"===r.tier&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(u.Xi,{onClick:()=>F("downgrade",r,"premium"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Premium"]}),(0,t.jsxs)(u.Xi,{onClick:()=>F("downgrade",r,"free"),children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Free"]})]}),r.isActive?(0,t.jsxs)(u.Xi,{onClick:()=>F("suspend",r),className:"text-red-600",children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Suspend User"]}):(0,t.jsxs)(u.Xi,{onClick:()=>F("reactivate",r),className:"text-green-600",children:[t.jsx(N.Z,{className:"mr-2 h-4 w-4"}),"Reactivate User"]})]})]})]}):t.jsx(c.z,{variant:"outline",size:"sm",asChild:!0,children:t.jsx(k.default,{href:`/dashboard/users/registered/${r.id}`,children:"View"})})}];return e?(0,t.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[t.jsx("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold",children:"Registered Users"}),t.jsx("p",{className:"text-muted-foreground",children:"Manage API consumers and their subscriptions"})]})}),E&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(n.ll,{className:"text-sm font-medium",children:"Total Users"}),t.jsx(w.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:E.totalUsers.toLocaleString()}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[E.newUsersThisMonth," new this month"]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(n.ll,{className:"text-sm font-medium",children:"Active Users"}),t.jsx(b.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:E.activeUsers.toLocaleString()}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(E.activeUsers/E.totalUsers*100),"% of total"]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(n.ll,{className:"text-sm font-medium",children:"API Calls"}),t.jsx(v.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:E.apiUsageStats.totalCallsThisMonth.toLocaleString()}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Avg ",E.apiUsageStats.averageCallsPerUser,"/user"]})]})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(n.ll,{className:"text-sm font-medium",children:"Revenue"}),t.jsx(A.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:["$",E.revenueMetrics.monthlyRecurringRevenue.toLocaleString()]}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Monthly recurring"})]})]})]}),T&&T.length>0&&(0,t.jsxs)(n.Zb,{className:"border-yellow-200 bg-yellow-50",children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{className:"flex items-center text-yellow-800",children:[t.jsx(C.Z,{className:"mr-2 h-5 w-5"}),"Users Approaching API Limits"]}),(0,t.jsxs)(n.SZ,{className:"text-yellow-700",children:[T.length," users are approaching their API usage limits and may need attention."]})]}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[T.slice(0,10).map(e=>t.jsx(d.C,{variant:"outline",className:"text-yellow-800 border-yellow-300",children:e.username},e.id)),T.length>10&&(0,t.jsxs)(d.C,{variant:"outline",className:"text-yellow-800 border-yellow-300",children:["+",T.length-10," more"]})]})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{children:t.jsx(n.ll,{children:"Filters"})}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{className:"text-sm font-medium",children:"Search"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(P.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),t.jsx(o.I,{placeholder:"Search users...",value:s.search,onChange:e=>R(e.target.value),className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{className:"text-sm font-medium",children:"Tier"}),(0,t.jsxs)(x.Ph,{value:s.tier||"all",onValueChange:e=>{r(s=>({...s,tier:"all"===e?void 0:e,page:1}))},children:[t.jsx(x.i4,{children:t.jsx(x.ki,{placeholder:"All tiers"})}),(0,t.jsxs)(x.Bw,{children:[t.jsx(x.Ql,{value:"all",children:"All tiers"}),t.jsx(x.Ql,{value:"free",children:"Free"}),t.jsx(x.Ql,{value:"premium",children:"Premium"}),t.jsx(x.Ql,{value:"enterprise",children:"Enterprise"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{className:"text-sm font-medium",children:"Status"}),(0,t.jsxs)(x.Ph,{value:void 0===s.isActive?"all":s.isActive?"active":"suspended",onValueChange:e=>{r(s=>({...s,isActive:"all"===e?void 0:"active"===e,page:1}))},children:[t.jsx(x.i4,{children:t.jsx(x.ki,{placeholder:"All statuses"})}),(0,t.jsxs)(x.Bw,{children:[t.jsx(x.Ql,{value:"all",children:"All statuses"}),t.jsx(x.Ql,{value:"active",children:"Active"}),t.jsx(x.Ql,{value:"suspended",children:"Suspended"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{className:"text-sm font-medium",children:"Email Status"}),(0,t.jsxs)(x.Ph,{value:void 0===s.isEmailVerified?"all":s.isEmailVerified?"verified":"unverified",onValueChange:e=>{r(s=>({...s,isEmailVerified:"all"===e?void 0:"verified"===e,page:1}))},children:[t.jsx(x.i4,{children:t.jsx(x.ki,{placeholder:"All statuses"})}),(0,t.jsxs)(x.Bw,{children:[t.jsx(x.Ql,{value:"all",children:"All statuses"}),t.jsx(x.Ql,{value:"verified",children:"Verified"}),t.jsx(x.Ql,{value:"unverified",children:"Unverified"})]})]})]})]})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{children:["Users (",Z?.meta.totalItems||0,")"]}),t.jsx(n.SZ,{children:"Manage registered users and their subscriptions"})]}),t.jsx(n.aY,{children:t.jsx(h.w,{columns:Q,data:Z?.data||[],loading:U,pagination:{page:s.page,limit:s.limit,total:Z?.meta.totalItems||0,onPageChange:e=>r(s=>({...s,page:e})),onLimitChange:e=>r(s=>({...s,limit:e,page:1}))}})})]}),t.jsx(m.aR,{open:L.isOpen,onOpenChange:e=>!e&&O({isOpen:!1,type:null,user:null}),children:(0,t.jsxs)(m._T,{children:[(0,t.jsxs)(m.fY,{children:[(0,t.jsxs)(m.f$,{children:["suspend"===L.type&&"Suspend User","reactivate"===L.type&&"Reactivate User","upgrade"===L.type&&"Upgrade User Tier","downgrade"===L.type&&"Downgrade User Tier"]}),(0,t.jsxs)(m.yT,{children:["suspend"===L.type&&`Are you sure you want to suspend ${L.user?.username}? This will prevent them from accessing the API.`,"reactivate"===L.type&&`Are you sure you want to reactivate ${L.user?.username}? They will regain access to the API.`,"upgrade"===L.type&&`Are you sure you want to upgrade ${L.user?.username} to ${L.newTier} tier? This will increase their API limits.`,"downgrade"===L.type&&`Are you sure you want to downgrade ${L.user?.username} to ${L.newTier} tier? This will reduce their API limits.`]})]}),(0,t.jsxs)(m.xo,{children:[t.jsx(m.le,{children:"Cancel"}),t.jsx(m.OL,{onClick:z,children:"Confirm"})]})]})})]}):t.jsx("div",{className:"container mx-auto py-6",children:t.jsx(n.Zb,{children:t.jsx(n.aY,{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx(w.Z,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Access Denied"}),t.jsx("p",{className:"text-muted-foreground",children:"You don't have permission to manage registered users."})]})})})})}},79770:(e,s,r)=>{"use strict";r.d(s,{OL:()=>h,_T:()=>c,aR:()=>d,f$:()=>x,fY:()=>o,le:()=>p,vW:()=>j,xo:()=>m,yT:()=>u});var t=r(95344),a=r(3729),i=r(81202),l=r(11453),n=r(5094);let d=({open:e,onOpenChange:s,children:r})=>(a.useEffect(()=>{let r=r=>{"Escape"===r.key&&e&&s?.(!1)};return e&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e,s]),e)?(0,i.createPortal)((0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[t.jsx("div",{className:"absolute inset-0 bg-black/50",onClick:()=>s?.(!1)}),t.jsx("div",{className:"relative z-10",children:r})]}),document.body):null,c=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx("div",{ref:a,className:(0,l.cn)("bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6",e),...r,children:s}));c.displayName="AlertDialogContent";let o=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left mb-4",e),...r,children:s}));o.displayName="AlertDialogHeader";let x=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx("h2",{ref:a,className:(0,l.cn)("text-lg font-semibold text-gray-900",e),...r,children:s}));x.displayName="AlertDialogTitle";let u=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx("p",{ref:a,className:(0,l.cn)("text-sm text-gray-600",e),...r,children:s}));u.displayName="AlertDialogDescription";let m=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx("div",{ref:a,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",e),...r,children:s}));m.displayName="AlertDialogFooter";let h=a.forwardRef(({className:e,variant:s="default",children:r,...a},i)=>t.jsx(n.z,{ref:i,className:(0,l.cn)("destructive"===s&&"bg-red-600 hover:bg-red-700 text-white",e),...a,children:r}));h.displayName="AlertDialogAction";let p=a.forwardRef(({className:e,children:s,...r},a)=>t.jsx(n.z,{ref:a,variant:"outline",className:(0,l.cn)("mt-2 sm:mt-0",e),...r,children:s}));p.displayName="AlertDialogCancel";let j=a.forwardRef(({className:e,children:s,asChild:r=!1,...i},l)=>r?a.cloneElement(s,{ref:l,...i}):t.jsx("button",{ref:l,className:e,...i,children:s}));j.displayName="AlertDialogTrigger"},76036:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let t=(0,r(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/registered/page.tsx`),{__esModule:a,$$typeof:i}=t,l=t.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,7966,2348,8512,6317,7833,7022,1231],()=>r(97860));module.exports=t})();