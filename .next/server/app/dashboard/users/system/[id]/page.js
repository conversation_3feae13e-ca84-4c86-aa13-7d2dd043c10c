(()=>{var e={};e.id=1579,e.ids=[1579],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},50330:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["users",{children:["system",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87443)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/page.tsx"],m="/dashboard/users/system/[id]/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/users/system/[id]/page",pathname:"/dashboard/users/system/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66009:(e,s,t)=>{Promise.resolve().then(t.bind(t,49572))},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},71206:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},46327:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},49572:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(95344),r=t(3729),i=t(8428),l=t(63024),n=t(46327),c=t(38271),d=t(23485),o=t(71206),m=t(55794);let u=(0,t(97075).Z)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var x=t(88534),h=t(25545),p=t(5094),y=t(23673),f=t(19591),v=t(14871),g=t(51467),j=t(50909),N=t(79770),b=t(60339),w=t(5921),A=t(36487);function Z(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,b.p)(),{canManageUsers:Z}=(0,A.TE)(),C=e.id,{data:k,isLoading:q,error:T}=w.OL.useGetById(C),{mutate:L,isLoading:S}=w.OL.useDelete(),{data:D=[]}=w.OL.useActivityLogs(C),[P,M]=(0,r.useState)(!1);return q?a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-6",children:[a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[a.jsx("div",{className:"h-64 bg-gray-200 rounded"}),a.jsx("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"h-32 bg-gray-200 rounded"}),a.jsx("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})}):T||!k?a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,a.jsxs)(p.z,{onClick:()=>s.push("/dashboard/users/system"),children:[a.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})}):(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(p.z,{variant:"ghost",size:"sm",onClick:()=>s.push("/dashboard/users/system"),children:[a.jsx(l.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:k.fullName||k.username}),a.jsx("p",{className:"text-gray-600",children:k.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[Z()&&(0,a.jsxs)(p.z,{variant:"outline",onClick:()=>s.push(`/dashboard/users/system/${C}/edit`),children:[a.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Edit"]}),Z()&&(0,a.jsxs)(N.aR,{open:P,onOpenChange:M,children:[a.jsx(N.vW,{asChild:!0,children:(0,a.jsxs)(p.z,{variant:"destructive",children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})}),(0,a.jsxs)(N._T,{children:[(0,a.jsxs)(N.fY,{children:[a.jsx(N.f$,{children:"Are you sure?"}),a.jsx(N.yT,{children:"This action cannot be undone. This will permanently delete the user account and remove all associated data."})]}),(0,a.jsxs)(N.xo,{children:[a.jsx(N.le,{children:"Cancel"}),a.jsx(N.OL,{onClick:()=>{L(Number(C),{onSuccess:()=>{t({title:"User deleted",description:"System user has been successfully deleted."}),s.push("/dashboard/users/system")},onError:e=>{t({title:"Error",description:e?.message||"Failed to delete user.",variant:"destructive"})}})},className:"bg-red-600 hover:bg-red-700",disabled:S,children:S?"Deleting...":"Delete User"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(g.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(g.dr,{children:[a.jsx(g.SP,{value:"overview",children:"Overview"}),a.jsx(g.SP,{value:"permissions",children:"Permissions"}),a.jsx(g.SP,{value:"activity",children:"Activity"})]}),(0,a.jsxs)(g.nU,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)(y.Zb,{children:[a.jsx(y.Ol,{children:(0,a.jsxs)(y.ll,{className:"flex items-center",children:[a.jsx(d.Z,{className:"w-5 h-5 mr-2"}),"Profile Information"]})}),(0,a.jsxs)(y.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(v.qE,{className:"w-16 h-16",children:a.jsx(v.Q5,{children:k.fullName?k.fullName.charAt(0):k.username.charAt(0)})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(f.C,{variant:(e=>{switch(e){case"admin":return"destructive";case"editor":return"default";case"moderator":return"secondary";default:return"outline"}})(k.role),children:k.role.charAt(0).toUpperCase()+k.role.slice(1)}),a.jsx(f.C,{variant:k.isActive?"default":"secondary",children:k.isActive?"Active":"Inactive"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",k.id]})]})]}),a.jsx(j.Z,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(o.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium",children:"Email"})]}),a.jsx("p",{className:"text-sm text-gray-600 ml-6",children:k.email})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(d.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium",children:"Username"})]}),a.jsx("p",{className:"text-sm text-gray-600 ml-6",children:k.username})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium",children:"Last Login"})]}),a.jsx("p",{className:"text-sm text-gray-600 ml-6",children:k.lastLoginAt?new Date(k.lastLoginAt).toLocaleString():"Never"})]})]})]})]}),(0,a.jsxs)(y.Zb,{children:[a.jsx(y.Ol,{children:(0,a.jsxs)(y.ll,{className:"flex items-center",children:[a.jsx(u,{className:"w-5 h-5 mr-2"}),"Account Details"]})}),a.jsx(y.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium",children:"Created At"})]}),a.jsx("p",{className:"text-sm text-gray-600 ml-6",children:new Date(k.createdAt).toLocaleString()})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium",children:"Updated At"})]}),a.jsx("p",{className:"text-sm text-gray-600 ml-6",children:new Date(k.updatedAt).toLocaleString()})]})]})})]})]}),a.jsx(g.nU,{value:"permissions",className:"space-y-6",children:(0,a.jsxs)(y.Zb,{children:[(0,a.jsxs)(y.Ol,{children:[a.jsx(y.ll,{children:"Role Permissions"}),(0,a.jsxs)(y.SZ,{children:["Permissions granted to this user based on their role: ",k.role]})]}),a.jsx(y.aY,{children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(f.C,{variant:"outline",children:"admin"===k.role?"Full System Access":"editor"===k.role?"Content Management":"Content Moderation"})})})})]})}),a.jsx(g.nU,{value:"activity",className:"space-y-6",children:(0,a.jsxs)(y.Zb,{children:[(0,a.jsxs)(y.Ol,{children:[(0,a.jsxs)(y.ll,{className:"flex items-center",children:[a.jsx(x.Z,{className:"w-5 h-5 mr-2"}),"Recent Activity"]}),a.jsx(y.SZ,{children:"Latest actions performed by this user"})]}),a.jsx(y.aY,{children:a.jsx("div",{className:"space-y-4",children:D.length>0?D.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium",children:e.action}),a.jsx("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()}),a.jsx("p",{className:"text-xs text-gray-400",children:e.details})]})]},e.id)):a.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No activity logs available"})})})]})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(y.Zb,{children:[a.jsx(y.Ol,{children:(0,a.jsxs)(y.ll,{className:"flex items-center",children:[a.jsx(h.Z,{className:"w-5 h-5 mr-2"}),"Quick Stats"]})}),(0,a.jsxs)(y.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Days Since Created"}),a.jsx("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(k.createdAt).getTime())/864e5)})]}),k.lastLoginAt&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Days Since Last Login"}),a.jsx("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(k.lastLoginAt).getTime())/864e5)})]})]})]}),Z()&&(0,a.jsxs)(y.Zb,{children:[a.jsx(y.Ol,{children:a.jsx(y.ll,{children:"Quick Actions"})}),(0,a.jsxs)(y.aY,{className:"space-y-2",children:[(0,a.jsxs)(p.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>s.push(`/dashboard/users/system/${C}/edit`),children:[a.jsx(n.Z,{className:"w-4 h-4 mr-2"}),"Edit Profile"]}),(0,a.jsxs)(p.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{t({title:"Coming Soon",description:"Password reset functionality will be implemented."})},children:[a.jsx(u,{className:"w-4 h-4 mr-2"}),"Reset Password"]})]})]})]})]})]})}},79770:(e,s,t)=>{"use strict";t.d(s,{OL:()=>h,_T:()=>d,aR:()=>c,f$:()=>m,fY:()=>o,le:()=>p,vW:()=>y,xo:()=>x,yT:()=>u});var a=t(95344),r=t(3729),i=t(81202),l=t(11453),n=t(5094);let c=({open:e,onOpenChange:s,children:t})=>(r.useEffect(()=>{let t=t=>{"Escape"===t.key&&e&&s?.(!1)};return e&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[e,s]),e)?(0,i.createPortal)((0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"absolute inset-0 bg-black/50",onClick:()=>s?.(!1)}),a.jsx("div",{className:"relative z-10",children:t})]}),document.body):null,d=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6",e),...t,children:s}));d.displayName="AlertDialogContent";let o=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left mb-4",e),...t,children:s}));o.displayName="AlertDialogHeader";let m=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx("h2",{ref:r,className:(0,l.cn)("text-lg font-semibold text-gray-900",e),...t,children:s}));m.displayName="AlertDialogTitle";let u=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx("p",{ref:r,className:(0,l.cn)("text-sm text-gray-600",e),...t,children:s}));u.displayName="AlertDialogDescription";let x=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",e),...t,children:s}));x.displayName="AlertDialogFooter";let h=r.forwardRef(({className:e,variant:s="default",children:t,...r},i)=>a.jsx(n.z,{ref:i,className:(0,l.cn)("destructive"===s&&"bg-red-600 hover:bg-red-700 text-white",e),...r,children:t}));h.displayName="AlertDialogAction";let p=r.forwardRef(({className:e,children:s,...t},r)=>a.jsx(n.z,{ref:r,variant:"outline",className:(0,l.cn)("mt-2 sm:mt-0",e),...t,children:s}));p.displayName="AlertDialogCancel";let y=r.forwardRef(({className:e,children:s,asChild:t=!1,...i},l)=>t?r.cloneElement(s,{ref:l,...i}):a.jsx("button",{ref:l,className:e,...i,children:s}));y.displayName="AlertDialogTrigger"},50909:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),r=t(3729),i=t(62409),l="horizontal",n=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=l,...c}=e,d=n.includes(r)?r:l;return(0,a.jsx)(i.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=t(11453);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>a.jsx(c,{ref:i,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=c.displayName},51467:(e,s,t)=>{"use strict";t.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var a=t(95344),r=t(3729),i=t(89128),l=t(11453);let n=i.fC,c=r.forwardRef(({className:e,...s},t)=>a.jsx(i.aV,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.aV.displayName;let d=r.forwardRef(({className:e,...s},t)=>a.jsx(i.xz,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.xz.displayName;let o=r.forwardRef(({className:e,...s},t)=>a.jsx(i.VY,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=i.VY.displayName},60339:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(34755);let r=()=>({toast:e=>{"destructive"===e.variant?a.toast.error(e.title||e.description||"Error occurred"):a.toast.success(e.title||e.description||"Success")}})},5921:(e,s,t)=>{"use strict";t.d(s,{KX:()=>c,OL:()=>u,SZ:()=>m,aw:()=>o});var a=t(19738),r=t(11494),i=t(14373),l=t(59358),n=t(34755);let c=(e={})=>(0,a.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let t=e.search.toLowerCase();s=s.filter(e=>e.username.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.fullName?.toLowerCase().includes(t))}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let t=e.page||1,a=e.limit||10,r=(t-1)*a;return{data:s.slice(r,r+a),meta:{total:s.length,page:t,limit:a,totalPages:Math.ceil(s.length/a)}}},staleTime:3e5}),d=e=>(0,a.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:`user${e}`,email:`user${e}@fecms-sport.com`,role:"editor",fullName:`User ${e}`,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),o=()=>{let e=(0,r.NL)(),s=(0,i.D)({mutationFn:l.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user created successfully")},onError:e=>{n.toast.error(`Failed to create user: ${e.message}`)}});return{createUser:s,updateUser:(0,i.D)({mutationFn:({id:e,data:s})=>l.i.updateUser(e,s),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.id]}),n.toast.success("System user updated successfully")},onError:e=>{n.toast.error(`Failed to update user: ${e.message}`)}}),deleteUser:(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user deleted successfully")},onError:e=>{n.toast.error(`Failed to delete user: ${e.message}`)}}),toggleUserStatus:(0,i.D)({mutationFn:async({userId:e,isActive:s})=>l.i.updateUser(e,{isActive:s}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.userId]}),n.toast.success(`User ${t.isActive?"activated":"deactivated"} successfully`)},onError:e=>{n.toast.error(`Failed to update user status: ${e.message}`)}})}},m=()=>(0,a.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),u={useSystemUsers:c,useGetById:e=>d(e),useCreate:()=>{let{createUser:e}=o();return e},useUpdate:()=>{let{updateUser:e}=o();return e},useDelete:()=>{let{deleteUser:e}=o();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=o();return e},useStats:m,useActivityLogs:e=>(0,a.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}},87443:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},89128:(e,s,t)=>{"use strict";t.d(s,{VY:()=>S,aV:()=>T,fC:()=>q,xz:()=>L});var a=t(3729),r=t(85222),i=t(98462),l=t(34504),n=t(43234),c=t(62409),d=t(3975),o=t(33183),m=t(99048),u=t(95344),x="Tabs",[h,p]=(0,i.b)(x,[l.Pc]),y=(0,l.Pc)(),[f,v]=h(x),g=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:h="automatic",...p}=e,y=(0,d.gm)(n),[v,g]=(0,o.T)({prop:a,onChange:r,defaultProp:i??"",caller:x});return(0,u.jsx)(f,{scope:t,baseId:(0,m.M)(),value:v,onValueChange:g,orientation:l,dir:y,activationMode:h,children:(0,u.jsx)(c.WV.div,{dir:y,"data-orientation":l,...p,ref:s})})});g.displayName=x;var j="TabsList",N=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,i=v(j,t),n=y(t);return(0,u.jsx)(l.fC,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:a,children:(0,u.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});N.displayName=j;var b="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:i=!1,...n}=e,d=v(b,t),o=y(t),m=C(d.baseId,a),x=k(d.baseId,a),h=a===d.value;return(0,u.jsx)(l.ck,{asChild:!0,...o,focusable:!i,active:h,children:(0,u.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":x,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...n,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||i||!e||d.onValueChange(a)})})})});w.displayName=b;var A="TabsContent",Z=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:i,children:l,...d}=e,o=v(A,t),m=C(o.baseId,r),x=k(o.baseId,r),h=r===o.value,p=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(n.z,{present:i||h,children:({present:t})=>(0,u.jsx)(c.WV.div,{"data-state":h?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:x,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&l})})});function C(e,s){return`${e}-trigger-${s}`}function k(e,s){return`${e}-content-${s}`}Z.displayName=A;var q=g,T=N,L=w,S=Z}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>t(50330));module.exports=a})();