(()=>{var e={};e.id=13,e.ids=[13],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},30221:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o=["",{children:["dashboard",{children:["users",{children:["system",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,48952)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/edit/page.tsx"],u="/dashboard/users/system/[id]/edit/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/users/system/[id]/edit/page",pathname:"/dashboard/users/system/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6124:(e,s,t)=>{Promise.resolve().then(t.bind(t,98710))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},98710:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(95344),r=t(3729),i=t(8428),l=t(60708),n=t(85453),d=t(3389),o=t(63024),c=t(31498),u=t(18822),m=t(23485),p=t(5094),h=t(23673),x=t(46540),f=t(7361),y=t(38157),j=t(13611),g=t(50909),v=t(60339),N=t(5921),b=t(36487);let w=d.z.object({username:d.z.string().min(3,"Username must be at least 3 characters"),email:d.z.string().email("Invalid email address"),fullName:d.z.string().min(1,"Full name is required"),role:d.z.enum(["admin","editor","moderator"],{required_error:"Role is required"}),isActive:d.z.boolean()});function A(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,v.p)(),{canManageUsers:d}=(0,b.TE)(),A=e.id,{data:S,isLoading:Z,error:k}=N.OL.useGetById(A),{mutate:q,isLoading:C}=N.OL.useUpdate(),[P,T]=(0,r.useState)(!1),_=(0,l.cI)({resolver:(0,n.F)(w),defaultValues:{username:"",email:"",fullName:"",role:"editor",isActive:!0}});(0,r.useEffect)(()=>{S&&_.reset({username:S.username||"",email:S.email||"",fullName:S.fullName||"",role:S.role||"editor",isActive:S.isActive})},[S,_]),(0,r.useEffect)(()=>{let e=_.watch(()=>{T(!0)});return()=>e.unsubscribe()},[_]);let z=e=>{q({id:Number(A),data:e},{onSuccess:()=>{t({title:"User updated",description:"System user has been successfully updated."}),T(!1),s.push(`/dashboard/users/system/${A}`)},onError:e=>{t({title:"Error",description:e?.message||"Failed to update user.",variant:"destructive"})}})},F=()=>{P?confirm("You have unsaved changes. Are you sure you want to leave?")&&s.push(`/dashboard/users/system/${A}`):s.push(`/dashboard/users/system/${A}`)};return d()?Z?a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-6",children:[a.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"h-64 bg-gray-200 rounded"}),a.jsx("div",{className:"h-48 bg-gray-200 rounded"})]})]})}):k||!S?a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,a.jsxs)(p.z,{onClick:()=>s.push("/dashboard/users/system"),children:[a.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})}):(0,a.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(p.z,{variant:"ghost",size:"sm",onClick:F,children:[a.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit User"}),a.jsx("p",{className:"text-gray-600",children:S.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(p.z,{variant:"outline",onClick:F,children:"Cancel"}),(0,a.jsxs)(p.z,{onClick:_.handleSubmit(z),disabled:C||!P,children:[a.jsx(c.Z,{className:"w-4 h-4 mr-2"}),C?"Saving...":"Save Changes"]})]})]}),a.jsx("form",{onSubmit:_.handleSubmit(z),className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center",children:[a.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"Basic Information"]}),a.jsx(h.SZ,{children:"Update the user's personal information"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"fullName",children:"Full Name *"}),a.jsx(x.I,{id:"fullName",..._.register("fullName")}),_.formState.errors.fullName&&a.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.fullName.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"username",children:"Username *"}),a.jsx(x.I,{id:"username",..._.register("username")}),_.formState.errors.username&&a.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.username.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"email",children:"Email Address *"}),a.jsx(x.I,{id:"email",type:"email",..._.register("email")}),_.formState.errors.email&&a.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.email.message})]})]})]}),(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center",children:[a.jsx(m.Z,{className:"w-5 h-5 mr-2"}),"Role & Permissions"]}),a.jsx(h.SZ,{children:"Configure the user's role and access level"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"role",children:"Role *"}),(0,a.jsxs)(y.Ph,{value:_.watch("role"),onValueChange:e=>_.setValue("role",e),children:[a.jsx(y.i4,{children:a.jsx(y.ki,{placeholder:"Select a role"})}),(0,a.jsxs)(y.Bw,{children:[a.jsx(y.Ql,{value:"admin",children:"Administrator"}),a.jsx(y.Ql,{value:"editor",children:"Editor"}),a.jsx(y.Ql,{value:"moderator",children:"Moderator"})]})]}),_.formState.errors.role&&a.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.role.message})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(g.Z,{}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("h4",{className:"text-sm font-medium",children:"Role Permissions"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["admin"===_.watch("role")&&(0,a.jsxs)("ul",{className:"space-y-1",children:[a.jsx("li",{children:"• Full system access"}),a.jsx("li",{children:"• User management"}),a.jsx("li",{children:"• System configuration"}),a.jsx("li",{children:"• All content operations"})]}),"editor"===_.watch("role")&&(0,a.jsxs)("ul",{className:"space-y-1",children:[a.jsx("li",{children:"• Content creation and editing"}),a.jsx("li",{children:"• Media management"}),a.jsx("li",{children:"• Limited user management"})]}),"moderator"===_.watch("role")&&(0,a.jsxs)("ul",{className:"space-y-1",children:[a.jsx("li",{children:"• Content review and approval"}),a.jsx("li",{children:"• User content moderation"}),a.jsx("li",{children:"• Basic reporting access"})]})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[a.jsx(h.ll,{children:"Account Status"}),a.jsx(h.SZ,{children:"Manage the user's account status"})]}),a.jsx(h.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(f._,{htmlFor:"isActive",className:"text-sm font-medium",children:"Account Active"}),a.jsx(j.r,{id:"isActive",checked:_.watch("isActive"),onCheckedChange:e=>_.setValue("isActive",e)})]})})]}),(0,a.jsxs)(h.Zb,{children:[a.jsx(h.Ol,{children:a.jsx(h.ll,{children:"Account Information"})}),(0,a.jsxs)(h.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"User ID"}),a.jsx("span",{className:"font-medium",children:S.id})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Created"}),a.jsx("span",{className:"font-medium",children:new Date(S.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Last Updated"}),a.jsx("span",{className:"font-medium",children:new Date(S.updatedAt).toLocaleDateString()})]}),S.lastLoginAt&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Last Login"}),a.jsx("span",{className:"font-medium",children:new Date(S.lastLoginAt).toLocaleDateString()})]})]})]}),(0,a.jsxs)(h.Zb,{children:[a.jsx(h.Ol,{children:a.jsx(h.ll,{children:"Security Actions"})}),(0,a.jsxs)(h.aY,{className:"space-y-2",children:[a.jsx(p.z,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{t({title:"Coming Soon",description:"Password reset functionality will be implemented."})},children:"Reset Password"}),a.jsx(p.z,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{t({title:"Coming Soon",description:"Force logout functionality will be implemented."})},children:"Force Logout"})]})]})]})]})})]}):a.jsx("div",{className:"container mx-auto p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"You don't have permission to edit users."}),(0,a.jsxs)(p.z,{onClick:()=>s.push("/dashboard/users/system"),children:[a.jsx(o.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})})}},7361:(e,s,t)=>{"use strict";t.d(s,{_:()=>o});var a=t(95344),r=t(3729),i=t(14217),l=t(49247),n=t(11453);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...s},t)=>a.jsx(i.f,{ref:t,className:(0,n.cn)(d(),e),...s}));o.displayName=i.f.displayName},38157:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>x,Ph:()=>c,Ql:()=>f,i4:()=>m,ki:()=>u});var a=t(95344),r=t(3729),i=t(32116),l=t(25390),n=t(12704),d=t(62312),o=t(11453);let c=i.fC;i.ZA;let u=i.B4,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.xz,{ref:r,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,a.jsx(i.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let p=r.forwardRef(({className:e,...s},t)=>a.jsx(i.u_,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=i.u_.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(i.$G,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:a.jsx(l.Z,{className:"h-4 w-4"})}));h.displayName=i.$G.displayName;let x=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(i.h_,{children:(0,a.jsxs)(i.VY,{ref:l,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(p,{}),a.jsx(i.l_,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),a.jsx(h,{})]})}));x.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.__,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let f=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(i.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(i.wU,{children:a.jsx(d.Z,{className:"h-4 w-4"})})}),a.jsx(i.eT,{children:s})]}));f.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(i.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},50909:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var a=t(95344),r=t(3729),i=t(62409),l="horizontal",n=["horizontal","vertical"],d=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=l,...d}=e,o=n.includes(r)?r:l;return(0,a.jsx)(i.WV.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...d,ref:s})});d.displayName="Separator";var o=t(11453);let c=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>a.jsx(d,{ref:i,decorative:t,orientation:s,className:(0,o.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=d.displayName},13611:(e,s,t)=>{"use strict";t.d(s,{r:()=>n});var a=t(95344),r=t(3729),i=t(19655),l=t(11453);let n=r.forwardRef(({className:e,...s},t)=>a.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:a.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},60339:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(34755);let r=()=>({toast:e=>{"destructive"===e.variant?a.toast.error(e.title||e.description||"Error occurred"):a.toast.success(e.title||e.description||"Success")}})},5921:(e,s,t)=>{"use strict";t.d(s,{KX:()=>d,OL:()=>m,SZ:()=>u,aw:()=>c});var a=t(19738),r=t(11494),i=t(14373),l=t(59358),n=t(34755);let d=(e={})=>(0,a.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let t=e.search.toLowerCase();s=s.filter(e=>e.username.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.fullName?.toLowerCase().includes(t))}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let t=e.page||1,a=e.limit||10,r=(t-1)*a;return{data:s.slice(r,r+a),meta:{total:s.length,page:t,limit:a,totalPages:Math.ceil(s.length/a)}}},staleTime:3e5}),o=e=>(0,a.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:`user${e}`,email:`user${e}@fecms-sport.com`,role:"editor",fullName:`User ${e}`,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),c=()=>{let e=(0,r.NL)(),s=(0,i.D)({mutationFn:l.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user created successfully")},onError:e=>{n.toast.error(`Failed to create user: ${e.message}`)}});return{createUser:s,updateUser:(0,i.D)({mutationFn:({id:e,data:s})=>l.i.updateUser(e,s),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.id]}),n.toast.success("System user updated successfully")},onError:e=>{n.toast.error(`Failed to update user: ${e.message}`)}}),deleteUser:(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user deleted successfully")},onError:e=>{n.toast.error(`Failed to delete user: ${e.message}`)}}),toggleUserStatus:(0,i.D)({mutationFn:async({userId:e,isActive:s})=>l.i.updateUser(e,{isActive:s}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.userId]}),n.toast.success(`User ${t.isActive?"activated":"deactivated"} successfully`)},onError:e=>{n.toast.error(`Failed to update user status: ${e.message}`)}})}},u=()=>(0,a.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),m={useSystemUsers:d,useGetById:e=>o(e),useCreate:()=>{let{createUser:e}=c();return e},useUpdate:()=>{let{updateUser:e}=c();return e},useDelete:()=>{let{deleteUser:e}=c();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=c();return e},useStats:u,useActivityLogs:e=>(0,a.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}},48952:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/system/[id]/edit/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},19655:(e,s,t)=>{"use strict";t.d(s,{bU:()=>w,fC:()=>b});var a=t(3729),r=t(85222),i=t(31405),l=t(98462),n=t(33183),d=t(92062),o=t(63085),c=t(62409),u=t(95344),m="Switch",[p,h]=(0,l.b)(m),[x,f]=p(m),y=a.forwardRef((e,s)=>{let{__scopeSwitch:t,name:l,checked:d,defaultChecked:o,required:p,disabled:h,value:f="on",onCheckedChange:y,form:j,...g}=e,[b,w]=a.useState(null),A=(0,i.e)(s,e=>w(e)),S=a.useRef(!1),Z=!b||j||!!b.closest("form"),[k,q]=(0,n.T)({prop:d,defaultProp:o??!1,onChange:y,caller:m});return(0,u.jsxs)(x,{scope:t,checked:k,disabled:h,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":k,"aria-required":p,"data-state":N(k),"data-disabled":h?"":void 0,disabled:h,value:f,...g,ref:A,onClick:(0,r.M)(e.onClick,e=>{q(e=>!e),Z&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),Z&&(0,u.jsx)(v,{control:b,bubbles:!S.current,name:l,value:f,checked:k,required:p,disabled:h,form:j,style:{transform:"translateX(-100%)"}})]})});y.displayName=m;var j="SwitchThumb",g=a.forwardRef((e,s)=>{let{__scopeSwitch:t,...a}=e,r=f(j,t);return(0,u.jsx)(c.WV.span,{"data-state":N(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:s})});g.displayName=j;var v=a.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:r=!0,...l},n)=>{let c=a.useRef(null),m=(0,i.e)(c,n),p=(0,d.D)(t),h=(0,o.t)(s);return a.useEffect(()=>{let e=c.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==t&&s){let a=new Event("click",{bubbles:r});s.call(e,t),e.dispatchEvent(a)}},[p,t,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:m,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=y,w=g}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,7966,4932,6317,7833],()=>t(30221));module.exports=a})();