(()=>{var e={};e.id=6184,e.ids=[6184],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},4059:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(50482),a=t(69108),i=t(62563),l=t.n(i),n=t(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["dashboard",{children:["users",{children:["system",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2901)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/system/create/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/users/system/create/page.tsx"],m="/dashboard/users/system/create/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/users/system/create/page",pathname:"/dashboard/users/system/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51380:(e,s,t)=>{Promise.resolve().then(t.bind(t,76300))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},76300:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var r=t(95344),a=t(3729),i=t(8428),l=t(60708),n=t(85453),o=t(3389),c=t(63024),d=t(31498),m=t(18822),u=t(23485),p=t(1222),h=t(53148),x=t(5094),f=t(23673),y=t(46540),j=t(7361),g=t(38157),v=t(13611),N=t(50909),b=t(60339),w=t(5921),S=t(36487);let A=o.z.object({username:o.z.string().min(3,"Username must be at least 3 characters"),email:o.z.string().email("Invalid email address"),fullName:o.z.string().min(1,"Full name is required"),password:o.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:o.z.string().min(8,"Please confirm your password"),role:o.z.enum(["admin","editor","moderator"],{required_error:"Role is required"}),isActive:o.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function Z(){let e=(0,i.useRouter)(),{toast:s}=(0,b.p)(),{canManageUsers:t}=(0,S.TE)(),{mutate:o,isLoading:Z}=w.OL.useCreate(),[k,q]=(0,a.useState)(!1),[C,P]=(0,a.useState)(!1),_=(0,l.cI)({resolver:(0,n.F)(A),defaultValues:{username:"",email:"",fullName:"",password:"",confirmPassword:"",role:"editor",isActive:!0}}),z=t=>{let{confirmPassword:r,...a}=t;o(a,{onSuccess:t=>{s({title:"User created",description:`System user ${t.fullName||t.username} has been successfully created.`}),e.push("/dashboard/users/system")},onError:e=>{s({title:"Error",description:e?.message||"Failed to create user.",variant:"destructive"})}})},T=()=>{e.push("/dashboard/users/system")};return t()?(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(x.z,{variant:"ghost",size:"sm",onClick:T,children:[r.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Create System User"}),r.jsx("p",{className:"text-gray-600",children:"Add a new administrator, editor, or moderator account"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(x.z,{variant:"outline",onClick:T,children:"Cancel"}),(0,r.jsxs)(x.z,{onClick:_.handleSubmit(z),disabled:Z,children:[r.jsx(d.Z,{className:"w-4 h-4 mr-2"}),Z?"Creating...":"Create User"]})]})]}),r.jsx("form",{onSubmit:_.handleSubmit(z),className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[r.jsx(m.Z,{className:"w-5 h-5 mr-2"}),"Basic Information"]}),r.jsx(f.SZ,{children:"Enter the user's personal information"})]}),(0,r.jsxs)(f.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"fullName",children:"Full Name *"}),r.jsx(y.I,{id:"fullName",..._.register("fullName")}),_.formState.errors.fullName&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.fullName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"username",children:"Username *"}),r.jsx(y.I,{id:"username",..._.register("username"),placeholder:"Enter a unique username"}),_.formState.errors.username&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.username.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"email",children:"Email Address *"}),r.jsx(y.I,{id:"email",type:"email",..._.register("email"),placeholder:"<EMAIL>"}),_.formState.errors.email&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.email.message})]})]})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[r.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"Security"]}),r.jsx(f.SZ,{children:"Set up the user's login credentials"})]}),(0,r.jsxs)(f.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"password",children:"Password *"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(y.I,{id:"password",type:k?"text":"password",..._.register("password"),placeholder:"Enter a strong password",className:"pr-10"}),r.jsx(x.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>q(!k),children:k?r.jsx(p.Z,{className:"h-4 w-4 text-gray-400"}):r.jsx(h.Z,{className:"h-4 w-4 text-gray-400"})})]}),_.formState.errors.password&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.password.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"confirmPassword",children:"Confirm Password *"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(y.I,{id:"confirmPassword",type:C?"text":"password",..._.register("confirmPassword"),placeholder:"Confirm the password",className:"pr-10"}),r.jsx(x.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>P(!C),children:C?r.jsx(p.Z,{className:"h-4 w-4 text-gray-400"}):r.jsx(h.Z,{className:"h-4 w-4 text-gray-400"})})]}),_.formState.errors.confirmPassword&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.confirmPassword.message})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 mt-2",children:[r.jsx("p",{className:"font-medium mb-1",children:"Password requirements:"}),(0,r.jsxs)("ul",{className:"space-y-1 text-xs",children:[r.jsx("li",{children:"• At least 8 characters long"}),r.jsx("li",{children:"• Should include uppercase and lowercase letters"}),r.jsx("li",{children:"• Should include numbers and special characters"})]})]})]})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[r.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"Role & Permissions"]}),r.jsx(f.SZ,{children:"Configure the user's role and access level"})]}),(0,r.jsxs)(f.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(j._,{htmlFor:"role",children:"Role *"}),(0,r.jsxs)(g.Ph,{value:_.watch("role"),onValueChange:e=>_.setValue("role",e),children:[r.jsx(g.i4,{children:r.jsx(g.ki,{placeholder:"Select a role"})}),(0,r.jsxs)(g.Bw,{children:[r.jsx(g.Ql,{value:"admin",children:"Administrator"}),r.jsx(g.Ql,{value:"editor",children:"Editor"}),r.jsx(g.Ql,{value:"moderator",children:"Moderator"})]})]}),_.formState.errors.role&&r.jsx("p",{className:"text-sm text-red-600",children:_.formState.errors.role.message})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(N.Z,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-medium",children:"Role Permissions"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["admin"===_.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[r.jsx("li",{children:"• Full system access"}),r.jsx("li",{children:"• User management"}),r.jsx("li",{children:"• System configuration"}),r.jsx("li",{children:"• All content operations"})]}),"editor"===_.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[r.jsx("li",{children:"• Content creation and editing"}),r.jsx("li",{children:"• Media management"}),r.jsx("li",{children:"• Limited user management"})]}),"moderator"===_.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[r.jsx("li",{children:"• Content review and approval"}),r.jsx("li",{children:"• User content moderation"}),r.jsx("li",{children:"• Basic reporting access"})]})]})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[r.jsx(f.ll,{children:"Account Status"}),r.jsx(f.SZ,{children:"Configure the initial account status"})]}),(0,r.jsxs)(f.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(j._,{htmlFor:"isActive",className:"text-sm font-medium",children:"Account Active"}),r.jsx(v.r,{id:"isActive",checked:_.watch("isActive"),onCheckedChange:e=>_.setValue("isActive",e)})]}),r.jsx("p",{className:"text-xs text-gray-600",children:"Active accounts can log in immediately. Inactive accounts will need to be activated later."})]})]}),(0,r.jsxs)(f.Zb,{children:[r.jsx(f.Ol,{children:r.jsx(f.ll,{children:"Instructions"})}),r.jsx(f.aY,{className:"space-y-3",children:(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[r.jsx("h4",{className:"font-medium mb-2",children:"After creating the user:"}),(0,r.jsxs)("ul",{className:"space-y-1 text-xs",children:[r.jsx("li",{children:"• The user will receive an email with their login credentials"}),r.jsx("li",{children:"• They will be prompted to change their password on first login"}),r.jsx("li",{children:"• You can edit their details or deactivate the account anytime"}),r.jsx("li",{children:"• Role permissions take effect immediately"})]})]})})]}),(0,r.jsxs)(f.Zb,{children:[r.jsx(f.Ol,{children:r.jsx(f.ll,{children:"Security Notice"})}),r.jsx(f.aY,{children:r.jsx("p",{className:"text-xs text-amber-600",children:"⚠️ Only create administrator accounts for trusted personnel. Admin users have full system access including the ability to modify other users and system settings."})})]})]})]})})]}):r.jsx("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"You don't have permission to create users."}),(0,r.jsxs)(x.z,{onClick:()=>e.push("/dashboard/users/system"),children:[r.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})})}},7361:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var r=t(95344),a=t(3729),i=t(14217),l=t(49247),n=t(11453);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>r.jsx(i.f,{ref:t,className:(0,n.cn)(o(),e),...s}));c.displayName=i.f.displayName},38157:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>x,Ph:()=>d,Ql:()=>f,i4:()=>u,ki:()=>m});var r=t(95344),a=t(3729),i=t(32116),l=t(25390),n=t(12704),o=t(62312),c=t(11453);let d=i.fC;i.ZA;let m=i.B4,u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.xz,{ref:a,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,r.jsx(i.JO,{asChild:!0,children:r.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.xz.displayName;let p=a.forwardRef(({className:e,...s},t)=>r.jsx(i.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=i.u_.displayName;let h=a.forwardRef(({className:e,...s},t)=>r.jsx(i.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(l.Z,{className:"h-4 w-4"})}));h.displayName=i.$G.displayName;let x=a.forwardRef(({className:e,children:s,position:t="popper",...a},l)=>r.jsx(i.h_,{children:(0,r.jsxs)(i.VY,{ref:l,className:(0,c.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[r.jsx(p,{}),r.jsx(i.l_,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),r.jsx(h,{})]})}));x.displayName=i.VY.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.__,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let f=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.ck,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:s})]}));f.displayName=i.ck.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},50909:(e,s,t)=>{"use strict";t.d(s,{Z:()=>d});var r=t(95344),a=t(3729),i=t(62409),l="horizontal",n=["horizontal","vertical"],o=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=l,...o}=e,c=n.includes(a)?a:l;return(0,r.jsx)(i.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:s})});o.displayName="Separator";var c=t(11453);let d=a.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...a},i)=>r.jsx(o,{ref:i,decorative:t,orientation:s,className:(0,c.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...a}));d.displayName=o.displayName},13611:(e,s,t)=>{"use strict";t.d(s,{r:()=>n});var r=t(95344),a=t(3729),i=t(19655),l=t(11453);let n=a.forwardRef(({className:e,...s},t)=>r.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:r.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},60339:(e,s,t)=>{"use strict";t.d(s,{p:()=>a});var r=t(34755);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},5921:(e,s,t)=>{"use strict";t.d(s,{KX:()=>o,OL:()=>u,SZ:()=>m,aw:()=>d});var r=t(19738),a=t(11494),i=t(14373),l=t(59358),n=t(34755);let o=(e={})=>(0,r.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let t=e.search.toLowerCase();s=s.filter(e=>e.username.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.fullName?.toLowerCase().includes(t))}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let t=e.page||1,r=e.limit||10,a=(t-1)*r;return{data:s.slice(a,a+r),meta:{total:s.length,page:t,limit:r,totalPages:Math.ceil(s.length/r)}}},staleTime:3e5}),c=e=>(0,r.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:`user${e}`,email:`user${e}@fecms-sport.com`,role:"editor",fullName:`User ${e}`,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),d=()=>{let e=(0,a.NL)(),s=(0,i.D)({mutationFn:l.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user created successfully")},onError:e=>{n.toast.error(`Failed to create user: ${e.message}`)}});return{createUser:s,updateUser:(0,i.D)({mutationFn:({id:e,data:s})=>l.i.updateUser(e,s),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.id]}),n.toast.success("System user updated successfully")},onError:e=>{n.toast.error(`Failed to update user: ${e.message}`)}}),deleteUser:(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user deleted successfully")},onError:e=>{n.toast.error(`Failed to delete user: ${e.message}`)}}),toggleUserStatus:(0,i.D)({mutationFn:async({userId:e,isActive:s})=>l.i.updateUser(e,{isActive:s}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.userId]}),n.toast.success(`User ${t.isActive?"activated":"deactivated"} successfully`)},onError:e=>{n.toast.error(`Failed to update user status: ${e.message}`)}})}},m=()=>(0,r.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),u={useSystemUsers:o,useGetById:e=>c(e),useCreate:()=>{let{createUser:e}=d();return e},useUpdate:()=>{let{updateUser:e}=d();return e},useDelete:()=>{let{deleteUser:e}=d();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=d();return e},useStats:m,useActivityLogs:e=>(0,r.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}},2901:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/system/create/page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default},19655:(e,s,t)=>{"use strict";t.d(s,{bU:()=>w,fC:()=>b});var r=t(3729),a=t(85222),i=t(31405),l=t(98462),n=t(33183),o=t(92062),c=t(63085),d=t(62409),m=t(95344),u="Switch",[p,h]=(0,l.b)(u),[x,f]=p(u),y=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:l,checked:o,defaultChecked:c,required:p,disabled:h,value:f="on",onCheckedChange:y,form:j,...g}=e,[b,w]=r.useState(null),S=(0,i.e)(s,e=>w(e)),A=r.useRef(!1),Z=!b||j||!!b.closest("form"),[k,q]=(0,n.T)({prop:o,defaultProp:c??!1,onChange:y,caller:u});return(0,m.jsxs)(x,{scope:t,checked:k,disabled:h,children:[(0,m.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":k,"aria-required":p,"data-state":N(k),"data-disabled":h?"":void 0,disabled:h,value:f,...g,ref:S,onClick:(0,a.M)(e.onClick,e=>{q(e=>!e),Z&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),Z&&(0,m.jsx)(v,{control:b,bubbles:!A.current,name:l,value:f,checked:k,required:p,disabled:h,form:j,style:{transform:"translateX(-100%)"}})]})});y.displayName=u;var j="SwitchThumb",g=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,a=f(j,t);return(0,m.jsx)(d.WV.span,{"data-state":N(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:s})});g.displayName=j;var v=r.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:a=!0,...l},n)=>{let d=r.useRef(null),u=(0,i.e)(d,n),p=(0,o.D)(t),h=(0,c.t)(s);return r.useEffect(()=>{let e=d.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==t&&s){let r=new Event("click",{bubbles:a});s.call(e,t),e.dispatchEvent(r)}},[p,t,a]),(0,m.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:u,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=y,w=g}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,6126,337,2609,3649,732,7966,4932,6317,7833],()=>t(4059));module.exports=r})();