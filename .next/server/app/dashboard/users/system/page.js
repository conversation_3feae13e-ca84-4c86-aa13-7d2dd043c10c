(()=>{var e={};e.id=5297,e.ids=[5297],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},28590:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(50482),a=t(69108),l=t(62563),i=t.n(l),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["dashboard",{children:["users",{children:["system",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79415)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/system/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/system/page.tsx"],m="/dashboard/users/system/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/users/system/page",pathname:"/dashboard/users/system",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78453:(e,s,t)=>{Promise.resolve().then(t.bind(t,53404))},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},38271:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},53404:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(95344),a=t(3729),l=t(5921),i=t(36487),n=t(77022),d=t(23673),c=t(5094),o=t(46540),m=t(19591),u=t(38157),x=t(47585),h=t(79770),p=t(23485),y=t(46327),f=t(15366),j=t(89895),g=t(32817),v=t(25545),N=t(87957),b=t(38271);let w=(0,t(97075).Z)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var A=t(88534),Z=t(28765),q=t(56506),T=t(63185);function C(){let{canManageUsers:e}=(0,i.TE)(),[s,t]=(0,a.useState)({search:"",role:void 0,isActive:void 0,page:1,limit:10}),{data:C,isLoading:S,error:k}=(0,l.KX)(s),{data:E}=(0,l.SZ)(),{deleteUser:F,toggleUserStatus:L}=(0,l.aw)(),P=e=>{t(s=>({...s,search:e,page:1}))},D=e=>{switch(e){case"admin":return"bg-red-100 text-red-800 border-red-200";case"editor":return"bg-blue-100 text-blue-800 border-blue-200";case"moderator":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},U=e=>{switch(e){case"admin":return r.jsx(p.Z,{className:"h-3 w-3"});case"editor":return r.jsx(y.Z,{className:"h-3 w-3"});case"moderator":return r.jsx(f.Z,{className:"h-3 w-3"});default:return r.jsx(j.Z,{className:"h-3 w-3"})}},M=[{key:"fullName",title:"User",render:(e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-sm font-medium",children:s.fullName?.charAt(0)||s.username.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:s.fullName||s.username}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["@",s.username]})]})]})},{key:"email",title:"Email",render:e=>r.jsx("div",{className:"font-mono text-sm",children:e})},{key:"role",title:"Role",render:e=>(0,r.jsxs)(m.C,{className:`${D(e)} flex items-center space-x-1 w-fit`,children:[U(e),r.jsx("span",{className:"capitalize",children:e})]})},{key:"isActive",title:"Status",render:e=>(0,r.jsxs)(m.C,{variant:e?"default":"secondary",className:"flex items-center space-x-1 w-fit",children:[e?r.jsx(f.Z,{className:"h-3 w-3"}):r.jsx(g.Z,{className:"h-3 w-3"}),r.jsx("span",{children:e?"Active":"Inactive"})]})},{key:"lastLoginAt",title:"Last Login",render:e=>e?(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm",children:[r.jsx(v.Z,{className:"h-3 w-3 text-gray-400"}),r.jsx("span",{children:(0,T.Q)(new Date(e),{addSuffix:!0})})]}):r.jsx("span",{className:"text-gray-400",children:"Never"})},{key:"actions",title:"Actions",render:(s,t)=>e()?(0,r.jsxs)(x.h_,{children:[r.jsx(x.$F,{asChild:!0,children:(0,r.jsxs)(c.z,{variant:"ghost",className:"h-8 w-8 p-0",children:[r.jsx("span",{className:"sr-only",children:"Open menu"}),r.jsx(N.Z,{className:"h-4 w-4"})]})}),(0,r.jsxs)(x.AW,{align:"end",children:[r.jsx(x.Ju,{children:"Actions"}),r.jsx(x.VD,{}),r.jsx(x.Xi,{asChild:!0,children:(0,r.jsxs)(q.default,{href:`/dashboard/users/system/${t.id}`,children:[r.jsx(f.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),r.jsx(x.Xi,{asChild:!0,children:(0,r.jsxs)(q.default,{href:`/dashboard/users/system/${t.id}/edit`,children:[r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Edit User"]})}),r.jsx(x.VD,{}),r.jsx(x.Xi,{onClick:()=>L.mutate({userId:t.id,isActive:!t.isActive}),children:t.isActive?(0,r.jsxs)(r.Fragment,{children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Deactivate"]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(f.Z,{className:"mr-2 h-4 w-4"}),"Activate"]})}),(0,r.jsxs)(h.aR,{children:[r.jsx(h.vW,{asChild:!0,children:(0,r.jsxs)(x.Xi,{onSelect:e=>e.preventDefault(),children:[r.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Delete User"]})}),(0,r.jsxs)(h._T,{children:[(0,r.jsxs)(h.fY,{children:[r.jsx(h.f$,{children:"Are you sure?"}),(0,r.jsxs)(h.yT,{children:["This action cannot be undone. This will permanently delete the user account for ",r.jsx("strong",{children:t.fullName||t.username})," and remove all associated data."]})]}),(0,r.jsxs)(h.xo,{children:[r.jsx(h.le,{children:"Cancel"}),r.jsx(h.OL,{onClick:()=>F.mutate(t.id),className:"bg-red-600 hover:bg-red-700",children:"Delete User"})]})]})]})]})]}):null}];return e()?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"System Users"}),r.jsx("p",{className:"text-gray-600",children:"Manage administrator, editor, and moderator accounts"})]}),r.jsx(c.z,{asChild:!0,children:(0,r.jsxs)(q.default,{href:"/dashboard/users/system/create",children:[r.jsx(w,{className:"mr-2 h-4 w-4"}),"Add System User"]})})]}),E&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"Total Users"}),r.jsx(j.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:E.total}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[E.active," active, ",E.inactive," inactive"]})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"Administrators"}),r.jsx(p.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:E.byRole.admin}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Full system access"})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"Editors"}),r.jsx(y.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:E.byRole.editor}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Content management"})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"Recent Logins"}),r.jsx(A.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:E.recentLogins}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Last 24 hours"})]})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{children:[r.jsx(d.ll,{children:"Filters"}),r.jsx(d.SZ,{children:"Search and filter system users"})]}),r.jsx(d.aY,{children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(Z.Z,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),r.jsx(o.I,{placeholder:"Search by name, username, or email...",value:s.search,onChange:e=>P(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)(u.Ph,{value:s.role,onValueChange:e=>{t(s=>({...s,role:"all"===e?void 0:e,page:1}))},children:[r.jsx(u.i4,{className:"w-full md:w-[180px]",children:r.jsx(u.ki,{placeholder:"Filter by role"})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"all",children:"All Roles"}),r.jsx(u.Ql,{value:"admin",children:"Administrator"}),r.jsx(u.Ql,{value:"editor",children:"Editor"}),r.jsx(u.Ql,{value:"moderator",children:"Moderator"})]})]}),(0,r.jsxs)(u.Ph,{value:void 0===s.isActive?"all":s.isActive?"active":"inactive",onValueChange:e=>{t(s=>({...s,isActive:"all"===e?void 0:"active"===e,page:1}))},children:[r.jsx(u.i4,{className:"w-full md:w-[180px]",children:r.jsx(u.ki,{placeholder:"Filter by status"})}),(0,r.jsxs)(u.Bw,{children:[r.jsx(u.Ql,{value:"all",children:"All Status"}),r.jsx(u.Ql,{value:"active",children:"Active"}),r.jsx(u.Ql,{value:"inactive",children:"Inactive"})]})]})]})})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{children:[r.jsx(d.ll,{children:"System Users"}),r.jsx(d.SZ,{children:C?.meta&&`Showing ${C.data.length} of ${C.meta.total} users`})]}),r.jsx(d.aY,{children:r.jsx(n.w,{columns:M,data:C?.data||[],loading:S,pagination:{page:s.page,limit:s.limit,total:C?.meta?.total||0,onPageChange:e=>t(s=>({...s,page:e})),onLimitChange:e=>t(s=>({...s,limit:e,page:1}))}})})]})]}):r.jsx("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(p.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),r.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Access Denied"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"You don't have permission to view system users."})]})})}},79770:(e,s,t)=>{"use strict";t.d(s,{OL:()=>h,_T:()=>c,aR:()=>d,f$:()=>m,fY:()=>o,le:()=>p,vW:()=>y,xo:()=>x,yT:()=>u});var r=t(95344),a=t(3729),l=t(81202),i=t(11453),n=t(5094);let d=({open:e,onOpenChange:s,children:t})=>(a.useEffect(()=>{let t=t=>{"Escape"===t.key&&e&&s?.(!1)};return e&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[e,s]),e)?(0,l.createPortal)((0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[r.jsx("div",{className:"absolute inset-0 bg-black/50",onClick:()=>s?.(!1)}),r.jsx("div",{className:"relative z-10",children:t})]}),document.body):null,c=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6",e),...t,children:s}));c.displayName="AlertDialogContent";let o=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left mb-4",e),...t,children:s}));o.displayName="AlertDialogHeader";let m=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx("h2",{ref:a,className:(0,i.cn)("text-lg font-semibold text-gray-900",e),...t,children:s}));m.displayName="AlertDialogTitle";let u=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-gray-600",e),...t,children:s}));u.displayName="AlertDialogDescription";let x=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",e),...t,children:s}));x.displayName="AlertDialogFooter";let h=a.forwardRef(({className:e,variant:s="default",children:t,...a},l)=>r.jsx(n.z,{ref:l,className:(0,i.cn)("destructive"===s&&"bg-red-600 hover:bg-red-700 text-white",e),...a,children:t}));h.displayName="AlertDialogAction";let p=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx(n.z,{ref:a,variant:"outline",className:(0,i.cn)("mt-2 sm:mt-0",e),...t,children:s}));p.displayName="AlertDialogCancel";let y=a.forwardRef(({className:e,children:s,asChild:t=!1,...l},i)=>t?a.cloneElement(s,{ref:i,...l}):r.jsx("button",{ref:i,className:e,...l,children:s}));y.displayName="AlertDialogTrigger"},5921:(e,s,t)=>{"use strict";t.d(s,{KX:()=>d,OL:()=>u,SZ:()=>m,aw:()=>o});var r=t(19738),a=t(11494),l=t(14373),i=t(59358),n=t(34755);let d=(e={})=>(0,r.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let t=e.search.toLowerCase();s=s.filter(e=>e.username.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.fullName?.toLowerCase().includes(t))}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let t=e.page||1,r=e.limit||10,a=(t-1)*r;return{data:s.slice(a,a+r),meta:{total:s.length,page:t,limit:r,totalPages:Math.ceil(s.length/r)}}},staleTime:3e5}),c=e=>(0,r.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:`user${e}`,email:`user${e}@fecms-sport.com`,role:"editor",fullName:`User ${e}`,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),o=()=>{let e=(0,a.NL)(),s=(0,l.D)({mutationFn:i.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user created successfully")},onError:e=>{n.toast.error(`Failed to create user: ${e.message}`)}});return{createUser:s,updateUser:(0,l.D)({mutationFn:({id:e,data:s})=>i.i.updateUser(e,s),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.id]}),n.toast.success("System user updated successfully")},onError:e=>{n.toast.error(`Failed to update user: ${e.message}`)}}),deleteUser:(0,l.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),n.toast.success("System user deleted successfully")},onError:e=>{n.toast.error(`Failed to delete user: ${e.message}`)}}),toggleUserStatus:(0,l.D)({mutationFn:async({userId:e,isActive:s})=>i.i.updateUser(e,{isActive:s}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.userId]}),n.toast.success(`User ${t.isActive?"activated":"deactivated"} successfully`)},onError:e=>{n.toast.error(`Failed to update user status: ${e.message}`)}})}},m=()=>(0,r.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),u={useSystemUsers:d,useGetById:e=>c(e),useCreate:()=>{let{createUser:e}=o();return e},useUpdate:()=>{let{updateUser:e}=o();return e},useDelete:()=>{let{deleteUser:e}=o();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=o();return e},useStats:m,useActivityLogs:e=>(0,r.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}},79415:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/system/page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,6126,337,2609,3649,732,7966,2348,8512,6317,7833,7022],()=>t(28590));module.exports=r})();