(()=>{var e={};e.id=2873,e.ids=[2873],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},47894:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var t=a(50482),r=a(69108),i=a(62563),l=a.n(i),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["dashboard",{children:["users",{children:["tiers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80492)),"/home/<USER>/FECMS-sport/src/app/dashboard/users/tiers/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/users/tiers/page.tsx"],x="/dashboard/users/tiers/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/users/tiers/page",pathname:"/dashboard/users/tiers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56250:(e,s,a)=>{Promise.resolve().then(a.bind(a,78430))},88534:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},12594:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},48411:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},18452:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},17910:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},78430:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(95344),r=a(3729),i=a(97075);let l=(0,i.Z)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),n=(0,i.Z)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]);var c=a(18452),d=a(89895),o=a(48411),x=a(17910),m=a(46064);let h=(0,i.Z)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var u=a(12594),p=a(88534),j=a(23673),g=a(51467),f=a(38157),y=a(19591),v=a(47210),N=a(54578);function b(){let[e,s]=(0,r.useState)("30d"),{data:a,isLoading:i}=N.Xn.useTierStats(e),{data:b,isLoading:w}=N.Xn.useRevenueStats(e),{data:Z,isLoading:C}=N.Xn.useTierMigration(e),k={free:"#6B7280",premium:"#3B82F6",enterprise:"#10B981"},P=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),M=e=>`${e>0?"+":""}${e.toFixed(1)}%`,R=e=>e>0?t.jsx(l,{className:"w-4 h-4 text-green-500"}):e<0?t.jsx(n,{className:"w-4 h-4 text-red-500"}):t.jsx(c.Z,{className:"w-4 h-4 text-gray-500"}),T=e=>e>0?"text-green-600":e<0?"text-red-600":"text-gray-600";return i||w||C?t.jsx("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[t.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(e=>t.jsx("div",{className:"h-32 bg-gray-200 rounded"},e))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t.jsx("div",{className:"h-64 bg-gray-200 rounded"}),t.jsx("div",{className:"h-64 bg-gray-200 rounded"})]})]})}):(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"User Tiers Analytics"}),t.jsx("p",{className:"text-gray-600",children:"Monitor subscription tiers, revenue, and user migrations"})]}),t.jsx("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(f.Ph,{value:e,onValueChange:s,children:[t.jsx(f.i4,{className:"w-32",children:t.jsx(f.ki,{})}),(0,t.jsxs)(f.Bw,{children:[t.jsx(f.Ql,{value:"7d",children:"Last 7 days"}),t.jsx(f.Ql,{value:"30d",children:"Last 30 days"}),t.jsx(f.Ql,{value:"90d",children:"Last 90 days"}),t.jsx(f.Ql,{value:"1y",children:"Last year"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[t.jsx(j.Zb,{children:(0,t.jsxs)(j.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),t.jsx("p",{className:"text-2xl font-bold",children:a?.totalUsers?.toLocaleString()||0})]}),t.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:t.jsx(d.Z,{className:"w-6 h-6 text-blue-600"})})]}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[R(a?.totalUsersChange||0),t.jsx("span",{className:`text-sm ml-1 ${T(a?.totalUsersChange||0)}`,children:M(a?.totalUsersChange||0)}),t.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),t.jsx(j.Zb,{children:(0,t.jsxs)(j.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Monthly Revenue"}),t.jsx("p",{className:"text-2xl font-bold",children:P(b?.monthlyRevenue||0)})]}),t.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:t.jsx(o.Z,{className:"w-6 h-6 text-green-600"})})]}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[R(b?.revenueChange||0),t.jsx("span",{className:`text-sm ml-1 ${T(b?.revenueChange||0)}`,children:M(b?.revenueChange||0)}),t.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),t.jsx(j.Zb,{children:(0,t.jsxs)(j.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Avg Revenue per User"}),t.jsx("p",{className:"text-2xl font-bold",children:P(b?.arpu||0)})]}),t.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center",children:t.jsx(x.Z,{className:"w-6 h-6 text-purple-600"})})]}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[R(b?.arpuChange||0),t.jsx("span",{className:`text-sm ml-1 ${T(b?.arpuChange||0)}`,children:M(b?.arpuChange||0)}),t.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),t.jsx(j.Zb,{children:(0,t.jsxs)(j.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:[(a?.conversionRate||0).toFixed(1),"%"]})]}),t.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center",children:t.jsx(m.Z,{className:"w-6 h-6 text-yellow-600"})})]}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[R(a?.conversionRateChange||0),t.jsx("span",{className:`text-sm ml-1 ${T(a?.conversionRateChange||0)}`,children:M(a?.conversionRateChange||0)}),t.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})})]}),(0,t.jsxs)(g.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,t.jsxs)(g.dr,{children:[t.jsx(g.SP,{value:"overview",children:"Overview"}),t.jsx(g.SP,{value:"distribution",children:"Distribution"}),t.jsx(g.SP,{value:"migration",children:"Migration"}),t.jsx(g.SP,{value:"revenue",children:"Revenue"})]}),(0,t.jsxs)(g.nU,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[(0,t.jsxs)(j.ll,{className:"flex items-center",children:[t.jsx(h,{className:"w-5 h-5 mr-2"}),"Tier Distribution"]}),t.jsx(j.SZ,{children:"Current user distribution across tiers"})]}),t.jsx(j.aY,{className:"space-y-4",children:a?.tierDistribution?.map(e=>t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:k[e.tier]}}),t.jsx("span",{className:"text-sm font-medium capitalize",children:e.tier}),t.jsxs(y.C,{variant:"outline",children:[e.count.toLocaleString()," users"]})]}),t.jsxs("span",{className:"text-sm text-gray-600",children:[e.percentage.toFixed(1),"%"]})]}),t.jsx(v.E,{value:e.percentage,className:"h-2"})]},e.tier))||t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No data available"})})]}),(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[(0,t.jsxs)(j.ll,{className:"flex items-center",children:[t.jsx(u.Z,{className:"w-5 h-5 mr-2"}),"Growth Trends"]}),t.jsx(j.SZ,{children:"User growth by tier over time"})]}),t.jsx(j.aY,{children:t.jsx("div",{className:"space-y-4",children:a?.growthTrends?.map(e=>t.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:k[e.tier]}}),t.jsx("span",{className:"font-medium capitalize",children:e.tier})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[R(e.change),t.jsx("span",{className:`font-medium ${T(e.change)}`,children:M(e.change)})]})]},e.tier))||t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No trend data available"})})})]})]}),(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[(0,t.jsxs)(j.ll,{className:"flex items-center",children:[t.jsx(p.Z,{className:"w-5 h-5 mr-2"}),"API Usage by Tier"]}),t.jsx(j.SZ,{children:"Average API usage across different tiers"})]}),t.jsx(j.aY,{children:t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:a?.apiUsageByTier?.map(e=>t.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[t.jsx("div",{className:"w-4 h-4 rounded-full mx-auto mb-2",style:{backgroundColor:k[e.tier]}}),t.jsx("p",{className:"text-sm font-medium capitalize",children:e.tier}),t.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.avgUsage.toLocaleString()}),t.jsx("p",{className:"text-xs text-gray-500",children:"avg calls/month"}),t.jsx("div",{className:"mt-2",children:t.jsx(v.E,{value:e.usagePercentage,className:"h-1"})})]},e.tier))||t.jsx("p",{className:"text-gray-500 italic text-center py-4 col-span-3",children:"No usage data available"})})})]})]}),t.jsx(g.nU,{value:"distribution",className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[t.jsx(j.ll,{children:"Tier Breakdown"}),t.jsx(j.SZ,{children:"Detailed breakdown of users in each tier"})]}),t.jsx(j.aY,{children:t.jsx("div",{className:"space-y-4",children:a?.tierDistribution?.map(e=>t.jsxs("div",{className:"p-4 border rounded-lg",children:[t.jsxs("div",{className:"flex items-center justify-between mb-2",children:[t.jsxs("h3",{className:"font-semibold capitalize",children:[e.tier," Tier"]}),t.jsxs(y.C,{variant:"enterprise"===e.tier?"default":"premium"===e.tier?"secondary":"outline",children:[e.count.toLocaleString()," users"]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Percentage of total"}),t.jsxs("span",{children:[e.percentage.toFixed(1),"%"]})]}),t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Monthly growth"}),t.jsx("span",{className:T(e.monthlyGrowth||0),children:M(e.monthlyGrowth||0)})]}),t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Avg API usage"}),t.jsx("span",{children:(e.avgApiUsage||0).toLocaleString()})]})]})]},e.tier))||t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No distribution data available"})})})]}),(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[t.jsx(j.ll,{children:"Tier Characteristics"}),t.jsx(j.SZ,{children:"Key metrics for each subscription tier"})]}),t.jsx(j.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 border border-green-200 bg-green-50 rounded-lg",children:[t.jsx("h3",{className:"font-semibold text-green-800",children:"Enterprise"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-green-700",children:[t.jsx("p",{children:"• 100,000 API calls/month"}),t.jsx("p",{children:"• $99/month subscription"}),t.jsx("p",{children:"• 24/7 priority support"}),t.jsx("p",{children:"• Custom integrations"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-blue-200 bg-blue-50 rounded-lg",children:[t.jsx("h3",{className:"font-semibold text-blue-800",children:"Premium"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-blue-700",children:[t.jsx("p",{children:"• 10,000 API calls/month"}),t.jsx("p",{children:"• $29/month subscription"}),t.jsx("p",{children:"• Priority email support"}),t.jsx("p",{children:"• Advanced analytics"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-gray-200 bg-gray-50 rounded-lg",children:[t.jsx("h3",{className:"font-semibold text-gray-800",children:"Free"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-gray-700",children:[t.jsx("p",{children:"• 1,000 API calls/month"}),t.jsx("p",{children:"• Free tier"}),t.jsx("p",{children:"• Community support"}),t.jsx("p",{children:"• Basic documentation"})]})]})]})})]})]})}),t.jsx(g.nU,{value:"migration",className:"space-y-6",children:(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[(0,t.jsxs)(j.ll,{className:"flex items-center",children:[t.jsx(m.Z,{className:"w-5 h-5 mr-2"}),"Tier Migration Patterns"]}),t.jsx(j.SZ,{children:"How users move between subscription tiers"})]}),t.jsx(j.aY,{children:t.jsx("div",{className:"space-y-6",children:Z?.migrations?.map(e=>t.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(y.C,{variant:"enterprise"===e.fromTier?"default":"premium"===e.fromTier?"secondary":"outline",children:e.fromTier}),t.jsx("span",{children:"→"}),t.jsx(y.C,{variant:"enterprise"===e.toTier?"default":"premium"===e.toTier?"secondary":"outline",children:e.toTier})]}),t.jsxs("span",{className:"text-sm text-gray-600",children:[e.count," users migrated"]})]}),t.jsxs("div",{className:"text-right",children:[t.jsxs("p",{className:"text-sm font-medium",children:[e.percentage.toFixed(1),"%"]}),t.jsx("p",{className:"text-xs text-gray-500",children:"of total migrations"})]})]},`${e.fromTier}-${e.toTier}`))||t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No migration data available"})})})]})}),t.jsx(g.nU,{value:"revenue",className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[(0,t.jsxs)(j.ll,{className:"flex items-center",children:[t.jsx(o.Z,{className:"w-5 h-5 mr-2"}),"Revenue by Tier"]}),t.jsx(j.SZ,{children:"Revenue contribution from each tier"})]}),t.jsx(j.aY,{className:"space-y-4",children:b?.revenueByTier?.map(e=>t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:k[e.tier]}}),t.jsx("span",{className:"text-sm font-medium capitalize",children:e.tier}),t.jsx(y.C,{variant:"outline",children:P(e.amount)})]}),t.jsxs("span",{className:"text-sm text-gray-600",children:[e.percentage.toFixed(1),"%"]})]}),t.jsx(v.E,{value:e.percentage,className:"h-2"})]},e.tier))||t.jsx("p",{className:"text-gray-500 italic text-center py-4",children:"No revenue data available"})})]}),(0,t.jsxs)(j.Zb,{children:[(0,t.jsxs)(j.Ol,{children:[t.jsx(j.ll,{children:"Revenue Metrics"}),t.jsx(j.SZ,{children:"Key financial performance indicators"})]}),t.jsx(j.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[t.jsx("p",{className:"text-sm text-green-600 font-medium",children:"MRR"}),t.jsx("p",{className:"text-lg font-bold text-green-800",children:P(b?.mrr||0)})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[t.jsx("p",{className:"text-sm text-blue-600 font-medium",children:"ARR"}),t.jsx("p",{className:"text-lg font-bold text-blue-800",children:P(12*(b?.mrr||0))})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[t.jsx("p",{className:"text-sm text-purple-600 font-medium",children:"ARPU"}),t.jsx("p",{className:"text-lg font-bold text-purple-800",children:P(b?.arpu||0)})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[t.jsx("p",{className:"text-sm text-yellow-600 font-medium",children:"Churn Rate"}),(0,t.jsxs)("p",{className:"text-lg font-bold text-yellow-800",children:[(b?.churnRate||0).toFixed(1),"%"]})]})]})})]})]})})]})]})}},38157:(e,s,a)=>{"use strict";a.d(s,{Bw:()=>p,Ph:()=>o,Ql:()=>j,i4:()=>m,ki:()=>x});var t=a(95344),r=a(3729),i=a(32116),l=a(25390),n=a(12704),c=a(62312),d=a(11453);let o=i.fC;i.ZA;let x=i.B4,m=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.xz,{ref:r,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,t.jsx(i.JO,{asChild:!0,children:t.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let h=r.forwardRef(({className:e,...s},a)=>t.jsx(i.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(n.Z,{className:"h-4 w-4"})}));h.displayName=i.u_.displayName;let u=r.forwardRef(({className:e,...s},a)=>t.jsx(i.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:t.jsx(l.Z,{className:"h-4 w-4"})}));u.displayName=i.$G.displayName;let p=r.forwardRef(({className:e,children:s,position:a="popper",...r},l)=>t.jsx(i.h_,{children:(0,t.jsxs)(i.VY,{ref:l,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[t.jsx(h,{}),t.jsx(i.l_,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),t.jsx(u,{})]})}));p.displayName=i.VY.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(i.__,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let j=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.ck,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[t.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(i.wU,{children:t.jsx(c.Z,{className:"h-4 w-4"})})}),t.jsx(i.eT,{children:s})]}));j.displayName=i.ck.displayName,r.forwardRef(({className:e,...s},a)=>t.jsx(i.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},51467:(e,s,a)=>{"use strict";a.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var t=a(95344),r=a(3729),i=a(89128),l=a(11453);let n=i.fC,c=r.forwardRef(({className:e,...s},a)=>t.jsx(i.aV,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.aV.displayName;let d=r.forwardRef(({className:e,...s},a)=>t.jsx(i.xz,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.xz.displayName;let o=r.forwardRef(({className:e,...s},a)=>t.jsx(i.VY,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=i.VY.displayName},80492:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/users/tiers/page.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},89128:(e,s,a)=>{"use strict";a.d(s,{VY:()=>S,aV:()=>R,fC:()=>M,xz:()=>T});var t=a(3729),r=a(85222),i=a(98462),l=a(34504),n=a(43234),c=a(62409),d=a(3975),o=a(33183),x=a(99048),m=a(95344),h="Tabs",[u,p]=(0,i.b)(h,[l.Pc]),j=(0,l.Pc)(),[g,f]=u(h),y=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:u="automatic",...p}=e,j=(0,d.gm)(n),[f,y]=(0,o.T)({prop:t,onChange:r,defaultProp:i??"",caller:h});return(0,m.jsx)(g,{scope:a,baseId:(0,x.M)(),value:f,onValueChange:y,orientation:l,dir:j,activationMode:u,children:(0,m.jsx)(c.WV.div,{dir:j,"data-orientation":l,...p,ref:s})})});y.displayName=h;var v="TabsList",N=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...r}=e,i=f(v,a),n=j(a);return(0,m.jsx)(l.fC,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:t,children:(0,m.jsx)(c.WV.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});N.displayName=v;var b="TabsTrigger",w=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:i=!1,...n}=e,d=f(b,a),o=j(a),x=k(d.baseId,t),h=P(d.baseId,t),u=t===d.value;return(0,m.jsx)(l.ck,{asChild:!0,...o,focusable:!i,active:u,children:(0,m.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:x,...n,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||i||!e||d.onValueChange(t)})})})});w.displayName=b;var Z="TabsContent",C=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:i,children:l,...d}=e,o=f(Z,a),x=k(o.baseId,r),h=P(o.baseId,r),u=r===o.value,p=t.useRef(u);return t.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(n.z,{present:i||u,children:({present:a})=>(0,m.jsx)(c.WV.div,{"data-state":u?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function k(e,s){return`${e}-trigger-${s}`}function P(e,s){return`${e}-content-${s}`}C.displayName=Z;var M=y,R=N,T=w,S=C}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,7966,6317,7833,1231],()=>a(47894));module.exports=t})();