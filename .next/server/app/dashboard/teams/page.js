(()=>{var e={};e.id=7861,e.ids=[7861],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},21483:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=a(50482),r=a(69108),n=a(62563),i=a.n(n),l=a(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c=["",{children:["dashboard",{children:["teams",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,77007)),"/home/<USER>/FECMS-sport/src/app/dashboard/teams/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/teams/page.tsx"],m="/dashboard/teams/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/teams/page",pathname:"/dashboard/teams",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},49723:(e,t,a)=>{Promise.resolve().then(a.bind(a,59738))},71532:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},96885:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},53148:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76394:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},30304:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},46064:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},59738:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>L});var s=a(95344),r=a(3729),n=a(8428),i=a(19738),l=a(23673),o=a(5094),c=a(46540),d=a(19591),m=a(77022),u=a(36487),h=a(58701),x=a(59836),p=a(11723),g=a(34755),f=a(53148),y=a(46064),j=a(89895),v=a(33733),w=a(96885),b=a(76394),N=a(28765),k=a(30304),T=a(30782),C=a(55794);function L(){let e=(0,n.useRouter)(),{isEditor:t,isAdmin:a}=(0,u.TE)(),[L,P]=(0,r.useState)({page:1,limit:20}),[q,S]=(0,r.useState)(""),[Z,E]=(0,r.useState)(""),[$,M]=(0,r.useState)(""),{teams:F,teamsMeta:I,isLoading:A,error:_}=(0,h.y2)(L);console.log("\uD83D\uDD0D Teams Page Debug (FIXED):",{teamsCount:F.length,firstTeam:F[0],firstTeamName:F[0]?.name,firstTeamNameType:typeof F[0]?.name,firstTeamNameLength:F[0]?.name?.length,dataStructureValid:!!F[0]?.name,filters:L,teamsMeta:I,error:_}),F.length>0&&console.log("\uD83D\uDD0D All teams names (FIXED):",F.slice(0,3).map(e=>({id:e?.id,name:e?.name,nameType:typeof e?.name,nameValid:!!e?.name,fullTeam:e})));let{data:D}=(0,i.a)({queryKey:["leagues","all"],queryFn:()=>x.A.getLeagues({limit:100})}),z=e=>{S(e),P(t=>({...t,search:e||void 0,page:1}))},B=e=>{E(e),P(t=>({...t,league:e?parseInt(e):void 0,page:1}))},G=e=>{M(e),P(t=>({...t,country:e||void 0,page:1}))},R=Array.from(new Set(F.filter(e=>e?.country).map(e=>e.country).filter(Boolean)));return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold tracking-tight flex items-center gap-2",children:[s.jsx(j.Z,{className:"w-8 h-8 text-blue-600"}),"Teams Management"]}),s.jsx("p",{className:"text-muted-foreground",children:"Browse and manage football teams from leagues worldwide"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>window.location.reload(),children:[s.jsx(v.Z,{className:"w-4 h-4 mr-2"}),"Refresh"]}),a()&&(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>g.toast.info("Export feature coming soon"),children:[s.jsx(w.Z,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,s.jsxs)(l.Zb,{children:[s.jsx(l.Ol,{children:(0,s.jsxs)(l.ll,{className:"flex items-center gap-2",children:[s.jsx(b.Z,{className:"w-5 h-5"}),"Search & Filters"]})}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[s.jsx(N.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),s.jsx(c.I,{placeholder:"Search teams...",value:q,onChange:e=>S(e.target.value),onKeyDown:e=>"Enter"===e.key&&z(q),className:"pl-9"})]}),s.jsx(o.z,{onClick:()=>z(q),children:"Search"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"text-sm font-medium mb-2 block",children:"League"}),(0,s.jsxs)("select",{value:Z,onChange:e=>B(e.target.value),className:"w-full p-2 border rounded-md",children:[s.jsx("option",{value:"",children:"All Leagues"}),D?.data?.map(e=>s.jsxs("option",{value:e.externalId,children:[e.name," ",e.season&&`(${e.season})`]},e.externalId))]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Country"}),(0,s.jsxs)("select",{value:$,onChange:e=>G(e.target.value),className:"w-full p-2 border rounded-md",children:[s.jsx("option",{value:"",children:"All Countries"}),R.map(e=>s.jsx("option",{value:e,children:e},e))]})]}),s.jsx("div",{className:"flex items-end",children:s.jsx(o.z,{variant:"outline",onClick:()=>{S(""),E(""),M(""),P({page:1,limit:20})},className:"w-full",children:"Clear Filters"})})]})]})]}),(0,s.jsxs)(l.Zb,{children:[s.jsx(l.Ol,{children:(0,s.jsxs)(l.ll,{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{children:["Teams (",I?.totalItems||0,")"]}),I&&(0,s.jsxs)(d.C,{variant:"outline",children:["Page ",I.currentPage," of ",I.totalPages]})]})}),s.jsx(l.aY,{children:s.jsx(m.w,{data:F,columns:[{title:"Team",key:"name",render:(e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("img",{src:(0,p.Bf)(t?.logo)||"/images/default-team.png",alt:`${t?.name||"Team"} logo`,className:"w-8 h-8 rounded-full object-cover",onError:e=>{e.currentTarget.src="/images/default-team.png"}}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-medium",children:t?.name||"Unknown Team"}),t?.code&&s.jsx("div",{className:"text-sm text-muted-foreground",children:t.code})]})]})},{title:"Country",key:"country",render:(e,t)=>s.jsx("div",{className:"flex items-center space-x-2",children:t&&t.country&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("img",{src:(0,p.ou)(t.country)||"/images/default-flag.png",alt:`${t.country} flag`,className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),s.jsx("span",{children:t.country})]})})},{title:"Founded",key:"founded",render:(e,t)=>s.jsx("div",{className:"text-center",children:t?.founded?s.jsx(d.C,{variant:"outline",className:"font-mono",children:t.founded}):s.jsx("span",{className:"text-muted-foreground",children:"-"})})},{title:"Actions",key:"actions",render:(t,a)=>(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[s.jsx(o.z,{variant:"ghost",size:"sm",onClick:()=>e.push(`/dashboard/teams/${a?.externalId}`),disabled:!a?.externalId,children:s.jsx(f.Z,{className:"w-4 h-4"})}),s.jsx(o.z,{variant:"ghost",size:"sm",onClick:()=>e.push(`/dashboard/teams/${a?.externalId}/statistics`),disabled:!a?.externalId,children:s.jsx(y.Z,{className:"w-4 h-4"})})]})}],loading:A,pagination:{page:I?.currentPage||1,limit:I?.limit||20,total:I?.totalItems||0,onPageChange:e=>{P(t=>({...t,page:e}))},onLimitChange:e=>{P(t=>({...t,limit:e,page:1}))}},emptyMessage:_?`Error loading teams: ${_?.message||"Unknown error"}`:"No teams found"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(j.Z,{className:"w-8 h-8 text-blue-500"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Total Teams"}),s.jsx("p",{className:"text-2xl font-bold",children:I?.totalItems||0})]})]})})}),s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(k.Z,{className:"w-8 h-8 text-green-500"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Countries"}),s.jsx("p",{className:"text-2xl font-bold",children:R.length})]})]})})}),s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(T.Z,{className:"w-8 h-8 text-yellow-500"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Leagues"}),s.jsx("p",{className:"text-2xl font-bold",children:D?.data?.length||0})]})]})})}),s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(C.Z,{className:"w-8 h-8 text-purple-500"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Current Page"}),s.jsx("p",{className:"text-2xl font-bold",children:I?.currentPage||1})]})]})})})]})]})}},59836:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(50053);let r=()=>null,n={getLeagues:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/leagues?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?`${e}-${t}`:e.toString(),s=await fetch(`/api/leagues/${a}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||`Failed to fetch league ${e}`);return await s.json()},createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let s=r(),i={"Content-Type":"application/json"};s&&(i.Authorization=`Bearer ${s}`);let l=await n.getLeagueById(e,a);if(!l||!l.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);let o=await fetch(`/api/leagues/${l.id}`,{method:"PATCH",headers:i,body:JSON.stringify(t)});if(!o.ok)throw Error((await o.json()).message||`Failed to update league ${e}`);return await o.json()},deleteLeague:async(e,t)=>{let a=await n.getLeagueById(e,t);if(!a||!a.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);await s.x.delete(`/football/leagues/${a.id}`)},getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>n.updateLeague(e,{active:t},a)}},73286:(e,t,a)=>{"use strict";a.d(t,{k:()=>r});var s=a(50053);let r={getTeams:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/teams?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch teams");return await a.json()},getTeamById:async e=>await s.x.get(`/football/teams/${e}`),getTeamStatistics:async(e,t,a)=>{let r=new URLSearchParams({league:e.toString(),season:t.toString(),team:a.toString()});return await s.x.get(`/football/teams/statistics?${r.toString()}`)},getTeamsByLeague:async(e,t)=>{let a={league:e};return t&&(a.season=t),r.getTeams(a)},getTeamsByCountry:async e=>r.getTeams({country:e}),searchTeams:async(e,t={})=>{let a=await r.getTeams(t),s=a.data.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.code?.toLowerCase().includes(e.toLowerCase()));return{data:s,meta:{...a.meta,totalItems:s.length,totalPages:Math.ceil(s.length/(t.limit||10))}}},deleteTeam:async e=>{await s.x.delete(`/football/teams/${e}`)}}},58701:(e,t,a)=>{"use strict";a.d(t,{t7:()=>i,vt:()=>l,y2:()=>n});var s=a(19738),r=a(73286);let n=(e={})=>{let t=(0,s.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeams(e),staleTime:6e5});return{teams:t.data?.data||[],teamsMeta:t.data?.meta,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},i=e=>{let t=(0,s.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},l=e=>{let t=(0,s.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},11723:(e,t,a)=>{"use strict";function s(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${t}`}function r(e){return s(e)}function n(e){return s(e)}function i(e){return s(e)}a.d(t,{Bf:()=>r,Fc:()=>i,Sc:()=>s,ou:()=>n})},77007:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let s=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/teams/page.tsx`),{__esModule:r,$$typeof:n}=s,i=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,6126,337,2609,3649,732,7966,6317,7833,7022],()=>a(21483));module.exports=s})();