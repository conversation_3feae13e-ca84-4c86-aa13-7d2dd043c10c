(()=>{var e={};e.id=1784,e.ids=[1784],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},35207:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),i=t(62563),n=t.n(i),l=t(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["teams",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98400)),"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx"],m="/dashboard/teams/[id]/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/teams/[id]/page",pathname:"/dashboard/teams/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21602:(e,s,t)=>{Promise.resolve().then(t.bind(t,17760))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},12594:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},17760:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T});var a=t(95344),r=t(8428),i=t(3729),n=t(11494),l=t(14373),c=t(23673),d=t(5094),o=t(19591),m=t(86688),x=t(73875),h=t(36487),u=t(58701),p=t(73286),j=t(11723),g=t(34755),y=t(63024),f=t(89895),v=t(12594),N=t(46327),b=t(38271),w=t(55794),C=t(65719);function T(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),{isEditor:t,isAdmin:T}=(0,h.TE)(),[k,q]=(0,i.useState)(!1),P=(0,n.NL)(),F=parseInt(e.id),{team:z,isLoading:S,error:E}=(0,u.t7)(F),L=(0,l.D)({mutationFn:()=>p.k.deleteTeam(F),onSuccess:()=>{P.invalidateQueries({queryKey:["teams"]}),g.toast.success("Team deleted successfully"),q(!1),s.push("/dashboard/teams")},onError:e=>{g.toast.error(e.message||"Failed to delete team"),q(!1)}});return S?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(m.Od,{className:"h-10 w-20"}),a.jsx(m.Od,{className:"h-8 w-48"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2 space-y-6",children:a.jsx(m.Od,{className:"h-96"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(m.Od,{className:"h-64"}),a.jsx(m.Od,{className:"h-48"})]})]})]}):E||!z?(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[a.jsx(y.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(f.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Team not found"}),a.jsx("p",{className:"text-gray-500",children:"The team you're looking for doesn't exist or you don't have permission to view it."})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[a.jsx(y.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,j.Bf)(z.logo)?a.jsx("img",{src:(0,j.Bf)(z.logo)||"",alt:z.name,className:"w-12 h-12 object-contain rounded-full",onError:e=>{e.target.style.display="none"}}):a.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(f.Z,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:z.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[z.country&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("img",{src:(0,j.ou)(z.country)||"",alt:`${z.country} flag`,className:"w-4 h-3 object-cover",onError:e=>{e.target.style.display="none"}}),a.jsx("span",{children:z.country})]}),z.code&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{children:"•"}),a.jsx("span",{className:"font-mono text-sm",children:z.code})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>{s.push(`/dashboard/teams/${F}/statistics`)},children:[a.jsx(v.Z,{className:"w-4 h-4 mr-2"}),"Statistics"]}),t()&&(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>{s.push(`/dashboard/teams/${F}/edit`)},children:[a.jsx(N.Z,{className:"w-4 h-4 mr-2"}),"Edit"]}),T()&&(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>{q(!0)},className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:[a.jsx(b.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2 space-y-6",children:(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[a.jsx(f.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Team Information"})]})}),a.jsx(c.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Team Name"}),a.jsx("p",{className:"text-lg font-medium",children:z.name})]}),z.code&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Team Code"}),a.jsx("p",{className:"text-lg font-medium font-mono",children:z.code})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Country"}),a.jsx("div",{className:"flex items-center space-x-2",children:z.country&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("img",{src:(0,j.ou)(z.country)||"",alt:`${z.country} flag`,className:"w-6 h-4 object-cover",onError:e=>{e.target.style.display="none"}}),a.jsx("span",{className:"text-lg font-medium",children:z.country})]})})]}),z.founded&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Founded"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.Z,{className:"w-4 h-4 text-gray-400"}),a.jsx("p",{className:"text-lg font-medium",children:z.founded})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Team ID"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:["#",z.externalId]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Internal ID"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:["#",z.id]})]})]})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,j.Bf)(z.logo)&&(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{className:"text-lg",children:"Team Logo"})}),a.jsx(c.aY,{children:a.jsx("div",{className:"flex justify-center",children:a.jsx("img",{src:(0,j.Bf)(z.logo)||"",alt:z.name,className:"w-32 h-32 object-contain"})})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{className:"text-lg",children:"Quick Info"})}),(0,a.jsxs)(c.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Country"}),a.jsx("div",{className:"flex items-center space-x-2",children:z.country&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("img",{src:(0,j.ou)(z.country)||"",alt:`${z.country} flag`,className:"w-4 h-3 object-cover",onError:e=>{e.target.style.display="none"}}),a.jsx("span",{className:"text-sm font-medium",children:z.country})]})})]}),z.founded&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Founded"}),a.jsx(o.C,{variant:"outline",className:"font-mono",children:z.founded})]}),z.code&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Code"}),a.jsx(o.C,{variant:"secondary",className:"font-mono",children:z.code})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Team ID"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",z.externalId]})]})]})]})]})]}),a.jsx(x.u_,{isOpen:k,onClose:()=>q(!1),title:"Delete Team",description:"Are you sure you want to delete this team? This action cannot be undone.",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[a.jsx(C.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the team:"}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:[a.jsx("strong",{children:z.name})," (",z.country,")"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 pt-4",children:[a.jsx(d.z,{variant:"outline",onClick:()=>q(!1),disabled:L.isLoading,children:"Cancel"}),a.jsx(d.z,{variant:"destructive",onClick:()=>{L.mutate()},disabled:L.isLoading,children:L.isLoading?"Deleting...":"Delete Team"})]})]})})]})}},73875:(e,s,t)=>{"use strict";t.d(s,{sm:()=>x,uB:()=>h,u_:()=>m});var a=t(95344),r=t(3729),i=t(81021),n=t(13659),l=t(14513),c=t(5094),d=t(11453);let o={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},m=({isOpen:e,onClose:s,title:t,description:m,children:x,size:h="md",showCloseButton:u=!0,closeOnOverlayClick:p=!0,className:j})=>a.jsx(i.u,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(n.Vq,{as:"div",className:"relative z-50",onClose:p?s:()=>{},children:[a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(n.Vq.Panel,{className:(0,d.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",o[h],j),children:[(t||u)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[t&&a.jsx(n.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:t}),m&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:m})]}),u&&a.jsx(c.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:a.jsx(l.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"mt-2",children:x})]})})})})]})}),x=({isOpen:e,onClose:s,onConfirm:t,title:r="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:n="Confirm",cancelText:l="Cancel",variant:d="default",loading:o=!1})=>a.jsx(m,{isOpen:e,onClose:s,title:r,size:"sm",closeOnOverlayClick:!o,children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[a.jsx(c.z,{variant:"outline",onClick:s,disabled:o,children:l}),a.jsx(c.z,{variant:"destructive"===d?"destructive":"default",onClick:t,disabled:o,children:o?"Processing...":n})]})]})}),h=({isOpen:e,onClose:s,title:t,description:r,children:i,onSubmit:n,submitText:l="Save",cancelText:d="Cancel",loading:o=!1,size:x="md"})=>a.jsx(m,{isOpen:e,onClose:s,title:t,description:r,size:x,closeOnOverlayClick:!o,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n?.()},className:"space-y-4",children:[i,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[a.jsx(c.z,{type:"button",variant:"outline",onClick:s,disabled:o,children:d}),n&&a.jsx(c.z,{type:"submit",disabled:o,children:o?"Saving...":l})]})]})})},73286:(e,s,t)=>{"use strict";t.d(s,{k:()=>r});var a=t(50053);let r={getTeams:async(e={})=>{let s=new URLSearchParams;Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=await fetch(`/api/teams?${s.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch teams");return await t.json()},getTeamById:async e=>await a.x.get(`/football/teams/${e}`),getTeamStatistics:async(e,s,t)=>{let r=new URLSearchParams({league:e.toString(),season:s.toString(),team:t.toString()});return await a.x.get(`/football/teams/statistics?${r.toString()}`)},getTeamsByLeague:async(e,s)=>{let t={league:e};return s&&(t.season=s),r.getTeams(t)},getTeamsByCountry:async e=>r.getTeams({country:e}),searchTeams:async(e,s={})=>{let t=await r.getTeams(s),a=t.data.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.code?.toLowerCase().includes(e.toLowerCase()));return{data:a,meta:{...t.meta,totalItems:a.length,totalPages:Math.ceil(a.length/(s.limit||10))}}},deleteTeam:async e=>{await a.x.delete(`/football/teams/${e}`)}}},58701:(e,s,t)=>{"use strict";t.d(s,{t7:()=>n,vt:()=>l,y2:()=>i});var a=t(19738),r=t(73286);let i=(e={})=>{let s=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeams(e),staleTime:6e5});return{teams:s.data?.data||[],teamsMeta:s.data?.meta,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},n=e=>{let s=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},l=e=>{let s=(0,a.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}}},11723:(e,s,t)=>{"use strict";function a(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let s=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${s}`}function r(e){return a(e)}function i(e){return a(e)}function n(e){return a(e)}t.d(s,{Bf:()=>r,Fc:()=>n,Sc:()=>a,ou:()=>i})},98400:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,2527,6317,7833],()=>t(35207));module.exports=a})();