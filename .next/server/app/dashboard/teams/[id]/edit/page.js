(()=>{var e={};e.id=6574,e.ids=[6574],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},43683:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var a=s(50482),r=s(69108),n=s(62563),i=s.n(n),o=s(68300),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l=["",{children:["dashboard",{children:["teams",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14691)),"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/edit/page.tsx"],m="/dashboard/teams/[id]/edit/page",u={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/teams/[id]/edit/page",pathname:"/dashboard/teams/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},39110:(e,t,s)=>{Promise.resolve().then(s.bind(s,23882))},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},95269:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},31498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},23882:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(95344),r=s(3729),n=s(8428),i=s(11494),o=s(14373),d=s(23673),l=s(5094),c=s(46540),m=s(7361),u=s(86688),h=s(58701),p=s(11723),x=s(34755),g=s(89895),f=s(63024),y=s(95269),j=s(31498);function v(){let e=(0,n.useParams)(),t=(0,n.useRouter)(),s=(0,i.NL)(),v=parseInt(e.id),[b,w]=(0,r.useState)({name:"",code:"",country:"",founded:"",logo:""}),{team:N,isLoading:q,error:T}=(0,h.t7)(v),P=(0,o.D)({mutationFn:async e=>(console.log("Update team data:",e),await new Promise(e=>setTimeout(e,1e3)),e),onSuccess:()=>{x.toast.success("Team updated successfully"),s.invalidateQueries({queryKey:["team",v]}),s.invalidateQueries({queryKey:["teams"]}),t.push(`/dashboard/teams/${v}`)},onError:e=>{x.toast.error("Failed to update team"),console.error("Update team error:",e)}});(0,r.useEffect)(()=>{N&&w({name:N.name||"",code:N.code||"",country:N.country||"",founded:N.founded?.toString()||"",logo:N.logo||""})},[N]);let C=(e,t)=>{w(s=>({...s,[e]:t}))},L=()=>{t.push(`/dashboard/teams/${v}`)};return q?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(u.Od,{className:"h-8 w-8"}),a.jsx(u.Od,{className:"h-8 w-48"})]}),(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:a.jsx(u.Od,{className:"h-6 w-32"})}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[a.jsx(u.Od,{className:"h-10 w-full"}),a.jsx(u.Od,{className:"h-10 w-full"}),a.jsx(u.Od,{className:"h-10 w-full"}),a.jsx(u.Od,{className:"h-10 w-full"})]})]})]}):T||!N?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[a.jsx(g.Z,{className:"h-16 w-16 text-muted-foreground"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-xl font-semibold",children:"Team Not Found"}),a.jsx("p",{className:"text-muted-foreground",children:"The team you're looking for doesn't exist or has been removed."})]}),a.jsx(l.z,{onClick:()=>t.push("/dashboard/teams"),children:"Back to Teams"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(l.z,{variant:"ghost",size:"sm",onClick:L,className:"flex items-center gap-2",children:[a.jsx(f.Z,{className:"h-4 w-4"}),"Back"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold",children:"Edit Team"}),a.jsx("p",{className:"text-muted-foreground",children:"Update team information and details"})]})]}),(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"flex items-center gap-2",children:[a.jsx(g.Z,{className:"h-5 w-5"}),"Team Information"]})}),a.jsx(d.aY,{children:(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!b.name.trim()||!b.code.trim()||!b.country.trim()){x.toast.error("Please fill in all required fields");return}P.mutate(b)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("div",{className:"w-16 h-16 border border-border rounded-lg overflow-hidden bg-muted flex items-center justify-center",children:b.logo?a.jsx("img",{src:(0,p.Bf)(b.logo)||"",alt:b.name,className:"w-full h-full object-contain",onError:e=>{e.currentTarget.style.display="none"}}):a.jsx(g.Z,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium",children:b.name||"Team Name"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:b.code||"Team Code"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(m._,{htmlFor:"name",children:"Team Name *"}),a.jsx(c.I,{id:"name",value:b.name,onChange:e=>C("name",e.target.value),placeholder:"Enter team name",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(m._,{htmlFor:"code",children:"Team Code *"}),a.jsx(c.I,{id:"code",value:b.code,onChange:e=>C("code",e.target.value.toUpperCase()),placeholder:"e.g., MAN, CHE, LIV",maxLength:5,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(m._,{htmlFor:"country",children:"Country *"}),a.jsx(c.I,{id:"country",value:b.country,onChange:e=>C("country",e.target.value),placeholder:"Enter country",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(m._,{htmlFor:"founded",children:"Founded Year"}),a.jsx(c.I,{id:"founded",type:"number",value:b.founded,onChange:e=>C("founded",e.target.value),placeholder:"e.g., 1902",min:"1800",max:new Date().getFullYear()})]}),(0,a.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[a.jsx(m._,{htmlFor:"logo",children:"Logo URL"}),a.jsx(c.I,{id:"logo",value:b.logo,onChange:e=>C("logo",e.target.value),placeholder:"Enter logo URL"})]})]}),a.jsx("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx(y.Z,{className:"h-5 w-5 text-amber-600 mt-0.5"}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-amber-800",children:"Development Notice"}),a.jsx("p",{className:"text-sm text-amber-700 mt-1",children:"Team editing functionality is currently under development. The API endpoint for updating teams has not been implemented yet. This form is ready for integration once the backend API is available."})]})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-3 pt-4 border-t",children:[(0,a.jsxs)(l.z,{type:"submit",disabled:P.isLoading,className:"flex items-center gap-2",children:[a.jsx(j.Z,{className:"h-4 w-4"}),P.isLoading?"Saving...":"Save Changes"]}),a.jsx(l.z,{type:"button",variant:"outline",onClick:L,disabled:P.isLoading,children:"Cancel"})]})]})})]})]})}},7361:(e,t,s)=>{"use strict";s.d(t,{_:()=>l});var a=s(95344),r=s(3729),n=s(14217),i=s(49247),o=s(11453);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=r.forwardRef(({className:e,...t},s)=>a.jsx(n.f,{ref:s,className:(0,o.cn)(d(),e),...t}));l.displayName=n.f.displayName},73286:(e,t,s)=>{"use strict";s.d(t,{k:()=>r});var a=s(50053);let r={getTeams:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/teams?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch teams");return await s.json()},getTeamById:async e=>await a.x.get(`/football/teams/${e}`),getTeamStatistics:async(e,t,s)=>{let r=new URLSearchParams({league:e.toString(),season:t.toString(),team:s.toString()});return await a.x.get(`/football/teams/statistics?${r.toString()}`)},getTeamsByLeague:async(e,t)=>{let s={league:e};return t&&(s.season=t),r.getTeams(s)},getTeamsByCountry:async e=>r.getTeams({country:e}),searchTeams:async(e,t={})=>{let s=await r.getTeams(t),a=s.data.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.code?.toLowerCase().includes(e.toLowerCase()));return{data:a,meta:{...s.meta,totalItems:a.length,totalPages:Math.ceil(a.length/(t.limit||10))}}},deleteTeam:async e=>{await a.x.delete(`/football/teams/${e}`)}}},58701:(e,t,s)=>{"use strict";s.d(t,{t7:()=>i,vt:()=>o,y2:()=>n});var a=s(19738),r=s(73286);let n=(e={})=>{let t=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeams(e),staleTime:6e5});return{teams:t.data?.data||[],teamsMeta:t.data?.meta,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},i=e=>{let t=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},o=e=>{let t=(0,a.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},11723:(e,t,s)=>{"use strict";function a(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${t}`}function r(e){return a(e)}function n(e){return a(e)}function i(e){return a(e)}s.d(t,{Bf:()=>r,Fc:()=>i,Sc:()=>a,ou:()=>n})},14691:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/edit/page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default},14217:(e,t,s)=>{"use strict";s.d(t,{f:()=>o});var a=s(3729),r=s(62409),n=s(95344),i=a.forwardRef((e,t)=>(0,n.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>s(43683));module.exports=a})();