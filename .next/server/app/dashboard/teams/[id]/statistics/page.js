(()=>{var e={};e.id=1247,e.ids=[1247],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},41469:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),c=t.n(l),i=t(68300),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let d=["",{children:["dashboard",{children:["teams",{children:["[id]",{children:["statistics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20028)),"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/statistics/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/statistics/page.tsx"],x="/dashboard/teams/[id]/statistics/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/teams/[id]/statistics/page",pathname:"/dashboard/teams/[id]/statistics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41324:(e,s,t)=>{Promise.resolve().then(t.bind(t,3045))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},12594:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18452:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},77402:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3045:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(95344),r=t(8428),l=t(23673),c=t(5094),i=t(19591),n=t(86688),d=t(58701),o=t(11723),x=t(63024),m=t(12594),h=t(46064),u=t(77402),j=t(18452),g=t(89895),p=t(53148),y=t(55794),f=t(30782),N=t(17910),w=t(23485);function v(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),t=parseInt(e.id),{team:v,isLoading:b,error:Z}=(0,d.t7)(t),{statistics:M,isLoading:P,error:k}=(0,d.vt)(t);if(b||P)return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(n.Od,{className:"h-10 w-20"}),a.jsx(n.Od,{className:"h-8 w-64"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:8}).map((e,s)=>a.jsx(n.Od,{className:"h-24"},s))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsx(n.Od,{className:"h-96"}),a.jsx(n.Od,{className:"h-96"})]})]});if(Z||k||!v)return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Statistics not available"}),a.jsx("p",{className:"text-gray-500",children:"Unable to load team statistics at this time."})]})})})]});let C=M||{totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]},q=e=>{switch(e){case"W":return"bg-green-100 text-green-800";case"L":return"bg-red-100 text-red-800";case"D":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.Bf)(v.logo)?a.jsx("img",{src:(0,o.Bf)(v.logo)||"",alt:v.name,className:"w-10 h-10 object-contain rounded-full",onError:e=>{e.target.style.display="none"}}):a.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(g.Z,{className:"w-5 h-5 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[v.name," Statistics"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[v.country&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("img",{src:(0,o.ou)(v.country)||"",alt:`${v.country} flag`,className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),a.jsx("span",{children:v.country})]}),v.code&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{children:"•"}),a.jsx("span",{className:"font-mono text-sm",children:v.code})]})]})]})]})]}),(0,a.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>{s.push(`/dashboard/teams/${t}`)},children:[a.jsx(p.Z,{className:"w-4 h-4 mr-2"}),"View Team"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:C.totalMatches}),a.jsx("p",{className:"text-sm text-gray-600",children:"Total Matches"})]}),a.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:a.jsx(y.Z,{className:"w-6 h-6 text-blue-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-green-600",children:C.wins}),a.jsx("p",{className:"text-sm text-gray-600",children:"Wins"})]}),a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx(f.Z,{className:"w-6 h-6 text-green-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:C.draws}),a.jsx("p",{className:"text-sm text-gray-600",children:"Draws"})]}),a.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:a.jsx(j.Z,{className:"w-6 h-6 text-yellow-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-red-600",children:C.losses}),a.jsx("p",{className:"text-sm text-gray-600",children:"Losses"})]}),a.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:a.jsx(u.Z,{className:"w-6 h-6 text-red-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-blue-600",children:C.goalsScored}),a.jsx("p",{className:"text-sm text-gray-600",children:"Goals Scored"})]}),a.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:a.jsx(N.Z,{className:"w-6 h-6 text-blue-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-orange-600",children:C.goalsConceded}),a.jsx("p",{className:"text-sm text-gray-600",children:"Goals Conceded"})]}),a.jsx("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:a.jsx(w.Z,{className:"w-6 h-6 text-orange-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-2xl font-bold text-emerald-600",children:C.cleanSheets}),a.jsx("p",{className:"text-sm text-gray-600",children:"Clean Sheets"})]}),a.jsx("div",{className:"w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center",children:a.jsx(w.Z,{className:"w-6 h-6 text-emerald-600"})})]})})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[C.winPercentage,"%"]}),a.jsx("p",{className:"text-sm text-gray-600",children:"Win Rate"})]}),a.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:a.jsx(h.Z,{className:"w-6 h-6 text-purple-600"})})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Performance Breakdown"})]})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Overall Record"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[C.wins,"W - ",C.draws,"D - ",C.losses,"L"]})]}),(0,a.jsxs)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:[a.jsx("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:`${C.wins/C.totalMatches*100}%`}}),a.jsx("div",{className:"bg-yellow-600 h-2",style:{width:`${C.draws/C.totalMatches*100}%`,marginLeft:`${C.wins/C.totalMatches*100}%`}})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Home Record"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[C.homeRecord.wins,"W - ",C.homeRecord.draws,"D - ",C.homeRecord.losses,"L"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:`${C.homeRecord.wins/(C.homeRecord.wins+C.homeRecord.draws+C.homeRecord.losses)*100}%`}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Away Record"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[C.awayRecord.wins,"W - ",C.awayRecord.draws,"D - ",C.awayRecord.losses,"L"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:`${C.awayRecord.wins/(C.awayRecord.wins+C.awayRecord.draws+C.awayRecord.losses)*100}%`}})})]}),a.jsx("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Goal Difference"}),(0,a.jsxs)("span",{className:`text-lg font-bold ${C.goalsScored-C.goalsConceded>=0?"text-green-600":"text-red-600"}`,children:[C.goalsScored-C.goalsConceded>=0?"+":"",C.goalsScored-C.goalsConceded]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Avg Goals Scored"}),a.jsx("p",{className:"text-lg font-bold text-blue-600",children:C.avgGoalsPerMatch})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Avg Goals Conceded"}),a.jsx("p",{className:"text-lg font-bold text-orange-600",children:C.avgGoalsConcededPerMatch})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[a.jsx(h.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Recent Form"})]})}),a.jsx(l.aY,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Last 5 matches:"}),a.jsx("div",{className:"flex space-x-1",children:C.recentForm.map((e,s)=>a.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${q(e)}`,children:e},s))})]})})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[a.jsx(g.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Team Info"})]})}),(0,a.jsxs)(l.aY,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Country"}),a.jsx("div",{className:"flex items-center space-x-2",children:v.country&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("img",{src:(0,o.ou)(v.country)||"",alt:`${v.country} flag`,className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),a.jsx("span",{className:"text-sm font-medium",children:v.country})]})})]}),v.founded&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Founded"}),a.jsx(i.C,{variant:"outline",className:"font-mono",children:v.founded})]}),v.code&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Team Code"}),a.jsx(i.C,{variant:"secondary",className:"font-mono",children:v.code})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Team ID"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",v.externalId]})]})]})]})]})]})]})}},73286:(e,s,t)=>{"use strict";t.d(s,{k:()=>r});var a=t(50053);let r={getTeams:async(e={})=>{let s=new URLSearchParams;Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=await fetch(`/api/teams?${s.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch teams");return await t.json()},getTeamById:async e=>await a.x.get(`/football/teams/${e}`),getTeamStatistics:async(e,s,t)=>{let r=new URLSearchParams({league:e.toString(),season:s.toString(),team:t.toString()});return await a.x.get(`/football/teams/statistics?${r.toString()}`)},getTeamsByLeague:async(e,s)=>{let t={league:e};return s&&(t.season=s),r.getTeams(t)},getTeamsByCountry:async e=>r.getTeams({country:e}),searchTeams:async(e,s={})=>{let t=await r.getTeams(s),a=t.data.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.code?.toLowerCase().includes(e.toLowerCase()));return{data:a,meta:{...t.meta,totalItems:a.length,totalPages:Math.ceil(a.length/(s.limit||10))}}},deleteTeam:async e=>{await a.x.delete(`/football/teams/${e}`)}}},58701:(e,s,t)=>{"use strict";t.d(s,{t7:()=>c,vt:()=>i,y2:()=>l});var a=t(19738),r=t(73286);let l=(e={})=>{let s=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeams(e),staleTime:6e5});return{teams:s.data?.data||[],teamsMeta:s.data?.meta,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},c=e=>{let s=(0,a.a)({queryKey:["teams",e],queryFn:()=>r.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},i=e=>{let s=(0,a.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}}},11723:(e,s,t)=>{"use strict";function a(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let s=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${s}`}function r(e){return a(e)}function l(e){return a(e)}function c(e){return a(e)}t.d(s,{Bf:()=>r,Fc:()=>c,Sc:()=>a,ou:()=>l})},20028:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>c});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/statistics/page.tsx`),{__esModule:r,$$typeof:l}=a,c=a.default}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>t(41469));module.exports=a})();