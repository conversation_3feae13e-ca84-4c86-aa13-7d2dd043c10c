(()=>{var e={};e.id=5026,e.ids=[5026,6419],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},31596:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=a(50482),s=a(69108),i=a(62563),n=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let o=["",{children:["dashboard",{children:["leagues",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,48896)),"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/edit/page.tsx"],u="/dashboard/leagues/[id]/edit/page",p={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/leagues/[id]/edit/page",pathname:"/dashboard/leagues/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},46605:(e,t,a)=>{Promise.resolve().then(a.bind(a,70571))},63024:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31498:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},70571:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95344),s=a(3729),i=a(8428),n=a(11494),l=a(23673),d=a(5094),o=a(86688),c=a(40874),u=a(67999),p=a(25179),m=a(69142),f=a(11723),x=a(63024),h=a(30782),g=a(65719),y=a(31498),b=a(34755),v=a(54074);function j(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),a=(0,n.NL)(),{user:j,hasPermission:w}=(0,v.t)(),N=e.id,[k,C]=N.includes("-")?N.split("-"):[N,new Date().getFullYear().toString()],L=parseInt(k),E=parseInt(C),[R,q]=(0,s.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:"",season:E||new Date().getFullYear()}),[P,S]=(0,s.useState)(!1),[$,_]=(0,s.useState)({}),[F,Z]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!E||isNaN(E)){console.warn("Invalid season provided, redirecting to league detail page"),t.push(`/dashboard/leagues/${L}`);return}},[E,L,t]);let{league:D,isLoading:I}=(0,m.HK)(L,E),{updateLeague:M,isUpdateLoading:T}=(0,m.My)();(0,s.useEffect)(()=>{D&&!P&&(q({name:D.name||"",country:D.country||"",type:D.type?.toLowerCase()||"",active:D.active??!0,isHot:D.isHot??!1,logo:D.logo||"",season:D.season||E||new Date().getFullYear()}),S(!0))},[D,P,E]);let z=(e,t)=>{q(a=>({...a,[e]:t})),e in $&&$[e]&&_(t=>({...t,[e]:void 0}))},A=()=>{let e={};return R.name.trim()||(e.name="League name is required"),R.country.trim()||(e.country="Country is required"),R.type.trim()||(e.type="League type is required"),_(e),0===Object.keys(e).length};return I?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(o.Od,{className:"h-10 w-20"}),r.jsx(o.Od,{className:"h-8 w-48"})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[r.jsx(o.Od,{className:"h-6 w-32"}),r.jsx(o.Od,{className:"h-4 w-64"})]}),r.jsx(l.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsx(o.Od,{className:"h-20"}),r.jsx(o.Od,{className:"h-20"}),r.jsx(o.Od,{className:"h-20"}),r.jsx(o.Od,{className:"h-20"})]})})]})]}):D?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[r.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold tracking-tight",children:["Edit League ",E&&`(Season ${E})`]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Update league information and settings",E&&` for season ${E}`]})]})]}),(!j||!w("editor"))&&(0,r.jsxs)(c.bZ,{variant:"destructive",children:[r.jsx(g.Z,{className:"h-4 w-4"}),r.jsx(c.X,{children:j?"You need Editor permissions to edit leagues. Contact an administrator for access.":"You must be logged in to edit leagues."})]}),F&&(0,r.jsxs)(c.bZ,{variant:"destructive",children:[r.jsx(g.Z,{className:"h-4 w-4"}),r.jsx(c.X,{children:F})]}),r.jsx("form",{onSubmit:e=>{if(e.preventDefault(),!j||!w("editor")){Z("You need Editor permissions to update leagues"),b.toast.error("Authentication required: Editor permissions needed");return}if(!D||!D.id){b.toast.error("League data not loaded. Please try again.");return}if(!A()){b.toast.error("Please fix the form errors");return}M({externalId:L,season:R.season,data:{name:R.name.trim(),country:R.country.trim(),type:R.type.trim(),active:R.active,isHot:R.isHot,...R.logo&&{logo:R.logo.trim()}}},{onSuccess:()=>{a.invalidateQueries({queryKey:["leagues",L,R.season]}),a.invalidateQueries({queryKey:["leagues"]}),b.toast.success("League updated successfully"),t.push(`/dashboard/leagues/${L}-${R.season}`)},onError:e=>{console.error("League update error:",e),e.message?.includes("401")||e.message?.includes("unauthorized")?(Z("Authentication failed. Please login again."),b.toast.error("Authentication failed. Please login again.")):e.message?.includes("403")||e.message?.includes("forbidden")?(Z("You do not have permission to update this league"),b.toast.error("Permission denied: Editor access required")):b.toast.error(e.message||"Failed to update league")}})},children:(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[r.jsx(h.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"League Information"})]}),r.jsx(l.SZ,{children:"Edit the league details and configuration settings."})]}),(0,r.jsxs)(l.aY,{className:"space-y-6",children:[r.jsx(p.hj,{title:"Basic Information",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsx(p.UP,{label:"League Name",placeholder:"Enter league name",required:!0,value:R.name,onChange:e=>z("name",e.target.value),error:$.name}),r.jsx(p.UP,{label:"Country",placeholder:"Enter country",required:!0,value:R.country,onChange:e=>z("country",e.target.value),error:$.country}),r.jsx(p.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:R.type,onValueChange:e=>z("type",e),options:[{value:"league",label:"League"},{value:"cup",label:"Cup"},{value:"playoffs",label:"Playoffs"},{value:"friendly",label:"Friendly"},{value:"qualification",label:"Qualification"}],error:$.type},`league-type-${P?"loaded":"loading"}`),r.jsx(p.UP,{label:"Season",placeholder:"Season cannot be edited",required:!0,type:"number",min:"2000",max:"2050",value:R.season.toString(),onChange:e=>z("season",parseInt(e.target.value)||new Date().getFullYear()),disabled:!0,description:"Season cannot be modified during edit. Create a new league for a different season."}),r.jsx(p.UP,{label:"Logo URL",placeholder:"Enter logo URL (optional)",value:R.logo,onChange:e=>z("logo",e.target.value)})]})}),r.jsx(p.hj,{title:"Settings",children:r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[r.jsx("div",{className:"space-y-4",children:r.jsx(u.Z,{checked:R.active,onCheckedChange:e=>z("active",e),label:"Active Status",description:"Enable or disable this league",variant:"success",size:"md"})}),r.jsx("div",{className:"space-y-4",children:r.jsx(u.Z,{checked:R.isHot,onCheckedChange:e=>z("isHot",e),label:"Hot League",description:"Mark as featured/popular league",variant:"danger",size:"md"})})]})})}),R.logo&&r.jsx(p.hj,{title:"Logo Preview",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[r.jsx("img",{src:(0,f.Fc)(R.logo)||R.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let t=e.target;t.src="",t.style.display="none"}}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Logo Preview"}),r.jsx("p",{className:"text-xs text-gray-500",children:R.logo})]})]})}),(0,r.jsxs)(p.iN,{children:[r.jsx(d.z,{type:"button",variant:"outline",onClick:()=>t.back(),children:"Cancel"}),(0,r.jsxs)(d.z,{type:"submit",disabled:T||!j||!w("editor"),children:[r.jsx(y.Z,{className:"w-4 h-4 mr-2"}),T?"Updating...":"Update League"]})]})]})]})})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[r.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),r.jsx(l.Zb,{children:r.jsx(l.aY,{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),r.jsx("p",{className:"text-gray-500",children:"The league you're trying to edit doesn't exist or you don't have permission to edit it."})]})})})]})}},40874:(e,t,a)=>{"use strict";a.d(t,{X:()=>o,bZ:()=>d});var r=a(95344),s=a(3729),i=a(49247),n=a(11453);let l=(0,i.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=s.forwardRef(({className:e,variant:t,...a},s)=>r.jsx("div",{ref:s,role:"alert",className:(0,n.cn)(l({variant:t}),e),...a}));d.displayName="Alert",s.forwardRef(({className:e,...t},a)=>r.jsx("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let o=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));o.displayName="AlertDescription"},25179:(e,t,a)=>{"use strict";a.d(t,{ji:()=>y,iN:()=>v,hj:()=>b,UP:()=>x,mg:()=>g,XL:()=>h});var r=a(95344),s=a(3729),i=a(7361),n=a(46540),l=a(2690),d=a(38157),o=a(85684),c=a(62312),u=a(11453);let p=s.forwardRef(({className:e,...t},a)=>r.jsx(o.fC,{ref:a,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:r.jsx(o.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:r.jsx(c.Z,{className:"h-4 w-4"})})}));p.displayName=o.fC.displayName;var m=a(68065);let f=(0,s.forwardRef)(({label:e,description:t,error:a,required:s,className:n,children:l},d)=>(0,r.jsxs)("div",{ref:d,className:(0,u.cn)("space-y-2",n),children:[e&&(0,r.jsxs)(i._,{className:(0,u.cn)("text-sm font-medium",a&&"text-red-600"),children:[e,s&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),l,t&&!a&&r.jsx("p",{className:"text-sm text-gray-500",children:t}),a&&r.jsx("p",{className:"text-sm text-red-600",children:a})]}));f.displayName="FormField";let x=(0,s.forwardRef)(({label:e,description:t,error:a,required:s,className:i,...l},d)=>r.jsx(f,{label:e,description:t,error:a,required:s,children:r.jsx(n.I,{ref:d,className:(0,u.cn)(a&&"border-red-500 focus:border-red-500",i),...l})}));x.displayName="InputField";let h=(0,s.forwardRef)(({label:e,description:t,error:a,required:s,className:i,...n},d)=>r.jsx(f,{label:e,description:t,error:a,required:s,children:r.jsx(l.g,{ref:d,className:(0,u.cn)(a&&"border-red-500 focus:border-red-500",i),...n})}));h.displayName="TextareaField";let g=(0,s.forwardRef)(({label:e,description:t,error:a,required:s,placeholder:i,value:n,onValueChange:l,options:o,className:c,disabled:p},m)=>{let x=o.find(e=>e.value===n),h="http://*************";return r.jsx(f,{label:e,description:t,error:a,required:s,children:(0,r.jsxs)(d.Ph,{value:n,onValueChange:l,disabled:p,children:[r.jsx(d.i4,{ref:m,className:(0,u.cn)(a&&"border-red-500 focus:border-red-500",c),children:r.jsx("div",{className:"flex items-center justify-between w-full",children:r.jsx("div",{className:"flex items-center space-x-2 flex-1",children:x?x?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[x.logo&&r.jsx("img",{src:`${h}/${x.logo}`,alt:x.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),r.jsx("span",{children:x.label})]}):i:r.jsx("span",{className:"text-muted-foreground",children:i})})})}),r.jsx(d.Bw,{children:o.map(e=>r.jsx(d.Ql,{value:e.value,disabled:e.disabled,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&r.jsx("img",{src:`${h}/${e.logo}`,alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),r.jsx("span",{children:e.label})]})},e.value))})]})})});g.displayName="SelectField";let y=(0,s.forwardRef)(({label:e,description:t,error:a,checked:s,onCheckedChange:n,className:l},d)=>r.jsx(f,{description:t,error:a,className:l,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(p,{ref:d,checked:s,onCheckedChange:n,className:(0,u.cn)(a&&"border-red-500")}),e&&r.jsx(i._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",a&&"text-red-600"),children:e})]})}));y.displayName="CheckboxField",(0,s.forwardRef)(({label:e,description:t,error:a,required:s,value:n,onValueChange:l,options:d,orientation:o="vertical",className:c},p)=>r.jsx(f,{label:e,description:t,error:a,required:s,className:c,children:r.jsx(m.E,{ref:p,value:n,onValueChange:l,className:(0,u.cn)("horizontal"===o?"flex flex-row space-x-4":"space-y-2"),children:d.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(m.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(a&&"border-red-500")}),r.jsx(i._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})).displayName="RadioField";let b=({title:e,description:t,children:a,className:s})=>(0,r.jsxs)("div",{className:(0,u.cn)("space-y-4",s),children:[(e||t)&&(0,r.jsxs)("div",{className:"space-y-1",children:[e&&r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e}),t&&r.jsx("p",{className:"text-sm text-gray-600",children:t})]}),r.jsx("div",{className:"space-y-4",children:a})]}),v=({children:e,className:t,align:a="right"})=>r.jsx("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===a&&"justify-start","center"===a&&"justify-center","right"===a&&"justify-end",t),children:e})},7361:(e,t,a)=>{"use strict";a.d(t,{_:()=>o});var r=a(95344),s=a(3729),i=a(14217),n=a(49247),l=a(11453);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef(({className:e,...t},a)=>r.jsx(i.f,{ref:a,className:(0,l.cn)(d(),e),...t}));o.displayName=i.f.displayName},68065:(e,t,a)=>{"use strict";a.d(t,{E:()=>d,m:()=>o});var r=a(95344),s=a(3729),i=a(9913),n=a(82958),l=a(11453);let d=s.forwardRef(({className:e,...t},a)=>r.jsx(i.fC,{className:(0,l.cn)("grid gap-2",e),...t,ref:a}));d.displayName=i.fC.displayName;let o=s.forwardRef(({className:e,...t},a)=>r.jsx(i.ck,{ref:a,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:r.jsx(i.z$,{className:"flex items-center justify-center",children:r.jsx(n.Z,{className:"h-3.5 w-3.5 fill-primary"})})}));o.displayName=i.ck.displayName},38157:(e,t,a)=>{"use strict";a.d(t,{Bw:()=>x,Ph:()=>c,Ql:()=>h,i4:()=>p,ki:()=>u});var r=a(95344),s=a(3729),i=a(32116),n=a(25390),l=a(12704),d=a(62312),o=a(11453);let c=i.fC;i.ZA;let u=i.B4,p=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(i.xz,{ref:s,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.xz.displayName;let m=s.forwardRef(({className:e,...t},a)=>r.jsx(i.u_,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(l.Z,{className:"h-4 w-4"})}));m.displayName=i.u_.displayName;let f=s.forwardRef(({className:e,...t},a)=>r.jsx(i.$G,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(n.Z,{className:"h-4 w-4"})}));f.displayName=i.$G.displayName;let x=s.forwardRef(({className:e,children:t,position:a="popper",...s},n)=>r.jsx(i.h_,{children:(0,r.jsxs)(i.VY,{ref:n,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[r.jsx(m,{}),r.jsx(i.l_,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),r.jsx(f,{})]})}));x.displayName=i.VY.displayName,s.forwardRef(({className:e,...t},a)=>r.jsx(i.__,{ref:a,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",e),...t})).displayName=i.__.displayName;let h=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(i.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[r.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(d.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:t})]}));h.displayName=i.ck.displayName,s.forwardRef(({className:e,...t},a)=>r.jsx(i.Z0,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.Z0.displayName},13611:(e,t,a)=>{"use strict";a.d(t,{r:()=>l});var r=a(95344),s=a(3729),i=a(19655),n=a(11453);let l=s.forwardRef(({className:e,...t},a)=>r.jsx(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:r.jsx(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));l.displayName=i.fC.displayName},2690:(e,t,a)=>{"use strict";a.d(t,{g:()=>n});var r=a(95344),s=a(3729),i=a(11453);let n=s.forwardRef(({className:e,...t},a)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Textarea"},67999:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var r=a(95344);a(3729);var s=a(13611),i=a(7361);function n({checked:e,onCheckedChange:t,label:a,description:n,disabled:l=!1,size:d="md",variant:o="default"}){return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(s.r,{id:a,checked:e,onCheckedChange:t,disabled:l}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(i._,{htmlFor:a,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[d]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[o]} ${l?"opacity-50":""}`,children:a}),n&&r.jsx("span",{className:`text-xs text-gray-500 ${l?"opacity-50":""}`,children:n})]})]})}},59836:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(50053);let s=()=>null,i={getLeagues:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/leagues?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?`${e}-${t}`:e.toString(),r=await fetch(`/api/leagues/${a}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||`Failed to fetch league ${e}`);return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let r=s(),n={"Content-Type":"application/json"};r&&(n.Authorization=`Bearer ${r}`);let l=await i.getLeagueById(e,a);if(!l||!l.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);let d=await fetch(`/api/leagues/${l.id}`,{method:"PATCH",headers:n,body:JSON.stringify(t)});if(!d.ok)throw Error((await d.json()).message||`Failed to update league ${e}`);return await d.json()},deleteLeague:async(e,t)=>{let a=await i.getLeagueById(e,t);if(!a||!a.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);await r.x.delete(`/football/leagues/${a.id}`)},getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async e=>i.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>i.updateLeague(e,{active:t},a)}},69142:(e,t,a)=>{"use strict";a.d(t,{HK:()=>d,My:()=>o,sF:()=>l});var r=a(19738),s=a(11494),i=a(14373),n=a(59836);let l=(e={})=>{let t=(0,r.a)({queryKey:["leagues",e],queryFn:()=>n.A.getLeagues(e),staleTime:6e5});return{leagues:t.data?.data||[],leaguesMeta:t.data?.meta,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},d=(e,t)=>{let a=(0,r.a)({queryKey:["leagues",e,t],queryFn:()=>n.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},o=()=>{let e=(0,s.NL)(),t=(0,i.D)({mutationFn:e=>n.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,i.D)({mutationFn:({externalId:e,data:t,season:a})=>n.A.updateLeague(e,t,a),onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId,a.season],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,i.D)({mutationFn:({id:e,active:t})=>n.A.toggleLeagueStatus(e,t),onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isLoading,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isLoading,updateError:a.error,updateData:a.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},11723:(e,t,a)=>{"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return`http://*************/${t}`}function s(e){return r(e)}function i(e){return r(e)}function n(e){return r(e)}a.d(t,{Bf:()=>s,Fc:()=>n,Sc:()=>r,ou:()=>i})},48896:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>s,default:()=>n});let r=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/edit/page.tsx`),{__esModule:s,$$typeof:i}=r,n=r.default},85684:(e,t,a)=>{"use strict";a.d(t,{fC:()=>j,z$:()=>N});var r=a(3729),s=a(31405),i=a(98462),n=a(85222),l=a(33183),d=a(92062),o=a(63085),c=a(43234),u=a(62409),p=a(95344),m="Checkbox",[f,x]=(0,i.b)(m),[h,g]=f(m);function y(e){let{__scopeCheckbox:t,checked:a,children:s,defaultChecked:i,disabled:n,form:d,name:o,onCheckedChange:c,required:u,value:f="on",internal_do_not_use_render:x}=e,[g,y]=(0,l.T)({prop:a,defaultProp:i??!1,onChange:c,caller:m}),[b,v]=r.useState(null),[j,w]=r.useState(null),N=r.useRef(!1),k=!b||!!d||!!b.closest("form"),C={checked:g,disabled:n,setChecked:y,control:b,setControl:v,name:o,form:d,value:f,hasConsumerStoppedPropagationRef:N,required:u,defaultChecked:!L(i)&&i,isFormControl:k,bubbleInput:j,setBubbleInput:w};return(0,p.jsx)(h,{scope:t,...C,children:"function"==typeof x?x(C):s})}var b="CheckboxTrigger",v=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:a,...i},l)=>{let{control:d,value:o,disabled:c,checked:m,required:f,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:y,isFormControl:v,bubbleInput:j}=g(b,e),w=(0,s.e)(l,x),N=r.useRef(m);return r.useEffect(()=>{let e=d?.form;if(e){let t=()=>h(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,h]),(0,p.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":L(m)?"mixed":m,"aria-required":f,"data-state":E(m),"data-disabled":c?"":void 0,disabled:c,value:o,...i,ref:w,onKeyDown:(0,n.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.M)(a,e=>{h(e=>!!L(e)||!e),j&&v&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});v.displayName=b;var j=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:r,checked:s,defaultChecked:i,required:n,disabled:l,value:d,onCheckedChange:o,form:c,...u}=e;return(0,p.jsx)(y,{__scopeCheckbox:a,checked:s,defaultChecked:i,disabled:l,required:n,onCheckedChange:o,name:r,form:c,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(v,{...u,ref:t,__scopeCheckbox:a}),e&&(0,p.jsx)(C,{__scopeCheckbox:a})]})})});j.displayName=m;var w="CheckboxIndicator",N=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:r,...s}=e,i=g(w,a);return(0,p.jsx)(c.z,{present:r||L(i.checked)||!0===i.checked,children:(0,p.jsx)(u.WV.span,{"data-state":E(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=w;var k="CheckboxBubbleInput",C=r.forwardRef(({__scopeCheckbox:e,...t},a)=>{let{control:i,hasConsumerStoppedPropagationRef:n,checked:l,defaultChecked:c,required:m,disabled:f,name:x,value:h,form:y,bubbleInput:b,setBubbleInput:v}=g(k,e),j=(0,s.e)(a,v),w=(0,d.D)(l),N=(0,o.t)(i);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!n.current;if(w!==l&&e){let a=new Event("click",{bubbles:t});b.indeterminate=L(l),e.call(b,!L(l)&&l),b.dispatchEvent(a)}},[b,w,l,n]);let C=r.useRef(!L(l)&&l);return(0,p.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??C.current,required:m,disabled:f,name:x,value:h,form:y,...t,tabIndex:-1,ref:j,style:{...t.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function L(e){return"indeterminate"===e}function E(e){return L(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=k},14217:(e,t,a)=>{"use strict";a.d(t,{f:()=>l});var r=a(3729),s=a(62409),i=a(95344),n=r.forwardRef((e,t)=>(0,i.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},9913:(e,t,a)=>{"use strict";a.d(t,{ck:()=>M,fC:()=>I,z$:()=>T});var r=a(3729),s=a(85222),i=a(31405),n=a(98462),l=a(62409),d=a(34504),o=a(33183),c=a(3975),u=a(63085),p=a(92062),m=a(43234),f=a(95344),x="Radio",[h,g]=(0,n.b)(x),[y,b]=h(x),v=r.forwardRef((e,t)=>{let{__scopeRadio:a,name:n,checked:d=!1,required:o,disabled:c,value:u="on",onCheck:p,form:m,...x}=e,[h,g]=r.useState(null),b=(0,i.e)(t,e=>g(e)),v=r.useRef(!1),j=!h||m||!!h.closest("form");return(0,f.jsxs)(y,{scope:a,checked:d,disabled:c,children:[(0,f.jsx)(l.WV.button,{type:"button",role:"radio","aria-checked":d,"data-state":k(d),"data-disabled":c?"":void 0,disabled:c,value:u,...x,ref:b,onClick:(0,s.M)(e.onClick,e=>{d||p?.(),j&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})}),j&&(0,f.jsx)(N,{control:h,bubbles:!v.current,name:n,value:u,checked:d,required:o,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});v.displayName=x;var j="RadioIndicator",w=r.forwardRef((e,t)=>{let{__scopeRadio:a,forceMount:r,...s}=e,i=b(j,a);return(0,f.jsx)(m.z,{present:r||i.checked,children:(0,f.jsx)(l.WV.span,{"data-state":k(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t})})});w.displayName=j;var N=r.forwardRef(({__scopeRadio:e,control:t,checked:a,bubbles:s=!0,...n},d)=>{let o=r.useRef(null),c=(0,i.e)(o,d),m=(0,p.D)(a),x=(0,u.t)(t);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[m,a,s]),(0,f.jsx)(l.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:c,style:{...n.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}N.displayName="RadioBubbleInput";var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],L="RadioGroup",[E,R]=(0,n.b)(L,[d.Pc,g]),q=(0,d.Pc)(),P=g(),[S,$]=E(L),_=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,name:r,defaultValue:s,value:i,required:n=!1,disabled:u=!1,orientation:p,dir:m,loop:x=!0,onValueChange:h,...g}=e,y=q(a),b=(0,c.gm)(m),[v,j]=(0,o.T)({prop:i,defaultProp:s??null,onChange:h,caller:L});return(0,f.jsx)(S,{scope:a,name:r,required:n,disabled:u,value:v,onValueChange:j,children:(0,f.jsx)(d.fC,{asChild:!0,...y,orientation:p,dir:b,loop:x,children:(0,f.jsx)(l.WV.div,{role:"radiogroup","aria-required":n,"aria-orientation":p,"data-disabled":u?"":void 0,dir:b,...g,ref:t})})})});_.displayName=L;var F="RadioGroupItem",Z=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,disabled:n,...l}=e,o=$(F,a),c=o.disabled||n,u=q(a),p=P(a),m=r.useRef(null),x=(0,i.e)(t,m),h=o.value===l.value,g=r.useRef(!1);return r.useEffect(()=>{let e=e=>{C.includes(e.key)&&(g.current=!0)},t=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,f.jsx)(d.ck,{asChild:!0,...u,focusable:!c,active:h,children:(0,f.jsx)(v,{disabled:c,required:o.required,checked:h,...p,...l,name:o.name,ref:x,onCheck:()=>o.onValueChange(l.value),onKeyDown:(0,s.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,s.M)(l.onFocus,()=>{g.current&&m.current?.click()})})})});Z.displayName=F;var D=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,...r}=e,s=P(a);return(0,f.jsx)(w,{...s,...r,ref:t})});D.displayName="RadioGroupIndicator";var I=_,M=Z,T=D},19655:(e,t,a)=>{"use strict";a.d(t,{bU:()=>N,fC:()=>w});var r=a(3729),s=a(85222),i=a(31405),n=a(98462),l=a(33183),d=a(92062),o=a(63085),c=a(62409),u=a(95344),p="Switch",[m,f]=(0,n.b)(p),[x,h]=m(p),g=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:n,checked:d,defaultChecked:o,required:m,disabled:f,value:h="on",onCheckedChange:g,form:y,...b}=e,[w,N]=r.useState(null),k=(0,i.e)(t,e=>N(e)),C=r.useRef(!1),L=!w||y||!!w.closest("form"),[E,R]=(0,l.T)({prop:d,defaultProp:o??!1,onChange:g,caller:p});return(0,u.jsxs)(x,{scope:a,checked:E,disabled:f,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":E,"aria-required":m,"data-state":j(E),"data-disabled":f?"":void 0,disabled:f,value:h,...b,ref:k,onClick:(0,s.M)(e.onClick,e=>{R(e=>!e),L&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),L&&(0,u.jsx)(v,{control:w,bubbles:!C.current,name:n,value:h,checked:E,required:m,disabled:f,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var y="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=h(y,a);return(0,u.jsx)(c.WV.span,{"data-state":j(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});b.displayName=y;var v=r.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:s=!0,...n},l)=>{let c=r.useRef(null),p=(0,i.e)(c,l),m=(0,d.D)(a),f=(0,o.t)(t);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[m,a,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:p,style:{...n.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var w=g,N=b}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6126,337,2609,3649,732,7966,6317,7833],()=>a(31596));module.exports=r})();