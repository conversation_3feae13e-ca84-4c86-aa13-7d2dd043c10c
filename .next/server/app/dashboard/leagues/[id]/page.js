(()=>{var e={};e.id=2785,e.ids=[2785],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},12969:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d});var t=a(50482),r=a(69108),l=a(62563),n=a.n(l),i=a(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let d=["",{children:["dashboard",{children:["leagues",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,98124)),"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx"],m="/dashboard/leagues/[id]/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/leagues/[id]/page",pathname:"/dashboard/leagues/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95757:(e,s,a)=>{Promise.resolve().then(a.bind(a,9752))},88534:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63024:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},12594:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},66262:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99046:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},87957:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},2768:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},37121:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},20439:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},80508:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},18452:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},17910:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},77402:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},46064:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},79200:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},9752:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eF});var t=a(95344),r=a(8428),l=a(3729),n=a(11494),i=a(14373),c=a(23673),d=a(5094),o=a(19591),m=a(86688),x=a(73875),h=a(67999),u=a(36487),g=a(69142),p=a(59836),j=a(11723),f=a(34755),N=a(63024),v=a(30782),y=a(30304),w=a(46327),b=a(38271),C=a(79200),M=a(55794),S=a(25545),Z=a(65719),k=a(33733),F=a(19738),L=a(12594),D=a(88534),T=a(89895),z=a(97075);let A=(0,z.Z)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var $=a(17910),P=a(46064);let E=function({league:e,className:s=""}){var a;let{data:r,isLoading:l,error:n}=(a=e.externalId,(0,F.a)({queryKey:["league-statistics",a],queryFn:async()=>({totalTeams:Math.floor(20*Math.random())+16,totalFixtures:Math.floor(300*Math.random())+200,completedFixtures:Math.floor(150*Math.random())+100,upcomingFixtures:Math.floor(80*Math.random())+40,liveFixtures:Math.floor(5*Math.random()),seasonProgress:Math.floor(100*Math.random()),currentRound:`Round ${Math.floor(38*Math.random())+1}`,coverageScore:Math.floor(5*Math.random())+6,insights:["Season is 67% complete with high activity","Above average scoring rate this season","Strong home team advantage observed"],lastUpdated:new Date().toISOString()}),staleTime:9e5,enabled:!!a}));if(l)return(0,t.jsxs)(c.Zb,{className:s,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(L.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Statistics"})]})}),t.jsx(c.aY,{children:t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(m.Od,{className:"h-4 w-20"}),t.jsx(m.Od,{className:"h-8 w-16"})]},s))})})]});if(n||!r)return(0,t.jsxs)(c.Zb,{className:s,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(L.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Statistics"})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[t.jsx(D.Z,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),t.jsx("p",{className:"text-sm",children:"Statistics not available"})]})})]});let i=[{label:"Total Teams",value:r.totalTeams||0,icon:T.Z,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Total Fixtures",value:r.totalFixtures||0,icon:M.Z,color:"text-green-600",bgColor:"bg-green-50"},{label:"Completed",value:r.completedFixtures||0,icon:v.Z,color:"text-yellow-600",bgColor:"bg-yellow-50"},{label:"Upcoming",value:r.upcomingFixtures||0,icon:A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Live Matches",value:r.liveFixtures||0,icon:D.Z,color:"text-red-600",bgColor:"bg-red-50"},{label:"Season Progress",value:`${r.seasonProgress||0}%`,icon:$.Z,color:"text-indigo-600",bgColor:"bg-indigo-50"},{label:"Current Round",value:r.currentRound||"N/A",icon:P.Z,color:"text-orange-600",bgColor:"bg-orange-50"},{label:"Coverage Score",value:`${r.coverageScore||0}/10`,icon:L.Z,color:"text-teal-600",bgColor:"bg-teal-50"}];return(0,t.jsxs)(c.Zb,{className:s,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(L.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Statistics"})]}),(0,t.jsxs)(o.C,{variant:"outline",className:"text-xs",children:["Season ",e.season]})]})}),(0,t.jsxs)(c.aY,{children:[t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:i.map((e,s)=>{let a=e.icon;return(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors",children:[t.jsx("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:t.jsx(a,{className:`w-4 h-4 ${e.color}`})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-xs text-gray-500 font-medium",children:e.label}),t.jsx("p",{className:"text-lg font-bold text-gray-900",children:e.value})]})]},s)})}),r.insights&&r.insights.length>0&&(0,t.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:[(0,t.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[t.jsx(P.Z,{className:"w-4 h-4 mr-2"}),"Key Insights"]}),t.jsx("div",{className:"space-y-2",children:r.insights.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[t.jsx("div",{className:"w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0"}),t.jsx("p",{className:"text-sm text-gray-600",children:e})]},s))})]}),e.season_detail?.coverage&&(0,t.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Coverage Breakdown"}),t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:Object.entries(e.season_detail.coverage).map(([e,s])=>"fixtures"===e?null:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-xs text-gray-500 capitalize",children:e.replace("_"," ")}),t.jsx("div",{className:`w-2 h-2 rounded-full ${s?"bg-green-500":"bg-red-500"}`})]},e))})]})]})]})};var I=a(46540),O=a(20439);let q=(0,z.Z)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var R=a(28765),U=a(76394),V=a(80508),_=a(53148);let Y=(0,z.Z)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),H=async(e,s)=>{try{let a=s||new Date().getFullYear();console.log("\uD83D\uDD04 Fetching teams for league:",e,"season:",a);let t=await fetch(`/api/teams?league=${e}&season=${a}`);if(!t.ok)throw Error(`Failed to fetch teams: ${t.status}`);let r=await t.json();if(console.log("✅ Teams API response:",r),!r.data||!Array.isArray(r.data))return console.warn("⚠️ No teams data in API response, using fallback"),G(e);return r.data.map(e=>({id:e.externalId?.toString()||e.id?.toString()||Math.random().toString(),name:e.name||"Unknown Team",logo:e.logo||"",foundedYear:e.founded||new Date().getFullYear(),country:e.country||"Unknown",city:e.venue?.city||"Unknown",stadium:e.venue?.name||"Unknown Stadium",capacity:e.venue?.capacity||0,website:"",description:"",stats:{matchesPlayed:0,wins:0,draws:0,losses:0,goalsFor:0,goalsAgainst:0,points:0,position:void 0},recentForm:[],manager:"",playersCount:0,externalId:e.externalId,code:e.code,venue:e.venue?{id:e.venue.id,name:e.venue.name,address:e.venue.address,city:e.venue.city,capacity:e.venue.capacity,surface:e.venue.surface,image:e.venue.image}:void 0,founded:e.founded}))}catch(s){return console.error("❌ Error fetching teams:",s),G(e)}},G=e=>{let s=["England","Scotland","Wales","Ireland"],a=["London","Manchester","Liverpool","Birmingham","Newcastle","Brighton","Sheffield","Burnley","Luton","Bournemouth"],t=["Emirates Stadium","Old Trafford","Anfield","Stamford Bridge","Etihad Stadium","Tottenham Hotspur Stadium","St. James' Park","American Express Community Stadium","Villa Park","London Stadium"],r=["Mikel Arteta","Erik ten Hag","J\xfcrgen Klopp","Mauricio Pochettino","Pep Guardiola","Ange Postecoglou","Eddie Howe","Roberto De Zerbi","Unai Emery","David Moyes","Roy Hodgson","Marco Silva"];return["Arsenal FC","Manchester United","Liverpool FC","Chelsea FC","Manchester City","Tottenham Hotspur","Newcastle United","Brighton & Hove","Aston Villa","West Ham United","Crystal Palace","Fulham FC","Brentford FC","Wolverhampton","Everton FC","Nottingham Forest","Bournemouth AFC","Sheffield United","Burnley FC","Luton Town"].map((l,n)=>{let i=Math.floor(38*Math.random())+10,c=Math.floor(Math.random()*i*.6),d=Math.floor(Math.random()*(i-c)*.7),o=i-c-d,m=[];for(let e=0;e<5;e++){let e=Math.random();e<.4?m.push("W"):e<.7?m.push("D"):m.push("L")}return{id:`team-${e}-${n+1}`,name:l,logo:`/images/teams/${l.toLowerCase().replace(/\s+/g,"-")}.png`,foundedYear:Math.floor(120*Math.random())+1880,country:s[Math.floor(Math.random()*s.length)],city:a[Math.floor(Math.random()*a.length)],stadium:t[Math.floor(Math.random()*t.length)],capacity:Math.floor(6e4*Math.random())+2e4,website:`https://${l.toLowerCase().replace(/\s+/g,"")}.com`,description:`${l} is one of the most prestigious football clubs with a rich history and passionate fanbase.`,stats:{matchesPlayed:i,wins:c,draws:o,losses:d,goalsFor:Math.floor(80*Math.random())+20,goalsAgainst:Math.floor(60*Math.random())+15,points:3*c+o,position:n+1},recentForm:m,manager:r[Math.floor(Math.random()*r.length)],playersCount:Math.floor(10*Math.random())+25}}).sort((e,s)=>s.stats.points-e.stats.points)},B=({leagueId:e,season:s})=>{let[a,t]=(0,l.useState)([]),[r,n]=(0,l.useState)(!0),[i,c]=(0,l.useState)(null),[d,o]=(0,l.useState)(""),[m,x]=(0,l.useState)("position"),[h,u]=(0,l.useState)("asc"),[g,p]=(0,l.useState)(""),j=async()=>{try{n(!0),c(null);let a=await H(e,s);t(a)}catch(e){c(e instanceof Error?e.message:"Failed to fetch teams")}finally{n(!1)}};(0,l.useEffect)(()=>{e&&j()},[e,s]);let f=(0,l.useMemo)(()=>{let e=a;return d&&(e=e.filter(e=>e.name.toLowerCase().includes(d.toLowerCase())||e.country.toLowerCase().includes(d.toLowerCase())||e.city.toLowerCase().includes(d.toLowerCase()))),g&&(e=e.filter(e=>e.country===g)),[...e].sort((e,s)=>{let a,t;switch(m){case"name":a=e.name.toLowerCase(),t=s.name.toLowerCase();break;case"foundedYear":a=e.foundedYear,t=s.foundedYear;break;case"country":a=e.country.toLowerCase(),t=s.country.toLowerCase();break;case"points":a=e.stats.points,t=s.stats.points;break;case"position":a=e.stats.position||999,t=s.stats.position||999;break;default:return 0}return a<t?"asc"===h?-1:1:a>t?"asc"===h?1:-1:0})},[a,d,g,m,h]),N=(0,l.useMemo)(()=>Array.from(new Set(a.map(e=>e.country))).sort(),[a]),v=(0,l.useMemo)(()=>{let e=a.length,s=N.length,t=e>0?Math.round(a.reduce((e,s)=>e+s.foundedYear,0)/e):0;return{totalTeams:e,totalCountries:s,averageFoundedYear:t,totalMatches:a.reduce((e,s)=>e+s.stats.matchesPlayed,0)}},[a,N]);return{teams:a,loading:r,error:i,searchTerm:d,setSearchTerm:o,sortBy:m,setSortBy:x,sortOrder:h,setSortOrder:u,countryFilter:g,setCountryFilter:p,filteredAndSortedTeams:f,countries:N,stats:v,refreshTeams:async()=>{await j()}}};var W=a(73582),K=a(50909),Q=a(14871),J=a(14513),X=a(2768);let ee=({team:e,isOpen:s,onClose:a,onViewDetails:r})=>{if(!e)return null;let l=e.stats.matchesPlayed>0?Math.round(e.stats.wins/e.stats.matchesPlayed*100):0,n=e.stats.goalsFor-e.stats.goalsAgainst,i=e=>{switch(e){case"W":return"bg-green-500";case"L":return"bg-red-500";case"D":return"bg-yellow-500";default:return"bg-gray-500"}};return t.jsx(W.Vq,{open:s,onOpenChange:a,children:(0,t.jsxs)(W.cZ,{className:"max-w-2xl",children:[(0,t.jsxs)(W.fK,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(W.$N,{className:"text-xl font-semibold",children:"Team Quick View"}),t.jsx(d.z,{variant:"ghost",size:"icon",onClick:a,className:"h-8 w-8",children:t.jsx(J.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(Q.qE,{className:"h-16 w-16",children:[t.jsx(Q.F$,{src:e.logo,alt:`${e.name} logo`}),t.jsx(Q.Q5,{className:"bg-blue-500 text-white text-lg font-bold",children:e.name.substring(0,2).toUpperCase()})]}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("h2",{className:"text-2xl font-bold",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(M.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Founded ",e.foundedYear]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(V.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.city,", ",e.country]})]})]})]}),e.stats.position&&(0,t.jsxs)(o.C,{variant:"secondary",className:"text-lg px-3 py-1",children:["#",e.stats.position]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[e.description&&t.jsx("div",{children:t.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.description})}),t.jsx(K.Z,{}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.stats.points}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Points"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[l,"%"]}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Win Rate"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,t.jsxs)("div",{className:`text-2xl font-bold ${n>=0?"text-green-600":"text-red-600"}`,children:[n>=0?"+":"",n]}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Goal Diff"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold",children:e.stats.matchesPlayed}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Matches"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-semibold mb-3 flex items-center space-x-2",children:[t.jsx(L.Z,{className:"h-4 w-4"}),t.jsx("span",{children:"Recent Form (Last 5 matches)"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[e.recentForm.map((e,s)=>t.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${i(e)}`,children:e},s)),0===e.recentForm.length&&t.jsx("span",{className:"text-sm text-muted-foreground",children:"No recent matches"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-semibold mb-3",children:"Season Statistics"}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Wins:"}),t.jsx("span",{className:"font-medium text-green-600",children:e.stats.wins})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Draws:"}),t.jsx("span",{className:"font-medium text-yellow-600",children:e.stats.draws})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Losses:"}),t.jsx("span",{className:"font-medium text-red-600",children:e.stats.losses})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Goals For:"}),t.jsx("span",{className:"font-medium",children:e.stats.goalsFor})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Goals Against:"}),t.jsx("span",{className:"font-medium",children:e.stats.goalsAgainst})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Total Points:"}),t.jsx("span",{className:"font-bold text-blue-600",children:e.stats.points})]})]})]}),t.jsx(K.Z,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.stadium&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{className:"text-muted-foreground",children:"Stadium:"}),t.jsx("span",{className:"font-medium",children:e.stadium}),e.capacity&&(0,t.jsxs)("span",{className:"text-muted-foreground",children:["(",e.capacity.toLocaleString(),")"]})]}),e.manager&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(T.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{className:"text-muted-foreground",children:"Manager:"}),t.jsx("span",{className:"font-medium",children:e.manager})]}),e.playersCount&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(T.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{className:"text-muted-foreground",children:"Squad Size:"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.playersCount," players"]})]}),e.website&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(X.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline font-medium",children:"Official Website"})]})]})]}),(0,t.jsxs)(W.cN,{className:"space-x-2",children:[t.jsx(d.z,{variant:"outline",onClick:a,children:"Close"}),r&&t.jsx(d.z,{onClick:()=>r(e.id),children:"View Full Details"})]})]})})},es=({leagueId:e,season:s,className:a=""})=>{let[r,n]=(0,l.useState)(""),[i,x]=(0,l.useState)("grid"),[h,u]=(0,l.useState)(null),[g,p]=(0,l.useState)(!1),{teams:f,loading:N,error:y,stats:w,refreshTeams:b}=B({leagueId:e,season:s}),C=f.filter(e=>e.name.toLowerCase().includes(r.toLowerCase())||e.stadium?.toLowerCase().includes(r.toLowerCase())||e.city?.toLowerCase().includes(r.toLowerCase())),S=e=>{u(e),p(!0)};return N?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(T.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Teams"})]}),t.jsx(m.Od,{className:"h-8 w-24"})]})}),t.jsx(c.aY,{children:t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,s)=>t.jsx(m.Od,{className:"h-32"},s))})})]}):y?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(T.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Teams"})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(T.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Failed to load teams data"}),t.jsx(d.z,{variant:"outline",className:"mt-4",onClick:b,children:"Try Again"})]})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(T.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Teams"}),(0,t.jsxs)(o.C,{variant:"secondary",className:"ml-2",children:[f.length," teams"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(d.z,{variant:"outline",size:"sm",onClick:()=>x("grid"===i?"list":"grid"),children:"grid"===i?t.jsx(O.Z,{className:"w-4 h-4"}):t.jsx(q,{className:"w-4 h-4"})}),t.jsx(d.z,{variant:"outline",size:"sm",onClick:b,children:t.jsx(P.Z,{className:"w-4 h-4"})})]})]})}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[t.jsx(R.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),"                                    ",t.jsx(I.I,{placeholder:"Search teams by name, stadium, or city...",value:r,onChange:e=>n(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(d.z,{variant:"outline",size:"sm",children:[t.jsx(U.Z,{className:"w-4 h-4 mr-2"}),"Filter"]})]}),0===C.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(T.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:r?"No teams found matching your search":"No teams available"})]}):t.jsx("div",{className:"grid"===i?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4":"space-y-3",children:C.map(e=>(0,t.jsxs)("div",{className:`
                                                            border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer
                                                            ${"list"===i?"flex items-center space-x-4":""}
                                                      `,onClick:()=>S(e),children:[t.jsx("div",{className:`
                                                            ${"list"===i?"flex-shrink-0":"text-center mb-3"}
                                                      `,children:e.logo&&(0,j.Bf)(e.logo)?t.jsx("img",{src:(0,j.Bf)(e.logo)||"",alt:e.name,className:`
                                                                              object-contain
                                                                              ${"list"===i?"w-12 h-12":"w-16 h-16 mx-auto"}
                                                                        `,onError:e=>{e.target.style.display="none"}}):t.jsx("div",{className:`
                                                                        bg-gray-100 rounded-lg flex items-center justify-center
                                                                        ${"list"===i?"w-12 h-12":"w-16 h-16 mx-auto"}
                                                                  `,children:t.jsx(v.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,t.jsxs)("div",{className:`
                                                            ${"list"===i?"flex-1":""}
                                                      `,children:[t.jsx("h4",{className:`
                                                                  font-medium text-gray-900
                                                                  ${"list"===i?"text-base":"text-sm mb-1"}
                                                            `,children:e.name}),e.stadium&&(0,t.jsxs)("div",{className:`
                                                                        flex items-center text-gray-500 text-xs
                                                                        ${"list"===i?"mt-1":"justify-center mt-2"}
                                                                  `,children:[t.jsx(V.Z,{className:"w-3 h-3 mr-1"}),t.jsx("span",{children:e.stadium})]}),e.foundedYear&&(0,t.jsxs)("div",{className:`
                                                                        flex items-center text-gray-500 text-xs mt-1
                                                                        ${"list"===i?"":"justify-center"}
                                                                  `,children:[t.jsx(M.Z,{className:"w-3 h-3 mr-1"}),(0,t.jsxs)("span",{children:["Founded ",e.foundedYear]})]}),"list"===i&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[t.jsx("div",{className:"flex items-center space-x-2",children:e.country&&t.jsx(o.C,{variant:"secondary",className:"text-xs",children:e.country})}),t.jsx(d.z,{variant:"ghost",size:"sm",children:t.jsx(_.Z,{className:"w-4 h-4"})})]})]}),"grid"===i&&t.jsx("div",{className:"flex items-center justify-center mt-3",children:(0,t.jsxs)(d.z,{variant:"ghost",size:"sm",children:[t.jsx(_.Z,{className:"w-4 h-4 mr-1"}),"View Details"]})})]},e.id))}),C.length>0&&t.jsx("div",{className:"text-center pt-4",children:(0,t.jsxs)(d.z,{variant:"outline",children:["View All Teams",t.jsx(Y,{className:"w-4 h-4 ml-2"})]})})]})]}),t.jsx(ee,{team:h,isOpen:g,onClose:()=>{p(!1),u(null)}})]})};var ea=a(51467),et=a(81137),er=a(80958),el=a(87957);let en=async(e,s,a)=>{try{let t=s||new Date().toISOString().split("T")[0];console.log("\uD83D\uDD04 Fetching fixtures for league:",e,"date:",t,"season:",a);let r=`/api/fixtures?league=${e}&date=${t}`;a&&(r+=`&season=${a}`);let l=await fetch(r);if(!l.ok)throw Error(`Failed to fetch fixtures: ${l.status}`);let n=await l.json();if(console.log("✅ Fixtures API response:",n),!n.data||!Array.isArray(n.data))return console.warn("⚠️ No fixtures data in API response, using fallback"),ei(e);return n.data.map(e=>{let s="scheduled";if(e.status){let a=e.status.toLowerCase();["ft","aet","pen"].includes(a)?s="finished":["1h","2h","ht","et","p"].includes(a)?s="live":["pst","canc","abd","wo"].includes(a)&&(s="pst"===a?"postponed":"cancelled")}return{id:e.externalId?.toString()||e.id?.toString()||Math.random().toString(),date:e.date||new Date().toISOString(),status:s,minute:e.elapsed||void 0,homeTeam:{id:e.homeTeamId?.toString()||"",name:e.homeTeamName||"Unknown Home Team",logo:e.homeTeamLogo||""},awayTeam:{id:e.awayTeamId?.toString()||"",name:e.awayTeamName||"Unknown Away Team",logo:e.awayTeamLogo||""},homeScore:e.goalsHome||void 0,awayScore:e.goalsAway||void 0,venue:e.venue?.name||"",referee:e.referee||"",attendance:void 0,round:e.round||"",season:e.season?.toString()||"",externalId:e.externalId,timestamp:e.timestamp,timezone:e.fixture?.timezone,periods:e.fixture?.periods,goals:e.goals}})}catch(s){return console.error("❌ Error fetching fixtures:",s),ei(e)}},ei=e=>{let s=["Arsenal FC","Manchester United","Liverpool FC","Chelsea FC","Manchester City","Tottenham Hotspur","Newcastle United","Brighton & Hove","Aston Villa","West Ham United","Crystal Palace","Fulham FC","Brentford FC","Wolverhampton","Everton FC","Nottingham Forest","Bournemouth AFC","Sheffield United","Burnley FC","Luton Town"],a=["Emirates Stadium","Old Trafford","Anfield","Stamford Bridge","Etihad Stadium","Tottenham Hotspur Stadium","St. James' Park","American Express Community Stadium","Villa Park","London Stadium","Selhurst Park","Craven Cottage","Goodison Park","City Ground"],t=["Michael Oliver","Anthony Taylor","Craig Pawson","Stuart Attwell","Paul Tierney","Simon Hooper","Chris Kavanagh","Andy Madley","Jarred Gillett","Tim Robinson","Tony Harrington","David Coote"],r=[],l=new Date;for(let n=-14;n<=28;n++){let i=new Date(l);i.setDate(l.getDate()+n);let c=Math.floor(4*Math.random());for(let d=0;d<c;d++){let c,o,m,x;let h=Math.floor(Math.random()*s.length),u=Math.floor(Math.random()*s.length);for(;u===h;)u=Math.floor(Math.random()*s.length);let g=new Date(i);if(g.setHours(Math.floor(6*Math.random())+12),g.setMinutes(.5>Math.random()?0:30),g<l){let e=Math.random();e<.02?c="postponed":e<.03?c="cancelled":(c="finished",o=Math.floor(5*Math.random()),m=Math.floor(5*Math.random()))}else{let e=(g.getTime()-l.getTime())/36e5;e<2&&e>-2&&.1>Math.random()?(c="live",o=Math.floor(4*Math.random()),m=Math.floor(4*Math.random()),x=Math.floor(90*Math.random())+1):c="scheduled"}let p={id:`fixture-${e}-${n}-${d}`,date:g.toISOString(),status:c,minute:x,homeTeam:{id:`team-${h}`,name:s[h],logo:`/images/teams/${s[h].toLowerCase().replace(/\s+/g,"-")}.png`},awayTeam:{id:`team-${u}`,name:s[u],logo:`/images/teams/${s[u].toLowerCase().replace(/\s+/g,"-")}.png`},homeScore:o,awayScore:m,venue:a[Math.floor(Math.random()*a.length)],referee:t[Math.floor(Math.random()*t.length)],attendance:"finished"===c?Math.floor(6e4*Math.random())+2e4:void 0,round:`Matchday ${Math.floor(38*Math.random())+1}`,season:"2024-25"};r.push(p)}}return r.sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime())},ec=({leagueId:e,season:s,selectedDate:a})=>{let[t,r]=(0,l.useState)([]),[n,i]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),[o,m]=(0,l.useState)(a||new Date().toISOString().split("T")[0]),x=async a=>{try{i(!0),d(null);let t=await en(e,a||o,s);r(t)}catch(e){d(e instanceof Error?e.message:"Failed to fetch fixtures")}finally{i(!1)}};(0,l.useEffect)(()=>{e&&x()},[e,s,o]);let{recentFixtures:h,upcomingFixtures:u,liveFixtures:g}=(0,l.useMemo)(()=>{let e=new Date,s=new Date(e.getTime()-864e5),a=new Date(e.getTime()+6048e5);return{recentFixtures:t.filter(a=>{let t=new Date(a.date);return"finished"===a.status&&t>=s&&t<=e}).sort((e,s)=>new Date(s.date).getTime()-new Date(e.date).getTime()).slice(0,10),upcomingFixtures:t.filter(s=>{let t=new Date(s.date);return"scheduled"===s.status&&t>e&&t<=a}).sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()).slice(0,10),liveFixtures:t.filter(e=>"live"===e.status).sort((e,s)=>(e.minute||0)-(s.minute||0))}},[t]),p=(0,l.useMemo)(()=>{let e=t.length,s=t.filter(e=>"finished"===e.status).length,a=t.filter(e=>"scheduled"===e.status).length,r=t.filter(e=>"live"===e.status).length,l=t.filter(e=>"finished"===e.status&&void 0!==e.homeScore&&void 0!==e.awayScore),n=l.reduce((e,s)=>e+(s.homeScore||0)+(s.awayScore||0),0);return{totalFixtures:e,completedFixtures:s,scheduledFixtures:a,liveFixtures:r,averageGoalsPerMatch:l.length>0?Math.round(n/l.length*10)/10:0}},[t]);return{fixtures:t,loading:n,error:c,recentFixtures:h,upcomingFixtures:u,liveFixtures:g,selectedDate:o,setSelectedDate:m,stats:p,refreshFixtures:async()=>{await x()}}},ed=({leagueId:e,season:s,className:a=""})=>{let[r,n]=(0,l.useState)("upcoming"),{fixtures:i,loading:x,error:h,recentFixtures:u,upcomingFixtures:g,liveFixtures:p,selectedDate:f,setSelectedDate:N,stats:v,refreshFixtures:y}=ec({leagueId:e,season:s}),w=({team:e,size:s=24})=>{let[a,r]=(0,l.useState)(!1),n=(0,j.Bf)(e.logo);return t.jsx("div",{className:"w-6 h-6 flex items-center justify-center rounded",children:n&&!a?t.jsx("img",{src:n,alt:`${e.name} logo`,className:"w-6 h-6 object-contain",onError:()=>r(!0)}):t.jsx("div",{className:"w-6 h-6 bg-muted rounded flex items-center justify-center",children:t.jsx("span",{className:"text-xs font-bold",children:e.name.substring(0,2).toUpperCase()})})})},b=e=>{switch(e.toLowerCase()){case"live":case"in_progress":return"bg-red-500 text-white";case"finished":case"completed":return"bg-green-500 text-white";case"scheduled":case"upcoming":return"bg-blue-500 text-white";case"postponed":return"bg-yellow-500 text-white";case"cancelled":return"bg-gray-500 text-white";default:return"bg-gray-400 text-white"}},C=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),Z=(e,s=!0)=>t.jsx(c.Zb,{className:"group hover:shadow-sm transition-all",children:(0,t.jsxs)(c.aY,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[s&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[t.jsx(M.Z,{className:"h-3 w-3 mr-1"}),C(e.date)]}),(0,t.jsxs)(o.C,{className:`text-xs ${b(e.status)}`,children:["live"===e.status&&t.jsx(et.Z,{className:"h-2 w-2 mr-1 animate-pulse"}),e.status.toUpperCase()]})]}),e.venue&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[t.jsx(V.Z,{className:"h-3 w-3 mr-1"}),t.jsx("span",{className:"truncate max-w-20",children:e.venue})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[t.jsx(w,{team:e.homeTeam}),t.jsx("span",{className:"font-medium text-sm truncate",children:e.homeTeam.name})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(w,{team:e.awayTeam}),t.jsx("span",{className:"font-medium text-sm truncate",children:e.awayTeam.name})]})]}),t.jsx("div",{className:"text-center ml-4",children:"finished"===e.status?(0,t.jsxs)("div",{className:"text-lg font-bold",children:[(0,t.jsxs)("div",{children:[e.homeScore," - ",e.awayScore]}),e.minute&&t.jsx("div",{className:"text-xs text-muted-foreground",children:"FT"})]}):"live"===e.status?(0,t.jsxs)("div",{className:"text-lg font-bold text-red-600",children:[(0,t.jsxs)("div",{children:[e.homeScore||0," - ",e.awayScore||0]}),(0,t.jsxs)("div",{className:"text-xs text-red-600 animate-pulse",children:[e.minute,"'"]})]}):t.jsx("div",{className:"text-sm text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(S.Z,{className:"h-3 w-3 mr-1"}),new Date(e.date).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})]})})}),t.jsx(d.z,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity ml-2 h-8 w-8 p-0",onClick:()=>{console.log("View fixture details:",e.id)},children:t.jsx(_.Z,{className:"h-4 w-4"})})]}),(e.referee||e.attendance)&&t.jsx("div",{className:"mt-3 pt-3 border-t border-muted text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[e.referee&&(0,t.jsxs)("span",{children:["Ref: ",e.referee]}),e.attendance&&(0,t.jsxs)("span",{children:["Att: ",e.attendance.toLocaleString()]})]})})]})},e.id);return x?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(M.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Recent & Upcoming Fixtures"})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(m.Od,{className:"h-8 w-full"}),Array.from({length:5}).map((e,s)=>t.jsx(m.Od,{className:"h-20"},s))]})})]}):h?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(M.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Recent & Upcoming Fixtures"})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"text-center py-8 text-destructive",children:[t.jsx(M.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsxs)("p",{className:"text-sm",children:["Failed to load fixtures: ",h]}),(0,t.jsxs)(d.z,{variant:"outline",onClick:y,className:"mt-4",children:[t.jsx(k.Z,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})})]}):(0,t.jsxs)(c.Zb,{className:a,children:[(0,t.jsxs)(c.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(M.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Recent & Upcoming Fixtures"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[p.length>0&&(0,t.jsxs)(o.C,{variant:"destructive",className:"animate-pulse",children:[t.jsx(et.Z,{className:"h-2 w-2 mr-1"}),p.length," Live"]}),t.jsx(d.z,{variant:"outline",size:"sm",onClick:y,children:t.jsx(k.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 pt-4 border-t",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(er.Z,{className:"h-4 w-4 text-muted-foreground"}),t.jsx("span",{className:"text-sm font-medium",children:"Filter by Date:"})]}),t.jsx(I.I,{type:"date",value:f,onChange:e=>N(e.target.value),className:"w-auto"}),t.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>N(new Date().toISOString().split("T")[0]),className:"text-xs",children:"Today"})]})]}),(0,t.jsxs)(c.aY,{className:"space-y-6",children:[p.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(et.Z,{className:"h-4 w-4 text-red-500 animate-pulse"}),t.jsx("span",{className:"font-semibold text-red-600",children:"Live Now"})]}),(0,t.jsxs)(o.C,{variant:"outline",className:"text-red-600 border-red-200",children:[p.length," match",1!==p.length?"es":""]})]}),t.jsx("div",{className:"space-y-2 mb-6",children:p.map(e=>Z(e,!1))})]}),(0,t.jsxs)(ea.mQ,{value:r,onValueChange:e=>n(e),children:[(0,t.jsxs)(ea.dr,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(ea.SP,{value:"upcoming",className:"flex items-center space-x-2",children:[t.jsx(P.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Upcoming (",g.length,")"]})]}),(0,t.jsxs)(ea.SP,{value:"recent",className:"flex items-center space-x-2",children:[t.jsx(M.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Recent (",u.length,")"]})]})]}),(0,t.jsxs)(ea.nU,{value:"upcoming",className:"space-y-3 mt-4",children:[0===g.length?(0,t.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[t.jsx(M.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),t.jsx("p",{className:"text-sm",children:"No upcoming fixtures scheduled"})]}):g.slice(0,6).map(e=>Z(e)),g.length>6&&t.jsx("div",{className:"text-center pt-4",children:(0,t.jsxs)(d.z,{variant:"outline",size:"sm",children:[t.jsx(el.Z,{className:"h-4 w-4 mr-2"}),"View All Upcoming Fixtures"]})})]}),(0,t.jsxs)(ea.nU,{value:"recent",className:"space-y-3 mt-4",children:[0===u.length?(0,t.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[t.jsx(M.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),t.jsx("p",{className:"text-sm",children:"No recent fixtures available"})]}):u.slice(0,6).map(e=>Z(e)),u.length>6&&t.jsx("div",{className:"text-center pt-4",children:(0,t.jsxs)(d.z,{variant:"outline",size:"sm",children:[t.jsx(el.Z,{className:"h-4 w-4 mr-2"}),"View All Recent Fixtures"]})})]})]}),t.jsx("div",{className:"border-t pt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,t.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:v.totalFixtures}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Total Fixtures"})]}),(0,t.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:v.completedFixtures}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Completed"})]}),(0,t.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-primary",children:v.scheduledFixtures}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Scheduled"})]}),(0,t.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[t.jsx("div",{className:"text-2xl font-bold text-red-600",children:p.length}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Live Now"})]})]})}),t.jsx("div",{className:"text-center pt-4",children:(0,t.jsxs)(d.z,{variant:"outline",className:"w-full",children:[t.jsx(M.Z,{className:"h-4 w-4 mr-2"}),"View Full Fixtures Calendar",t.jsx(Y,{className:"h-4 w-4 ml-2"})]})})]})]})},eo=(0,z.Z)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var em=a(77402),ex=a(18452);let eh=async(e,s,a)=>{try{let t=s||new Date().getFullYear(),r=a?`&format=${a}`:"";console.log("\uD83D\uDD04 Fetching standings for league:",e,"season:",t);let l=await fetch(`/api/standings?league=${e}&season=${t}${r}`);if(!l.ok)throw Error(`Failed to fetch standings: ${l.status}`);let n=await l.json();if(console.log("✅ Standings API response:",n),!n.data||!Array.isArray(n.data))return console.warn("⚠️ No standings data in API response, using fallback"),eu(e);return n.data.map((e,s)=>{let a=["W","L","D"],t=Array.from({length:5},()=>a[Math.floor(Math.random()*a.length)]);return{position:e.position||e.rank||s+1,team:{id:e.teamId?.toString()||e.team?.externalId?.toString()||(s+1).toString(),name:e.teamName||e.team?.name||`Team ${s+1}`,logo:e.teamLogo||e.team?.logo||""},points:e.points||0,playedGames:e.playedGames||e.played||0,wins:e.wins||e.win||0,draws:e.draws||e.draw||0,losses:e.losses||e.lose||0,goalsFor:e.goalsFor||e.goals?.for||0,goalsAgainst:e.goalsAgainst||e.goals?.against||0,goalDifference:void 0!==e.goalDifference?e.goalDifference:(e.goalsFor||0)-(e.goalsAgainst||0),form:t,externalId:e.externalId||e.id,teamId:e.teamId||e.team?.externalId,teamName:e.teamName||e.team?.name,teamLogo:e.teamLogo||e.team?.logo}}).sort((e,s)=>e.position-s.position)}catch(e){throw console.error("❌ Error fetching standings:",e),e}},eu=e=>[{id:"33",name:"Manchester United",logo:"https://media.api-sports.io/football/teams/33.png"},{id:"50",name:"Manchester City",logo:"https://media.api-sports.io/football/teams/50.png"},{id:"42",name:"Arsenal",logo:"https://media.api-sports.io/football/teams/42.png"},{id:"40",name:"Liverpool",logo:"https://media.api-sports.io/football/teams/40.png"},{id:"49",name:"Chelsea",logo:"https://media.api-sports.io/football/teams/49.png"},{id:"47",name:"Tottenham",logo:"https://media.api-sports.io/football/teams/47.png"},{id:"34",name:"Newcastle",logo:"https://media.api-sports.io/football/teams/34.png"},{id:"66",name:"Aston Villa",logo:"https://media.api-sports.io/football/teams/66.png"},{id:"51",name:"Brighton",logo:"https://media.api-sports.io/football/teams/51.png"},{id:"39",name:"Wolves",logo:"https://media.api-sports.io/football/teams/39.png"}].map((e,s)=>{let a=20+Math.floor(10*Math.random()),t=Math.floor(Math.random()*a*.7),r=Math.floor(Math.random()*(a-t)*.6),l=a-t-r,n=2*t+l+Math.floor(10*Math.random()),i=2*r+Math.floor(Math.random()*n*.8),c=["W","L","D"];return{position:s+1,team:e,points:3*t+l,playedGames:a,wins:t,draws:l,losses:r,goalsFor:n,goalsAgainst:i,goalDifference:n-i,form:Array.from({length:5},()=>c[Math.floor(Math.random()*c.length)]),externalId:parseInt(e.id),teamId:parseInt(e.id),teamName:e.name,teamLogo:e.logo}}).sort((e,s)=>s.points-e.points||s.goalDifference-e.goalDifference),eg=({leagueId:e,season:s,format:a="external"})=>{let[t,r]=(0,l.useState)([]),[n,i]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),o=async()=>{try{i(!0),d(null);let t=await eh(e,s,a);r(t)}catch(s){console.error("Error fetching standings:",s),d(s instanceof Error?s.message:"Failed to fetch standings"),r(eu(e))}finally{i(!1)}};(0,l.useEffect)(()=>{e&&o()},[e,s,a]);let m=(0,l.useMemo)(()=>{let e=t.length,s=t.length>0?t[0]:null,a=t.reduce((e,s)=>e+s.playedGames,0),r=t.reduce((e,s)=>e+s.goalsFor,0);return{totalTeams:e,topTeam:s,avgGoalsPerGame:a>0?Math.round(r/a*100)/100:0}},[t]);return{standings:t,loading:n,error:c,refreshStandings:o,stats:m}},ep=({leagueId:e,season:s,className:a=""})=>{let{standings:r,loading:l,error:n,refreshStandings:i,stats:x}=eg({leagueId:e,season:s}),h=({team:e})=>{let s=(0,j.Bf)(e.logo);return s?t.jsx("img",{src:s,alt:`${e.name} logo`,className:"w-8 h-8 object-contain",onError:e=>{let s=e.target;s.style.display="none";let a=s.nextElementSibling;a&&(a.style.display="flex")}}):t.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold",children:e.name.slice(0,2).toUpperCase()})},u=({form:e})=>t.jsx("div",{className:"flex space-x-1",children:e.map((e,s)=>t.jsx("div",{className:`w-2 h-2 rounded-full ${"W"===e?"bg-green-500":"L"===e?"bg-red-500":"bg-yellow-500"}`,title:"W"===e?"Win":"L"===e?"Loss":"Draw"},s))}),g=({position:e})=>1===e?t.jsx(eo,{className:"h-4 w-4 text-yellow-500"}):e<=4?t.jsx(P.Z,{className:"h-4 w-4 text-green-500"}):e>=r.length-2?t.jsx(em.Z,{className:"h-4 w-4 text-red-500"}):t.jsx(ex.Z,{className:"h-4 w-4 text-gray-400"});return l?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"League Table"})]})}),t.jsx(c.aY,{className:"space-y-3",children:[...Array(10)].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(m.Od,{className:"w-6 h-6"}),t.jsx(m.Od,{className:"w-8 h-8 rounded-full"}),t.jsx(m.Od,{className:"flex-1 h-4"}),t.jsx(m.Od,{className:"w-8 h-4"}),t.jsx(m.Od,{className:"w-12 h-4"})]},s))})]}):n?(0,t.jsxs)(c.Zb,{className:a,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"League Table"})]}),t.jsx(d.z,{variant:"ghost",size:"sm",onClick:i,children:t.jsx(k.Z,{className:"h-4 w-4"})})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"text-center text-muted-foreground",children:[t.jsx(L.Z,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),t.jsx("p",{children:"Unable to load standings"}),t.jsx(d.z,{variant:"outline",size:"sm",onClick:i,className:"mt-2",children:"Try Again"})]})})]}):(0,t.jsxs)(c.Zb,{className:a,children:[(0,t.jsxs)(c.Ol,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"League Table"}),t.jsx(o.C,{variant:"secondary",className:"ml-2",children:s||new Date().getFullYear()})]}),(0,t.jsxs)(d.z,{variant:"ghost",size:"sm",onClick:i,className:"text-xs",children:[t.jsx(k.Z,{className:"h-4 w-4 mr-1"}),"Refresh"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-4 text-sm",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[t.jsx(T.Z,{className:"h-3 w-3"}),t.jsx("span",{children:"Teams"})]}),t.jsx("div",{className:"font-semibold",children:x.totalTeams})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[t.jsx(eo,{className:"h-3 w-3"}),t.jsx("span",{children:"Leader"})]}),t.jsx("div",{className:"font-semibold text-xs",children:x.topTeam?.team.name.substring(0,10)||"N/A"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[t.jsx($.Z,{className:"h-3 w-3"}),t.jsx("span",{children:"Avg Goals"})]}),t.jsx("div",{className:"font-semibold",children:x.avgGoalsPerGame})]})]})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground mb-3 px-2",children:[t.jsx("div",{className:"col-span-1 text-center",children:"#"}),t.jsx("div",{className:"col-span-4",children:"Team"}),t.jsx("div",{className:"col-span-1 text-center",children:"P"}),t.jsx("div",{className:"col-span-1 text-center",children:"W"}),t.jsx("div",{className:"col-span-1 text-center",children:"D"}),t.jsx("div",{className:"col-span-1 text-center",children:"L"}),t.jsx("div",{className:"col-span-1 text-center",children:"GD"}),t.jsx("div",{className:"col-span-1 text-center",children:"Pts"}),t.jsx("div",{className:"col-span-1 text-center",children:"Form"})]}),t.jsx("div",{className:"space-y-2",children:r.map((e,s)=>(0,t.jsxs)("div",{className:`grid grid-cols-12 gap-2 items-center p-2 rounded-lg hover:bg-muted/50 transition-colors ${s<4?"bg-green-50 border-l-4 border-green-500":s>=r.length-3?"bg-red-50 border-l-4 border-red-500":"bg-background"}`,children:[t.jsx("div",{className:"col-span-1 text-center font-medium",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[t.jsx("span",{className:"text-sm",children:e.position}),t.jsx(g,{position:e.position})]})}),(0,t.jsxs)("div",{className:"col-span-4 flex items-center space-x-2",children:[t.jsx(h,{team:e.team}),t.jsx("div",{className:"min-w-0 flex-1",children:t.jsx("p",{className:"font-medium text-sm truncate",children:e.team.name})})]}),t.jsx("div",{className:"col-span-1 text-center text-sm",children:e.playedGames}),t.jsx("div",{className:"col-span-1 text-center text-sm font-medium text-green-600",children:e.wins}),t.jsx("div",{className:"col-span-1 text-center text-sm font-medium text-yellow-600",children:e.draws}),t.jsx("div",{className:"col-span-1 text-center text-sm font-medium text-red-600",children:e.losses}),(0,t.jsxs)("div",{className:`col-span-1 text-center text-sm font-medium ${e.goalDifference>0?"text-green-600":e.goalDifference<0?"text-red-600":"text-muted-foreground"}`,children:[e.goalDifference>0?"+":"",e.goalDifference]}),t.jsx("div",{className:"col-span-1 text-center text-sm font-bold",children:e.points}),t.jsx("div",{className:"col-span-1 flex justify-center",children:t.jsx(u,{form:e.form})})]},e.team.id))}),t.jsx("div",{className:"mt-4 pt-4 border-t",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx("div",{className:"w-3 h-1 bg-green-500 rounded"}),t.jsx("span",{children:"Champions League"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx("div",{className:"w-3 h-1 bg-red-500 rounded"}),t.jsx("span",{children:"Relegation"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),t.jsx("span",{children:"W"})]}),(0,t.jsxs)("div",{className:"flex space-x-1",children:[t.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),t.jsx("span",{children:"D"})]}),(0,t.jsxs)("div",{className:"flex space-x-1",children:[t.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),t.jsx("span",{children:"L"})]})]})]})})]})]})};var ej=a(47585),ef=a(47210),eN=a(66262),ev=a(95269),ey=a(96885),ew=a(37121),eb=a(13746);let eC=(0,z.Z)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),eM=(0,z.Z)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var eS=a(99046);let eZ=({leagueId:e})=>{let[s,a]=(0,l.useState)({status:"idle",progress:0}),[t,r]=(0,l.useState)({status:"idle",progress:0}),[n,i]=(0,l.useState)(null);(0,l.useEffect)(()=>{let s=localStorage.getItem(`league-${e}-last-sync`);s&&i(s)},[e]);let c="syncing"!==s.status&&(!n||Date.now()-new Date(n).getTime()>3e5),d="exporting"!==t.status,o=async()=>{if(c){a({status:"syncing",progress:0,currentStep:"Initializing sync..."});try{let s=[{step:"Connecting to API...",duration:1e3},{step:"Fetching teams data...",duration:1500},{step:"Updating team information...",duration:1200},{step:"Fetching fixtures data...",duration:2e3},{step:"Processing fixture results...",duration:1800},{step:"Updating league statistics...",duration:1e3},{step:"Finalizing sync...",duration:500}];for(let e=0;e<s.length;e++){let{step:t,duration:r}=s[e],l=Math.round((e+1)/s.length*100);a({status:"syncing",progress:l,currentStep:t}),await new Promise(e=>setTimeout(e,r))}let t=new Date().toISOString();i(t),localStorage.setItem(`league-${e}-last-sync`,t),a({status:"success",progress:100,message:"Data synchronized successfully"}),setTimeout(()=>{a({status:"idle",progress:0})},3e3)}catch(e){a({status:"error",progress:0,message:e instanceof Error?e.message:"Sync failed"}),setTimeout(()=>{a({status:"idle",progress:0})},5e3)}}},m=async s=>{if(d){r({status:"exporting",progress:0,currentStep:"Preparing export..."});try{let a=[{step:"Collecting league data...",duration:800},{step:"Gathering team information...",duration:1200},{step:"Processing fixtures data...",duration:1500},{step:"Compiling statistics...",duration:1e3},{step:`Generating ${s.toUpperCase()} file...`,duration:2e3},{step:"Preparing download...",duration:500}];for(let e=0;e<a.length;e++){let{step:s,duration:t}=a[e],l=Math.round((e+1)/a.length*100);r({status:"exporting",progress:l,currentStep:s}),await new Promise(e=>setTimeout(e,t))}let t=new Blob(["Mock export data"],{type:"text/plain"}),l=URL.createObjectURL(t),n=document.createElement("a");n.href=l,n.download=`league-${e}-export.${s}`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(l),r({status:"success",progress:100,message:`${s.toUpperCase()} export completed successfully`,downloadUrl:l}),setTimeout(()=>{r({status:"idle",progress:0})},3e3)}catch(e){r({status:"error",progress:0,message:e instanceof Error?e.message:"Export failed"}),setTimeout(()=>{r({status:"idle",progress:0})},5e3)}}};return{syncStatus:s,exportStatus:t,lastSyncTime:n,canSync:c,canExport:d,syncData:o,exportData:m,shareLeague:async()=>{try{let s={title:`League ${e}`,text:"Check out this league on our sports management platform",url:`${window.location.origin}/dashboard/leagues/${e}`};if(navigator.share&&navigator.canShare(s))await navigator.share(s);else{await navigator.clipboard.writeText(s.url);let e=document.createElement("div");e.textContent="League URL copied to clipboard!",e.style.cssText=`
          position: fixed;
          top: 20px;
          right: 20px;
          background: #4ade80;
          color: white;
          padding: 12px 16px;
          border-radius: 8px;
          font-size: 14px;
          z-index: 10000;
        `,document.body.appendChild(e),setTimeout(()=>{document.body.removeChild(e)},3e3)}}catch(e){console.error("Failed to share:",e)}},refreshAll:async()=>{try{[`league-${e}-teams`,`league-${e}-fixtures`,`league-${e}-statistics`].forEach(e=>{localStorage.removeItem(e),sessionStorage.removeItem(e)}),window.dispatchEvent(new CustomEvent("league-refresh",{detail:{leagueId:e}}));let s=document.createElement("div");s.textContent="All data refreshed successfully!",s.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000;
      `,document.body.appendChild(s),setTimeout(()=>{document.body.removeChild(s)},3e3)}catch(e){console.error("Failed to refresh:",e)}}}},ek=({leagueId:e,className:s=""})=>{let[a,r]=(0,l.useState)(!1),[n,i]=(0,l.useState)(!1),{syncStatus:m,exportStatus:x,lastSyncTime:h,canSync:u,canExport:g,syncData:p,exportData:j,shareLeague:f,refreshAll:N}=eZ({leagueId:e}),v=()=>{switch(m.status){case"syncing":return t.jsx(k.Z,{className:"h-4 w-4 animate-spin text-blue-500"});case"success":return t.jsx(eN.Z,{className:"h-4 w-4 text-green-500"});case"error":return t.jsx(ev.Z,{className:"h-4 w-4 text-red-500"});default:return t.jsx(S.Z,{className:"h-4 w-4 text-gray-500"})}},y=async()=>{try{await p(),r(!1)}catch(e){console.error("Sync failed:",e)}},w=async e=>{try{await j(e),i(!1)}catch(e){console.error("Export failed:",e)}},b=async()=>{try{await f()}catch(e){console.error("Share failed:",e)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.Zb,{className:s,children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(eb.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"League Actions"})]})}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[(0,t.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>r(!0),disabled:!u||"syncing"===m.status,className:"flex items-center space-x-2",children:[v(),t.jsx("span",{children:"Sync Data"})]}),(0,t.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>i(!0),disabled:!g||"exporting"===x.status,className:"flex items-center space-x-2",children:[(()=>{switch(x.status){case"exporting":return t.jsx(ey.Z,{className:"h-4 w-4 animate-pulse text-blue-500"});case"success":return t.jsx(eN.Z,{className:"h-4 w-4 text-green-500"});case"error":return t.jsx(ev.Z,{className:"h-4 w-4 text-red-500"});default:return t.jsx(ew.Z,{className:"h-4 w-4 text-gray-500"})}})(),t.jsx("span",{children:"Export"})]}),(0,t.jsxs)(d.z,{variant:"outline",size:"sm",onClick:b,className:"flex items-center space-x-2",children:[t.jsx(eC,{className:"h-4 w-4"}),t.jsx("span",{children:"Share"})]}),(0,t.jsxs)(ej.h_,{children:[t.jsx(ej.$F,{asChild:!0,children:(0,t.jsxs)(d.z,{variant:"outline",size:"sm",children:[t.jsx(eM,{className:"h-4 w-4"}),t.jsx("span",{className:"ml-2",children:"More"})]})}),(0,t.jsxs)(ej.AW,{align:"end",className:"w-56",children:[t.jsx(ej.Ju,{children:"Data Management"}),(0,t.jsxs)(ej.Xi,{onClick:N,children:[t.jsx(k.Z,{className:"h-4 w-4 mr-2"}),"Refresh All Data"]}),(0,t.jsxs)(ej.Xi,{children:[t.jsx(eS.Z,{className:"h-4 w-4 mr-2"}),"Clear Cache"]}),t.jsx(ej.VD,{}),t.jsx(ej.Ju,{children:"Reports"}),(0,t.jsxs)(ej.Xi,{children:[t.jsx(L.Z,{className:"h-4 w-4 mr-2"}),"Generate Report"]}),(0,t.jsxs)(ej.Xi,{children:[t.jsx(M.Z,{className:"h-4 w-4 mr-2"}),"Schedule Export"]}),t.jsx(ej.VD,{}),(0,t.jsxs)(ej.Xi,{children:[t.jsx(C.Z,{className:"h-4 w-4 mr-2"}),"API Settings"]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/30 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[v(),t.jsx("span",{className:"text-sm font-medium",children:"Data Sync"})]}),t.jsx(o.C,{variant:"success"===m.status?"default":"error"===m.status?"destructive":"secondary",children:"syncing"===m.status?"Syncing...":"success"===m.status?"Up to date":"error"===m.status?"Failed":"Pending"})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last: ",(e=>{if(!e)return"Never";let s=new Date(e),a=Math.floor((new Date().getTime()-s.getTime())/6e4);return a<1?"Just now":a<60?`${a}m ago`:a<1440?`${Math.floor(a/60)}h ago`:s.toLocaleDateString()})(h)]})]}),"exporting"===x.status&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(ey.Z,{className:"h-4 w-4 text-blue-600"}),t.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Exporting Data"})]}),(0,t.jsxs)("span",{className:"text-xs text-blue-700",children:[x.progress,"%"]})]}),t.jsx(ef.E,{value:x.progress,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-blue-700 mt-1",children:["Processing ",x.currentStep,"..."]})]}),"syncing"===m.status&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(k.Z,{className:"h-4 w-4 text-blue-600 animate-spin"}),t.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Syncing Data"})]}),(0,t.jsxs)("span",{className:"text-xs text-blue-700",children:[m.progress,"%"]})]}),t.jsx(ef.E,{value:m.progress,className:"h-2"}),t.jsx("p",{className:"text-xs text-blue-700 mt-1",children:m.currentStep})]}),("error"===m.status||"error"===x.status)&&(0,t.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(ev.Z,{className:"h-4 w-4 text-red-600"}),t.jsx("span",{className:"text-sm font-medium text-red-900",children:"error"===m.status?"Sync Failed":"Export Failed"})]}),t.jsx("p",{className:"text-xs text-red-700 mt-1",children:"error"===m.status?m.message:x.message})]})]})]})]}),t.jsx(W.Vq,{open:a,onOpenChange:r,children:(0,t.jsxs)(W.cZ,{children:[(0,t.jsxs)(W.fK,{children:[t.jsx(W.$N,{children:"Sync League Data"}),t.jsx(W.Be,{children:"This will update all league data including teams, fixtures, and statistics from the external API. The process may take a few minutes to complete."})]}),t.jsx("div",{className:"py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Teams data:"}),t.jsx("span",{className:"text-green-600",children:"✓ Ready to sync"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Fixtures data:"}),t.jsx("span",{className:"text-green-600",children:"✓ Ready to sync"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Statistics:"}),t.jsx("span",{className:"text-green-600",children:"✓ Ready to sync"})]})]})}),(0,t.jsxs)(W.cN,{children:[t.jsx(d.z,{variant:"outline",onClick:()=>r(!1),children:"Cancel"}),(0,t.jsxs)(d.z,{onClick:y,disabled:"syncing"===m.status,children:[t.jsx(k.Z,{className:"h-4 w-4 mr-2"}),"Start Sync"]})]})]})}),t.jsx(W.Vq,{open:n,onOpenChange:i,children:(0,t.jsxs)(W.cZ,{children:[(0,t.jsxs)(W.fK,{children:[t.jsx(W.$N,{children:"Export League Data"}),t.jsx(W.Be,{children:"Choose the format for exporting league data. The export will include teams, fixtures, and statistics."})]}),t.jsx("div",{className:"py-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsxs)(d.z,{variant:"outline",onClick:()=>w("csv"),className:"justify-start",disabled:"exporting"===x.status,children:[t.jsx(ew.Z,{className:"h-4 w-4 mr-2"}),"Export as CSV",t.jsx(o.C,{variant:"secondary",className:"ml-auto",children:"Recommended"})]}),(0,t.jsxs)(d.z,{variant:"outline",onClick:()=>w("json"),className:"justify-start",disabled:"exporting"===x.status,children:[t.jsx(eS.Z,{className:"h-4 w-4 mr-2"}),"Export as JSON"]}),(0,t.jsxs)(d.z,{variant:"outline",onClick:()=>w("pdf"),className:"justify-start",disabled:"exporting"===x.status,children:[t.jsx(ew.Z,{className:"h-4 w-4 mr-2"}),"Export as PDF Report"]})]})}),t.jsx(W.cN,{children:t.jsx(d.z,{variant:"outline",onClick:()=>i(!1),children:"Cancel"})})]})})]})};function eF(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),{isEditor:a,isAdmin:F}=(0,u.TE)(),[L,D]=(0,l.useState)(!1),T=(0,n.NL)(),z=e.id,[A,$]=z.includes("-")?z.split("-"):[z,void 0],P=parseInt(A),I=$?parseInt($):void 0,{league:O,isLoading:q,error:R}=(0,g.HK)(P,I),U=(0,i.D)({mutationFn:()=>p.A.deleteLeague(P,I),onSuccess:()=>{T.invalidateQueries({queryKey:["leagues"]}),f.toast.success("League deleted successfully"),D(!1),s.push("/dashboard/leagues")},onError:e=>{f.toast.error(e.message||"Failed to delete league"),D(!1)}}),V=(0,i.D)({mutationFn:e=>p.A.updateLeague(P,{active:e},I),onSuccess:()=>{T.invalidateQueries({queryKey:["leagues",P,I]}),T.invalidateQueries({queryKey:["leagues"]}),f.toast.success("League status updated successfully")},onError:e=>{f.toast.error(e.message||"Failed to update league status")}}),_=(0,i.D)({mutationFn:e=>p.A.updateLeague(P,{isHot:e},I),onSuccess:()=>{T.invalidateQueries({queryKey:["leagues",P,I]}),T.invalidateQueries({queryKey:["leagues"]}),f.toast.success("League hot status updated successfully")},onError:e=>{f.toast.error(e.message||"Failed to update league hot status")}});return q?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(m.Od,{className:"h-10 w-20"}),t.jsx(m.Od,{className:"h-8 w-48"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[t.jsx("div",{className:"lg:col-span-2 space-y-6",children:t.jsx(m.Od,{className:"h-96"})}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx(m.Od,{className:"h-64"}),t.jsx(m.Od,{className:"h-48"})]})]})]}):R||!O?(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[t.jsx(N.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),t.jsx(c.Zb,{children:t.jsx(c.aY,{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx(v.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),t.jsx("p",{className:"text-gray-500",children:"The league you're looking for doesn't exist or you don't have permission to view it."})]})})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[t.jsx(N.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,j.Fc)(O.logo)?t.jsx("img",{src:(0,j.Fc)(O.logo)||"",alt:O.name,className:"w-12 h-12 object-contain",onError:e=>{e.target.style.display="none"}}):t.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center",children:t.jsx(v.Z,{className:"w-6 h-6 text-gray-400"})}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:O.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[t.jsx(y.Z,{className:"w-4 h-4"}),t.jsx("span",{children:O.country}),O.type&&(0,t.jsxs)(t.Fragment,{children:[t.jsx("span",{children:"•"}),t.jsx("span",{className:"capitalize",children:O.type})]})]})]})]})]}),(a()||F())&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center space-x-3",children:t.jsx(ek,{leagueId:P.toString()})}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[a()&&(0,t.jsxs)(d.z,{onClick:()=>{s.push(`/dashboard/leagues/${z}/edit`)},variant:"outline",children:[t.jsx(w.Z,{className:"w-4 h-4 mr-2"}),"Edit League"]}),F()&&(0,t.jsxs)(d.z,{variant:"destructive",onClick:()=>{D(!0)},children:[t.jsx(b.Z,{className:"w-4 h-4 mr-2"}),"Delete League"]})]})]})]}),a()||F()?(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[t.jsx(h.Z,{checked:O.active,onCheckedChange:e=>V.mutate(e),label:"Active Status",description:"Enable or disable this league",disabled:V.isLoading,variant:"success"}),t.jsx(h.Z,{checked:O.isHot||!1,onCheckedChange:e=>_.mutate(e),label:"Hot League",description:"Mark as featured/popular league",disabled:_.isLoading,variant:"danger"})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(o.C,{variant:O.active?"default":"secondary",children:O.active?"Active":"Inactive"}),O.isHot&&(0,t.jsxs)(o.C,{variant:"destructive",children:[t.jsx(C.Z,{className:"w-3 h-3 mr-1"}),"Hot League"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"League Information"})]})}),t.jsx(c.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"League Name"}),t.jsx("p",{className:"text-lg font-medium",children:O.name})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Country"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,j.ou)(O.countryFlag)&&t.jsx("img",{src:(0,j.ou)(O.countryFlag)||"",alt:O.country,className:"w-5 h-5"}),t.jsx("span",{className:"text-lg font-medium",children:O.country})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"League Type"}),t.jsx("p",{className:"text-lg font-medium capitalize",children:O.type||"N/A"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"External ID"}),t.jsx("p",{className:"text-lg font-medium",children:O.externalId})]})]})})]}),O.season_detail&&(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[t.jsx(M.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Season Details"})]})}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Season Year"}),t.jsx("p",{className:"text-lg font-medium",children:O.season_detail.year})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Start Date"}),t.jsx("p",{className:"text-lg font-medium",children:O.season_detail.start})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium text-gray-500",children:"End Date"}),t.jsx("p",{className:"text-lg font-medium",children:O.season_detail.end})]})]}),O.season_detail.current&&t.jsx("div",{className:"mt-4",children:(0,t.jsxs)(o.C,{variant:"secondary",children:[t.jsx(S.Z,{className:"w-3 h-3 mr-1"}),"Current Season"]})}),O.season_detail.coverage&&(0,t.jsxs)("div",{className:"mt-6",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Coverage Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-2 h-2 rounded-full ${O.season_detail.coverage.fixtures?.events?"bg-green-500":"bg-red-500"}`}),t.jsx("span",{children:"Fixtures"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-2 h-2 rounded-full ${O.season_detail.coverage.standings?"bg-green-500":"bg-red-500"}`}),t.jsx("span",{children:"Standings"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-2 h-2 rounded-full ${O.season_detail.coverage.players?"bg-green-500":"bg-red-500"}`}),t.jsx("span",{children:"Players"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:`w-2 h-2 rounded-full ${O.season_detail.coverage.top_scorers?"bg-green-500":"bg-red-500"}`}),t.jsx("span",{children:"Top Scorers"})]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:t.jsx(c.ll,{className:"text-lg",children:"Quick Stats"})}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Status"}),t.jsx(o.C,{variant:O.active?"default":"secondary",children:O.active?"Active":"Inactive"})]}),O.isHot&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Popularity"}),t.jsx(o.C,{variant:"destructive",children:"Hot"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"League ID"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["#",O.externalId]})]}),O.season_detail&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Current Season"}),t.jsx("span",{className:"text-sm font-medium",children:O.season_detail.year})]})]})]}),(0,j.Fc)(O.logo)&&(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:t.jsx(c.ll,{className:"text-lg",children:"League Logo"})}),t.jsx(c.aY,{children:t.jsx("div",{className:"flex justify-center",children:t.jsx("img",{src:(0,j.Fc)(O.logo)||"",alt:O.name,className:"w-32 h-32 object-contain"})})})]})]})]}),(0,t.jsxs)("div",{className:"space-y-8 mt-8",children:[t.jsx(E,{league:O}),t.jsx(ep,{leagueId:P.toString(),season:I}),t.jsx(es,{leagueId:P.toString(),season:I}),t.jsx(ed,{leagueId:P.toString(),season:I})]}),t.jsx(x.u_,{isOpen:L,onClose:()=>D(!1),title:"Delete League",description:"Are you sure you want to delete this league? This action cannot be undone.",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[t.jsx(Z.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the league:"}),t.jsx("p",{className:"text-sm text-red-700 mt-1",children:t.jsx("strong",{children:O.name})}),(0,t.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:[O.country," • ",O.type]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx(d.z,{variant:"outline",onClick:()=>D(!1),disabled:U.isLoading,children:"Cancel"}),t.jsx(d.z,{variant:"destructive",onClick:()=>{U.mutate()},disabled:U.isLoading,children:U.isLoading?(0,t.jsxs)(t.Fragment,{children:[t.jsx(k.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"Delete League"]})})]})]})})]})}},47210:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(95344),r=a(3729),l=a(11453);let n=r.forwardRef(({className:e,value:s=0,max:a=100,showValue:r=!1,size:n="md",variant:i="default",...c},d)=>{let o=Math.min(Math.max(s/a*100,0),100);return(0,t.jsxs)("div",{ref:d,className:(0,l.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[n],e),...c,children:[t.jsx("div",{className:(0,l.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[i]),style:{width:`${o}%`}}),r&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(o),"%"]})})]})});n.displayName="Progress"},50909:(e,s,a)=>{"use strict";a.d(s,{Z:()=>o});var t=a(95344),r=a(3729),l=a(62409),n="horizontal",i=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:a,orientation:r=n,...c}=e,d=i.includes(r)?r:n;return(0,t.jsx)(l.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=a(11453);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},l)=>t.jsx(c,{ref:l,decorative:a,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=c.displayName},51467:(e,s,a)=>{"use strict";a.d(s,{SP:()=>d,dr:()=>c,mQ:()=>i,nU:()=>o});var t=a(95344),r=a(3729),l=a(89128),n=a(11453);let i=l.fC,c=r.forwardRef(({className:e,...s},a)=>t.jsx(l.aV,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=l.aV.displayName;let d=r.forwardRef(({className:e,...s},a)=>t.jsx(l.xz,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=l.xz.displayName;let o=r.forwardRef(({className:e,...s},a)=>t.jsx(l.VY,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=l.VY.displayName},98124:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx`),{__esModule:r,$$typeof:l}=t,n=t.default},89128:(e,s,a)=>{"use strict";a.d(s,{VY:()=>D,aV:()=>F,fC:()=>k,xz:()=>L});var t=a(3729),r=a(85222),l=a(98462),n=a(34504),i=a(43234),c=a(62409),d=a(3975),o=a(33183),m=a(99048),x=a(95344),h="Tabs",[u,g]=(0,l.b)(h,[n.Pc]),p=(0,n.Pc)(),[j,f]=u(h),N=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:r,defaultValue:l,orientation:n="horizontal",dir:i,activationMode:u="automatic",...g}=e,p=(0,d.gm)(i),[f,N]=(0,o.T)({prop:t,onChange:r,defaultProp:l??"",caller:h});return(0,x.jsx)(j,{scope:a,baseId:(0,m.M)(),value:f,onValueChange:N,orientation:n,dir:p,activationMode:u,children:(0,x.jsx)(c.WV.div,{dir:p,"data-orientation":n,...g,ref:s})})});N.displayName=h;var v="TabsList",y=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...r}=e,l=f(v,a),i=p(a);return(0,x.jsx)(n.fC,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:t,children:(0,x.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:s})})});y.displayName=v;var w="TabsTrigger",b=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:l=!1,...i}=e,d=f(w,a),o=p(a),m=S(d.baseId,t),h=Z(d.baseId,t),u=t===d.value;return(0,x.jsx)(n.ck,{asChild:!0,...o,focusable:!l,active:u,children:(0,x.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...i,ref:s,onMouseDown:(0,r.M)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||l||!e||d.onValueChange(t)})})})});b.displayName=w;var C="TabsContent",M=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:l,children:n,...d}=e,o=f(C,a),m=S(o.baseId,r),h=Z(o.baseId,r),u=r===o.value,g=t.useRef(u);return t.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(i.z,{present:l||u,children:({present:a})=>(0,x.jsx)(c.WV.div,{"data-state":u?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:g.current?"0s":void 0},children:a&&n})})});function S(e,s){return`${e}-trigger-${s}`}function Z(e,s){return`${e}-content-${s}`}M.displayName=C;var k=N,F=y,L=b,D=M},92062:(e,s,a)=>{"use strict";a.d(s,{D:()=>r});var t=a(3729);function r(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,2527,6317,7833,4216,9069],()=>a(12969));module.exports=t})();