(()=>{var e={};e.id=2175,e.ids=[2175],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},44065:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(50482),r=t(69108),l=t(62563),n=t.n(l),i=t(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["leagues",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23637)),"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx"],x="/dashboard/leagues/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/leagues/page",pathname:"/dashboard/leagues",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81273:(e,s,t)=>{Promise.resolve().then(t.bind(t,82281))},71532:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76394:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},82281:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(95344),r=t(3729),l=t(8428),n=t(11494),i=t(14373),c=t(23673),d=t(5094),o=t(46540),x=t(19591),u=t(77022),m=t(73875),h=t(67999),p=t(36487),g=t(69142),j=t(59836),y=t(11723),v=t(34755),f=t(30782),N=t(53148),b=t(46327),w=t(38271),C=t(51838),S=t(80958),k=t(30304),Z=t(76394),F=t(28765),L=t(65719),q=t(7361),A=t(96885),_=t(33733),P=t(95269);function z(){let e=new Date().getFullYear(),[s,t]=(0,r.useState)(e.toString()),[l,n]=(0,r.useState)(null),u=(0,i.D)({mutationFn:async e=>{let s=await fetch(`/api/leagues/sync?season=${e}&newdb=true`,{method:"GET"});if(!s.ok)throw Error((await s.json()).error||"Failed to sync leagues");return s.json()},onSuccess:e=>{n(e);let t=e.response?.length||0;v.toast.success(`Successfully synced ${t} leagues for season ${s}`)},onError:e=>{console.error("Sync error:",e),n({error:e.message}),v.toast.error(`Sync failed: ${e.message}`)}}),m=e=>{(""===e||/^\d{1,4}$/.test(e))&&t(e)};return(0,a.jsxs)(c.Zb,{className:"border-blue-200 bg-blue-50/50",children:[(0,a.jsxs)(c.Ol,{className:"pb-3",children:[(0,a.jsxs)(c.ll,{className:"text-lg flex items-center gap-2",children:[a.jsx(A.Z,{className:"w-5 h-5 text-blue-600"}),"Quick Sync Leagues"]}),a.jsx("p",{className:"text-sm text-gray-600",children:"Sync leagues data from external API for a specific season"})]}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(q._,{htmlFor:"season",children:"Season Year"}),a.jsx(o.I,{id:"season",type:"text",value:s,onChange:e=>m(e.target.value),placeholder:"2025",className:"w-32",maxLength:4}),a.jsx("p",{className:"text-xs text-gray-500",children:"Enter the year for the season you want to sync"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(d.z,{onClick:()=>{if(!s||isNaN(Number(s))){v.toast.error("Please enter a valid year");return}u.mutate(s)},disabled:u.isLoading||!s,className:"bg-blue-600 hover:bg-blue-700",children:u.isLoading?(0,a.jsxs)(a.Fragment,{children:[a.jsx(_.Z,{className:"w-4 h-4 mr-2 animate-spin"}),"Syncing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(A.Z,{className:"w-4 h-4 mr-2"}),"Sync Leagues"]})}),u.isLoading&&a.jsx(x.C,{variant:"secondary",className:"animate-pulse",children:"Please wait..."})]}),l&&a.jsx("div",{className:"pt-2 border-t",children:l.error?(0,a.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[a.jsx(P.Z,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"text-sm font-medium text-red-800",children:"Sync Failed"}),a.jsx("div",{className:"text-xs text-red-600",children:l.error}),l.details&&a.jsx("div",{className:"text-xs text-red-500",children:l.details})]})]}):(0,a.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-green-50 border border-green-200 rounded-md",children:[a.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"text-sm font-medium text-green-800",children:"Sync Completed Successfully"}),(0,a.jsxs)("div",{className:"text-xs text-green-600",children:[l.response?.length||0," leagues synced for season ",s]})]})]})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 bg-gray-50 p-2 rounded border",children:[a.jsx("strong",{children:"Note:"})," This will sync leagues data with newdb=true parameter, which may create new database entries. Use with caution."]})]})]})}function E(){let e=(0,l.useRouter)(),s=(0,n.NL)(),{isEditor:t,isAdmin:q}=(0,p.TE)(),[A,_]=(0,r.useState)({page:1,limit:20}),[P,E]=(0,r.useState)(""),[I,M]=(0,r.useState)(""),[$,O]=(0,r.useState)(!1),[Y,D]=(0,r.useState)(!1),[H,T]=(0,r.useState)(null),{leagues:G,leaguesMeta:Q,isLoading:K,error:R}=(0,g.sF)(A),U=(0,i.D)({mutationFn:({externalId:e,season:s})=>j.A.deleteLeague(e,s),onSuccess:()=>{s.invalidateQueries({queryKey:["leagues"]}),v.toast.success("League deleted successfully"),D(!1),T(null)},onError:e=>{console.error("Delete error:",e),v.toast.error(e?.response?.data?.message||"Failed to delete league")}}),X=(0,i.D)({mutationFn:({externalId:e,season:s,active:t})=>j.A.updateLeague(e,{active:t},s),onSuccess:()=>{s.invalidateQueries({queryKey:["leagues"]}),v.toast.success("League status updated successfully")},onError:e=>{console.error("Toggle status error:",e),v.toast.error(e?.response?.data?.message||"Failed to update league status")}}),B=(0,i.D)({mutationFn:({externalId:e,season:s,isHot:t})=>j.A.updateLeague(e,{isHot:t},s),onSuccess:()=>{s.invalidateQueries({queryKey:["leagues"]}),v.toast.success("League hot status updated successfully")},onError:e=>{console.error("Toggle hot error:",e),v.toast.error(e?.response?.data?.message||"Failed to update league hot status")}}),J=e=>{T(e),D(!0)},V=e=>{E(e),_(s=>({...s,search:e||void 0,page:1}))},W=e=>{_(s=>({...s,country:e||void 0,page:1}))},ee=e=>{_(s=>({...s,active:e,page:1}))},es=()=>new Date().getFullYear(),et=()=>G?Array.from(new Set(G.map(e=>e.season))).sort((e,s)=>s-e):[],ea=()=>{let e=es();return[e,e-1,e-2,e-3]},er=e=>{if("custom"===e){O(!0);return}_(s=>({...s,season:e?parseInt(e):void 0,page:1}))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Leagues Management"}),a.jsx("p",{className:"text-gray-600",children:"Manage football leagues, seasons, and configurations"})]}),t()&&(0,a.jsxs)(d.z,{onClick:()=>e.push("/dashboard/leagues/create"),children:[a.jsx(C.Z,{className:"w-4 h-4 mr-2"}),"Add League"]})]}),q()&&a.jsx(z,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Total Leagues"}),a.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:Q?.totalItems?.toLocaleString()||"Loading..."}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Across all countries"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Active Leagues"}),a.jsx(S.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:G?.filter(e=>e.active).length||0}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Currently running seasons"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Countries"}),a.jsx(k.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:new Set(G?.map(e=>e.country)).size||0}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Unique countries represented"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Hot Leagues"}),a.jsx(f.Z,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:G?.filter(e=>e.isHot).length||0}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Popular leagues"})]})]}),(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(c.ll,{className:"text-sm font-medium",children:"Active Seasons"}),a.jsx(S.Z,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(c.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:et().length||0}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Different seasons"})]})]})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[a.jsx(Z.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Filters & Search"})]})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(F.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx(o.I,{placeholder:"Search leagues...",value:P,onChange:e=>V(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>W(e.target.value),value:A.country||"",children:[a.jsx("option",{value:"",children:"All Countries"}),Array.from(new Set(G?.map(e=>e.country))).sort().map(e=>a.jsx("option",{value:e,children:e},e))]}),(0,a.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>er(e.target.value),value:A.season?.toString()||"",children:[a.jsx("option",{value:"",children:"All Seasons"}),ea().map(e=>a.jsx("option",{value:e.toString(),children:e===es()?`${e} (Current)`:e},e)),et().filter(e=>!ea().includes(e)).map(e=>a.jsx("option",{value:e.toString(),children:e},e)),a.jsx("option",{value:"custom",children:"✏️ Custom..."})]}),(0,a.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>ee(""===e.target.value?void 0:"true"===e.target.value),value:void 0===A.active?"":A.active.toString(),children:[a.jsx("option",{value:"",children:"All Status"}),a.jsx("option",{value:"true",children:"Active Only"}),a.jsx("option",{value:"false",children:"Inactive Only"})]}),a.jsx(d.z,{variant:"outline",onClick:()=>{E(""),_({page:1,limit:20}),M(""),O(!1)},children:"Clear Filters"})]})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Leagues List"})}),a.jsx(c.aY,{children:a.jsx(u.w,{columns:[{key:"logo",title:"",render:(e,s)=>a.jsx("div",{className:"w-16 h-16 flex items-center justify-center bg-white rounded shadow border border-gray-200",children:s.logo?a.jsx("img",{src:(0,y.Fc)(s.logo)||"",alt:s.name,className:"w-14 h-14 object-contain transition-transform duration-200 hover:scale-105",onError:e=>{e.target.style.display="none"}}):a.jsx(f.Z,{className:"w-10 h-10 text-gray-400"})})},{key:"name",title:"League Name",render:(e,s)=>(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"font-medium",children:s.name}),s.type&&a.jsx("div",{className:"text-sm text-gray-500 capitalize",children:s.type})]})},{key:"country",title:"Country",render:(e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[s.countryFlag&&a.jsx("img",{src:(0,y.ou)(s.countryFlag)||"",alt:s.country,className:"w-4 h-4"}),a.jsx("span",{children:s.country})]})},{key:"season",title:"Year",render:(e,s)=>(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("span",{className:`font-medium ${s.season===es()?"text-blue-600":""}`,children:s.season}),s.season===es()&&a.jsx("div",{className:"text-xs text-blue-500",children:"Current"})]})},{key:"season_detail",title:"Season",render:(e,s)=>{let t=s.season_detail;return t?(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"text-sm font-medium",children:t.year}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t.start," - ",t.end]}),t.current&&a.jsx(x.C,{variant:"secondary",className:"text-xs",children:"Current"})]}):a.jsx("span",{className:"text-gray-400",children:"-"})}},{key:"isHot",title:"Status",render:(e,s)=>a.jsx("div",{className:"space-y-2",children:t()||q()?(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{checked:s.active,onCheckedChange:e=>X.mutate({externalId:s.externalId,season:s.season,active:e}),label:"Active",disabled:X.isLoading,variant:"success",size:"sm"}),a.jsx(h.Z,{checked:s.isHot||!1,onCheckedChange:e=>B.mutate({externalId:s.externalId,season:s.season,isHot:e}),label:"Hot",disabled:B.isLoading,variant:"danger",size:"sm"})]}):(0,a.jsxs)("div",{className:"space-y-1",children:[s.isHot&&a.jsx(x.C,{variant:"destructive",className:"text-xs",children:"Hot"}),a.jsx(x.C,{variant:s.active?"default":"secondary",className:"text-xs",children:s.active?"Active":"Inactive"})]})})},{key:"actions",title:"Actions",render:(s,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>e.push(`/dashboard/leagues/${r.externalId}-${r.season}`),children:a.jsx(N.Z,{className:"w-4 h-4"})}),t()&&a.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>e.push(`/dashboard/leagues/${r.externalId}-${r.season}/edit`),children:a.jsx(b.Z,{className:"w-4 h-4"})}),q()&&a.jsx(d.z,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>J(r),children:a.jsx(w.Z,{className:"w-4 h-4"})})]})}],data:G||[],loading:K,pagination:{page:Q?.currentPage||1,limit:Q?.limit||20,total:Q?.totalItems||0,onPageChange:e=>{_(s=>({...s,page:e}))},onLimitChange:e=>{_(s=>({...s,limit:e,page:1}))}}})})]}),a.jsx(m.u_,{isOpen:Y,onClose:()=>{D(!1),T(null)},title:"Delete League",children:H&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[a.jsx(L.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium text-red-800",children:"Are you sure you want to delete this league?"}),a.jsx("p",{className:"text-sm text-red-600 mt-1",children:"This action cannot be undone. All related data will be permanently removed."})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[H.logo?a.jsx("img",{src:(0,y.Fc)(H.logo)||"",alt:H.name,className:"w-8 h-8 object-contain"}):a.jsx(f.Z,{className:"w-8 h-8 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-lg",children:H.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[H.countryFlag&&a.jsx("img",{src:(0,y.ou)(H.countryFlag)||"",alt:H.country,className:"w-4 h-4"}),a.jsx("span",{children:H.country}),H.type&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{children:"•"}),a.jsx("span",{className:"capitalize",children:H.type})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Status:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx(x.C,{variant:H.active?"default":"secondary",className:"text-xs",children:H.active?"Active":"Inactive"}),H.isHot&&a.jsx(x.C,{variant:"destructive",className:"text-xs",children:"Hot"})]})]}),H.season_detail&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-500",children:"Season:"}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("div",{className:"font-medium",children:H.season_detail.year}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[H.season_detail.start," - ",H.season_detail.end]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[a.jsx(d.z,{variant:"outline",onClick:()=>{D(!1),T(null)},disabled:U.isLoading,children:"Cancel"}),a.jsx(d.z,{variant:"destructive",onClick:()=>{H&&U.mutate({externalId:H.externalId,season:H.season})},disabled:U.isLoading,children:U.isLoading?"Deleting...":"Delete League"})]})]})}),a.jsx(m.u_,{isOpen:$,onClose:()=>{O(!1),M("")},title:"Custom Season Filter",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Season Year"}),a.jsx(o.I,{type:"number",placeholder:"e.g., 2023",value:I,onChange:e=>M(e.target.value),min:"2000",max:"2030",className:"w-full"}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Please enter a year between 2000 and 2030"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[a.jsx(d.z,{variant:"outline",onClick:()=>{O(!1),M("")},children:"Cancel"}),a.jsx(d.z,{onClick:()=>{let e=parseInt(I);e&&e>=2e3&&e<=2030?(_(s=>({...s,season:e,page:1})),O(!1),M(""),v.toast.success(`Filtering leagues for season ${e}`)):v.toast.error("Please enter a valid year (2000-2030)")},disabled:!I,children:"Apply Filter"})]})]})})]})}},7361:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var a=t(95344),r=t(3729),l=t(14217),n=t(49247),i=t(11453);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},t)=>a.jsx(l.f,{ref:t,className:(0,i.cn)(c(),e),...s}));d.displayName=l.f.displayName},23637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx`),{__esModule:r,$$typeof:l}=a,n=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,7966,2527,6317,7833,7022,9069],()=>t(44065));module.exports=a})();