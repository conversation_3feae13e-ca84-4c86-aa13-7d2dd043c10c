(()=>{var e={};e.id=5871,e.ids=[5871],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},10695:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(50482),s=t(69108),l=t(62563),i=t.n(l),n=t(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(a,o);let d=["",{children:["dashboard",{children:["leagues",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85185)),"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/leagues/create/page.tsx"],u="/dashboard/leagues/create/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/leagues/create/page",pathname:"/dashboard/leagues/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95493:(e,a,t)=>{Promise.resolve().then(t.bind(t,69619))},91991:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(97075).Z)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},76755:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(97075).Z)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},69619:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var r=t(95344),s=t(3729),l=t(8428),i=t(23673),n=t(5094),o=t(19591),d=t(50909),c=t(25179),u=t(79),m=t(69142),x=t(11723),p=t(11453),g=t(63024),h=t(66262),f=t(91991),y=t(65719),b=t(30782),j=t(76755),v=t(30304),N=t(31498),w=t(34755);let L=[{value:"league",label:"League",description:"Regular league with home/away format"},{value:"cup",label:"Cup",description:"Knockout tournament format"},{value:"playoffs",label:"Playoffs",description:"End-of-season playoff tournament"},{value:"friendly",label:"Friendly",description:"Non-competitive matches"},{value:"qualification",label:"Qualification",description:"Qualifying rounds for main competition"}],k=["England","Spain","Germany","Italy","France","Brazil","Argentina","Netherlands","Portugal","International"];function C(){let e=(0,l.useRouter)(),[a,t]=(0,s.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:"",externalId:void 0,logoFile:void 0}),[C,E]=(0,s.useState)({}),[S,I]=(0,s.useState)(!1),{createLeague:P,isCreateLoading:q}=(0,m.My)(),F=(e,a)=>{t(t=>({...t,[e]:a})),("name"===e||"country"===e||"type"===e||"logo"===e||"externalId"===e)&&E(a=>({...a,[e]:void 0}))},R=()=>{let e={};if(a.name.trim()?a.name.trim().length<2?e.name="League name must be at least 2 characters":a.name.trim().length>100&&(e.name="League name must be less than 100 characters"):e.name="League name is required",a.country.trim()?a.country.trim().length<2&&(e.country="Country must be at least 2 characters"):e.country="Country is required",a.type.trim()||(e.type="League type is required"),a.externalId&&(a.externalId<1||!Number.isInteger(a.externalId))&&(e.externalId="External ID must be a positive integer"),a.logo&&a.logo.trim()&&!a.logo.startsWith("uploaded-file://"))try{new URL(a.logo)}catch{e.logo="Please enter a valid URL"}return E(e),0===Object.keys(e).length},Z=()=>{let e=0;return a.name.trim()&&e++,a.country.trim()&&e++,a.type.trim()&&e++,Math.round(e/3*100)},_=a.name.trim()&&a.country.trim()&&a.type.trim(),$=t=>{if(t.preventDefault(),I(!0),!R()){w.toast.error("Please fix the form errors before submitting"),I(!1);return}let r=w.toast.loading("Creating league...");P({name:a.name.trim(),country:a.country.trim(),type:a.type.trim(),active:a.active,isHot:a.isHot,flag:"",season:new Date().getFullYear(),...a.logo&&{logo:a.logo.trim()},...a.externalId&&"number"==typeof a.externalId&&{externalId:a.externalId}},{onSuccess:t=>{w.toast.dismiss(r),localStorage.removeItem("league-create-draft"),w.toast.success(`League "${a.name}" created successfully!`,{description:"Redirecting to league details...",duration:3e3}),setTimeout(()=>{e.push(`/dashboard/leagues/${t.externalId}`)},500)},onError:e=>{w.toast.dismiss(r),w.toast.error(e.message||"Failed to create league. Please try again."),I(!1)}})};return(0,s.useEffect)(()=>{let a=a=>{(a.ctrlKey||a.metaKey)&&"s"===a.key&&(a.preventDefault(),!_||q||S||$(a)),"Escape"===a.key&&e.back()};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[_,q,S,e,$]),(0,s.useEffect)(()=>{let e=setTimeout(()=>{(a.name||a.country||a.type)&&localStorage.setItem("league-create-draft",JSON.stringify(a))},1e3);return()=>clearTimeout(e)},[a]),(0,s.useEffect)(()=>{let e=localStorage.getItem("league-create-draft");if(e)try{let a=JSON.parse(e);t(e=>({...e,...a})),w.toast.info("Draft restored from previous session")}catch(e){console.warn("Failed to load draft:",e)}},[]),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>e.back(),children:[r.jsx(g.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Create League"}),_&&(0,r.jsxs)(o.C,{variant:"secondary",className:"bg-green-100 text-green-800",children:[r.jsx(h.Z,{className:"w-3 h-3 mr-1"}),"Ready to submit"]})]}),r.jsx("p",{className:"text-gray-600 mt-1",children:"Add a new football league to the system"}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[r.jsx("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[Z(),"% complete"]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:r.jsx("div",{className:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:{width:`${Z()}%`}})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[r.jsx(i.Zb,{className:"border-blue-200 bg-blue-50",children:(0,r.jsxs)(i.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[r.jsx(f.Z,{className:"w-4 h-4 text-blue-600"}),r.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Quick Setup"})]}),r.jsx("p",{className:"text-xs text-blue-700",children:"Fill in the basic information to get started. You can edit all details later."}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-blue-600",children:[r.jsx("kbd",{className:"px-1.5 py-0.5 bg-white rounded border",children:"Ctrl+S"})," to save quickly"]})]})}),r.jsx(i.Zb,{className:"border-green-200 bg-green-50",children:(0,r.jsxs)(i.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[r.jsx(h.Z,{className:"w-4 h-4 text-green-600"}),r.jsx("span",{className:"text-sm font-medium text-green-800",children:"Auto-Save"})]}),r.jsx("p",{className:"text-xs text-green-700",children:"Your progress is automatically saved as you type. No data loss!"}),!1]})}),r.jsx(i.Zb,{className:"border-orange-200 bg-orange-50",children:(0,r.jsxs)(i.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[r.jsx(y.Z,{className:"w-4 h-4 text-orange-600"}),r.jsx("span",{className:"text-sm font-medium text-orange-800",children:"Logo Upload"})]}),r.jsx("p",{className:"text-xs text-orange-700",children:"File upload feature requires backend API. URL method works immediately."})]})})]}),r.jsx("form",{onSubmit:$,children:(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[r.jsx(b.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"League Information"})]}),r.jsx(i.SZ,{children:"Enter the league details and configuration settings."})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[r.jsx(c.hj,{title:"Basic Information",description:"Enter the core details that identify this league",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsx(c.UP,{label:"League Name",placeholder:"e.g., Premier League, Champions League",required:!0,value:a.name,onChange:e=>F("name",e.target.value),error:C.name,description:"The official name of the football league"}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c.UP,{label:"Country",placeholder:"e.g., England, Spain, International",required:!0,value:a.country,onChange:e=>F("country",e.target.value),error:C.country,description:"The country or region where this league operates"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[r.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"Popular:"}),k.map(e=>r.jsx(o.C,{variant:"outline",className:"text-xs cursor-pointer hover:bg-blue-50 hover:border-blue-300",onClick:()=>F("country",e),children:e},e))]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:a.type,onValueChange:e=>F("type",e),options:L.map(e=>({value:e.value,label:e.label})),error:C.type,description:"Choose the format that best describes this competition"}),a.type&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(b.Z,{className:"w-4 h-4 text-blue-600"}),r.jsx("span",{className:"text-sm font-medium text-blue-800",children:L.find(e=>e.value===a.type)?.label})]}),r.jsx("p",{className:"text-xs text-blue-700 mt-1",children:L.find(e=>e.value===a.type)?.description})]})]}),r.jsx(c.UP,{label:"External ID (Optional)",placeholder:"Enter external API ID",type:"number",value:a.externalId||"",onChange:e=>F("externalId",e.target.value?parseInt(e.target.value):void 0),error:C.externalId,description:"Used for syncing with external APIs (e.g., Football API, ESPN)"})]})}),r.jsx(d.Z,{}),r.jsx(c.hj,{title:"Visual Identity",description:"Add a logo to make the league easily recognizable",children:r.jsx("div",{className:"grid grid-cols-1 gap-6",children:(0,r.jsxs)("div",{className:"md:col-span-2",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"League Logo (Optional)"}),r.jsx(u.U,{value:a.logo,onChange:e=>F("logo",e),onFileSelect:e=>{t(a=>({...a,logoFile:e})),w.toast.info("Logo file selected. Note: File upload will be implemented when backend API is ready.")},error:C.logo,accept:"image/*",maxSize:5,placeholder:"Add league logo via URL or upload file",description:"Upload an image file or paste an image URL. Recommended size: 200x200px or larger."})]})})}),r.jsx(d.Z,{}),r.jsx(c.hj,{title:"Configuration",description:"Set the initial status and visibility settings",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c.mg,{label:"Initial Status",value:a.active.toString(),onValueChange:e=>F("active","true"===e),options:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}],description:"Active leagues are visible to users and can have fixtures"}),r.jsx("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:a.active?(0,r.jsxs)(r.Fragment,{children:[r.jsx(h.Z,{className:"w-3 h-3 text-green-500"}),r.jsx("span",{children:"League will be immediately available"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(y.Z,{className:"w-3 h-3 text-orange-500"}),r.jsx("span",{children:"League will be hidden until activated"})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c.mg,{label:"Hot League",description:"Mark as hot/popular league for prominence",value:a.isHot.toString(),onValueChange:e=>F("isHot","true"===e),options:[{value:"false",label:"Normal League"},{value:"true",label:"Hot League"}]}),r.jsx("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:a.isHot?(0,r.jsxs)(r.Fragment,{children:[r.jsx(j.Z,{className:"w-3 h-3 text-yellow-500"}),r.jsx("span",{children:"Will appear in featured/popular leagues"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(v.Z,{className:"w-3 h-3 text-gray-400"}),r.jsx("span",{children:"Standard visibility in league listings"})]})})]})]})}),a.logo&&r.jsx(c.hj,{title:"Logo Preview",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[r.jsx("img",{src:(0,x.Fc)(a.logo)||a.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let a=e.target;a.src="",a.style.display="none",w.toast.error("Failed to load logo image")}}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium",children:"Logo Preview"}),r.jsx("p",{className:"text-xs text-gray-500 break-all",children:a.logo})]})]})}),(0,r.jsxs)(c.iN,{children:[r.jsx(n.z,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:q||S,children:"Cancel"}),r.jsx(n.z,{type:"submit",disabled:q||S||!_,className:(0,p.cn)("transition-all duration-200",_?"bg-blue-600 hover:bg-blue-700":""),children:q||S?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(N.Z,{className:"w-4 h-4 mr-2"}),"Create League"]})})]})]})]})})]})}},25179:(e,a,t)=>{"use strict";t.d(a,{ji:()=>y,iN:()=>j,hj:()=>b,UP:()=>g,mg:()=>f,XL:()=>h});var r=t(95344),s=t(3729),l=t(7361),i=t(46540),n=t(2690),o=t(38157),d=t(85684),c=t(62312),u=t(11453);let m=s.forwardRef(({className:e,...a},t)=>r.jsx(d.fC,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...a,children:r.jsx(d.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:r.jsx(c.Z,{className:"h-4 w-4"})})}));m.displayName=d.fC.displayName;var x=t(68065);let p=(0,s.forwardRef)(({label:e,description:a,error:t,required:s,className:i,children:n},o)=>(0,r.jsxs)("div",{ref:o,className:(0,u.cn)("space-y-2",i),children:[e&&(0,r.jsxs)(l._,{className:(0,u.cn)("text-sm font-medium",t&&"text-red-600"),children:[e,s&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),n,a&&!t&&r.jsx("p",{className:"text-sm text-gray-500",children:a}),t&&r.jsx("p",{className:"text-sm text-red-600",children:t})]}));p.displayName="FormField";let g=(0,s.forwardRef)(({label:e,description:a,error:t,required:s,className:l,...n},o)=>r.jsx(p,{label:e,description:a,error:t,required:s,children:r.jsx(i.I,{ref:o,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",l),...n})}));g.displayName="InputField";let h=(0,s.forwardRef)(({label:e,description:a,error:t,required:s,className:l,...i},o)=>r.jsx(p,{label:e,description:a,error:t,required:s,children:r.jsx(n.g,{ref:o,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",l),...i})}));h.displayName="TextareaField";let f=(0,s.forwardRef)(({label:e,description:a,error:t,required:s,placeholder:l,value:i,onValueChange:n,options:d,className:c,disabled:m},x)=>{let g=d.find(e=>e.value===i),h="http://*************";return r.jsx(p,{label:e,description:a,error:t,required:s,children:(0,r.jsxs)(o.Ph,{value:i,onValueChange:n,disabled:m,children:[r.jsx(o.i4,{ref:x,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",c),children:r.jsx("div",{className:"flex items-center justify-between w-full",children:r.jsx("div",{className:"flex items-center space-x-2 flex-1",children:g?g?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[g.logo&&r.jsx("img",{src:`${h}/${g.logo}`,alt:g.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),r.jsx("span",{children:g.label})]}):l:r.jsx("span",{className:"text-muted-foreground",children:l})})})}),r.jsx(o.Bw,{children:d.map(e=>r.jsx(o.Ql,{value:e.value,disabled:e.disabled,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&r.jsx("img",{src:`${h}/${e.logo}`,alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),r.jsx("span",{children:e.label})]})},e.value))})]})})});f.displayName="SelectField";let y=(0,s.forwardRef)(({label:e,description:a,error:t,checked:s,onCheckedChange:i,className:n},o)=>r.jsx(p,{description:a,error:t,className:n,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(m,{ref:o,checked:s,onCheckedChange:i,className:(0,u.cn)(t&&"border-red-500")}),e&&r.jsx(l._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",t&&"text-red-600"),children:e})]})}));y.displayName="CheckboxField",(0,s.forwardRef)(({label:e,description:a,error:t,required:s,value:i,onValueChange:n,options:o,orientation:d="vertical",className:c},m)=>r.jsx(p,{label:e,description:a,error:t,required:s,className:c,children:r.jsx(x.E,{ref:m,value:i,onValueChange:n,className:(0,u.cn)("horizontal"===d?"flex flex-row space-x-4":"space-y-2"),children:o.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(x.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(t&&"border-red-500")}),r.jsx(l._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})).displayName="RadioField";let b=({title:e,description:a,children:t,className:s})=>(0,r.jsxs)("div",{className:(0,u.cn)("space-y-4",s),children:[(e||a)&&(0,r.jsxs)("div",{className:"space-y-1",children:[e&&r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e}),a&&r.jsx("p",{className:"text-sm text-gray-600",children:a})]}),r.jsx("div",{className:"space-y-4",children:t})]}),j=({children:e,className:a,align:t="right"})=>r.jsx("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===t&&"justify-start","center"===t&&"justify-center","right"===t&&"justify-end",a),children:e})},38157:(e,a,t)=>{"use strict";t.d(a,{Bw:()=>g,Ph:()=>c,Ql:()=>h,i4:()=>m,ki:()=>u});var r=t(95344),s=t(3729),l=t(32116),i=t(25390),n=t(12704),o=t(62312),d=t(11453);let c=l.fC;l.ZA;let u=l.B4,m=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.xz,{ref:s,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,r.jsx(l.JO,{asChild:!0,children:r.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=l.xz.displayName;let x=s.forwardRef(({className:e,...a},t)=>r.jsx(l.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(n.Z,{className:"h-4 w-4"})}));x.displayName=l.u_.displayName;let p=s.forwardRef(({className:e,...a},t)=>r.jsx(l.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(i.Z,{className:"h-4 w-4"})}));p.displayName=l.$G.displayName;let g=s.forwardRef(({className:e,children:a,position:t="popper",...s},i)=>r.jsx(l.h_,{children:(0,r.jsxs)(l.VY,{ref:i,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...s,children:[r.jsx(x,{}),r.jsx(l.l_,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),r.jsx(p,{})]})}));g.displayName=l.VY.displayName,s.forwardRef(({className:e,...a},t)=>r.jsx(l.__,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",e),...a})).displayName=l.__.displayName;let h=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.ck,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(l.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),r.jsx(l.eT,{children:a})]}));h.displayName=l.ck.displayName,s.forwardRef(({className:e,...a},t)=>r.jsx(l.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=l.Z0.displayName},50909:(e,a,t)=>{"use strict";t.d(a,{Z:()=>c});var r=t(95344),s=t(3729),l=t(62409),i="horizontal",n=["horizontal","vertical"],o=s.forwardRef((e,a)=>{let{decorative:t,orientation:s=i,...o}=e,d=n.includes(s)?s:i;return(0,r.jsx)(l.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...o,ref:a})});o.displayName="Separator";var d=t(11453);let c=s.forwardRef(({className:e,orientation:a="horizontal",decorative:t=!0,...s},l)=>r.jsx(o,{ref:l,decorative:t,orientation:a,className:(0,d.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...s}));c.displayName=o.displayName},59836:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var r=t(50053);let s=()=>null,l={getLeagues:async(e={})=>{let a=new URLSearchParams;Object.entries(e).forEach(([e,t])=>{void 0!==t&&a.append(e,t.toString())});let t=await fetch(`/api/leagues?${a.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch leagues");return await t.json()},getLeagueById:async(e,a)=>{let t=a?`${e}-${a}`:e.toString(),r=await fetch(`/api/leagues/${t}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||`Failed to fetch league ${e}`);return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,a,t)=>{let r=s(),i={"Content-Type":"application/json"};r&&(i.Authorization=`Bearer ${r}`);let n=await l.getLeagueById(e,t);if(!n||!n.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);let o=await fetch(`/api/leagues/${n.id}`,{method:"PATCH",headers:i,body:JSON.stringify(a)});if(!o.ok)throw Error((await o.json()).message||`Failed to update league ${e}`);return await o.json()},deleteLeague:async(e,a)=>{let t=await l.getLeagueById(e,a);if(!t||!t.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);await r.x.delete(`/football/leagues/${t.id}`)},getActiveLeagues:async()=>l.getLeagues({active:!0}),getLeaguesByCountry:async e=>l.getLeagues({country:e}),toggleLeagueStatus:async(e,a,t)=>l.updateLeague(e,{active:a},t)}},69142:(e,a,t)=>{"use strict";t.d(a,{HK:()=>o,My:()=>d,sF:()=>n});var r=t(19738),s=t(11494),l=t(14373),i=t(59836);let n=(e={})=>{let a=(0,r.a)({queryKey:["leagues",e],queryFn:()=>i.A.getLeagues(e),staleTime:6e5});return{leagues:a.data?.data||[],leaguesMeta:a.data?.meta,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},o=(e,a)=>{let t=(0,r.a)({queryKey:["leagues",e,a],queryFn:()=>i.A.getLeagueById(e,a),enabled:!!e,staleTime:6e5});return{league:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},d=()=>{let e=(0,s.NL)(),a=(0,l.D)({mutationFn:e=>i.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),t=(0,l.D)({mutationFn:({externalId:e,data:a,season:t})=>i.A.updateLeague(e,a,t),onSuccess:(a,t)=>{e.setQueryData(["leagues",a.externalId,t.season],a),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,l.D)({mutationFn:({id:e,active:a})=>i.A.toggleLeagueStatus(e,a),onSuccess:a=>{e.setQueryData(["leagues",a.externalId],a),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:a.mutate,isCreateLoading:a.isLoading,createError:a.error,createData:a.data,updateLeague:t.mutate,isUpdateLoading:t.isLoading,updateError:t.error,updateData:t.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},11723:(e,a,t)=>{"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let a=e.startsWith("/")?e.slice(1):e;return`http://*************/${a}`}function s(e){return r(e)}function l(e){return r(e)}function i(e){return r(e)}t.d(a,{Bf:()=>s,Fc:()=>i,Sc:()=>r,ou:()=>l})},85185:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>l,__esModule:()=>s,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/leagues/create/page.tsx`),{__esModule:s,$$typeof:l}=r,i=r.default},85684:(e,a,t)=>{"use strict";t.d(a,{fC:()=>v,z$:()=>w});var r=t(3729),s=t(31405),l=t(98462),i=t(85222),n=t(33183),o=t(92062),d=t(63085),c=t(43234),u=t(62409),m=t(95344),x="Checkbox",[p,g]=(0,l.b)(x),[h,f]=p(x);function y(e){let{__scopeCheckbox:a,checked:t,children:s,defaultChecked:l,disabled:i,form:o,name:d,onCheckedChange:c,required:u,value:p="on",internal_do_not_use_render:g}=e,[f,y]=(0,n.T)({prop:t,defaultProp:l??!1,onChange:c,caller:x}),[b,j]=r.useState(null),[v,N]=r.useState(null),w=r.useRef(!1),L=!b||!!o||!!b.closest("form"),k={checked:f,disabled:i,setChecked:y,control:b,setControl:j,name:d,form:o,value:p,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!C(l)&&l,isFormControl:L,bubbleInput:v,setBubbleInput:N};return(0,m.jsx)(h,{scope:a,...k,children:"function"==typeof g?g(k):s})}var b="CheckboxTrigger",j=r.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:t,...l},n)=>{let{control:o,value:d,disabled:c,checked:x,required:p,setControl:g,setChecked:h,hasConsumerStoppedPropagationRef:y,isFormControl:j,bubbleInput:v}=f(b,e),N=(0,s.e)(n,g),w=r.useRef(x);return r.useEffect(()=>{let e=o?.form;if(e){let a=()=>h(w.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[o,h]),(0,m.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":C(x)?"mixed":x,"aria-required":p,"data-state":E(x),"data-disabled":c?"":void 0,disabled:c,value:d,...l,ref:N,onKeyDown:(0,i.M)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(t,e=>{h(e=>!!C(e)||!e),v&&j&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});j.displayName=b;var v=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:r,checked:s,defaultChecked:l,required:i,disabled:n,value:o,onCheckedChange:d,form:c,...u}=e;return(0,m.jsx)(y,{__scopeCheckbox:t,checked:s,defaultChecked:l,disabled:n,required:i,onCheckedChange:d,name:r,form:c,value:o,internal_do_not_use_render:({isFormControl:e})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(j,{...u,ref:a,__scopeCheckbox:t}),e&&(0,m.jsx)(k,{__scopeCheckbox:t})]})})});v.displayName=x;var N="CheckboxIndicator",w=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:r,...s}=e,l=f(N,t);return(0,m.jsx)(c.z,{present:r||C(l.checked)||!0===l.checked,children:(0,m.jsx)(u.WV.span,{"data-state":E(l.checked),"data-disabled":l.disabled?"":void 0,...s,ref:a,style:{pointerEvents:"none",...e.style}})})});w.displayName=N;var L="CheckboxBubbleInput",k=r.forwardRef(({__scopeCheckbox:e,...a},t)=>{let{control:l,hasConsumerStoppedPropagationRef:i,checked:n,defaultChecked:c,required:x,disabled:p,name:g,value:h,form:y,bubbleInput:b,setBubbleInput:j}=f(L,e),v=(0,s.e)(t,j),N=(0,o.D)(n),w=(0,d.t)(l);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!i.current;if(N!==n&&e){let t=new Event("click",{bubbles:a});b.indeterminate=C(n),e.call(b,!C(n)&&n),b.dispatchEvent(t)}},[b,N,n,i]);let k=r.useRef(!C(n)&&n);return(0,m.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??k.current,required:x,disabled:p,name:g,value:h,form:y,...a,tabIndex:-1,ref:v,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function E(e){return C(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=L}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6126,337,2609,3649,732,7966,8530,6317,7833,958],()=>t(10695));module.exports=r})();