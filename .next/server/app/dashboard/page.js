(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},29518:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(50482),r=s(69108),i=s(62563),o=s.n(i),n=s(68300),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99468)),"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx"],u="/dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},14254:(e,t,s)=>{Promise.resolve().then(s.bind(s,52038))},25545:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52038:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(95344),r=s(48333),i=s(36487),o=s(19738),n=s(23673),l=s(19591),c=s(55794),d=s(81137),u=s(30782),x=s(25545),p=s(23485),h=s(89895),g=s(20255),m=s(59836),f=s(56506);function y(){let{user:e}=(0,r.a)(),{isAdmin:t,isEditor:s,isModerator:y}=(0,i.TE)(),{data:j}=(0,o.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>g.L.getUpcomingAndLive({limit:5}),refetchInterval:3e4}),{data:w}=(0,o.a)({queryKey:["fixtures","all"],queryFn:()=>g.L.getFixtures({limit:1})}),{data:v}=(0,o.a)({queryKey:["leagues","all"],queryFn:()=>m.A.getLeagues({limit:1})}),b=[{title:"Total Fixtures",value:w?.meta?.totalItems?.toLocaleString()||"Loading...",icon:c.Z,description:"Active fixtures in database",loading:!w},{title:"Live Matches",value:j?.data?.filter(e=>["1H","2H","HT"].includes(e.status)).length||"0",icon:d.Z,description:"Currently live matches",loading:!j},{title:"Active Leagues",value:v?.meta?.totalItems?.toLocaleString()||"Loading...",icon:u.Z,description:"Currently active leagues",loading:!v},{title:"Upcoming Today",value:j?.data?.filter(e=>"NS"===e.status).length||"0",icon:x.Z,description:"Matches scheduled today",loading:!j}];return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"bg-white rounded-lg border p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[(()=>{let e=new Date().getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"})(),", ",e?.fullName||e?.username,"!"]}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Welcome to the APISportsGame Content Management System"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(l.C,{className:`${(e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"editor":return"bg-blue-100 text-blue-800";case"moderator":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(e?.role||"")} flex items-center space-x-1`,children:[a.jsx(p.Z,{className:"h-3 w-3"}),a.jsx("span",{className:"capitalize",children:e?.role})]}),(0,a.jsxs)("div",{className:"text-right text-sm text-gray-500",children:[a.jsx("p",{children:"Last login"}),a.jsx("p",{className:"font-medium",children:e?.lastLoginAt?new Date(e.lastLoginAt).toLocaleDateString():"First time"})]})]})]})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,t)=>{let s=e.icon;return a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.title}),a.jsx("p",{className:`text-2xl font-bold ${e.loading?"animate-pulse text-gray-400":"text-gray-900"}`,children:e.value}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),a.jsx("div",{className:"bg-blue-100 p-3 rounded-full",children:a.jsx(s,{className:"h-6 w-6 text-blue-600"})})]})})},t)})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(x.Z,{className:"mr-2 h-5 w-5"}),"Recent Activities"]}),a.jsx(n.SZ,{children:"Latest system activities and updates"})]}),a.jsx(n.aY,{children:a.jsx("div",{className:"space-y-4",children:[{action:"Fixture sync completed",time:"2 minutes ago",type:"sync"},{action:"New broadcast link added",time:"15 minutes ago",type:"broadcast"},{action:"League updated: Premier League",time:"1 hour ago",type:"league"},{action:"User tier upgraded",time:"2 hours ago",type:"user"}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.action}),a.jsx("p",{className:"text-xs text-gray-500",children:e.time})]})]},t))})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"Quick Actions"}),a.jsx(n.SZ,{children:"Common tasks and shortcuts"})]}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(f.default,{href:"/dashboard/fixtures/live",className:"block",children:a.jsx("div",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(c.Z,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"View Live Fixtures"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Check ongoing matches"})]})]}),(j?.data?.filter(e=>["1H","2H","HT"].includes(e.status))?.length||0)>0&&(0,a.jsxs)(l.C,{className:"bg-green-100 text-green-800",children:[j?.data?.filter(e=>["1H","2H","HT"].includes(e.status))?.length||0," Live"]})]})})}),s()&&a.jsx("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(d.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Add Broadcast Link"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Add new streaming link"})]})]})}),t()&&a.jsx("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(h.Z,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Manage Users"}),a.jsx("p",{className:"text-sm text-gray-500",children:"User administration"})]})]})})]})})]})]})]})}},20255:(e,t,s)=>{"use strict";s.d(t,{L:()=>r});var a=s(50053);let r={getFixtures:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch fixtures: ${s.statusText}`);return await s.json()},getFixtureById:async e=>{let t=await fetch(`/api/fixtures/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture: ${t.statusText}`);return await t.json()},getUpcomingAndLive:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures/live?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch live fixtures: ${s.statusText}`);return await s.json()},getTeamSchedule:async(e,t={})=>{let s=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())}),await a.x.get(`/football/fixtures/schedules/${e}?${s.toString()}`)},getFixtureStatistics:async e=>await a.x.get(`/football/fixtures/statistics/${e}`),triggerSeasonSync:async()=>{let e=(console.warn("❌ Season sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger season sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Season sync successful"),s},triggerDailySync:async()=>{let e=(console.warn("❌ Daily sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger daily sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Daily sync successful"),s},getSyncStatus:async()=>{let e=(console.warn("❌ Sync status - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||`Failed to get sync status: ${t.statusText}`)}let s=await t.json();return console.log("✅ Sync status successful"),s},createFixture:async e=>{let t=(console.warn("❌ Create fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!t.Authorization,data:e});let s=await fetch("/api/fixtures",{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to create fixture: ${s.statusText}`)}let a=await s.json();return console.log("✅ Create fixture successful:",a.data?.id),a.data||a},updateFixture:async(e,t)=>{let s=(console.warn("❌ Update fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!s.Authorization,data:t});let a=await fetch(`/api/fixtures/${e}`,{method:"PUT",headers:s,body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",a.status,a.statusText,e),Error(e.message||`Failed to update fixture: ${a.statusText}`)}let r=await a.json();return console.log("✅ Update fixture successful:",e),r.data||r},deleteFixture:async e=>{let t=(console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let s=await fetch(`/api/fixtures/${e}`,{method:"DELETE",headers:t});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to delete fixture: ${s.statusText}`)}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch(`/api/fixtures/${e}/statistics`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture statistics: ${t.statusText}`);return await t.json()},getFixtureEvents:async e=>{let t=await fetch(`/api/fixtures/${e}/events`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture events: ${t.statusText}`);return await t.json()},getFixture:async e=>(await r.getFixtureById(e)).data}},59836:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(50053);let r=()=>null,i={getLeagues:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/leagues?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,t)=>{let s=t?`${e}-${t}`:e.toString(),a=await fetch(`/api/leagues/${s}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||`Failed to fetch league ${e}`);return await a.json()},createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t,s)=>{let a=r(),o={"Content-Type":"application/json"};a&&(o.Authorization=`Bearer ${a}`);let n=await i.getLeagueById(e,s);if(!n||!n.id)throw Error(`League not found: ${e}${s?`-${s}`:""}`);let l=await fetch(`/api/leagues/${n.id}`,{method:"PATCH",headers:o,body:JSON.stringify(t)});if(!l.ok)throw Error((await l.json()).message||`Failed to update league ${e}`);return await l.json()},deleteLeague:async(e,t)=>{let s=await i.getLeagueById(e,t);if(!s||!s.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);await a.x.delete(`/football/leagues/${s.id}`)},getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async e=>i.getLeagues({country:e}),toggleLeagueStatus:async(e,t,s)=>i.updateLeague(e,{active:t},s)}},99468:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx`),{__esModule:r,$$typeof:i}=a,o=a.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>s(29518));module.exports=a})();