(()=>{var e={};e.id=5857,e.ids=[5857],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},48904:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var t=a(50482),r=a(69108),l=a(62563),n=a.n(l),i=a(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let o=["",{children:["dashboard",{children:["players",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,40585)),"/home/<USER>/FECMS-sport/src/app/dashboard/players/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/players/page.tsx"],u="/dashboard/players/page",x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/players/page",pathname:"/dashboard/players",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93395:(e,s,a)=>{Promise.resolve().then(a.bind(a,90301))},71532:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},95269:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},66262:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2768:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},64989:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},76755:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},17910:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},90301:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>G});var t=a(95344),r=a(3729),l=a(8428),n=a(19738),i=a(23673),c=a(5094),o=a(46540),d=a(19591),u=a(77022),x=a(36487),m=a(11494),h=a(14373);let p={getPlayers:async(e={})=>{let s=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&s.append(e,a.toString())}),console.log("\uD83D\uDD04 Fetching players via proxy:",`/api/players?${s.toString()}`);let a=await fetch(`/api/players?${s.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json().catch(()=>({message:"Failed to fetch players"}))).message||"Failed to fetch players");let t=await a.json();return console.log("✅ Players fetched successfully:",t.meta||`${t.data?.length||0} players`),t},getPlayer:async e=>{console.log("\uD83D\uDD04 Fetching player by ID:",e);let s=await fetch(`/api/players/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json().catch(()=>({message:"Failed to fetch player"}))).message||"Failed to fetch player");let a=await s.json();return console.log("✅ Player fetched successfully:",a.player?.name),a},searchPlayers:async(e,s=20)=>p.getPlayers({search:e,limit:s}),getPlayersByTeam:async(e,s)=>p.getPlayers({team:e,season:s,limit:100}),getPlayersByLeague:async(e,s)=>p.getPlayers({league:e,season:s,limit:100}),getPlayersByPosition:async(e,s=50)=>p.getPlayers({position:e,limit:s}),getTopScorers:async(e,s,a=20)=>{let t=new URLSearchParams;e&&t.append("league",e.toString()),s&&t.append("season",s.toString()),a&&t.append("limit",a.toString()),console.log("\uD83D\uDD04 Fetching top scorers via proxy:",`/api/players/topscorers?${t.toString()}`);let r=await fetch(`/api/players/topscorers?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json().catch(()=>({message:"Failed to fetch top scorers"}))).message||"Failed to fetch top scorers");let l=await r.json();return console.log("✅ Top scorers fetched successfully:",l.meta),l}};var g=a(34755);let y=(e={})=>{let s=(0,n.a)({queryKey:["players",e],queryFn:()=>p.getPlayers(e),staleTime:3e5});return{players:s.data?.data||[],playersMeta:s.data?.meta,isPlayersLoading:s.isLoading,playersError:s.error,refetchPlayers:s.refetch}},j=(e,s,a=20,t=!0)=>{let r=(0,n.a)({queryKey:["players","topscorers",e,s,a],queryFn:()=>p.getTopScorers(e,s,a),enabled:t&&!!e,staleTime:3e5});return{topScorers:r.data?.data||[],topScorersMeta:r.data?.meta,isTopScorersLoading:r.isLoading,topScorersError:r.error,refetchTopScorers:r.refetch}},f=()=>{let e=(0,m.NL)(),s=(0,h.D)({mutationFn:async({leagueId:e,season:s})=>{let a=await fetch("/api/players/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({leagueId:e,season:s})});if(!a.ok)throw Error((await a.json().catch(()=>({message:"Sync failed"}))).message||"Failed to sync players");return await a.json()},onSuccess:s=>{e.invalidateQueries({queryKey:["players"]}),g.toast.success(`Successfully synced ${s.count||0} players`)},onError:e=>{g.toast.error(e.message||"Failed to sync players")}});return{syncPlayers:s.mutate,isSyncing:s.isLoading,syncError:s.error}};var v=a(59836),w=a(11723),N=a(89895);let b=(0,a(97075).Z)("medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]);var S=a(30782),P=a(17910),k=a(53148),Z=a(64989),C=a(55794),L=a(76394),F=a(28765),T=a(33733),q=a(76755),E=a(73582),D=a(7361),$=a(47210),M=a(40874),I=a(56389),A=a(95269),z=a(2768),_=a(66262);let Y=({open:e,onClose:s})=>{let[a,l]=(0,r.useState)(""),[i,u]=(0,r.useState)(new Date().getFullYear()),[x,m]=(0,r.useState)("select"),[h,p]=(0,r.useState)(null),{syncPlayers:g,isSyncing:y}=f(),{data:j,isLoading:b}=(0,n.a)({queryKey:["leagues","all"],queryFn:()=>v.A.getLeagues({limit:100,active:!0})}),k=j?.data?.find(e=>e.externalId.toString()===a),L=()=>{m("select"),l(""),u(new Date().getFullYear()),p(null),s()};return t.jsx(E.Vq,{open:e,onOpenChange:L,children:(0,t.jsxs)(E.cZ,{className:"sm:max-w-md",children:[(0,t.jsxs)(E.fK,{children:[(0,t.jsxs)(E.$N,{className:"flex items-center space-x-2",children:[t.jsx(Z.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Sync Players"})]}),(0,t.jsxs)(E.Be,{children:["select"===x&&"Select league and season to sync top scorers","confirm"===x&&"Confirm the sync operation","syncing"===x&&"Syncing players data...","complete"===x&&"Sync operation completed"]})]}),(()=>{switch(x){case"select":return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(D._,{htmlFor:"league",children:"Select League"}),b?(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-md",children:[t.jsx(I.Z,{className:"w-4 h-4 animate-spin"}),t.jsx("span",{children:"Loading leagues..."})]}):(0,t.jsxs)("select",{id:"league",value:a,onChange:e=>l(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[t.jsx("option",{value:"",children:"Choose a league"}),j?.data?.map(e=>t.jsxs("option",{value:e.externalId.toString(),children:[e.name," (",e.country,")"]},e.externalId))]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(D._,{htmlFor:"season",children:"Season"}),t.jsx(o.I,{id:"season",type:"number",value:i,onChange:e=>u(parseInt(e.target.value)||new Date().getFullYear()),min:"2000",max:new Date().getFullYear()+1,placeholder:"Enter season year"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current year: ",new Date().getFullYear()]})]}),k&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-muted/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[k.logo&&t.jsx("img",{src:(0,w.Fc)(k.logo)||"/images/default-league.png",alt:k.name,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium",children:k.name}),t.jsx("p",{className:"text-sm text-muted-foreground",children:k.country})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,t.jsxs)(d.C,{variant:"outline",children:[t.jsx(S.Z,{className:"w-3 h-3 mr-1"}),"League ID: ",k.externalId]}),(0,t.jsxs)(d.C,{variant:"outline",children:[t.jsx(C.Z,{className:"w-3 h-3 mr-1"}),"Season: ",i]})]})]}),(0,t.jsxs)(M.bZ,{children:[t.jsx(A.Z,{className:"h-4 w-4"}),t.jsx(M.X,{children:"This will sync top scorers from the external API for the selected league and season. The process may take a few moments depending on the amount of data."})]})]});case"confirm":return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:t.jsx(P.Z,{className:"w-8 h-8 text-blue-600"})}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Confirm Player Sync"}),t.jsx("p",{className:"text-muted-foreground",children:"You are about to sync top scorers data for:"})]}),(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-muted/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[k?.logo&&t.jsx("img",{src:(0,w.Fc)(k.logo)||"/images/default-league.png",alt:k.name,className:"w-10 h-10 object-contain"}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium",children:k?.name}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[k?.country," • Season ",i]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[t.jsx(z.Z,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["API Endpoint: /football/players?league=",a,"&season=",i]})]})]}),(0,t.jsxs)(M.bZ,{children:[t.jsx(A.Z,{className:"h-4 w-4"}),(0,t.jsxs)(M.X,{children:[t.jsx("strong",{children:"Note:"})," This will fetch and update player statistics for top scorers. Existing data may be updated with the latest information from the external API."]})]})]});case"syncing":return(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[t.jsx("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:t.jsx(I.Z,{className:"w-8 h-8 text-blue-600 animate-spin"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Syncing Players..."}),t.jsx("p",{className:"text-muted-foreground mb-4",children:"Fetching top scorers data from external API"}),t.jsx($.E,{value:void 0,className:"w-full"})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,t.jsxs)("p",{children:["League: ",k?.name]}),(0,t.jsxs)("p",{children:["Season: ",i]}),t.jsx("p",{children:"Please wait while we sync the data..."})]})]});case"complete":return(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[t.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:t.jsx(_.Z,{className:"w-8 h-8 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Sync Complete!"}),t.jsx("p",{className:"text-muted-foreground",children:"Successfully synced player data"})]}),h&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-green-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[t.jsx("span",{className:"font-medium",children:"Sync Results"}),(0,t.jsxs)(d.C,{variant:"default",className:"bg-green-100 text-green-800",children:[t.jsx(N.Z,{className:"w-3 h-3 mr-1"}),h.count||0," players"]})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:["League: ",k?.name]}),(0,t.jsxs)("p",{children:["Season: ",i]}),(0,t.jsxs)("p",{children:["Players synced: ",h.count||0]}),h.updated&&(0,t.jsxs)("p",{children:["Players updated: ",h.updated]}),h.created&&(0,t.jsxs)("p",{children:["New players: ",h.created]})]})]}),(0,t.jsxs)(M.bZ,{children:[t.jsx(_.Z,{className:"h-4 w-4"}),t.jsx(M.X,{children:"The players data has been successfully updated. You can now view the latest top scorers in the players management page."})]})]});default:return null}})(),t.jsx(E.cN,{children:(0,t.jsxs)("div",{className:"flex space-x-2 w-full",children:["select"===x&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(c.z,{variant:"outline",onClick:L,className:"flex-1",children:"Cancel"}),t.jsx(c.z,{onClick:()=>{"select"===x&&a&&i&&m("confirm")},disabled:!a||!i,className:"flex-1",children:"Next"})]}),"confirm"===x&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(c.z,{variant:"outline",onClick:()=>m("select"),className:"flex-1",children:"Back"}),(0,t.jsxs)(c.z,{onClick:()=>{a&&i&&(m("syncing"),g({leagueId:parseInt(a),season:i},{onSuccess:e=>{p(e),m("complete")},onError:()=>{m("select")}}))},disabled:y,className:"flex-1",children:[t.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),"Start Sync"]})]}),"syncing"===x&&(0,t.jsxs)(c.z,{variant:"outline",disabled:!0,className:"w-full",children:[t.jsx(I.Z,{className:"w-4 h-4 mr-2 animate-spin"}),"Syncing..."]}),"complete"===x&&(0,t.jsxs)(c.z,{onClick:L,className:"w-full",children:[t.jsx(_.Z,{className:"w-4 h-4 mr-2"}),"Done"]})]})})]})})};function G(){let e=(0,l.useRouter)(),{isEditor:s,isAdmin:a}=(0,x.TE)(),[m,h]=(0,r.useState)({page:1,limit:20}),[p,f]=(0,r.useState)(""),[E,D]=(0,r.useState)(""),[$,M]=(0,r.useState)(new Date().getFullYear()),[I,A]=(0,r.useState)(""),[z,_]=(0,r.useState)(!1),[G,O]=(0,r.useState)(!1),{players:R,playersMeta:B,isPlayersLoading:V,playersError:K}=y(m),{topScorers:U,topScorersMeta:X,isTopScorersLoading:W,topScorersError:H,refetchTopScorers:J}=j(E?parseInt(E):void 0,$,20,z),{data:Q}=(0,n.a)({queryKey:["leagues","active"],queryFn:()=>v.A.getLeagues({limit:100,active:!0})}),ee=z&&!E;console.log("\uD83D\uDD0D Players Page Debug:",{playersCount:R.length,topScorersCount:U.length,showTopScorers:z,selectedLeague:E,selectedSeason:$,filters:m,playersMeta:B,playersError:K,topScorersError:H,showTopScorersEmptyState:ee});let es=e=>{f(e),h(s=>({...s,search:e||void 0,page:1}))},ea=e=>{D(e),h(s=>({...s,league:e?parseInt(e):void 0,page:1}))},et=e=>{M(e),h(s=>({...s,season:e,page:1}))},er=e=>{A(e),h(s=>({...s,position:e||void 0,page:1}))},el=z?U:R,en=z?X:B,ei=z?W:V,ec=z?H:K,eo=[{title:"Player",key:"name",render:(e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[s.player?.photo&&(0,w.Sc)(s.player.photo)?t.jsx("img",{src:(0,w.Sc)(s.player.photo),alt:`${s.player?.name||"Player"} photo`,className:"w-10 h-10 rounded-full object-cover bg-gray-100",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):null,t.jsx("div",{className:`w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center ${s.player?.photo&&(0,w.Sc)(s.player.photo)?"hidden":""}`,children:t.jsx(N.Z,{className:"w-5 h-5 text-gray-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium flex items-center space-x-2",children:[s.player?.name||"Unknown Player",z&&t.jsx(b,{className:"w-4 h-4 text-yellow-500"})]}),s.player?.nationality&&t.jsx("div",{className:"text-sm text-muted-foreground",children:s.player.nationality})]})]})},{title:"Position",key:"position",render:(e,s)=>t.jsx(d.C,{variant:"outline",className:"text-xs",children:s.statistics?.[0]?.games?.position||"N/A"})},{title:"Team",key:"team",render:(e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[s.statistics?.[0]?.team?.logo&&(0,w.Sc)(s.statistics[0].team.logo)?t.jsx("img",{src:(0,w.Sc)(s.statistics[0].team.logo),alt:`${s.statistics[0].team.name} logo`,className:"w-6 h-6 object-contain bg-gray-50 rounded",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):null,t.jsx("div",{className:`w-6 h-6 rounded bg-gray-100 flex items-center justify-center ${s.statistics?.[0]?.team?.logo&&(0,w.Sc)(s.statistics[0].team.logo)?"hidden":""}`,children:t.jsx(S.Z,{className:"w-3 h-3 text-gray-400"})}),t.jsx("span",{className:"text-sm",children:s.statistics?.[0]?.team?.name||"Free Agent"})]})},{title:"Age",key:"age",render:(e,s)=>t.jsx("div",{className:"text-center",children:s.player?.age?t.jsx(d.C,{variant:"secondary",className:"font-mono",children:s.player.age}):t.jsx("span",{className:"text-muted-foreground",children:"-"})})},{title:z?"Goals":"Stats",key:"statistics",render:(e,s)=>t.jsx("div",{className:"text-center",children:z&&s.statistics?.[0]?.goals?.total?(0,t.jsxs)(d.C,{variant:"default",className:"bg-green-100 text-green-800",children:[t.jsx(P.Z,{className:"w-3 h-3 mr-1"}),s.statistics[0].goals.total]}):s.statistics?.[0]?.games?.appearences?(0,t.jsxs)(d.C,{variant:"outline",children:[s.statistics[0].games.appearences," games"]}):t.jsx("span",{className:"text-muted-foreground",children:"-"})})},{title:"Actions",key:"actions",render:(s,a)=>t.jsx("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)(c.z,{variant:"ghost",size:"sm",onClick:()=>{let s=a.player?.id;s?e.push(`/dashboard/players/${s}`):g.toast.error("Player ID not available")},disabled:!a.player?.id,className:"hover:bg-blue-50 hover:text-blue-600 transition-colors",title:a.player?.id?`View ${a.player.name} details`:"Player ID not available",children:[t.jsx(k.Z,{className:"w-4 h-4"}),t.jsx("span",{className:"sr-only",children:"View player details"})]})})}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold tracking-tight flex items-center space-x-3",children:[t.jsx(N.Z,{className:"w-8 h-8"}),t.jsx("span",{children:"Players Management"}),z&&(0,t.jsxs)(d.C,{variant:"default",className:"bg-yellow-100 text-yellow-800",children:[t.jsx(S.Z,{className:"w-4 h-4 mr-1"}),"Top Scorers"]})]}),t.jsx("p",{className:"text-muted-foreground",children:z?`Top scorers for ${E?Q?.data?.find(e=>e.externalId.toString()===E)?.name:"selected league"} (${$})`:"Manage and view all players in the database"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(c.z,{variant:"outline",onClick:()=>{_(!z),!z&&E&&J()},children:z?(0,t.jsxs)(t.Fragment,{children:[t.jsx(N.Z,{className:"w-4 h-4 mr-2"}),"All Players"]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(S.Z,{className:"w-4 h-4 mr-2"}),"Top Scorers"]})}),(s()||a())&&(0,t.jsxs)(c.z,{onClick:()=>O(!0),variant:"default",children:[t.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),"Sync Players"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(i.ll,{className:"text-sm font-medium",children:"Total Players"}),t.jsx(N.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:en?.totalItems?.toLocaleString()||"0"}),t.jsx("p",{className:"text-xs text-muted-foreground",children:z?"Top scorers found":"Players in database"})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(i.ll,{className:"text-sm font-medium",children:"Current Page"}),t.jsx(C.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[en?.currentPage||1," / ",en?.totalPages||1]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Showing ",el.length," of ",en?.totalItems||0]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(i.ll,{className:"text-sm font-medium",children:"Selected League"}),t.jsx(S.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[t.jsx("div",{className:"text-lg font-bold truncate",children:E?Q?.data?.find(e=>e.externalId.toString()===E)?.name||"Unknown":"All Leagues"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Season ",$]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(i.ll,{className:"text-sm font-medium",children:"Filters Active"}),t.jsx(L.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[t.jsx("div",{className:"text-2xl font-bold",children:[p,E,I].filter(Boolean).length}),t.jsx("p",{className:"text-xs text-muted-foreground",children:z?"Top scorers mode":"Search & filter options"})]})]})]}),(0,t.jsxs)(i.Zb,{children:[t.jsx(i.Ol,{children:(0,t.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[t.jsx(F.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Search & Filters"})]})}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[t.jsx("div",{children:t.jsx(o.I,{placeholder:"Search players...",value:p,onChange:e=>es(e.target.value),className:"w-full"})}),t.jsx("div",{children:(0,t.jsxs)("select",{value:E,onChange:e=>ea(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[t.jsx("option",{value:"",children:"All Leagues"}),Q?.data?.map(e=>t.jsx("option",{value:e.externalId.toString(),children:e.name},e.externalId))]})}),t.jsx("div",{children:t.jsx(o.I,{type:"number",placeholder:"Season",value:$,onChange:e=>et(parseInt(e.target.value)||new Date().getFullYear()),min:"2000",max:new Date().getFullYear()+1,className:"w-full"})}),t.jsx("div",{children:(0,t.jsxs)("select",{value:I,onChange:e=>er(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[t.jsx("option",{value:"",children:"All Positions"}),t.jsx("option",{value:"Goalkeeper",children:"Goalkeeper"}),t.jsx("option",{value:"Defender",children:"Defender"}),t.jsx("option",{value:"Midfielder",children:"Midfielder"}),t.jsx("option",{value:"Forward",children:"Forward"})]})}),t.jsx("div",{children:(0,t.jsxs)(c.z,{variant:"outline",onClick:()=>window.location.reload(),className:"w-full",children:[t.jsx(T.Z,{className:"w-4 h-4 mr-2"}),"Refresh"]})}),t.jsx("div",{children:t.jsx(c.z,{variant:"outline",onClick:()=>{f(""),D(""),M(new Date().getFullYear()),A(""),_(!1),h({page:1,limit:20})},className:"w-full",children:"Clear All"})})]}),(p||E||I||z)&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mt-4",children:[p&&(0,t.jsxs)(d.C,{variant:"secondary",children:["Search: ",p]}),E&&(0,t.jsxs)(d.C,{variant:"secondary",children:["League: ",Q?.data?.find(e=>e.externalId.toString()===E)?.name]}),I&&(0,t.jsxs)(d.C,{variant:"secondary",children:["Position: ",I]}),z&&(0,t.jsxs)(d.C,{variant:"default",className:"bg-yellow-100 text-yellow-800",children:[t.jsx(q.Z,{className:"w-3 h-3 mr-1"}),"Top Scorers Mode"]})]})]})]}),(0,t.jsxs)(i.Zb,{children:[t.jsx(i.Ol,{children:t.jsx(i.ll,{className:"flex items-center space-x-2",children:z?(0,t.jsxs)(t.Fragment,{children:[t.jsx(S.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Top Scorers"})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(N.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Players List"})]})})}),t.jsx(i.aY,{children:ee?(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx(S.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select League for Top Scorers"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Please select a league and season to view top scorers data."}),(0,t.jsxs)("div",{className:"flex justify-center gap-2",children:[t.jsx(d.C,{variant:"outline",children:"1. Choose League"}),t.jsx(d.C,{variant:"outline",children:"2. Set Season"}),t.jsx(d.C,{variant:"outline",children:"3. View Results"})]})]}):ec?(0,t.jsxs)("div",{className:"text-center py-8 text-red-500",children:["Error loading players: ",ec?.message||"Unknown error"]}):t.jsx(u.w,{columns:eo,data:el,loading:ei,pagination:{page:en?.currentPage||1,limit:en?.limit||20,total:en?.totalItems||0,onPageChange:e=>{h(s=>({...s,page:e}))},onLimitChange:e=>h(s=>({...s,limit:e,page:1}))},emptyMessage:z?"No top scorers found. Try selecting a league and season.":"No players found."})})]}),t.jsx(Y,{open:G,onClose:()=>O(!1)})]})}},40874:(e,s,a)=>{"use strict";a.d(s,{X:()=>o,bZ:()=>c});var t=a(95344),r=a(3729),l=a(49247),n=a(11453);let i=(0,l.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef(({className:e,variant:s,...a},r)=>t.jsx("div",{ref:r,role:"alert",className:(0,n.cn)(i({variant:s}),e),...a}));c.displayName="Alert",r.forwardRef(({className:e,...s},a)=>t.jsx("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));o.displayName="AlertDescription"},47210:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(95344),r=a(3729),l=a(11453);let n=r.forwardRef(({className:e,value:s=0,max:a=100,showValue:r=!1,size:n="md",variant:i="default",...c},o)=>{let d=Math.min(Math.max(s/a*100,0),100);return(0,t.jsxs)("div",{ref:o,className:(0,l.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[n],e),...c,children:[t.jsx("div",{className:(0,l.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[i]),style:{width:`${d}%`}}),r&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(d),"%"]})})]})});n.displayName="Progress"},59836:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});var t=a(50053);let r=()=>null,l={getLeagues:async(e={})=>{let s=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&s.append(e,a.toString())});let a=await fetch(`/api/leagues?${s.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,s)=>{let a=s?`${e}-${s}`:e.toString(),t=await fetch(`/api/leagues/${a}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||`Failed to fetch league ${e}`);return await t.json()},createLeague:async e=>await t.x.post("/football/leagues",e),updateLeague:async(e,s,a)=>{let t=r(),n={"Content-Type":"application/json"};t&&(n.Authorization=`Bearer ${t}`);let i=await l.getLeagueById(e,a);if(!i||!i.id)throw Error(`League not found: ${e}${a?`-${a}`:""}`);let c=await fetch(`/api/leagues/${i.id}`,{method:"PATCH",headers:n,body:JSON.stringify(s)});if(!c.ok)throw Error((await c.json()).message||`Failed to update league ${e}`);return await c.json()},deleteLeague:async(e,s)=>{let a=await l.getLeagueById(e,s);if(!a||!a.id)throw Error(`League not found: ${e}${s?`-${s}`:""}`);await t.x.delete(`/football/leagues/${a.id}`)},getActiveLeagues:async()=>l.getLeagues({active:!0}),getLeaguesByCountry:async e=>l.getLeagues({country:e}),toggleLeagueStatus:async(e,s,a)=>l.updateLeague(e,{active:s},a)}},11723:(e,s,a)=>{"use strict";function t(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let s=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${s}`}function r(e){return t(e)}function l(e){return t(e)}function n(e){return t(e)}a.d(s,{Bf:()=>r,Fc:()=>n,Sc:()=>t,ou:()=>l})},40585:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/players/page.tsx`),{__esModule:r,$$typeof:l}=t,n=t.default},14217:(e,s,a)=>{"use strict";a.d(s,{f:()=>i});var t=a(3729),r=a(62409),l=a(95344),n=t.forwardRef((e,s)=>(0,l.jsx)(r.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=n}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,7966,6317,7833,7022,4216],()=>a(48904));module.exports=t})();