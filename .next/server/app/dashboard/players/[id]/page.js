(()=>{var e={};e.id=5491,e.ids=[5491],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},90275:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>n});var a=t(50482),r=t(69108),l=t(62563),i=t.n(l),d=t(68300),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(s,c);let n=["",{children:["dashboard",{children:["players",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13231)),"/home/<USER>/FECMS-sport/src/app/dashboard/players/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["/home/<USER>/FECMS-sport/src/app/dashboard/players/[id]/page.tsx"],o="/dashboard/players/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/players/[id]/page",pathname:"/dashboard/players/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},12929:(e,s,t)=>{Promise.resolve().then(t.bind(t,62064))},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},95269:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},26187:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},80508:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},17910:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},62064:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var a=t(95344),r=t(8428),l=t(19738),i=t(23673),d=t(5094),c=t(19591),n=t(11723),x=t(2273),o=t(97751),m=t(89895),h=t(18822),p=t(63024),g=t(95269),u=t(33733),j=t(26187),b=t(55794),y=t(88534),N=t(97075);let f=(0,N.Z)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]]),v=(0,N.Z)("weight",[["circle",{cx:"12",cy:"5",r:"3",key:"rqqgnr"}],["path",{d:"M6.5 8a2 2 0 0 0-1.905 1.46L2.1 18.5A2 2 0 0 0 4 21h16a2 2 0 0 0 1.925-2.54L19.4 9.5A2 2 0 0 0 17.48 8Z",key:"56o5sh"}]]);var w=t(80508),k=t(46064),Z=t(30782),P=t(17910);let q=(0,N.Z)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var S=t(56506);let C=async e=>{let s=await fetch(`/api/players/${e}`);if(!s.ok)throw Error("Failed to fetch player details");return s.json()},M=({playerId:e,playerName:s})=>(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6 p-4 bg-gray-50 rounded-lg border",children:[(0,a.jsxs)(S.default,{href:"/dashboard",className:"flex items-center hover:text-blue-600 transition-colors",children:[a.jsx(x.Z,{className:"w-4 h-4 mr-1"}),"Dashboard"]}),a.jsx(o.Z,{className:"w-4 h-4 text-gray-400"}),(0,a.jsxs)(S.default,{href:"/dashboard/players",className:"flex items-center hover:text-blue-600 transition-colors",children:[a.jsx(m.Z,{className:"w-4 h-4 mr-1"}),"Players"]}),a.jsx(o.Z,{className:"w-4 h-4 text-gray-400"}),(0,a.jsxs)("span",{className:"text-gray-900 font-medium flex items-center",children:[a.jsx(h.Z,{className:"w-4 h-4 mr-1"}),s||`Player ${e}`]})]}),F=(e,s)=>e&&s&&0!==s?`${(e/s*100).toFixed(1)}%`:"-",_=e=>{let s=Math.floor(e/60);return s>0?`${s}h ${e%60}m`:`${e}m`};function D(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),t=e.id,{data:x,isLoading:o,error:N}=(0,l.a)({queryKey:["player",t],queryFn:()=>C(t),enabled:!!t});return o?(0,a.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[a.jsx("div",{className:"h-16 bg-gray-200 animate-pulse rounded-lg"}),a.jsx("div",{className:"bg-gradient-to-r from-gray-300 to-gray-400 rounded-xl shadow-lg p-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[a.jsx("div",{className:"w-20 h-12 bg-gray-400 animate-pulse rounded"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"h-10 w-64 bg-gray-400 animate-pulse rounded"}),a.jsx("div",{className:"h-6 w-48 bg-gray-400 animate-pulse rounded"})]})]})}),(0,a.jsxs)("div",{className:"grid gap-6",children:[a.jsx("div",{className:"h-96 bg-gray-200 animate-pulse rounded-lg"}),a.jsx("div",{className:"h-64 bg-gray-200 animate-pulse rounded-lg"})]})]}):N||!x?(0,a.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[a.jsx("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(d.z,{variant:"outline",onClick:()=>s.back(),children:[a.jsx(p.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]})}),a.jsx(i.Zb,{className:"border-red-200",children:(0,a.jsxs)(i.aY,{className:"flex flex-col items-center justify-center py-16",children:[a.jsx("div",{className:"p-4 bg-red-100 rounded-full mb-6",children:a.jsx(g.Z,{className:"w-16 h-16 text-red-600"})}),a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Unable to Load Player"}),a.jsx("p",{className:"text-gray-600 text-center max-w-md mb-6",children:N?.message||"We encountered an error while loading the player details. This might be due to network issues or the player data being unavailable."}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(d.z,{onClick:()=>window.location.reload(),variant:"default",children:[a.jsx(u.Z,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,a.jsxs)(d.z,{onClick:()=>s.push("/dashboard/players"),variant:"outline",children:[a.jsx(p.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]})]})]})})]}):(0,a.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[a.jsx(M,{playerId:t,playerName:x.player.name}),a.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg text-white p-8",children:a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)(d.z,{variant:"secondary",onClick:()=>s.back(),className:"bg-white/20 hover:bg-white/30 text-white border-white/30",children:[a.jsx(p.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold tracking-tight flex items-center space-x-4",children:[a.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:a.jsx(h.Z,{className:"w-8 h-8"})}),a.jsx("span",{children:x.player.name}),x.player.injured&&(0,a.jsxs)(c.C,{variant:"destructive",className:"bg-red-500 hover:bg-red-600",children:[a.jsx(g.Z,{className:"w-3 h-3 mr-1"}),"Injured"]})]}),a.jsx("p",{className:"text-blue-100 text-lg mt-2",children:"Player Details & Career Statistics"})]})]})})}),(0,a.jsxs)(i.Zb,{className:"overflow-hidden",children:[a.jsx(i.Ol,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,a.jsxs)(i.ll,{className:"flex items-center space-x-2 text-xl",children:[a.jsx(m.Z,{className:"w-6 h-6 text-blue-600"}),a.jsx("span",{children:"Player Information"})]})}),a.jsx(i.aY,{className:"p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{className:"lg:col-span-1 flex flex-col items-center space-y-6",children:[(0,a.jsxs)("div",{className:"relative",children:[x.player.photo&&(0,n.Sc)(x.player.photo)?a.jsx("img",{src:(0,n.Sc)(x.player.photo),alt:`${x.player.name} photo`,className:"w-48 h-48 rounded-full object-cover bg-gray-100 border-4 border-white shadow-xl ring-4 ring-blue-100",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):null,a.jsx("div",{className:`w-48 h-48 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center border-4 border-white shadow-xl ring-4 ring-blue-100 ${x.player.photo&&(0,n.Sc)(x.player.photo)?"hidden":""}`,children:a.jsx(h.Z,{className:"w-24 h-24 text-gray-400"})}),x.player.injured&&a.jsx("div",{className:"absolute -top-2 -right-2",children:(0,a.jsxs)(c.C,{variant:"destructive",className:"shadow-lg",children:[a.jsx(g.Z,{className:"w-3 h-3 mr-1"}),"Injured"]})})]}),(0,a.jsxs)("div",{className:"text-center space-y-2",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:x.player.name}),(0,a.jsxs)("p",{className:"text-gray-600 text-lg font-medium",children:[x.player.firstname," ",x.player.lastname]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-4",children:[a.jsx(j.Z,{className:"w-4 h-4 text-gray-500"}),a.jsx("span",{className:"text-gray-600",children:x.player.nationality})]})]})]}),a.jsx("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 h-full",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold mb-6 text-blue-900 flex items-center",children:[a.jsx(h.Z,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(b.Z,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-blue-600 uppercase tracking-wide",children:"Age"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[x.player.age||(e=>{let s=new Date,t=new Date(e),a=s.getFullYear()-t.getFullYear(),r=s.getMonth()-t.getMonth();return(r<0||0===r&&s.getDate()<t.getDate())&&a--,a})(x.player.birth.date)," years old"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(j.Z,{className:"w-5 h-5 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-green-600 uppercase tracking-wide",children:"Nationality"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:x.player.nationality})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:a.jsx(b.Z,{className:"w-5 h-5 text-red-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-red-600 uppercase tracking-wide",children:"Date of Birth"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:new Date(x.player.birth.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]})]})}),a.jsx("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-orange-50 rounded-lg p-6 h-full",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold mb-6 text-orange-900 flex items-center",children:[a.jsx(y.Z,{className:"w-5 h-5 mr-2"}),"Physical Statistics"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(f,{className:"w-5 h-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-orange-600 uppercase tracking-wide",children:"Height"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:x.player.height||"Not available"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(v,{className:"w-5 h-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-purple-600 uppercase tracking-wide",children:"Weight"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:x.player.weight||"Not available"})]})]})]})]})}),a.jsx("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-6 h-full",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold mb-6 text-indigo-900 flex items-center",children:[a.jsx(w.Z,{className:"w-5 h-5 mr-2"}),"Origin & Background"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[x.player.birth.place&&(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-indigo-100 rounded-lg",children:a.jsx(w.Z,{className:"w-5 h-5 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-indigo-600 uppercase tracking-wide",children:"Place of Birth"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:x.player.birth.place}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:x.player.birth.country})]})]}),!x.player.birth.place&&(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:a.jsx(j.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 uppercase tracking-wide",children:"Country"}),a.jsx("p",{className:"text-lg font-bold text-gray-900",children:x.player.birth.country})]})]})]})]})})]})})]}),x.statistics&&x.statistics.length>0?(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-8 border border-green-200",children:[(0,a.jsxs)("h2",{className:"text-3xl font-bold flex items-center space-x-3 text-green-900 mb-2",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(k.Z,{className:"w-8 h-8 text-green-600"})}),a.jsx("span",{children:"Career Statistics"})]}),a.jsx("p",{className:"text-green-700 text-lg",children:"Professional football career performance across all competitions"})]}),x.statistics.map((e,s)=>(0,a.jsxs)(i.Zb,{className:"overflow-hidden border-0 shadow-lg",children:[a.jsx(i.Ol,{className:"bg-gradient-to-r from-gray-50 to-slate-50 border-b",children:(0,a.jsxs)(i.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.team.logo&&(0,n.Sc)(e.team.logo)?a.jsx("img",{src:(0,n.Sc)(e.team.logo),alt:`${e.team.name} logo`,className:"w-12 h-12 object-contain bg-white rounded-lg p-1 border",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling?.classList.remove("hidden")}}):null,a.jsx("div",{className:`w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center border ${e.team.logo&&(0,n.Sc)(e.team.logo)?"hidden":""}`,children:a.jsx(Z.Z,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900",children:e.team.name}),(0,a.jsxs)("p",{className:"text-gray-600 flex items-center space-x-2",children:[a.jsx("span",{children:e.league.name}),a.jsx("span",{children:"•"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.league.season," Season"]})]})]})]}),a.jsx(c.C,{variant:"outline",className:"text-sm font-medium px-3 py-1",children:e.games.position})]})}),(0,a.jsxs)(i.aY,{className:"p-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[a.jsx("div",{className:"bg-blue-50 rounded-xl p-6 text-center border border-blue-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-blue-600",children:e.games.appearences}),a.jsx("p",{className:"text-sm font-medium text-blue-700 mt-1",children:"Appearances"})]})}),a.jsx("div",{className:"bg-green-50 rounded-xl p-6 text-center border border-green-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-green-600",children:e.games.lineups}),a.jsx("p",{className:"text-sm font-medium text-green-700 mt-1",children:"Lineups"})]})}),a.jsx("div",{className:"bg-purple-50 rounded-xl p-6 text-center border border-purple-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-purple-600",children:_(e.games.minutes)}),a.jsx("p",{className:"text-sm font-medium text-purple-700 mt-1",children:"Minutes Played"})]})}),a.jsx("div",{className:"bg-red-50 rounded-xl p-6 text-center border border-red-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-red-600",children:e.goals.total||0}),a.jsx("p",{className:"text-sm font-medium text-red-700 mt-1",children:"Goals"}),e.games.appearences>0&&(0,a.jsxs)("p",{className:"text-xs text-red-500 mt-2 bg-red-100 px-2 py-1 rounded-full",children:[((e.goals.total||0)/e.games.appearences).toFixed(2)," per game"]})]})}),null!==e.goals.assists&&a.jsx("div",{className:"bg-orange-50 rounded-xl p-6 text-center border border-orange-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-orange-600",children:e.goals.assists}),a.jsx("p",{className:"text-sm font-medium text-orange-700 mt-1",children:"Assists"}),e.games.appearences>0&&(0,a.jsxs)("p",{className:"text-xs text-orange-500 mt-2 bg-orange-100 px-2 py-1 rounded-full",children:[(e.goals.assists/e.games.appearences).toFixed(2)," per game"]})]})}),e.games.rating&&a.jsx("div",{className:"bg-indigo-50 rounded-xl p-6 text-center border border-indigo-100 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("p",{className:"text-3xl font-bold text-indigo-600",children:parseFloat(e.games.rating).toFixed(1)}),a.jsx("p",{className:"text-sm font-medium text-indigo-700 mt-1",children:"Average Rating"})]})})]}),(e.cards.yellow>0||e.cards.red>0)&&(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[a.jsx(g.Z,{className:"w-5 h-5 mr-2 text-yellow-600"}),"Disciplinary Record"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-yellow-50 rounded-xl p-6 text-center border border-yellow-100",children:[a.jsx("p",{className:"text-3xl font-bold text-yellow-600",children:e.cards.yellow}),a.jsx("p",{className:"text-sm font-medium text-yellow-700 mt-1",children:"Yellow Cards"})]}),e.cards.red>0&&(0,a.jsxs)("div",{className:"bg-red-50 rounded-xl p-6 text-center border border-red-100",children:[a.jsx("p",{className:"text-3xl font-bold text-red-600",children:e.cards.red}),a.jsx("p",{className:"text-sm font-medium text-red-700 mt-1",children:"Red Cards"})]}),e.fouls.committed&&(0,a.jsxs)("div",{className:"bg-orange-50 rounded-xl p-6 text-center border border-orange-100",children:[a.jsx("p",{className:"text-3xl font-bold text-orange-600",children:e.fouls.committed}),a.jsx("p",{className:"text-sm font-medium text-orange-700 mt-1",children:"Fouls Committed"})]})]})]}),(e.shots.total||e.passes.total||e.tackles.total||e.duels.total)&&(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-8",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[a.jsx(y.Z,{className:"w-5 h-5 mr-2 text-blue-600"}),"Detailed Performance Statistics"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.shots.total&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-6 border border-red-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"p-2 bg-red-100 rounded-lg mr-3",children:a.jsx(P.Z,{className:"w-5 h-5 text-red-600"})}),a.jsx("h5",{className:"font-semibold text-red-900",children:"Shooting"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Total Shots"}),a.jsx("span",{className:"font-bold text-red-700",children:e.shots.total})]}),e.shots.on&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"On Target"}),a.jsx("span",{className:"font-bold text-red-700",children:e.shots.on})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Accuracy"}),a.jsx("span",{className:"font-bold text-red-700 bg-red-100 px-2 py-1 rounded-full text-xs",children:F(e.shots.on,e.shots.total)})]})]})]})]}),e.passes.total&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg mr-3",children:a.jsx(y.Z,{className:"w-5 h-5 text-blue-600"})}),a.jsx("h5",{className:"font-semibold text-blue-900",children:"Passing"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Total Passes"}),a.jsx("span",{className:"font-bold text-blue-700",children:e.passes.total.toLocaleString()})]}),e.passes.key&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Key Passes"}),a.jsx("span",{className:"font-bold text-blue-700",children:e.passes.key})]}),e.passes.accuracy&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Accuracy"}),(0,a.jsxs)("span",{className:"font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded-full text-xs",children:[e.passes.accuracy,"%"]})]})]})]}),e.tackles.total&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg mr-3",children:a.jsx(q,{className:"w-5 h-5 text-green-600"})}),a.jsx("h5",{className:"font-semibold text-green-900",children:"Defending"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Tackles"}),a.jsx("span",{className:"font-bold text-green-700",children:e.tackles.total})]}),e.tackles.interceptions&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Interceptions"}),a.jsx("span",{className:"font-bold text-green-700",children:e.tackles.interceptions})]}),e.tackles.blocks&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Blocks"}),a.jsx("span",{className:"font-bold text-green-700",children:e.tackles.blocks})]})]})]}),e.duels.total&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg mr-3",children:a.jsx(Z.Z,{className:"w-5 h-5 text-purple-600"})}),a.jsx("h5",{className:"font-semibold text-purple-900",children:"Duels & Dribbles"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Duels Won"}),(0,a.jsxs)("span",{className:"font-bold text-purple-700",children:[e.duels.won,"/",e.duels.total]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Success Rate"}),a.jsx("span",{className:"font-bold text-purple-700 bg-purple-100 px-2 py-1 rounded-full text-xs",children:F(e.duels.won,e.duels.total)})]}),e.dribbles.success&&e.dribbles.attempts&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-600 text-sm",children:"Dribbles"}),(0,a.jsxs)("span",{className:"font-bold text-purple-700",children:[e.dribbles.success,"/",e.dribbles.attempts]})]})]})]})]})]})]})]},s))]}):a.jsx(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"flex flex-col items-center justify-center py-12",children:[a.jsx(Z.Z,{className:"w-16 h-16 text-gray-300 mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Statistics Available"}),a.jsx("p",{className:"text-gray-500 text-center mb-4",children:"No career statistics found for this player. This could be because:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-500 text-left space-y-1",children:[a.jsx("li",{children:"• Player hasn't played in any matches yet"}),a.jsx("li",{children:"• Statistics are still being updated"}),a.jsx("li",{children:"• Player is not active in current leagues"})]})]})})]})}},11723:(e,s,t)=>{"use strict";function a(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let s=e.startsWith("/")?e.slice(1):e;return`http://172.31.213.61/${s}`}function r(e){return a(e)}function l(e){return a(e)}function i(e){return a(e)}t.d(s,{Bf:()=>r,Fc:()=>i,Sc:()=>a,ou:()=>l})},13231:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/players/[id]/page.tsx`),{__esModule:r,$$typeof:l}=a,i=a.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>t(90275));module.exports=a})();