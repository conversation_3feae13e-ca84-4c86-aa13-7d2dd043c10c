(()=>{var e={};e.id=4171,e.ids=[4171],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},23677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>c});var a=s(50482),r=s(69108),n=s(62563),i=s.n(n),o=s(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["dashboard",{children:["api-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79722)),"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx"],d="/dashboard/api-test/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/api-test/page",pathname:"/dashboard/api-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7338:(e,t,s)=>{Promise.resolve().then(s.bind(s,71747))},71747:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95344),r=s(3729),n=s(23673),i=s(5094),o=s(19591),l=s(50053),c=s(59358),u=s(20255),d=s(59836);function p(){let[e,t]=(0,r.useState)([]),[s,p]=(0,r.useState)(!1),x=(e,s)=>{t(t=>t.find(t=>t.endpoint===e)?t.map(t=>t.endpoint===e?{...t,...s}:t):[...t,{endpoint:e,status:"pending",...s}])},h=async(e,t)=>{let s=Date.now();x(e,{status:"pending"});try{let a=await t(),r=Date.now()-s;x(e,{status:"success",data:a,duration:r})}catch(a){let t=Date.now()-s;x(e,{status:"error",error:a.message||"Unknown error",duration:t})}},g=async()=>{p(!0),t([]),await h("API Documentation",async()=>{let e=await l.x.get("/api-docs-json");return{title:e.info?.title||"APISportsGame API",version:e.info?.version||"1.0.0",endpoints:Object.keys(e.paths||{}).length}}),await h("Public Fixtures",async()=>{let e=await u.L.getUpcomingAndLive({limit:3});return{totalFixtures:e.data?.length||0,liveMatches:e.data?.filter(e=>["1H","2H","HT"].includes(e.status)).length||0,upcomingMatches:e.data?.filter(e=>"NS"===e.status).length||0,sampleFixture:e.data?.[0]?.homeTeamName+" vs "+e.data?.[0]?.awayTeamName||"No fixtures"}}),await h("Public Leagues",async()=>{let e=await d.A.getLeagues({limit:3});return{totalLeagues:e.meta?.totalItems||0,currentPage:e.meta?.currentPage||1,sampleLeague:e.data?.[0]?.name||"No leagues"}}),await h("System Auth Login",async()=>{let e=await c.i.login({username:"admin",password:"admin123456"});return{username:e.user.username,role:e.user.role,email:e.user.email,tokenLength:e.accessToken.length}}),p(!1)},f=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},m=e=>{switch(e){case"success":return"✅";case"error":return"❌";case"pending":return"⏳";default:return"⚪"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"API Connection Test"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Test connection to APISportsGame API endpoints"})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"API Configuration"}),a.jsx(n.SZ,{children:"Current API settings and connection details"})]}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Base URL:"}),a.jsx("span",{className:"text-gray-600",children:"http://localhost:3000"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Timeout:"}),a.jsx("span",{className:"text-gray-600",children:"30 seconds"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Auth Token:"}),a.jsx("span",{className:"text-gray-600",children:"❌ Not found"})]})]})})]}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"Test Results"}),a.jsx(n.SZ,{children:"API endpoint connectivity tests"})]}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(i.z,{onClick:g,disabled:s,className:"w-full",children:s?"Running Tests...":"Run All Tests"}),e.length>0&&a.jsx("div",{className:"space-y-3",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-lg",children:m(e.status)}),a.jsx("span",{className:"font-medium",children:e.endpoint})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(o.C,{className:f(e.status),children:e.status}),e.duration&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration,"ms"]})]})]}),e.error&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded p-2 mt-2",children:[a.jsx("p",{className:"text-red-800 text-sm font-medium",children:"Error:"}),a.jsx("p",{className:"text-red-700 text-sm",children:e.error})]}),e.data&&"success"===e.status&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded p-2 mt-2",children:[a.jsx("p",{className:"text-green-800 text-sm font-medium",children:"Response:"}),(0,a.jsxs)("pre",{className:"text-green-700 text-xs mt-1 overflow-x-auto",children:[JSON.stringify(e.data,null,2).substring(0,200),JSON.stringify(e.data,null,2).length>200?"...":""]})]})]},t))})]})})]})]})}},20255:(e,t,s)=>{"use strict";s.d(t,{L:()=>r});var a=s(50053);let r={getFixtures:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch fixtures: ${s.statusText}`);return await s.json()},getFixtureById:async e=>{let t=await fetch(`/api/fixtures/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture: ${t.statusText}`);return await t.json()},getUpcomingAndLive:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures/live?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch live fixtures: ${s.statusText}`);return await s.json()},getTeamSchedule:async(e,t={})=>{let s=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())}),await a.x.get(`/football/fixtures/schedules/${e}?${s.toString()}`)},getFixtureStatistics:async e=>await a.x.get(`/football/fixtures/statistics/${e}`),triggerSeasonSync:async()=>{let e=(console.warn("❌ Season sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger season sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Season sync successful"),s},triggerDailySync:async()=>{let e=(console.warn("❌ Daily sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger daily sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Daily sync successful"),s},getSyncStatus:async()=>{let e=(console.warn("❌ Sync status - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||`Failed to get sync status: ${t.statusText}`)}let s=await t.json();return console.log("✅ Sync status successful"),s},createFixture:async e=>{let t=(console.warn("❌ Create fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!t.Authorization,data:e});let s=await fetch("/api/fixtures",{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to create fixture: ${s.statusText}`)}let a=await s.json();return console.log("✅ Create fixture successful:",a.data?.id),a.data||a},updateFixture:async(e,t)=>{let s=(console.warn("❌ Update fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!s.Authorization,data:t});let a=await fetch(`/api/fixtures/${e}`,{method:"PUT",headers:s,body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",a.status,a.statusText,e),Error(e.message||`Failed to update fixture: ${a.statusText}`)}let r=await a.json();return console.log("✅ Update fixture successful:",e),r.data||r},deleteFixture:async e=>{let t=(console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let s=await fetch(`/api/fixtures/${e}`,{method:"DELETE",headers:t});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to delete fixture: ${s.statusText}`)}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch(`/api/fixtures/${e}/statistics`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture statistics: ${t.statusText}`);return await t.json()},getFixtureEvents:async e=>{let t=await fetch(`/api/fixtures/${e}/events`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture events: ${t.statusText}`);return await t.json()},getFixture:async e=>(await r.getFixtureById(e)).data}},59836:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(50053);let r=()=>null,n={getLeagues:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/leagues?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,t)=>{let s=t?`${e}-${t}`:e.toString(),a=await fetch(`/api/leagues/${s}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||`Failed to fetch league ${e}`);return await a.json()},createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t,s)=>{let a=r(),i={"Content-Type":"application/json"};a&&(i.Authorization=`Bearer ${a}`);let o=await n.getLeagueById(e,s);if(!o||!o.id)throw Error(`League not found: ${e}${s?`-${s}`:""}`);let l=await fetch(`/api/leagues/${o.id}`,{method:"PATCH",headers:i,body:JSON.stringify(t)});if(!l.ok)throw Error((await l.json()).message||`Failed to update league ${e}`);return await l.json()},deleteLeague:async(e,t)=>{let s=await n.getLeagueById(e,t);if(!s||!s.id)throw Error(`League not found: ${e}${t?`-${t}`:""}`);await a.x.delete(`/football/leagues/${s.id}`)},getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t,s)=>n.updateLeague(e,{active:t},s)}},79722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>s(23677));module.exports=a})();