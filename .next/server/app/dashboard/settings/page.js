(()=>{var e={};e.id=3455,e.ids=[3455],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},53462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),o=s(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,18958)),"/home/<USER>/FECMS-sport/src/app/dashboard/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/settings/page.tsx"],u="/dashboard/settings/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},36370:(e,t,s)=>{Promise.resolve().then(s.bind(s,14027))},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},14027:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(95344),a=s(23673),i=s(5094),n=s(30782),o=s(89895),l=s(13746);let d=(0,s(97075).Z)("construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]]);var c=s(63024),u=s(56506);let p=({title:e,description:t="This page is under development and will be available soon.",iconName:s="construction",backUrl:p="/dashboard",backLabel:x="Back to Dashboard"})=>{let h=(e=>{switch(e){case"trophy":return n.Z;case"users":return o.Z;case"settings":return l.Z;default:return d}})(s);return r.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,r.jsxs)(a.Zb,{className:"w-full max-w-md text-center",children:[(0,r.jsxs)(a.Ol,{children:[r.jsx("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20",children:r.jsx(h,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"})}),r.jsx(a.ll,{className:"text-xl",children:e}),r.jsx(a.SZ,{className:"text-base",children:t})]}),r.jsx(a.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[r.jsx("p",{children:"Features coming soon:"}),(0,r.jsxs)("ul",{className:"mt-2 space-y-1 text-left",children:[r.jsx("li",{children:"• Data management interface"}),r.jsx("li",{children:"• CRUD operations"}),r.jsx("li",{children:"• Advanced filtering"}),r.jsx("li",{children:"• Export functionality"})]})]}),r.jsx(i.z,{asChild:!0,className:"w-full",children:(0,r.jsxs)(u.default,{href:p,children:[r.jsx(c.Z,{className:"mr-2 h-4 w-4"}),x]})})]})})]})})};function x(){return r.jsx(p,{title:"System Settings",description:"Configure application settings and preferences.",iconName:"settings"})}},18958:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/settings/page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>s(53462));module.exports=r})();