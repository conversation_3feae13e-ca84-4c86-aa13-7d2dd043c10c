(()=>{var e={};e.id=8815,e.ids=[8815],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},88164:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=s(50482),r=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["dashboard",{children:["fixtures",{children:["live",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8064)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/live/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/live/page.tsx"],u="/dashboard/fixtures/live/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/fixtures/live/page",pathname:"/dashboard/fixtures/live",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28829:(e,t,s)=>{Promise.resolve().then(s.bind(s,3251))},25545:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},80508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},3251:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95344),r=s(3729),i=s(19738),n=s(8428),l=s(23673),o=s(19591),c=s(5094),d=s(89895),u=s(80508),x=s(33733),h=s(25545),m=s(20255);function p(){let e=(0,n.useRouter)(),[t,s]=(0,r.useState)(!0),{data:p,isLoading:f,error:g,refetch:y}=(0,i.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>m.L.getUpcomingAndLive({limit:20}),refetchInterval:!!t&&3e4,staleTime:1e4}),j=e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800 animate-pulse";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}},v=(e,t)=>{switch(e){case"1H":case"2H":return`${t}'`;case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}},N=e=>{let t=new Date(e);return{date:t.toLocaleDateString(),time:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},w=({fixture:t})=>{let{date:s,time:r}=N(t.date),i=["1H","2H","HT"].includes(t.status);return(0,a.jsxs)(l.Zb,{className:`transition-all duration-200 cursor-pointer hover:shadow-md hover:ring-1 hover:ring-blue-300 hover:scale-[1.02] ${i?"ring-2 ring-green-500":""}`,onClick:()=>{e.push(`/dashboard/fixtures/${t.externalId}`)},title:"Click to view fixture details",children:[(0,a.jsxs)(l.Ol,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(o.C,{className:j(t.status),children:v(t.status,t.elapsed)}),i&&a.jsx(o.C,{variant:"outline",className:"text-green-600 border-green-600",children:"LIVE"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[s," • ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[t.leagueName," • ",t.round]})]}),a.jsx(l.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:a.jsx(d.Z,{className:"h-4 w-4 text-gray-600"})}),a.jsx("span",{className:"font-medium",children:t.homeTeamName})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:a.jsx(d.Z,{className:"h-4 w-4 text-gray-600"})}),a.jsx("span",{className:"font-medium",children:t.awayTeamName})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl font-bold",children:t.goalsHome??"-"}),a.jsx("div",{className:"text-2xl font-bold",children:t.goalsAway??"-"})]})]}),t.venue&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[a.jsx(u.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[t.venue.name,", ",t.venue.city]})]}),null!==t.scoreHalftimeHome&&null!==t.scoreHalftimeAway&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Half-time: ",t.scoreHalftimeHome," - ",t.scoreHalftimeAway]})]})})]})};return g?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,a.jsxs)(c.z,{onClick:()=>y(),children:[a.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:t?"default":"outline",size:"sm",onClick:()=>s(!t),children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Auto Refresh ",t?"ON":"OFF"]}),(0,a.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>y(),disabled:f,children:[a.jsx(x.Z,{className:`mr-2 h-4 w-4 ${f?"animate-spin":""}`}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsx(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:p?.data?.filter(e=>["1H","2H","HT"].includes(e.status)).length||0}),a.jsx("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),a.jsx(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:p?.data?.filter(e=>"NS"===e.status).length||0}),a.jsx("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),a.jsx(l.Zb,{children:(0,a.jsxs)(l.aY,{className:"p-4",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-600",children:p?.data?.length||0}),a.jsx("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})})]}),f?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,t)=>a.jsx(l.Zb,{className:"animate-pulse",children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded"})]})})},t))}):p?.data&&p.data.length>0?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.data.map(e=>a.jsx(w,{fixture:e},e.id))}):a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6 text-center",children:a.jsx("p",{className:"text-gray-600",children:"No live or upcoming fixtures found"})})}),p?.meta&&p.meta.totalPages>1&&a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Page ",p.meta.currentPage," of ",p.meta.totalPages,"(",p.meta.totalItems," total fixtures)"]})})]})}},20255:(e,t,s)=>{"use strict";s.d(t,{L:()=>r});var a=s(50053);let r={getFixtures:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch fixtures: ${s.statusText}`);return await s.json()},getFixtureById:async e=>{let t=await fetch(`/api/fixtures/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture: ${t.statusText}`);return await t.json()},getUpcomingAndLive:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=await fetch(`/api/fixtures/live?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Failed to fetch live fixtures: ${s.statusText}`);return await s.json()},getTeamSchedule:async(e,t={})=>{let s=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())}),await a.x.get(`/football/fixtures/schedules/${e}?${s.toString()}`)},getFixtureStatistics:async e=>await a.x.get(`/football/fixtures/statistics/${e}`),triggerSeasonSync:async()=>{let e=(console.warn("❌ Season sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger season sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Season sync successful"),s},triggerDailySync:async()=>{let e=(console.warn("❌ Daily sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger daily sync: ${t.statusText}`)}let s=await t.json();return console.log("✅ Daily sync successful"),s},getSyncStatus:async()=>{let e=(console.warn("❌ Sync status - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||`Failed to get sync status: ${t.statusText}`)}let s=await t.json();return console.log("✅ Sync status successful"),s},createFixture:async e=>{let t=(console.warn("❌ Create fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!t.Authorization,data:e});let s=await fetch("/api/fixtures",{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to create fixture: ${s.statusText}`)}let a=await s.json();return console.log("✅ Create fixture successful:",a.data?.id),a.data||a},updateFixture:async(e,t)=>{let s=(console.warn("❌ Update fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!s.Authorization,data:t});let a=await fetch(`/api/fixtures/${e}`,{method:"PUT",headers:s,body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",a.status,a.statusText,e),Error(e.message||`Failed to update fixture: ${a.statusText}`)}let r=await a.json();return console.log("✅ Update fixture successful:",e),r.data||r},deleteFixture:async e=>{let t=(console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let s=await fetch(`/api/fixtures/${e}`,{method:"DELETE",headers:t});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",s.status,s.statusText,e),Error(e.message||`Failed to delete fixture: ${s.statusText}`)}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch(`/api/fixtures/${e}/statistics`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture statistics: ${t.statusText}`);return await t.json()},getFixtureEvents:async e=>{let t=await fetch(`/api/fixtures/${e}/events`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture events: ${t.statusText}`);return await t.json()},getFixture:async e=>(await r.getFixtureById(e)).data}},8064:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/live/page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,6317,7833],()=>s(88164));module.exports=a})();