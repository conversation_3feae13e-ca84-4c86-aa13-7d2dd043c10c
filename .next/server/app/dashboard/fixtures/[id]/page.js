(()=>{var e={};e.id=9447,e.ids=[9447],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},55744:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var t=a(50482),r=a(69108),l=a(62563),i=a.n(l),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["dashboard",{children:["fixtures",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,40172)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx"],o="/dashboard/fixtures/[id]/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/fixtures/[id]/page",pathname:"/dashboard/fixtures/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77588:(e,s,a)=>{Promise.resolve().then(a.bind(a,39289))},63024:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},12594:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},95269:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},25545:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26187:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},80508:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},17910:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},32817:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(97075).Z)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},39289:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(95344),r=a(3729),l=a(11494),i=a(19738),n=a(14373),c=a(8428),d=a(23673),x=a(5094),o=a(86688),m=a(20255),h=a(63024),u=a(81137),p=a(46327),y=a(38271),j=a(55794),f=a(25545),N=a(30782),w=a(80508),g=a(65719),v=a(33733),b=a(36487),Z=a(19591),k=a(18822),T=a(85499);function C({fixture:e,className:s}){return t.jsx(d.Zb,{className:s,children:(0,t.jsxs)(d.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"text-sm font-medium text-gray-600",children:e.leagueName}),e.round&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["• ",e.round]})]}),t.jsx(Z.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(e.status),children:((e,s)=>{switch(e){case"1H":case"2H":return`${s}'`;case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(e.status,e.elapsed)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[e.homeTeamLogo&&t.jsx("img",{src:`http://172.31.213.61/${e.homeTeamLogo}`,alt:e.homeTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h3",{className:"font-semibold text-lg",children:e.homeTeamName}),t.jsx("span",{className:"text-sm text-gray-500",children:"Home"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[e.goalsHome??"-"," - ",e.goalsAway??"-"]}),null!==e.scoreHalftimeHome&&null!==e.scoreHalftimeAway&&(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["HT: ",e.scoreHalftimeHome," - ",e.scoreHalftimeAway]}),t.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"VS"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[e.awayTeamLogo&&t.jsx("img",{src:`http://172.31.213.61/${e.awayTeamLogo}`,alt:e.awayTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h3",{className:"font-semibold text-lg",children:e.awayTeamName}),t.jsx("span",{className:"text-sm text-gray-500",children:"Away"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(j.Z,{className:"h-4 w-4"}),t.jsx(T.U,{dateTime:e.date,showDate:!0,showTime:!0,className:"font-medium"})]}),e.venue&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(w.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["string"==typeof e.venue?e.venue:e.venue.name,"object"==typeof e.venue&&e.venue.city&&(0,t.jsxs)("span",{className:"text-gray-500",children:[", ",e.venue.city]})]})]}),e.referee&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(k.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Referee: ",e.referee]})]}),e.elapsed&&"NS"!==e.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(f.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.elapsed,"' minutes played"]})]})]})]})})}var O=a(12594),_=a(95269),q=a(17910),S=a(26187);function M({fixture:e}){let{data:s,isLoading:a,error:r}=(0,i.a)({queryKey:["fixture-statistics",e.externalId],queryFn:()=>m.L.getFixtureStatistics(e.externalId),enabled:!!e.externalId,staleTime:3e5}),l=(()=>{if(!s?.data||!Array.isArray(s.data)||s.data.length<2)return{possession:{home:65,away:35},shots:{home:12,away:8},shotsOnTarget:{home:6,away:3},corners:{home:7,away:4},fouls:{home:11,away:14},yellowCards:{home:2,away:3},redCards:{home:0,away:1},offsides:{home:3,away:2},isRealData:!1};let e=s.data[0],a=s.data[1],t=(e,s)=>{if(!e?.statistics)return 0;let a=e.statistics[s];return null==a?0:"string"==typeof a&&a.includes("%")?parseInt(a.replace("%","")):parseInt(a)||0};return{possession:{home:t(e,"possession"),away:t(a,"possession")},shots:{home:t(e,"totalShots"),away:t(a,"totalShots")},shotsOnTarget:{home:t(e,"shotsOnGoal"),away:t(a,"shotsOnGoal")},corners:{home:t(e,"corners"),away:t(a,"corners")},fouls:{home:t(e,"fouls")||0,away:t(a,"fouls")||0},yellowCards:{home:t(e,"yellowCards"),away:t(a,"yellowCards")},redCards:{home:t(e,"redCards"),away:t(a,"redCards")},offsides:{home:t(e,"offsides"),away:t(a,"offsides")},isRealData:!0}})(),n=({label:e,homeValue:s,awayValue:a,icon:r,isPercentage:l=!1})=>{let i=s+a,n=i>0?s/i*100:50,c=i>0?a/i*100:50;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"font-medium text-right w-12",children:[s,l?"%":""]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-1 justify-center",children:[t.jsx(r,{className:"h-4 w-4 text-gray-500"}),t.jsx("span",{className:"text-sm text-gray-600",children:e})]}),(0,t.jsxs)("span",{className:"font-medium text-left w-12",children:[a,l?"%":""]})]}),(0,t.jsxs)("div",{className:"flex h-2 bg-gray-200 rounded-full overflow-hidden",children:[t.jsx("div",{className:"bg-blue-500 transition-all duration-300",style:{width:`${n}%`}}),t.jsx("div",{className:"bg-red-500 transition-all duration-300",style:{width:`${c}%`}})]})]})};return a?(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[t.jsx(O.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Match Statistics"})]})}),t.jsx(d.aY,{className:"space-y-6",children:t.jsx("div",{className:"space-y-4",children:[...Array(6)].map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(o.Od,{className:"h-4 w-8"}),t.jsx(o.Od,{className:"h-4 w-20"}),t.jsx(o.Od,{className:"h-4 w-8"})]}),t.jsx(o.Od,{className:"h-2 w-full"})]},s))})})]}):(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[t.jsx(O.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Match Statistics"}),!l.isRealData&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-orange-600",children:[t.jsx(_.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Demo Data"})]})]})}),(0,t.jsxs)(d.aY,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm font-medium text-gray-600",children:[t.jsx("span",{className:"w-12 text-right",children:e.homeTeamName}),t.jsx("span",{className:"flex-1 text-center",children:"Statistics"}),t.jsx("span",{className:"w-12 text-left",children:e.awayTeamName})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(n,{label:"Possession",homeValue:l.possession.home,awayValue:l.possession.away,icon:O.Z,isPercentage:!0}),t.jsx(n,{label:"Shots",homeValue:l.shots.home,awayValue:l.shots.away,icon:q.Z}),t.jsx(n,{label:"Shots on Target",homeValue:l.shotsOnTarget.home,awayValue:l.shotsOnTarget.away,icon:q.Z}),t.jsx(n,{label:"Corners",homeValue:l.corners.home,awayValue:l.corners.away,icon:S.Z}),t.jsx(n,{label:"Fouls",homeValue:l.fouls.home,awayValue:l.fouls.away,icon:f.Z}),t.jsx(n,{label:"Yellow Cards",homeValue:l.yellowCards.home,awayValue:l.yellowCards.away,icon:S.Z}),t.jsx(n,{label:"Red Cards",homeValue:l.redCards.home,awayValue:l.redCards.away,icon:S.Z}),t.jsx(n,{label:"Offsides",homeValue:l.offsides.home,awayValue:l.offsides.away,icon:S.Z})]}),t.jsx("div",{className:"text-xs text-gray-500 text-center pt-4 border-t",children:"* Statistics are updated in real-time during the match"})]})]})}let F=(0,a(97075).Z)("goal",[["path",{d:"M12 13V2l8 4-8 4",key:"5wlwwj"}],["path",{d:"M20.561 10.222a9 9 0 1 1-12.55-5.29",key:"1c0wjv"}],["path",{d:"M8.002 9.997a5 5 0 1 0 8.9 2.02",key:"gb1g7m"}]]);var I=a(32817),V=a(64989);function P({fixture:e}){let{data:s,isLoading:a,error:r}=(0,i.a)({queryKey:["fixture-events",e.externalId],queryFn:()=>m.L.getFixtureEvents(e.externalId),enabled:!!e.externalId,staleTime:3e5});if(a)return(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[t.jsx(f.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Match Timeline"})]})}),t.jsx(d.aY,{children:t.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx(o.Od,{className:"h-6 w-12"}),t.jsx(o.Od,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[t.jsx(o.Od,{className:"h-4 w-24"}),t.jsx(o.Od,{className:"h-4 w-32"}),t.jsx(o.Od,{className:"h-3 w-20"})]})]},s))})})]});let{events:l,isRealData:n}=s?.data?.events&&Array.isArray(s.data.events)?{events:s.data.events.map((s,a)=>{let t=s.team.name===e.homeTeamName,r=s.time.elapsed+(s.time.extra||0),l="";return s.assist.name?l=`Assist: ${s.assist.name}`:"subst"===s.type&&s.assist.name&&(l=`In: ${s.assist.name}`),{id:a+1,minute:r,type:((e,s)=>{switch(e.toLowerCase()){case"goal":if(s.toLowerCase().includes("own"))return"own_goal";if(s.toLowerCase().includes("penalty"))return"penalty";return"goal";case"card":return s.toLowerCase().includes("red")?"red_card":"yellow_card";case"subst":return"substitution";default:return"goal"}})(s.type,s.detail),team:t?"home":"away",player:s.player.name,description:s.detail,additionalInfo:l}}).sort((e,s)=>e.minute-s.minute),isRealData:!0}:{events:[{id:1,minute:15,type:"goal",team:"home",player:"Marcus Rashford",description:"Goal",additionalInfo:"Assist: Bruno Fernandes"},{id:2,minute:23,type:"yellow_card",team:"away",player:"Virgil van Dijk",description:"Yellow Card",additionalInfo:"Foul"},{id:3,minute:45,type:"substitution",team:"home",player:"Anthony Martial",description:"Substitution",additionalInfo:"Out: Jadon Sancho"},{id:4,minute:67,type:"goal",team:"away",player:"Mohamed Salah",description:"Goal",additionalInfo:"Assist: Sadio Man\xe9"},{id:5,minute:89,type:"goal",team:"home",player:"Mason Greenwood",description:"Goal",additionalInfo:"Penalty"}],isRealData:!1},c=e=>{switch(e){case"goal":case"penalty":case"own_goal":return t.jsx(F,{className:"h-4 w-4"});case"yellow_card":return t.jsx(g.Z,{className:"h-4 w-4 text-yellow-500"});case"red_card":return t.jsx(I.Z,{className:"h-4 w-4 text-red-500"});case"substitution":return t.jsx(V.Z,{className:"h-4 w-4 text-blue-500"});default:return t.jsx(f.Z,{className:"h-4 w-4"})}},x=e=>{switch(e){case"goal":case"penalty":return"bg-green-100 text-green-800";case"own_goal":return"bg-orange-100 text-orange-800";case"yellow_card":return"bg-yellow-100 text-yellow-800";case"red_card":return"bg-red-100 text-red-800";case"substitution":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},h=e=>{switch(e){case"goal":return"Goal";case"penalty":return"Penalty Goal";case"own_goal":return"Own Goal";case"yellow_card":return"Yellow Card";case"red_card":return"Red Card";case"substitution":return"Substitution";default:return e}};return 0===l.length?(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[t.jsx(f.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Match Timeline"})]})}),t.jsx(d.aY,{children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[t.jsx(f.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),t.jsx("p",{children:"No events recorded for this match yet."})]})})]}):(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[t.jsx(f.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Match Timeline"}),!n&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-orange-600",children:[t.jsx(_.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"text-xs",children:"Demo Data"})]})]})}),(0,t.jsxs)(d.aY,{children:[t.jsx("div",{className:"space-y-4",children:l.map((s,a)=>(0,t.jsxs)("div",{className:"relative",children:[a<l.length-1&&t.jsx("div",{className:"absolute left-6 top-12 w-0.5 h-8 bg-gray-200"}),(0,t.jsxs)("div",{className:`flex items-start space-x-4 ${"away"===s.team?"flex-row-reverse space-x-reverse":""}`,children:[t.jsx("div",{className:"flex-shrink-0",children:(0,t.jsxs)(Z.C,{variant:"outline",className:"font-mono",children:[s.minute,"'"]})}),t.jsx("div",{className:`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${"home"===s.team?"bg-blue-100":"bg-red-100"}`,children:c(s.type)}),(0,t.jsxs)("div",{className:`flex-1 ${"away"===s.team?"text-right":""}`,children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(Z.C,{className:x(s.type),children:h(s.type)}),t.jsx("span",{className:"text-sm text-gray-500",children:"home"===s.team?e.homeTeamName:e.awayTeamName})]}),t.jsx("p",{className:"font-medium text-gray-900 mt-1",children:s.player}),s.additionalInfo&&t.jsx("p",{className:"text-sm text-gray-600 mt-1",children:s.additionalInfo})]})]})]},s.id))}),t.jsx("div",{className:"mt-8 pt-6 border-t",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-8 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),t.jsx("span",{children:"1st Half: 0-45'"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),t.jsx("span",{children:"2nd Half: 45-90'"})]}),e.elapsed&&e.elapsed>90&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),t.jsx("span",{children:"Extra Time: 90'+"})]})]})})]})]})}var L=a(4138),A=a(55085),D=a(89895);let H=({fixture:e,onBroadcastLinks:s,className:a=""})=>{let r=(0,c.useRouter)(),{isEditor:l}=(0,b.TE)();return(0,t.jsxs)("div",{className:`space-y-4 ${a}`,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Actions"}),l()&&(0,t.jsxs)(x.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{let s=e.externalId||e.id;r.push(`/dashboard/fixtures/${s}/edit`)},children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Edit Fixture"]}),(0,t.jsxs)(x.z,{variant:"outline",className:"w-full justify-start",onClick:s,children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Manage Broadcast Links"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Related Data"}),(0,t.jsxs)(x.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{e.homeTeamId&&window.open(`/dashboard/teams/${e.homeTeamId}`,"_blank")},disabled:!e.homeTeamId,children:[t.jsx(D.Z,{className:"mr-2 h-4 w-4"}),"View Home Team"]}),(0,t.jsxs)(x.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{e.awayTeamId&&window.open(`/dashboard/teams/${e.awayTeamId}`,"_blank")},disabled:!e.awayTeamId,children:[t.jsx(D.Z,{className:"mr-2 h-4 w-4"}),"View Away Team"]}),(0,t.jsxs)(x.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{e.leagueId&&window.open(`/dashboard/leagues/${e.leagueId}`,"_blank")},disabled:!e.leagueId,children:[t.jsx(j.Z,{className:"mr-2 h-4 w-4"}),"View League"]})]})]})};var z=a(73875),E=a(34755);function $(){let e=(0,c.useParams)(),s=(0,c.useRouter)(),a=(0,l.NL)(),Z=parseInt(e.id),[k,T]=(0,r.useState)(!1),[O,_]=(0,r.useState)(!1),{isEditor:q,isAdmin:S}=(0,b.TE)(),{data:F,isLoading:I,error:V,refetch:D}=(0,i.a)({queryKey:["fixture",Z],queryFn:()=>m.L.getFixture(Z),enabled:!!Z}),$=(0,n.D)({mutationFn:()=>m.L.deleteFixture(Z),onSuccess:()=>{a.invalidateQueries({queryKey:["fixtures"]}),E.toast.success("Fixture deleted successfully"),_(!1),s.push("/dashboard/fixtures")},onError:e=>{E.toast.error(e.message||"Failed to delete fixture"),_(!1)}}),R=()=>{T(!0)};return I?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(o.Od,{className:"h-10 w-10"}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o.Od,{className:"h-8 w-64"}),t.jsx(o.Od,{className:"h-4 w-48"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[t.jsx(o.Od,{className:"h-64"}),t.jsx(o.Od,{className:"h-48"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx(o.Od,{className:"h-32"}),t.jsx(o.Od,{className:"h-48"})]})]})]}):V||!F?(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(x.z,{variant:"outline",onClick:()=>s.back(),children:[t.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),t.jsx(d.Zb,{children:t.jsx(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load fixture details"}),t.jsx(x.z,{onClick:()=>s.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(A.Z,{variant:"detail",fixtureId:Z,onRefresh:D,isLoading:I}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[F.homeTeamName," vs ",F.awayTeamName]}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:[F.leagueName," • Fixture Details"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[q()&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(x.z,{variant:"outline",onClick:R,children:[t.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Broadcast Links"]}),(0,t.jsxs)(x.z,{variant:"outline",onClick:()=>{s.push(`/dashboard/fixtures/${Z}/edit`)},children:[t.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Edit"]})]}),S()&&(0,t.jsxs)(x.z,{variant:"outline",onClick:()=>{_(!0)},children:[t.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[t.jsx(C,{fixture:F}),t.jsx(P,{fixture:F})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx(M,{fixture:F}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center",children:[t.jsx(j.Z,{className:"mr-2 h-5 w-5"}),"Match Information"]})}),(0,t.jsxs)(d.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"Date"}),t.jsx("div",{className:"text-sm text-gray-600",children:new Date(F.date).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(f.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"Time"}),t.jsx("div",{className:"text-sm text-gray-600",children:new Date(F.date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"League"}),t.jsx("div",{className:"text-sm text-gray-600",children:F.leagueName})]})]}),F.venueName&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(w.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"Venue"}),t.jsx("div",{className:"text-sm text-gray-600",children:F.venueName})]})]})]})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Quick Actions"})}),t.jsx(d.aY,{children:t.jsx(H,{fixture:F,onBroadcastLinks:R})})]})]})]}),t.jsx(L.A,{isOpen:k,onClose:()=>T(!1),fixture:F}),t.jsx(z.u_,{isOpen:O,onClose:()=>_(!1),title:"Delete Fixture",description:"Are you sure you want to delete this fixture? This action cannot be undone.",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[t.jsx(g.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the fixture:"}),t.jsx("p",{className:"text-sm text-red-700 mt-1",children:(0,t.jsxs)("strong",{children:[F.homeTeamName," vs ",F.awayTeamName]})}),(0,t.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:[new Date(F.date).toLocaleDateString()," • ",F.leagueName]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx(x.z,{variant:"outline",onClick:()=>_(!1),disabled:$.isLoading,children:"Cancel"}),t.jsx(x.z,{variant:"destructive",onClick:()=>{$.mutate()},disabled:$.isLoading,children:$.isLoading?(0,t.jsxs)(t.Fragment,{children:[t.jsx(v.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Delete Fixture"]})})]})]})})]})}},7361:(e,s,a)=>{"use strict";a.d(s,{_:()=>d});var t=a(95344),r=a(3729),l=a(14217),i=a(49247),n=a(11453);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},a)=>t.jsx(l.f,{ref:a,className:(0,n.cn)(c(),e),...s}));d.displayName=l.f.displayName},40172:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let t=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx`),{__esModule:r,$$typeof:l}=t,i=t.default},87298:(e,s,a)=>{"use strict";a.d(s,{C2:()=>i,fC:()=>c});var t=a(3729),r=a(62409),l=a(95344),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),n=t.forwardRef((e,s)=>(0,l.jsx)(r.WV.span,{...e,ref:s,style:{...i,...e.style}}));n.displayName="VisuallyHidden";var c=n}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,6126,337,2609,3649,732,2527,2348,6614,6317,7833,6253,155],()=>a(55744));module.exports=t})();