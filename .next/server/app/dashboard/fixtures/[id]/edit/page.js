(()=>{var e={};e.id=3660,e.ids=[3660],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},25357:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>o});var s=t(50482),r=t(69108),l=t(62563),i=t.n(l),d=t(68300),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(a,n);let o=["",{children:["dashboard",{children:["fixtures",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42538)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx"],u="/dashboard/fixtures/[id]/edit/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/fixtures/[id]/edit/page",pathname:"/dashboard/fixtures/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},95787:(e,a,t)=>{Promise.resolve().then(t.bind(t,72080))},72080:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>N});var s=t(95344),r=t(3729),l=t(8428),i=t(11494),d=t(19738),n=t(14373),o=t(23673),c=t(5094),u=t(25179),m=t(90187),h=t(86688),x=t(67999),g=t(20255),p=t(59836),y=t(73286),v=t(63024),j=t(55794),b=t(31498),f=t(34755),T=t(55085);function N(){let e=(0,l.useParams)(),a=(0,l.useRouter)(),t=(0,i.NL)(),N=parseInt(e.id),[I,S]=(0,r.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:"",status:"",goalsHome:"",goalsAway:"",elapsed:"",isHot:!1}),[w,P]=(0,r.useState)({}),{data:q,isLoading:C}=(0,d.a)({queryKey:["fixture",N],queryFn:()=>g.L.getFixture(N),enabled:!!N}),[F,A]=(0,r.useState)(""),[M,H]=(0,r.useState)(""),[E,L]=(0,r.useState)([]),[O,k]=(0,r.useState)(""),[$,U]=(0,r.useState)([]),{data:D,isLoading:_,error:B}=(0,d.a)({queryKey:["leagues"],queryFn:()=>p.A.getLeagues({limit:100})}),{data:Z,isLoading:K,error:V}=(0,d.a)({queryKey:["teams"],queryFn:()=>y.k.getTeams({limit:100})});(0,r.useEffect)(()=>{if(!M.trim()){L([]);return}let e=setTimeout(async()=>{try{let e=await y.k.getTeams({limit:100,search:M});e?.data&&L(e.data)}catch(e){console.error("❌ Home team search error:",e),L([])}},3e3);return()=>clearTimeout(e)},[M]),(0,r.useEffect)(()=>{if(!O.trim()){U([]);return}let e=setTimeout(async()=>{try{let e=await y.k.getTeams({limit:100,search:O});e?.data&&U(e.data)}catch(e){console.error("❌ Away team search error:",e),U([])}},3e3);return()=>clearTimeout(e)},[O]);let z=(0,n.D)({mutationFn:e=>g.L.updateFixture(N,e),onSuccess:()=>{t.invalidateQueries({queryKey:["fixture",N]}),t.invalidateQueries({queryKey:["fixtures"]}),f.toast.success("Fixture updated successfully"),a.push(`/dashboard/fixtures/${N}`)},onError:e=>{f.toast.error(e.message||"Failed to update fixture")}});(0,r.useEffect)(()=>{if(q){let e=new Date(q.date);S({homeTeamId:q.homeTeamId?.toString()||"",awayTeamId:q.awayTeamId?.toString()||"",leagueId:q.leagueId?.toString()||"",date:e.toISOString().split("T")[0],time:e.toTimeString().slice(0,5),venueName:q.venue?.name||q.venueName||"",venueCity:q.venue?.city||q.venueCity||"",round:q.round||"",status:q.status||"",goalsHome:q.goalsHome?.toString()||"",goalsAway:q.goalsAway?.toString()||"",elapsed:q.elapsed?.toString()||"",referee:q.referee||"",temperature:q.temperature?.toString()||"",weather:q.weather||"",attendance:q.attendance?.toString()||"",isHot:q.isHot||!1})}},[q]),(0,r.useEffect)(()=>{q&&q.status&&!I.status&&S(e=>({...e,status:q.status}))},[q,I.status]);let G=()=>{let e={};return I.homeTeamId||(e.homeTeamId="Home team is required"),I.awayTeamId||(e.awayTeamId="Away team is required"),I.leagueId||(e.leagueId="League is required"),I.date||(e.date="Date is required"),I.time||(e.time="Time is required"),I.status||(e.status="Status is required"),I.homeTeamId===I.awayTeamId&&(e.awayTeamId="Away team must be different from home team"),P(e),0===Object.keys(e).length},R=(e,a)=>{S(t=>({...t,[e]:a})),w[e]&&P(a=>({...a,[e]:void 0}))},W=(0,r.useCallback)(e=>{A(e)},[]),Q=(0,r.useCallback)(e=>{H(e)},[]),Y=(0,r.useCallback)(e=>{k(e)},[]),X=(0,r.useMemo)(()=>D?.data?.map((e,a)=>({value:e.externalId.toString(),label:`${e.name}${e.season?` (${e.season})`:""}`,logo:e.logo,season:e.season,uniqueKey:`league-${e.id||e.externalId}-${a}`}))||[],[D?.data]),J=(0,r.useMemo)(()=>Z?.data?.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:`team-${e.id||e.externalId}-${a}`}))||[],[Z?.data]),ee=(0,r.useMemo)(()=>E.length>0?E.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:`search-home-team-${e.id||e.externalId}-${a}`})):J,[J,E]),ea=(0,r.useMemo)(()=>$.length>0?$.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:`search-away-team-${e.id||e.externalId}-${a}`})):J,[J,$]),{league:et,homeTeam:es,awayTeam:er}=(()=>{if(!q)return{league:null,homeTeam:null,awayTeam:null};let e=X.find(e=>e.value===I.leagueId),a=ee.find(e=>e.value===I.homeTeamId),t=ea.find(e=>e.value===I.awayTeamId);return{league:e||{value:I.leagueId,label:q.leagueName,logo:""},homeTeam:a||{value:I.homeTeamId,label:q.homeTeamName,logo:q.homeTeamLogo},awayTeam:t||{value:I.awayTeamId,label:q.awayTeamName,logo:q.awayTeamLogo}}})(),[el,ei]=(0,r.useState)(0);(0,r.useEffect)(()=>{ee.length>0&&ea.length>0&&X.length>0&&I.homeTeamId&&ei(e=>e+1)},[ee.length,ea.length,X.length,I.homeTeamId,I.awayTeamId,I.leagueId]);let ed=({label:e,selectedOption:a,placeholder:t="Not selected"})=>(0,s.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-2",children:e}),a?(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[a.logo&&s.jsx("img",{src:`http://172.31.213.61/${a.logo}`,alt:a.label,className:"w-8 h-8 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),s.jsx("span",{className:"text-lg font-semibold text-gray-900",children:a.label})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center",children:s.jsx("span",{className:"text-gray-400 text-xs",children:"?"})}),s.jsx("span",{className:"text-gray-500 italic",children:t})]})]});return C||_||K?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx(h.Od,{className:"h-10 w-20"}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(h.Od,{className:"h-8 w-64"}),s.jsx(h.Od,{className:"h-4 w-48"})]})]}),(0,s.jsxs)(o.Zb,{children:[(0,s.jsxs)(o.Ol,{children:[s.jsx(h.Od,{className:"h-6 w-48"}),s.jsx(h.Od,{className:"h-4 w-64"})]}),(0,s.jsxs)(o.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(h.Od,{className:"h-4 w-32"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(h.Od,{className:"h-10"}),s.jsx(h.Od,{className:"h-10"})]}),s.jsx(h.Od,{className:"h-10"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(h.Od,{className:"h-4 w-24"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(h.Od,{className:"h-10"}),s.jsx(h.Od,{className:"h-10"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[s.jsx(h.Od,{className:"h-10 w-20"}),s.jsx(h.Od,{className:"h-10 w-32"})]})]})]})]}):!q||B||V?(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>a.back(),children:[s.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),s.jsx(o.Zb,{children:s.jsx(o.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[!q&&s.jsx("p",{className:"text-red-600 mb-4",children:"Fixture not found"}),!!B&&s.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load leagues"}),!!V&&s.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load teams"}),s.jsx(c.z,{onClick:()=>a.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx(T.Z,{variant:"edit",fixtureId:N,isLoading:z.isLoading}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Edit Fixture: ",q.homeTeamName," vs ",q.awayTeamName]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Update fixture details and match information"})]})]}),(0,s.jsxs)(o.Zb,{children:[(0,s.jsxs)(o.Ol,{children:[(0,s.jsxs)(o.ll,{className:"flex items-center",children:[s.jsx(j.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),s.jsx(o.SZ,{children:"Update the fixture information"})]}),s.jsx(o.aY,{children:(0,s.jsxs)("form",{onSubmit:e=>{var a;if(e.preventDefault(),!G()){f.toast.error("Please fix the form errors");return}let t=new Date(`${I.date}T${I.time}`),s={homeTeamId:parseInt(I.homeTeamId),awayTeamId:parseInt(I.awayTeamId),leagueId:parseInt(I.leagueId),date:t.toISOString(),venueName:I.venueName||null,venueCity:I.venueCity||null,round:I.round||null,referee:I.referee||null,isHot:I.isHot,data:{status:I.status,statusLong:{TBD:"Time To Be Defined",NS:"Not Started",ST:"Scheduled","1H":"First Half",HT:"Halftime","2H":"Second Half",ET:"Extra Time",BT:"Break Time",P:"Penalty In Progress",SUSP:"Match Suspended",INT:"Match Interrupted",FT:"Match Finished",AET:"Match Finished After Extra Time",PEN:"Match Finished After Penalty",PST:"Match Postponed",CANC:"Match Cancelled",ABD:"Match Abandoned",AWD:"Technical Loss",WO:"WalkOver",LIVE:"In Progress"}[a=I.status]||a,statusExtra:0,elapsed:I.elapsed?parseInt(I.elapsed):null,goalsHome:I.goalsHome?parseInt(I.goalsHome):null,goalsAway:I.goalsAway?parseInt(I.goalsAway):null}};z.mutate(s)},className:"space-y-6",children:[(0,s.jsxs)(u.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx(ed,{label:"Selected Home Team",selectedOption:es,placeholder:"No home team selected"},`home-${el}`),s.jsx(m.L,{label:"Home Team",placeholder:K?"Loading teams...":"Select home team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:I.homeTeamId,onValueChange:e=>R("homeTeamId",e),options:ee,error:w.homeTeamId,disabled:K,onSearch:Q,isLoading:K},"home-team-search-stable")]}),(0,s.jsxs)("div",{children:[s.jsx(ed,{label:"Selected Away Team",selectedOption:er,placeholder:"No away team selected"},`away-${el}`),s.jsx(m.L,{label:"Away Team",placeholder:K?"Loading teams...":"Select away team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:I.awayTeamId,onValueChange:e=>R("awayTeamId",e),options:ea.filter(e=>e.value!==I.homeTeamId),error:w.awayTeamId,disabled:K,onSearch:Y,isLoading:K},"away-team-search-stable")]})]}),s.jsx("div",{children:s.jsx(()=>(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"flex items-center space-x-3 min-w-0 flex-1",children:et?(0,s.jsxs)(s.Fragment,{children:[et.logo&&s.jsx("img",{src:`http://172.31.213.61/${et.logo}`,alt:et.label,className:"w-8 h-8 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),s.jsx("span",{className:"text-lg font-semibold text-gray-900 truncate",children:et.label})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0",children:s.jsx("span",{className:"text-gray-400 text-xs",children:"?"})}),s.jsx("span",{className:"text-gray-500 italic",children:"No league selected"})]})}),(0,s.jsxs)("div",{className:"flex-shrink-0 w-64",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"League*"}),s.jsx(m.L,{placeholder:_?"Loading leagues...":"Select league",searchPlaceholder:"Search leagues...",required:!0,value:I.leagueId,onValueChange:e=>R("leagueId",e),options:X,error:w.leagueId,disabled:_,onSearch:W,isLoading:_},"league-search-stable")]})]}),{})})]}),(0,s.jsxs)(u.hj,{title:"Schedule",description:"Set the date and time (local timezone)",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(u.UP,{label:"Date *",type:"date",required:!0,value:I.date,onChange:e=>R("date",e.target.value),error:w.date,description:"Match date"}),s.jsx(u.UP,{label:"Time *",type:"time",required:!0,value:I.time,onChange:e=>R("time",e.target.value),error:w.time,description:`Local time (${Intl.DateTimeFormat().resolvedOptions().timeZone})`})]}),s.jsx("div",{className:"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:(0,s.jsxs)("p",{className:"flex items-center",children:[s.jsx("span",{className:"text-blue-600 mr-2",children:"ℹ️"}),s.jsx("strong",{children:"Timezone Info:"})," Times are displayed in your local timezone (",Intl.DateTimeFormat().resolvedOptions().timeZone,"). The asterisk (*) indicates required fields."]})})]}),(0,s.jsxs)(u.hj,{title:"Match Status",description:"Update match status and score",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(u.mg,{label:"Status",placeholder:"Select status",required:!0,value:I.status,onValueChange:e=>R("status",e),options:[{value:"TBD",label:"Time To Be Defined"},{value:"NS",label:"Not Started"},{value:"ST",label:"Scheduled"},{value:"1H",label:"First Half, Kick Off"},{value:"HT",label:"Halftime"},{value:"2H",label:"Second Half, 2nd Half Started"},{value:"ET",label:"Extra Time"},{value:"BT",label:"Break Time (in Extra Time)"},{value:"P",label:"Penalty In Progress"},{value:"LIVE",label:"In Progress"},{value:"FT",label:"Match Finished (Regular Time)"},{value:"AET",label:"Match Finished After Extra Time"},{value:"PEN",label:"Match Finished After Penalty"},{value:"SUSP",label:"Match Suspended"},{value:"INT",label:"Match Interrupted"},{value:"PST",label:"Match Postponed"},{value:"CANC",label:"Match Cancelled"},{value:"ABD",label:"Match Abandoned"},{value:"AWD",label:"Technical Loss"},{value:"WO",label:"WalkOver"}],error:w.status}),s.jsx(u.UP,{label:"Home Goals",type:"number",min:"0",value:I.goalsHome,onChange:e=>R("goalsHome",e.target.value)}),s.jsx(u.UP,{label:"Away Goals",type:"number",min:"0",value:I.goalsAway,onChange:e=>R("goalsAway",e.target.value)})]}),s.jsx(u.UP,{label:"Elapsed Time (minutes)",type:"number",min:"0",max:"120",value:I.elapsed,onChange:e=>R("elapsed",e.target.value),description:"Minutes played in the match"})]}),s.jsx(u.hj,{title:"Fixture Settings",description:"Additional fixture settings",children:s.jsx(x.Z,{checked:I.isHot,onCheckedChange:e=>S(a=>({...a,isHot:e})),label:"Hot Fixture",description:"Mark this fixture as hot/featured",variant:"danger"})}),(0,s.jsxs)(u.hj,{title:"Venue & Match Information",description:"Venue details and match context",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(u.UP,{label:"Venue Name",placeholder:"Stadium name",value:I.venueName,onChange:e=>R("venueName",e.target.value)}),s.jsx(u.UP,{label:"Venue City",placeholder:"City",value:I.venueCity,onChange:e=>R("venueCity",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(u.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:I.round,onChange:e=>R("round",e.target.value)}),s.jsx(u.UP,{label:"Referee",placeholder:"Referee name",value:I.referee||"",onChange:e=>R("referee",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(u.UP,{label:"Temperature (\xb0C)",type:"number",placeholder:"e.g., 22",value:I.temperature||"",onChange:e=>R("temperature",e.target.value)}),s.jsx(u.UP,{label:"Weather",placeholder:"e.g., Sunny, Rainy",value:I.weather||"",onChange:e=>R("weather",e.target.value)}),s.jsx(u.UP,{label:"Attendance",type:"number",placeholder:"Number of spectators",value:I.attendance||"",onChange:e=>R("attendance",e.target.value)})]})]}),(0,s.jsxs)(u.iN,{children:[s.jsx(c.z,{type:"button",variant:"outline",onClick:()=>a.back(),disabled:z.isLoading,children:"Cancel"}),(0,s.jsxs)(c.z,{type:"submit",disabled:z.isLoading,children:[s.jsx(b.Z,{className:"mr-2 h-4 w-4"}),z.isLoading?"Updating...":"Update Fixture"]})]})]})})]})]})}},42538:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx`),{__esModule:r,$$typeof:l}=s,i=s.default}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,6126,337,2609,3649,732,7966,6419,6317,7833,6253,6278],()=>t(25357));module.exports=s})();