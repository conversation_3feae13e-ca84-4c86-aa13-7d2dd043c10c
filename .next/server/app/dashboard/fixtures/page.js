(()=>{var e={};e.id=266,e.ids=[266],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},50619:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=a(50482),s=a(69108),n=a(62563),o=a.n(n),i=a(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let d=["",{children:["dashboard",{children:["fixtures",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,22613)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx"],u="/dashboard/fixtures/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/fixtures/page",pathname:"/dashboard/fixtures",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68628:(e,t,a)=>{Promise.resolve().then(a.bind(a,29401))},71532:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},96885:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},3380:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(97075).Z)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},29401:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ac});var r,s,n,o,i,l={};a.r(l),a.d(l,{Button:()=>eC,CaptionLabel:()=>eM,Chevron:()=>eT,Day:()=>eS,DayButton:()=>eE,Dropdown:()=>eO,DropdownNav:()=>e_,Footer:()=>eL,Month:()=>eF,MonthCaption:()=>eW,MonthGrid:()=>eP,Months:()=>eZ,MonthsDropdown:()=>eY,Nav:()=>eI,NextMonthButton:()=>ez,Option:()=>eH,PreviousMonthButton:()=>eq,Root:()=>eR,Select:()=>eU,Week:()=>eB,WeekNumber:()=>eV,WeekNumberHeader:()=>eK,Weekday:()=>eG,Weekdays:()=>eQ,Weeks:()=>eJ,YearsDropdown:()=>eX});var d={};a.r(d),a.d(d,{formatCaption:()=>e0,formatDay:()=>e2,formatMonthCaption:()=>e1,formatMonthDropdown:()=>e5,formatWeekNumber:()=>e3,formatWeekNumberHeader:()=>e4,formatWeekdayName:()=>e7,formatYearCaption:()=>e6,formatYearDropdown:()=>e8});var c={};a.r(c),a.d(c,{labelCaption:()=>te,labelDay:()=>tr,labelDayButton:()=>ta,labelGrid:()=>e9,labelGridcell:()=>tt,labelMonthDropdown:()=>tn,labelNav:()=>ts,labelNext:()=>to,labelPrevious:()=>ti,labelWeekNumber:()=>td,labelWeekNumberHeader:()=>tc,labelWeekday:()=>tl,labelYearDropdown:()=>tu});var u=a(95344),m=a(3729),h=a.n(m),f=a(11494),p=a(19738),y=a(14373),x=a(23673),g=a(5094),v=a(19591),b=a(67999),w=a(77022),N=a(73875),k=a(20255),D=a(53148),j=a(81137),C=a(46327),M=a(38271),T=a(33733),S=a(3380),E=a(96885),O=a(51838),_=a(55794),L=a(64989),F=a(86688),W=a(36487),P=a(4138),Z=a(3281),A=a(11453),$=a(71532),Y=a(97751);Symbol.for("constructDateFrom");let I={},z={};function H(e,t){try{let a=(I[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(a in z)return z[a];return R(a,a.split(":"))}catch{if(e in z)return z[e];let t=e?.match(q);if(t)return R(e,t.slice(1));return NaN}}let q=/([+-]\d\d):?(\d\d)?/;function R(e,t){let a=+t[0],r=+(t[1]||0);return z[e]=a>0?60*a+r:60*a-r}class U extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(H(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),Q(this,NaN),G(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new U(...t,e):new U(Date.now(),e)}withTimeZone(e){return new U(+this,e)}getTimezoneOffset(){return-H(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),G(this),+this}[Symbol.for("constructDateFrom")](e){return new U(+new Date(e),this.timeZone)}}let B=/^(get|set)(?!UTC)/;function G(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function Q(e){let t=H(e.timeZone,e),a=new Date(+e);a.setUTCHours(a.getUTCHours()-1);let r=-new Date(+e).getTimezoneOffset(),s=r- -new Date(+a).getTimezoneOffset(),n=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();s&&n&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+s);let o=r-t;o&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+o);let i=H(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-i-o;if(i!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=i-H(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!B.test(e))return;let t=e.replace(B,"$1UTC");U.prototype[t]&&(e.startsWith("get")?U.prototype[e]=function(){return this.internal[t]()}:(U.prototype[e]=function(){return Date.prototype[t].apply(this.internal,arguments),Date.prototype.setFullYear.call(this,this.internal.getUTCFullYear(),this.internal.getUTCMonth(),this.internal.getUTCDate()),Date.prototype.setHours.call(this,this.internal.getUTCHours(),this.internal.getUTCMinutes(),this.internal.getUTCSeconds(),this.internal.getUTCMilliseconds()),Q(this),+this},U.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),G(this),+this}))});class V extends U{static tz(e,...t){return t.length?new V(...t,e):new V(Date.now(),e)}toISOString(){let[e,t,a]=this.tzComponents(),r=`${e}${t}:${a}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,a,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${a} ${t} ${r}`}toTimeString(){var e;let t=this.internal.toUTCString().split(" ")[4],[a,r,s]=this.tzComponents();return`${t} GMT${a}${r}${s} (${e=this.timeZone,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(this).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),a=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,a]}withTimeZone(e){return new V(+this,e)}[Symbol.for("constructDateFrom")](e){return new V(+new Date(e),this.timeZone)}}(function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"})(r||(r={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(s||(s={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(n||(n={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(o||(o={}));var K=a(44246),J=a(10077),X=a(98037);function ee(e,t,a){let r=(0,X.Q)(e,a?.in);return isNaN(t)?(0,J.L)(a?.in||e,NaN):(t&&r.setDate(r.getDate()+t),r)}function et(e,t,a){let r=(0,X.Q)(e,a?.in);if(isNaN(t))return(0,J.L)(a?.in||e,NaN);if(!t)return r;let s=r.getDate(),n=(0,J.L)(a?.in||e,r.getTime());return(n.setMonth(r.getMonth()+t+1,0),s>=n.getDate())?n:(r.setFullYear(n.getFullYear(),n.getMonth(),s),r)}var ea=a(16061),er=a(89520),es=a(84951),en=a(55395);function eo(e,t){let a=(0,en.j)(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,s=(0,X.Q)(e,t?.in),n=s.getDay();return s.setDate(s.getDate()+((n<r?-7:0)+6-(n-r))),s.setHours(23,59,59,999),s}var ei=a(60631),el=a(92594),ed=a(29474),ec=a(6283),eu=a(27087),em=a(49953),eh=a(67700),ef=a(1707);function ep(e,t){let a=t.startOfMonth(e),r=a.getDay();return 1===r?a:0===r?t.addDays(a,-6):t.addDays(a,-1*(r-1))}class ey{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?V.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,a)=>this.overrides?.newDate?this.overrides.newDate(e,t,a):this.options.timeZone?new V(e,t,a,this.options.timeZone):new Date(e,t,a),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):ee(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):et(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):ee(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):et(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,ea.w)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):(0,er.T)(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){let{start:a,end:r}=function(e,t){let[a,r]=(0,es.d)(e,t.start,t.end);return{start:a,end:r}}(t?.in,e),s=+a>+r,n=s?+a:+r,o=s?r:a;o.setHours(0,0,0,0),o.setDate(1);let i=t?.step??1;if(!i)return[];i<0&&(i=-i,s=!s);let l=[];for(;+o<=n;)l.push((0,J.L)(a,o)),o.setMonth(o.getMonth()+i);return s?l.reverse():l}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let a=ep(e,t),r=function(e,t){let a=t.startOfMonth(e),r=a.getDay()>0?a.getDay():7,s=t.addDays(e,-r+1),n=t.addDays(s,34);return t.getMonth(e)===t.getMonth(n)?5:4}(e,t);return t.addDays(a,7*r-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):eo(e,{weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):(0,ei.V)(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):eo(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let a=(0,X.Q)(e,void 0),r=a.getFullYear();return a.setFullYear(r+1,0,0),a.setHours(23,59,59,999),a}(e),this.format=(e,t,a)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):(0,Z.WU)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,el.l)(e),this.getMonth=(e,t)=>{var a;return this.overrides?.getMonth?this.overrides.getMonth(e,this.options):(a=this.options,(0,X.Q)(e,a?.in).getMonth())},this.getYear=(e,t)=>{var a;return this.overrides?.getYear?this.overrides.getYear(e,this.options):(a=this.options,(0,X.Q)(e,a?.in).getFullYear())},this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,ed.Q)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):+(0,X.Q)(e)>+(0,X.Q)(t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+(0,X.Q)(e)<+(0,X.Q)(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,ec.J)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,a){let[r,s]=(0,es.d)(void 0,e,t);return+(0,eu.b)(r)==+(0,eu.b)(s)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,a){let[r,s]=(0,es.d)(void 0,e,t);return r.getFullYear()===s.getFullYear()&&r.getMonth()===s.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,a){let[r,s]=(0,es.d)(void 0,e,t);return r.getFullYear()===s.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let a,r;return e.forEach(e=>{r||"object"!=typeof e||(r=J.L.bind(null,e));let t=(0,X.Q)(e,r);(!a||a<t||isNaN(+t))&&(a=t)}),(0,J.L)(r,a||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let a,r;return e.forEach(e=>{r||"object"!=typeof e||(r=J.L.bind(null,e));let t=(0,X.Q)(e,r);(!a||a>t||isNaN(+t))&&(a=t)}),(0,J.L)(r,a||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,a){let r=(0,X.Q)(e,a?.in),s=r.getFullYear(),n=r.getDate(),o=(0,J.L)(a?.in||e,0);o.setFullYear(s,t,15),o.setHours(0,0,0,0);let i=function(e,t){let a=(0,X.Q)(e,void 0),r=a.getFullYear(),s=a.getMonth(),n=(0,J.L)(a,0);return n.setFullYear(r,s+1,0),n.setHours(0,0,0,0),n.getDate()}(o);return r.setMonth(t,Math.min(n,i)),r}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,a){let r=(0,X.Q)(e,a?.in);return isNaN(+r)?(0,J.L)(a?.in||e,NaN):(r.setFullYear(t),r)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):ep(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,eu.b)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,em.T)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let a=(0,X.Q)(e,void 0);return a.setDate(1),a.setHours(0,0,0,0),a}(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,eh.z)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,ef.e)(e),this.options={locale:K._,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),a={};for(let e=0;e<10;e++)a[e.toString()]=t.format(e);return a}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let ex=new ey;function eg(e,t,a=!1,r=ex){let{from:s,to:n}=e,{differenceInCalendarDays:o,isSameDay:i}=r;return s&&n?(0>o(n,s)&&([s,n]=[n,s]),o(t,s)>=(a?1:0)&&o(n,t)>=(a?1:0)):!a&&n?i(n,t):!a&&!!s&&i(s,t)}function ev(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function eb(e){return!!(e&&"object"==typeof e&&"from"in e)}function ew(e){return!!(e&&"object"==typeof e&&"after"in e)}function eN(e){return!!(e&&"object"==typeof e&&"before"in e)}function ek(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function eD(e,t){return Array.isArray(e)&&e.every(t.isDate)}function ej(e,t,a=ex){let r=Array.isArray(t)?t:[t],{isSameDay:s,differenceInCalendarDays:n,isAfter:o}=a;return r.some(t=>{if("boolean"==typeof t)return t;if(a.isDate(t))return s(e,t);if(eD(t,a))return t.includes(e);if(eb(t))return eg(t,e,!1,a);if(ek(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(ev(t)){let a=n(t.before,e),r=n(t.after,e),s=a>0,i=r<0;return o(t.before,t.after)?i&&s:s||i}return ew(t)?n(e,t.after)>0:eN(t)?n(t.before,e)>0:"function"==typeof t&&t(e)})}function eC(e){return m.createElement("button",{...e})}function eM(e){return m.createElement("span",{...e})}function eT(e){let{size:t=24,orientation:a="left",className:r}=e;return m.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},"up"===a&&m.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===a&&m.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===a&&m.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===a&&m.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function eS(e){let{day:t,modifiers:a,...r}=e;return m.createElement("td",{...r})}function eE(e){let{day:t,modifiers:a,...r}=e,s=m.useRef(null);return m.useEffect(()=>{a.focused&&s.current?.focus()},[a.focused]),m.createElement("button",{ref:s,...r})}function eO(e){let{options:t,className:a,components:s,classNames:n,...o}=e,i=[n[r.Dropdown],a].join(" "),l=t?.find(({value:e})=>e===o.value);return m.createElement("span",{"data-disabled":o.disabled,className:n[r.DropdownRoot]},m.createElement(s.Select,{className:i,...o},t?.map(({value:e,label:t,disabled:a})=>m.createElement(s.Option,{key:e,value:e,disabled:a},t))),m.createElement("span",{className:n[r.CaptionLabel],"aria-hidden":!0},l?.label,m.createElement(s.Chevron,{orientation:"down",size:18,className:n[r.Chevron]})))}function e_(e){return m.createElement("div",{...e})}function eL(e){return m.createElement("div",{...e})}function eF(e){let{calendarMonth:t,displayIndex:a,...r}=e;return m.createElement("div",{...r},e.children)}function eW(e){let{calendarMonth:t,displayIndex:a,...r}=e;return m.createElement("div",{...r})}function eP(e){return m.createElement("table",{...e})}function eZ(e){return m.createElement("div",{...e})}let eA=(0,m.createContext)(void 0);function e$(){let e=(0,m.useContext)(eA);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function eY(e){let{components:t}=e$();return m.createElement(t.Dropdown,{...e})}function eI(e){let{onPreviousClick:t,onNextClick:a,previousMonth:s,nextMonth:n,...o}=e,{components:i,classNames:l,labels:{labelPrevious:d,labelNext:c}}=e$(),u=(0,m.useCallback)(e=>{n&&a?.(e)},[n,a]),h=(0,m.useCallback)(e=>{s&&t?.(e)},[s,t]);return m.createElement("nav",{...o},m.createElement(i.PreviousMonthButton,{type:"button",className:l[r.PreviousMonthButton],tabIndex:s?void 0:-1,"aria-disabled":!s||void 0,"aria-label":d(s),onClick:h},m.createElement(i.Chevron,{disabled:!s||void 0,className:l[r.Chevron],orientation:"left"})),m.createElement(i.NextMonthButton,{type:"button",className:l[r.NextMonthButton],tabIndex:n?void 0:-1,"aria-disabled":!n||void 0,"aria-label":c(n),onClick:u},m.createElement(i.Chevron,{disabled:!n||void 0,orientation:"right",className:l[r.Chevron]})))}function ez(e){let{components:t}=e$();return m.createElement(t.Button,{...e})}function eH(e){return m.createElement("option",{...e})}function eq(e){let{components:t}=e$();return m.createElement(t.Button,{...e})}function eR(e){let{rootRef:t,...a}=e;return m.createElement("div",{...a,ref:t})}function eU(e){return m.createElement("select",{...e})}function eB(e){let{week:t,...a}=e;return m.createElement("tr",{...a})}function eG(e){return m.createElement("th",{...e})}function eQ(e){return m.createElement("thead",{"aria-hidden":!0},m.createElement("tr",{...e}))}function eV(e){let{week:t,...a}=e;return m.createElement("th",{...a})}function eK(e){return m.createElement("th",{...e})}function eJ(e){return m.createElement("tbody",{...e})}function eX(e){let{components:t}=e$();return m.createElement(t.Dropdown,{...e})}function e0(e,t,a){return(a??new ey(t)).format(e,"LLLL y")}let e1=e0;function e2(e,t,a){return(a??new ey(t)).format(e,"d")}function e5(e,t=ex){return t.format(e,"LLLL")}function e3(e,t=ex){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function e4(){return""}function e7(e,t,a){return(a??new ey(t)).format(e,"cccccc")}function e8(e,t=ex){return t.format(e,"yyyy")}let e6=e8;function e9(e,t,a){return(a??new ey(t)).format(e,"LLLL y")}let te=e9;function tt(e,t,a,r){let s=(r??new ey(a)).format(e,"PPPP");return t?.today&&(s=`Today, ${s}`),s}function ta(e,t,a,r){let s=(r??new ey(a)).format(e,"PPPP");return t.today&&(s=`Today, ${s}`),t.selected&&(s=`${s}, selected`),s}let tr=ta;function ts(){return""}function tn(e){return"Choose the Month"}function to(e){return"Go to the Next Month"}function ti(e){return"Go to the Previous Month"}function tl(e,t,a){return(a??new ey(t)).format(e,"cccc")}function td(e,t){return`Week ${e}`}function tc(e){return"Week Number"}function tu(e){return"Choose the Year"}let tm=e=>e instanceof HTMLElement?e:null,th=e=>[...e.querySelectorAll("[data-animated-month]")??[]],tf=e=>tm(e.querySelector("[data-animated-month]")),tp=e=>tm(e.querySelector("[data-animated-caption]")),ty=e=>tm(e.querySelector("[data-animated-weeks]")),tx=e=>tm(e.querySelector("[data-animated-nav]")),tg=e=>tm(e.querySelector("[data-animated-weekdays]"));function tv(e,t){let{month:a,defaultMonth:r,today:s=t.today(),numberOfMonths:n=1,endMonth:o,startMonth:i}=e,l=a||r||s,{differenceInCalendarMonths:d,addMonths:c,startOfMonth:u}=t;return o&&0>d(o,l)&&(l=c(o,-1*(n-1))),i&&0>d(l,i)&&(l=i),u(l)}class tb{constructor(e,t,a=ex){this.date=e,this.displayMonth=t,this.outside=!!(t&&!a.isSameMonth(e,t)),this.dateLib=a}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class tw{constructor(e,t){this.days=t,this.weekNumber=e}}class tN{constructor(e,t){this.date=e,this.weeks=t}}function tk(e,t){let[a,r]=(0,m.useState)(e);return[void 0===t?a:t,r]}function tD(e){return!e[s.disabled]&&!e[s.hidden]&&!e[s.outside]}function tj(e,t,a=ex){return eg(e,t.from,!1,a)||eg(e,t.to,!1,a)||eg(t,e.from,!1,a)||eg(t,e.to,!1,a)}function tC(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new V(t.today,t.timeZone)),t.month&&(t.month=new V(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new V(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new V(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new V(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new V(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new V(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new V(t.selected.from,t.timeZone):void 0,to:t.selected.to?new V(t.selected.to,t.timeZone):void 0}));let{components:a,formatters:u,labels:h,dateLib:f,locale:p,classNames:y}=(0,m.useMemo)(()=>{var e,a;let i={...K._,...t.locale};return{dateLib:new ey({locale:i,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...l,...e}),formatters:(a=t.formatters,a?.formatMonthCaption&&!a.formatCaption&&(a.formatCaption=a.formatMonthCaption),a?.formatYearCaption&&!a.formatYearDropdown&&(a.formatYearDropdown=a.formatYearCaption),{...d,...a}),labels:{...c,...t.labels},locale:i,classNames:{...function(){let e={};for(let t in r)e[r[t]]=`rdp-${r[t]}`;for(let t in s)e[s[t]]=`rdp-${s[t]}`;for(let t in n)e[n[t]]=`rdp-${n[t]}`;for(let t in o)e[o[t]]=`rdp-${o[t]}`;return e}(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:x,mode:g,navLayout:v,numberOfMonths:b=1,onDayBlur:w,onDayClick:N,onDayFocus:k,onDayKeyDown:D,onDayMouseEnter:j,onDayMouseLeave:C,onNextClick:M,onPrevClick:T,showWeekNumber:S,styles:E}=t,{formatCaption:O,formatDay:_,formatMonthDropdown:L,formatWeekNumber:F,formatWeekNumberHeader:W,formatWeekdayName:P,formatYearDropdown:Z}=u,A=function(e,t){let[a,r]=function(e,t){let{startMonth:a,endMonth:r}=e,{startOfYear:s,startOfDay:n,startOfMonth:o,endOfMonth:i,addYears:l,endOfYear:d,newDate:c,today:u}=t,{fromYear:m,toYear:h,fromMonth:f,toMonth:p}=e;!a&&f&&(a=f),!a&&m&&(a=t.newDate(m,0,1)),!r&&p&&(r=p),!r&&h&&(r=c(h,11,31));let y="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return a?a=o(a):m?a=c(m,0,1):!a&&y&&(a=s(l(e.today??u(),-100))),r?r=i(r):h?r=c(h,11,31):!r&&y&&(r=d(e.today??u())),[a?n(a):a,r?n(r):r]}(e,t),{startOfMonth:s,endOfMonth:n}=t,o=tv(e,t),[i,l]=tk(o,e.month?o:void 0);(0,m.useEffect)(()=>{l(tv(e,t))},[e.timeZone]);let d=function(e,t,a,r){let{numberOfMonths:s=1}=a,n=[];for(let a=0;a<s;a++){let s=r.addMonths(e,a);if(t&&s>t)break;n.push(s)}return n}(i,r,e,t),c=function(e,t,a,r){let s=e[0],n=e[e.length-1],{ISOWeek:o,fixedWeeks:i,broadcastCalendar:l}=a??{},{addDays:d,differenceInCalendarDays:c,differenceInCalendarMonths:u,endOfBroadcastWeek:m,endOfISOWeek:h,endOfMonth:f,endOfWeek:p,isAfter:y,startOfBroadcastWeek:x,startOfISOWeek:g,startOfWeek:v}=r,b=l?x(s,r):o?g(s):v(s),w=c(l?m(n):o?h(f(n)):p(f(n)),b),N=u(n,s)+1,k=[];for(let e=0;e<=w;e++){let a=d(b,e);if(t&&y(a,t))break;k.push(a)}let D=(l?35:42)*N;if(i&&k.length<D){let e=D-k.length;for(let t=0;t<e;t++){let e=d(k[k.length-1],1);k.push(e)}}return k}(d,e.endMonth?n(e.endMonth):void 0,e,t),u=function(e,t,a,r){let{addDays:s,endOfBroadcastWeek:n,endOfISOWeek:o,endOfMonth:i,endOfWeek:l,getISOWeek:d,getWeek:c,startOfBroadcastWeek:u,startOfISOWeek:m,startOfWeek:h}=r,f=e.reduce((e,f)=>{let p=a.broadcastCalendar?u(f,r):a.ISOWeek?m(f):h(f),y=a.broadcastCalendar?n(f):a.ISOWeek?o(i(f)):l(i(f)),x=t.filter(e=>e>=p&&e<=y),g=a.broadcastCalendar?35:42;if(a.fixedWeeks&&x.length<g){let e=t.filter(e=>{let t=g-x.length;return e>y&&e<=s(y,t)});x.push(...e)}let v=x.reduce((e,t)=>{let s=a.ISOWeek?d(t):c(t),n=e.find(e=>e.weekNumber===s),o=new tb(t,f,r);return n?n.days.push(o):e.push(new tw(s,[o])),e},[]),b=new tN(f,v);return e.push(b),e},[]);return a.reverseMonths?f.reverse():f}(d,c,e,t),h=u.reduce((e,t)=>[...e,...t.weeks],[]),f=function(e){let t=[];return e.reduce((e,a)=>[...e,...a.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(u),p=function(e,t,a,r){if(a.disableNavigation)return;let{pagedNavigation:s,numberOfMonths:n}=a,{startOfMonth:o,addMonths:i,differenceInCalendarMonths:l}=r,d=o(e);if(!t||!(0>=l(d,t)))return i(d,-(s?n??1:1))}(i,a,e,t),y=function(e,t,a,r){if(a.disableNavigation)return;let{pagedNavigation:s,numberOfMonths:n=1}=a,{startOfMonth:o,addMonths:i,differenceInCalendarMonths:l}=r,d=o(e);if(!t||!(l(t,e)<n))return i(d,s?n:1)}(i,r,e,t),{disableNavigation:x,onMonthChange:g}=e,v=e=>h.some(t=>t.days.some(t=>t.isEqualTo(e))),b=e=>{if(x)return;let t=s(e);a&&t<s(a)&&(t=s(a)),r&&t>s(r)&&(t=s(r)),l(t),g?.(t)};return{months:u,weeks:h,days:f,navStart:a,navEnd:r,previousMonth:p,nextMonth:y,goToMonth:b,goToDay:e=>{v(e)||b(e.date)}}}(t,f),{days:$,months:Y,navStart:I,navEnd:z,previousMonth:H,nextMonth:q,goToMonth:R}=A,U=function(e,t,a){let{disabled:r,hidden:n,modifiers:o,showOutsideDays:i,broadcastCalendar:l,today:d}=t,{isSameDay:c,isSameMonth:u,startOfMonth:m,isBefore:h,endOfMonth:f,isAfter:p}=a,y=t.startMonth&&m(t.startMonth),x=t.endMonth&&f(t.endMonth),g={[s.focused]:[],[s.outside]:[],[s.disabled]:[],[s.hidden]:[],[s.today]:[]},v={};for(let t of e){let{date:e,displayMonth:s}=t,m=!!(s&&!u(e,s)),f=!!(y&&h(e,y)),b=!!(x&&p(e,x)),w=!!(r&&ej(e,r,a)),N=!!(n&&ej(e,n,a))||f||b||!l&&!i&&m||l&&!1===i&&m,k=c(e,d??a.today());m&&g.outside.push(t),w&&g.disabled.push(t),N&&g.hidden.push(t),k&&g.today.push(t),o&&Object.keys(o).forEach(r=>{let s=o?.[r];s&&ej(e,s,a)&&(v[r]?v[r].push(t):v[r]=[t])})}return e=>{let t={[s.focused]:!1,[s.disabled]:!1,[s.hidden]:!1,[s.outside]:!1,[s.today]:!1},a={};for(let a in g){let r=g[a];t[a]=r.some(t=>t===e)}for(let t in v)a[t]=v[t].some(t=>t===e);return{...t,...a}}}($,t,f),{isSelected:B,select:G,selected:Q}=function(e,t){let a=function(e,t){let{selected:a,required:r,onSelect:s}=e,[n,o]=tk(a,s?a:void 0),i=s?a:n,{isSameDay:l}=t;return{selected:i,select:(e,t,a)=>{let n=e;return!r&&i&&i&&l(e,i)&&(n=void 0),s||o(n),s?.(n,e,t,a),n},isSelected:e=>!!i&&l(i,e)}}(e,t),r=function(e,t){let{selected:a,required:r,onSelect:s}=e,[n,o]=tk(a,s?a:void 0),i=s?a:n,{isSameDay:l}=t,d=e=>i?.some(t=>l(t,e))??!1,{min:c,max:u}=e;return{selected:i,select:(e,t,a)=>{let n=[...i??[]];if(d(e)){if(i?.length===c||r&&i?.length===1)return;n=i?.filter(t=>!l(t,e))}else n=i?.length===u?[e]:[...n,e];return s||o(n),s?.(n,e,t,a),n},isSelected:d}}(e,t),s=function(e,t){let{disabled:a,excludeDisabled:r,selected:s,required:n,onSelect:o}=e,[i,l]=tk(s,o?s:void 0),d=o?s:i;return{selected:d,select:(s,i,c)=>{let{min:u,max:m}=e,h=s?function(e,t,a=0,r=0,s=!1,n=ex){let o;let{from:i,to:l}=t||{},{isSameDay:d,isAfter:c,isBefore:u}=n;if(i||l){if(i&&!l)o=d(i,e)?s?{from:i,to:void 0}:void 0:u(e,i)?{from:e,to:i}:{from:i,to:e};else if(i&&l){if(d(i,e)&&d(l,e))o=s?{from:i,to:l}:void 0;else if(d(i,e))o={from:i,to:a>0?void 0:e};else if(d(l,e))o={from:e,to:a>0?void 0:e};else if(u(e,i))o={from:e,to:l};else if(c(e,i))o={from:i,to:e};else if(c(e,l))o={from:i,to:e};else throw Error("Invalid range")}}else o={from:e,to:a>0?void 0:e};if(o?.from&&o?.to){let t=n.differenceInCalendarDays(o.to,o.from);r>0&&t>r?o={from:e,to:void 0}:a>1&&t<a&&(o={from:e,to:void 0})}return o}(s,d,u,m,n,t):void 0;return r&&a&&h?.from&&h.to&&function(e,t,a=ex){let r=Array.isArray(t)?t:[t];if(r.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:a.isDate(t)?eg(e,t,!1,a):eD(t,a)?t.some(t=>eg(e,t,!1,a)):eb(t)?!!t.from&&!!t.to&&tj(e,{from:t.from,to:t.to},a):ek(t)?function(e,t,a=ex){let r=Array.isArray(t)?t:[t],s=e.from,n=Math.min(a.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=n;e++){if(r.includes(s.getDay()))return!0;s=a.addDays(s,1)}return!1}(e,t.dayOfWeek,a):ev(t)?a.isAfter(t.before,t.after)?tj(e,{from:a.addDays(t.after,1),to:a.addDays(t.before,-1)},a):ej(e.from,t,a)||ej(e.to,t,a):!!(ew(t)||eN(t))&&(ej(e.from,t,a)||ej(e.to,t,a))))return!0;let s=r.filter(e=>"function"==typeof e);if(s.length){let t=e.from,r=a.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(s.some(e=>e(t)))return!0;t=a.addDays(t,1)}}return!1}({from:h.from,to:h.to},a,t)&&(h.from=s,h.to=void 0),o||l(h),o?.(h,s,i,c),h},isSelected:e=>d&&eg(d,e,!1,t)}}(e,t);switch(e.mode){case"single":return a;case"multiple":return r;case"range":return s;default:return}}(t,f)??{},{blur:J,focused:X,isFocusTarget:ee,moveFocus:et,setFocused:ea}=function(e,t,a,r,n){let{autoFocus:o}=e,[l,d]=(0,m.useState)(),c=function(e,t,a,r){let n;let o=-1;for(let l of e){let e=t(l);tD(e)&&(e[s.focused]&&o<i.FocusedModifier?(n=l,o=i.FocusedModifier):r?.isEqualTo(l)&&o<i.LastFocused?(n=l,o=i.LastFocused):a(l.date)&&o<i.Selected?(n=l,o=i.Selected):e[s.today]&&o<i.Today&&(n=l,o=i.Today))}return n||(n=e.find(e=>tD(t(e)))),n}(t.days,a,r||(()=>!1),l),[u,h]=(0,m.useState)(o?c:void 0);return{isFocusTarget:e=>!!c?.isEqualTo(e),setFocused:h,focused:u,blur:()=>{d(u),h(void 0)},moveFocus:(a,r)=>{if(!u)return;let s=function e(t,a,r,s,n,o,i,l=0){if(l>365)return;let d=function(e,t,a,r,s,n,o){let{ISOWeek:i,broadcastCalendar:l}=n,{addDays:d,addMonths:c,addWeeks:u,addYears:m,endOfBroadcastWeek:h,endOfISOWeek:f,endOfWeek:p,max:y,min:x,startOfBroadcastWeek:g,startOfISOWeek:v,startOfWeek:b}=o,w=({day:d,week:u,month:c,year:m,startOfWeek:e=>l?g(e,o):i?v(e):b(e),endOfWeek:e=>l?h(e):i?f(e):p(e)})[e](a,"after"===t?1:-1);return"before"===t&&r?w=y([r,w]):"after"===t&&s&&(w=x([s,w])),w}(t,a,r.date,s,n,o,i),c=!!(o.disabled&&ej(d,o.disabled,i)),u=!!(o.hidden&&ej(d,o.hidden,i)),m=new tb(d,d,i);return c||u?e(t,a,m,s,n,o,i,l+1):m}(a,r,u,t.navStart,t.navEnd,e,n);s&&(t.goToDay(s),h(s))}}}(t,A,U,B??(()=>!1),f),{labelDayButton:er,labelGridcell:es,labelGrid:en,labelMonthDropdown:eo,labelNav:ei,labelPrevious:el,labelNext:ed,labelWeekday:ec,labelWeekNumber:eu,labelWeekNumberHeader:em,labelYearDropdown:eh}=h,ef=(0,m.useMemo)(()=>(function(e,t,a){let r=e.today(),s=t?e.startOfISOWeek(r):e.startOfWeek(r),n=[];for(let t=0;t<7;t++){let a=e.addDays(s,t);n.push(a)}return n})(f,t.ISOWeek),[f,t.ISOWeek]),ep=void 0!==g||void 0!==N,eC=(0,m.useCallback)(()=>{H&&(R(H),T?.(H))},[H,R,T]),eM=(0,m.useCallback)(()=>{q&&(R(q),M?.(q))},[R,q,M]),eT=(0,m.useCallback)((e,t)=>a=>{a.preventDefault(),a.stopPropagation(),ea(e),G?.(e.date,t,a),N?.(e.date,t,a)},[G,N,ea]),eS=(0,m.useCallback)((e,t)=>a=>{ea(e),k?.(e.date,t,a)},[k,ea]),eE=(0,m.useCallback)((e,t)=>a=>{J(),w?.(e.date,t,a)},[J,w]),eO=(0,m.useCallback)((e,a)=>r=>{let s={ArrowLeft:["day","rtl"===t.dir?"after":"before"],ArrowRight:["day","rtl"===t.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(s[r.key]){r.preventDefault(),r.stopPropagation();let[e,t]=s[r.key];et(e,t)}D?.(e.date,a,r)},[et,D,t.dir]),e_=(0,m.useCallback)((e,t)=>a=>{j?.(e.date,t,a)},[j]),eL=(0,m.useCallback)((e,t)=>a=>{C?.(e.date,t,a)},[C]),eF=(0,m.useCallback)(e=>t=>{let a=Number(t.target.value);R(f.setMonth(f.startOfMonth(e),a))},[f,R]),eW=(0,m.useCallback)(e=>t=>{let a=Number(t.target.value);R(f.setYear(f.startOfMonth(e),a))},[f,R]),{className:eP,style:eZ}=(0,m.useMemo)(()=>({className:[y[r.Root],t.className].filter(Boolean).join(" "),style:{...E?.[r.Root],...t.style}}),[y,t.className,t.style,E]),e$=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,a])=>{e.startsWith("data-")&&(t[e]=a)}),t}(t),eY=(0,m.useRef)(null);!function(e,t,{classNames:a,months:r,focused:s,dateLib:n}){let i=(0,m.useRef)(null),l=(0,m.useRef)(r),d=(0,m.useRef)(!1);(0,m.useLayoutEffect)(()=>{let c=l.current;if(l.current=r,!t||!e.current||!(e.current instanceof HTMLElement)||0===r.length||0===c.length||r.length!==c.length)return;let u=n.isSameMonth(r[0].date,c[0].date),m=n.isAfter(r[0].date,c[0].date),h=m?a[o.caption_after_enter]:a[o.caption_before_enter],f=m?a[o.weeks_after_enter]:a[o.weeks_before_enter],p=i.current,y=e.current.cloneNode(!0);if(y instanceof HTMLElement?(th(y).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=tf(e);t&&e.contains(t)&&e.removeChild(t);let a=tp(e);a&&a.classList.remove(h);let r=ty(e);r&&r.classList.remove(f)}),i.current=y):i.current=null,d.current||u||s)return;let x=p instanceof HTMLElement?th(p):[],g=th(e.current);if(g&&g.every(e=>e instanceof HTMLElement)&&x&&x.every(e=>e instanceof HTMLElement)){d.current=!0;let t=[];e.current.style.isolation="isolate";let r=tx(e.current);r&&(r.style.zIndex="1"),g.forEach((s,n)=>{let i=x[n];if(!i)return;s.style.position="relative",s.style.overflow="hidden";let l=tp(s);l&&l.classList.add(h);let c=ty(s);c&&c.classList.add(f);let u=()=>{d.current=!1,e.current&&(e.current.style.isolation=""),r&&(r.style.zIndex=""),l&&l.classList.remove(h),c&&c.classList.remove(f),s.style.position="",s.style.overflow="",s.contains(i)&&s.removeChild(i)};t.push(u),i.style.pointerEvents="none",i.style.position="absolute",i.style.overflow="hidden",i.setAttribute("aria-hidden","true");let p=tg(i);p&&(p.style.opacity="0");let y=tp(i);y&&(y.classList.add(m?a[o.caption_before_exit]:a[o.caption_after_exit]),y.addEventListener("animationend",u));let g=ty(i);g&&g.classList.add(m?a[o.weeks_before_exit]:a[o.weeks_after_exit]),s.insertBefore(i,s.firstChild)})}})}(eY,!!t.animate,{classNames:y,months:Y,focused:X,dateLib:f});let eI={dayPickerProps:t,selected:Q,select:G,isSelected:B,months:Y,nextMonth:q,previousMonth:H,goToMonth:R,getModifiers:U,components:a,classNames:y,styles:E,labels:h,formatters:u};return m.createElement(eA.Provider,{value:eI},m.createElement(a.Root,{rootRef:t.animate?eY:void 0,className:eP,style:eZ,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...e$},m.createElement(a.Months,{className:y[r.Months],style:E?.[r.Months]},!t.hideNavigation&&!v&&m.createElement(a.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[r.Nav],style:E?.[r.Nav],"aria-label":ei(),onPreviousClick:eC,onNextClick:eM,previousMonth:H,nextMonth:q}),Y.map((e,o)=>{let i=function(e,t,a,r,s){let{startOfMonth:n,startOfYear:o,endOfYear:i,eachMonthOfInterval:l,getMonth:d}=s;return l({start:o(e),end:i(e)}).map(e=>{let o=r.formatMonthDropdown(e,s);return{value:d(e),label:o,disabled:t&&e<n(t)||a&&e>n(a)||!1}})}(e.date,I,z,u,f),l=function(e,t,a,r){if(!e||!t)return;let{startOfYear:s,endOfYear:n,addYears:o,getYear:i,isBefore:l,isSameYear:d}=r,c=s(e),u=n(t),m=[],h=c;for(;l(h,u)||d(h,u);)m.push(h),h=o(h,1);return m.map(e=>{let t=a.formatYearDropdown(e,r);return{value:i(e),label:t,disabled:!1}})}(I,z,u,f);return m.createElement(a.Month,{"data-animated-month":t.animate?"true":void 0,className:y[r.Month],style:E?.[r.Month],key:o,displayIndex:o,calendarMonth:e},"around"===v&&!t.hideNavigation&&0===o&&m.createElement(a.PreviousMonthButton,{type:"button",className:y[r.PreviousMonthButton],tabIndex:H?void 0:-1,"aria-disabled":!H||void 0,"aria-label":el(H),onClick:eC,"data-animated-button":t.animate?"true":void 0},m.createElement(a.Chevron,{disabled:!H||void 0,className:y[r.Chevron],orientation:"rtl"===t.dir?"right":"left"})),m.createElement(a.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:y[r.MonthCaption],style:E?.[r.MonthCaption],calendarMonth:e,displayIndex:o},x?.startsWith("dropdown")?m.createElement(a.DropdownNav,{className:y[r.Dropdowns],style:E?.[r.Dropdowns]},"dropdown"===x||"dropdown-months"===x?m.createElement(a.MonthsDropdown,{className:y[r.MonthsDropdown],"aria-label":eo(),classNames:y,components:a,disabled:!!t.disableNavigation,onChange:eF(e.date),options:i,style:E?.[r.Dropdown],value:f.getMonth(e.date)}):m.createElement("span",null,L(e.date,f)),"dropdown"===x||"dropdown-years"===x?m.createElement(a.YearsDropdown,{className:y[r.YearsDropdown],"aria-label":eh(f.options),classNames:y,components:a,disabled:!!t.disableNavigation,onChange:eW(e.date),options:l,style:E?.[r.Dropdown],value:f.getYear(e.date)}):m.createElement("span",null,Z(e.date,f)),m.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},O(e.date,f.options,f))):m.createElement(a.CaptionLabel,{className:y[r.CaptionLabel],role:"status","aria-live":"polite"},O(e.date,f.options,f))),"around"===v&&!t.hideNavigation&&o===b-1&&m.createElement(a.NextMonthButton,{type:"button",className:y[r.NextMonthButton],tabIndex:q?void 0:-1,"aria-disabled":!q||void 0,"aria-label":ed(q),onClick:eM,"data-animated-button":t.animate?"true":void 0},m.createElement(a.Chevron,{disabled:!q||void 0,className:y[r.Chevron],orientation:"rtl"===t.dir?"left":"right"})),o===b-1&&"after"===v&&!t.hideNavigation&&m.createElement(a.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[r.Nav],style:E?.[r.Nav],"aria-label":ei(),onPreviousClick:eC,onNextClick:eM,previousMonth:H,nextMonth:q}),m.createElement(a.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===g||"range"===g,"aria-label":en(e.date,f.options,f)||void 0,className:y[r.MonthGrid],style:E?.[r.MonthGrid]},!t.hideWeekdays&&m.createElement(a.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:y[r.Weekdays],style:E?.[r.Weekdays]},S&&m.createElement(a.WeekNumberHeader,{"aria-label":em(f.options),className:y[r.WeekNumberHeader],style:E?.[r.WeekNumberHeader],scope:"col"},W()),ef.map((e,t)=>m.createElement(a.Weekday,{"aria-label":ec(e,f.options,f),className:y[r.Weekday],key:t,style:E?.[r.Weekday],scope:"col"},P(e,f.options,f)))),m.createElement(a.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:y[r.Weeks],style:E?.[r.Weeks]},e.weeks.map((e,o)=>m.createElement(a.Week,{className:y[r.Week],key:e.weekNumber,style:E?.[r.Week],week:e},S&&m.createElement(a.WeekNumber,{week:e,style:E?.[r.WeekNumber],"aria-label":eu(e.weekNumber,{locale:p}),className:y[r.WeekNumber],scope:"row",role:"rowheader"},F(e.weekNumber,f)),e.days.map(e=>{let{date:o}=e,i=U(e);if(i[s.focused]=!i.hidden&&!!X?.isEqualTo(e),i[n.selected]=B?.(o)||i.selected,eb(Q)){let{from:e,to:t}=Q;i[n.range_start]=!!(e&&t&&f.isSameDay(o,e)),i[n.range_end]=!!(e&&t&&f.isSameDay(o,t)),i[n.range_middle]=eg(Q,o,!0,f)}let l=function(e,t={},a={}){let s={...t?.[r.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{s={...s,...a?.[e]}}),s}(i,E,t.modifiersStyles),d=function(e,t,a={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[r])=>(a[r]?e.push(a[r]):t[s[r]]?e.push(t[s[r]]):t[n[r]]&&e.push(t[n[r]]),e),[t[r.Day]])}(i,y,t.modifiersClassNames),c=ep||i.hidden?void 0:es(o,i,f.options,f);return m.createElement(a.Day,{key:`${f.format(o,"yyyy-MM-dd")}_${f.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:i,className:d.join(" "),style:l,role:"gridcell","aria-selected":i.selected||void 0,"aria-label":c,"data-day":f.format(o,"yyyy-MM-dd"),"data-month":e.outside?f.format(o,"yyyy-MM"):void 0,"data-selected":i.selected||void 0,"data-disabled":i.disabled||void 0,"data-hidden":i.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":i.focused||void 0,"data-today":i.today||void 0},!i.hidden&&ep?m.createElement(a.DayButton,{className:y[r.DayButton],style:E?.[r.DayButton],type:"button",day:e,modifiers:i,disabled:i.disabled||void 0,tabIndex:ee(e)?0:-1,"aria-label":er(o,i,f.options,f),onClick:eT(e,i),onBlur:eE(e,i),onFocus:eS(e,i),onKeyDown:eO(e,i),onMouseEnter:e_(e,i),onMouseLeave:eL(e,i)},_(o,f.options,f)):!i.hidden&&_(e.date,f.options,f))}))))))})),t.footer&&m.createElement(a.Footer,{className:y[r.Footer],style:E?.[r.Footer],role:"status","aria-live":"polite"},t.footer)))}function tM({className:e,classNames:t,showOutsideDays:a=!0,...r}){let s=new Date;return s.setHours(0,0,0,0),u.jsx(tC,{showOutsideDays:a,className:(0,A.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,A.cn)((0,g.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,A.cn)((0,g.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-red-500 text-white font-bold hover:bg-red-600",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},modifiers:{past:e=>{let t=new Date(e);return t.setHours(0,0,0,0),t<s},future:e=>{let t=new Date(e);return t.setHours(0,0,0,0),t>s}},modifiersClassNames:{past:"text-black bg-gray-200 hover:bg-gray-300",future:"text-blue-600 bg-blue-50 hover:bg-blue-100"},components:{Chevron:({orientation:e,...t})=>{let a="left"===e?$.Z:Y.Z;return u.jsx(a,{className:"h-4 w-4"})}},...r})}(function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"})(i||(i={})),tM.displayName="Calendar";var tT=a(85222),tS=a(31405),tE=a(98462),tO=a(44155),t_=a(1106),tL=a(27386),tF=a(99048),tW=a(37574),tP=a(31179),tZ=a(43234),tA=a(62409),t$=a(32751),tY=a(33183),tI=a(45904),tz=a(71210),tH="Popover",[tq,tR]=(0,tE.b)(tH,[tW.D7]),tU=(0,tW.D7)(),[tB,tG]=tq(tH),tQ=e=>{let{__scopePopover:t,children:a,open:r,defaultOpen:s,onOpenChange:n,modal:o=!1}=e,i=tU(t),l=m.useRef(null),[d,c]=m.useState(!1),[h,f]=(0,tY.T)({prop:r,defaultProp:s??!1,onChange:n,caller:tH});return(0,u.jsx)(tW.fC,{...i,children:(0,u.jsx)(tB,{scope:t,contentId:(0,tF.M)(),triggerRef:l,open:h,onOpenChange:f,onOpenToggle:m.useCallback(()=>f(e=>!e),[f]),hasCustomAnchor:d,onCustomAnchorAdd:m.useCallback(()=>c(!0),[]),onCustomAnchorRemove:m.useCallback(()=>c(!1),[]),modal:o,children:a})})};tQ.displayName=tH;var tV="PopoverAnchor";m.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,s=tG(tV,a),n=tU(a),{onCustomAnchorAdd:o,onCustomAnchorRemove:i}=s;return m.useEffect(()=>(o(),()=>i()),[o,i]),(0,u.jsx)(tW.ee,{...n,...r,ref:t})}).displayName=tV;var tK="PopoverTrigger",tJ=m.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,s=tG(tK,a),n=tU(a),o=(0,tS.e)(t,s.triggerRef),i=(0,u.jsx)(tA.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":ae(s.open),...r,ref:o,onClick:(0,tT.M)(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?i:(0,u.jsx)(tW.ee,{asChild:!0,...n,children:i})});tJ.displayName=tK;var tX="PopoverPortal",[t0,t1]=tq(tX,{forceMount:void 0}),t2=e=>{let{__scopePopover:t,forceMount:a,children:r,container:s}=e,n=tG(tX,t);return(0,u.jsx)(t0,{scope:t,forceMount:a,children:(0,u.jsx)(tZ.z,{present:a||n.open,children:(0,u.jsx)(tP.h,{asChild:!0,container:s,children:r})})})};t2.displayName=tX;var t5="PopoverContent",t3=m.forwardRef((e,t)=>{let a=t1(t5,e.__scopePopover),{forceMount:r=a.forceMount,...s}=e,n=tG(t5,e.__scopePopover);return(0,u.jsx)(tZ.z,{present:r||n.open,children:n.modal?(0,u.jsx)(t7,{...s,ref:t}):(0,u.jsx)(t8,{...s,ref:t})})});t3.displayName=t5;var t4=(0,t$.Z8)("PopoverContent.RemoveScroll"),t7=m.forwardRef((e,t)=>{let a=tG(t5,e.__scopePopover),r=m.useRef(null),s=(0,tS.e)(t,r),n=m.useRef(!1);return m.useEffect(()=>{let e=r.current;if(e)return(0,tI.Ry)(e)},[]),(0,u.jsx)(tz.Z,{as:t4,allowPinchZoom:!0,children:(0,u.jsx)(t6,{...e,ref:s,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,tT.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.current||a.triggerRef.current?.focus()}),onPointerDownOutside:(0,tT.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey,r=2===t.button||a;n.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,tT.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),t8=m.forwardRef((e,t)=>{let a=tG(t5,e.__scopePopover),r=m.useRef(!1),s=m.useRef(!1);return(0,u.jsx)(t6,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(s.current=!0));let n=t.target;a.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),t6=m.forwardRef((e,t)=>{let{__scopePopover:a,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:n,disableOutsidePointerEvents:o,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:d,onInteractOutside:c,...m}=e,h=tG(t5,a),f=tU(a);return(0,t_.EW)(),(0,u.jsx)(tL.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:n,children:(0,u.jsx)(tO.XB,{asChild:!0,disableOutsidePointerEvents:o,onInteractOutside:c,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:d,onDismiss:()=>h.onOpenChange(!1),children:(0,u.jsx)(tW.VY,{"data-state":ae(h.open),role:"dialog",id:h.contentId,...f,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),t9="PopoverClose";function ae(e){return e?"open":"closed"}m.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,s=tG(t9,a);return(0,u.jsx)(tA.WV.button,{type:"button",...r,ref:t,onClick:(0,tT.M)(e.onClick,()=>s.onOpenChange(!1))})}).displayName=t9,m.forwardRef((e,t)=>{let{__scopePopover:a,...r}=e,s=tU(a);return(0,u.jsx)(tW.Eh,{...s,...r,ref:t})}).displayName="PopoverArrow";let at=m.forwardRef(({className:e,align:t="center",sideOffset:a=4,...r},s)=>u.jsx(t2,{children:u.jsx(t3,{ref:s,align:t,sideOffset:a,className:(0,A.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));function aa({date:e,onDateChange:t,placeholder:a="Pick a date",className:r}){return(0,u.jsxs)(tQ,{children:[u.jsx(tJ,{asChild:!0,children:(0,u.jsxs)(g.z,{variant:"outline",className:(0,A.cn)("w-[280px] justify-start text-left font-normal",!e&&"text-muted-foreground",r),children:[u.jsx(_.Z,{className:"mr-2 h-4 w-4"}),e?(0,Z.WU)(e,"PPP"):u.jsx("span",{children:a})]})}),u.jsx(at,{className:"w-auto p-0",children:u.jsx(tM,{mode:"single",selected:e,onSelect:t,initialFocus:!0})})]})}at.displayName=t3.displayName;var ar=a(85499),as=a(14513),an=a(62312),ao=a(73582);function ai({isOpen:e,onClose:t,selectedDate:a,onDateSelect:r,onApplyFilter:s,onResetFilter:n}){let[o,i]=m.useState(a);m.useEffect(()=>{i(a)},[a,e]);let l=e=>{try{i(e)}catch(e){console.error("Error selecting date:",e)}};return u.jsx(ao.Vq,{open:e,onOpenChange:t,children:(0,u.jsxs)(ao.cZ,{className:"sm:max-w-[480px] p-0 overflow-hidden bg-white rounded-xl shadow-2xl",children:[(0,u.jsxs)(ao.fK,{className:"px-6 py-5 bg-gradient-to-r from-blue-600 to-blue-700 text-white",children:[(0,u.jsxs)(ao.$N,{className:"flex items-center gap-3 text-xl font-bold",children:[u.jsx(_.Z,{className:"h-6 w-6"}),"Select Date Filter"]}),u.jsx(ao.Be,{className:"text-blue-100 mt-2 text-sm",children:"Choose a specific date to filter fixtures, or reset to show today's fixtures."})]}),(0,u.jsxs)("div",{className:"px-6 py-6",children:[o&&u.jsx("div",{className:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[u.jsx("div",{className:"text-sm font-medium text-blue-800 mb-1",children:"Selected Date:"}),u.jsx("div",{className:"text-xl font-bold text-blue-900",children:(0,Z.WU)(o,"EEEE, MMMM d, yyyy")}),u.jsx("div",{className:"text-xs text-blue-600 mt-1",children:o<new Date?"Past date":o.toDateString()===new Date().toDateString()?"Today":"Future date"})]}),u.jsx(g.z,{variant:"ghost",size:"sm",onClick:()=>l(void 0),className:"text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-lg",children:u.jsx(as.Z,{className:"h-5 w-5"})})]})}),u.jsx("div",{className:"flex justify-center mb-6",children:u.jsx("div",{className:"bg-white rounded-xl border border-gray-200 shadow-lg p-2",children:u.jsx(tM,{mode:"single",selected:o,onSelect:l,initialFocus:!0,className:"rounded-lg"})})}),(0,u.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 border border-gray-200",children:[(0,u.jsxs)("div",{className:"text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[u.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Quick Actions"]}),(0,u.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[u.jsx(g.z,{variant:"outline",size:"sm",onClick:()=>l(new Date),className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Today"}),u.jsx(g.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+1),l(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Tomorrow"}),u.jsx(g.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+7),l(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Next Week"})]})]})]}),(0,u.jsxs)(ao.cN,{className:"px-6 py-5 bg-gray-50 border-t border-gray-200 flex-col sm:flex-row gap-3",children:[(0,u.jsxs)("div",{className:"flex gap-3 w-full sm:w-auto",children:[(0,u.jsxs)(g.z,{variant:"outline",onClick:()=>{try{i(void 0),n(),r(void 0),t()}catch(e){console.error("Error resetting date filter:",e)}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:[u.jsx(L.Z,{className:"h-4 w-4 mr-2"}),"Reset to Today"]}),u.jsx(g.z,{variant:"outline",onClick:()=>{try{i(a),t()}catch(e){console.error("Error canceling date filter:",e),t()}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:"Cancel"})]}),(0,u.jsxs)(g.z,{onClick:()=>{try{s(o),r(o),t()}catch(e){console.error("Error applying date filter:",e)}},disabled:!o,className:"w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[u.jsx(an.Z,{className:"h-4 w-4 mr-2"}),"Apply Filter"]})]})]})})}var al=a(82912),ad=a(34755);function ac(){let[e,t]=(0,m.useState)(1),[a,r]=(0,m.useState)(25),[s,n]=(0,m.useState)(""),[o,i]=(0,m.useState)(""),[l,d]=(0,m.useState)(""),[c,Z]=(0,m.useState)(""),[A,$]=(0,m.useState)(!1),[Y,I]=(0,m.useState)(null),[z,H]=(0,m.useState)(!1),[q,R]=(0,m.useState)(null),[U,B]=(0,m.useState)(void 0),[G,Q]=(0,m.useState)(!1),{isAdmin:V,isEditor:K}=(0,W.TE)(),J=(0,f.NL)(),{data:X,isLoading:ee,error:et,refetch:ea}=(0,p.a)({queryKey:["fixtures","all",e,a,o,l,c,U],queryFn:()=>{let t={page:e,limit:a};o&&o.trim()&&(t.search=o.trim()),l&&(t.status=l),c&&(t.league=c);let r=U||new Date;return t.date=(0,al.PM)(r),k.L.getFixtures(t)},staleTime:3e4,retry:!1,onError:e=>{console.log("API is down, using mock data:",e?.message||"Unknown error")}}),er=X||{data:[{id:1,homeTeamName:"Manchester United",awayTeamName:"Liverpool",homeTeamLogo:"/images/teams/1.png",awayTeamLogo:"/images/teams/2.png",date:"2024-12-19T14:30:00Z",status:"NS",leagueName:"Premier League",venue:"Old Trafford"},{id:2,homeTeamName:"Arsenal",awayTeamName:"Chelsea",homeTeamLogo:"/images/teams/3.png",awayTeamLogo:"/images/teams/4.png",date:"2024-12-20T16:00:00Z",status:"NS",leagueName:"Premier League",venue:"Emirates Stadium"},{id:3,homeTeamName:"Barcelona",awayTeamName:"Real Madrid",homeTeamLogo:"/images/teams/5.png",awayTeamLogo:"/images/teams/6.png",date:"2024-12-21T20:00:00Z",status:"LIVE",leagueName:"La Liga",venue:"Camp Nou"},{id:4,homeTeamName:"Bayern Munich",awayTeamName:"Borussia Dortmund",homeTeamLogo:"/images/teams/7.png",awayTeamLogo:"/images/teams/8.png",date:"2024-12-18T18:30:00Z",status:"FT",leagueName:"Bundesliga",venue:"Allianz Arena"},{id:5,homeTeamName:"PSG",awayTeamName:"Marseille",homeTeamLogo:"/images/teams/9.png",awayTeamLogo:"/images/teams/10.png",date:"2024-12-22T21:00:00Z",status:"NS",leagueName:"Ligue 1",venue:"Parc des Princes"}],totalItems:5,totalPages:1,currentPage:1,limit:25},es=!X,en=h().useMemo(()=>{if(!es||!o.trim())return er;let e=er.data.filter(e=>{let t=o.toLowerCase();return e.homeTeamName?.toLowerCase().includes(t)||e.awayTeamName?.toLowerCase().includes(t)||e.leagueName?.toLowerCase().includes(t)||e.venue?.toLowerCase().includes(t)||e.status?.toLowerCase().includes(t)});return{...er,data:e,meta:{...er.meta,totalItems:e.length,totalPages:Math.ceil(e.length/a)},totalItems:e.length,totalPages:Math.ceil(e.length/a)}},[er,o,es,a]),eo=(0,y.D)({mutationFn:e=>{let t=e.externalId||e.id;return k.L.deleteFixture(t)},onSuccess:()=>{J.invalidateQueries({queryKey:["fixtures"]}),console.log("Fixture deleted successfully"),$(!1),I(null)},onError:e=>{console.error("Failed to delete fixture:",e.message)}}),ei=(0,y.D)({mutationFn:({fixtureId:e,isHot:t})=>k.L.updateFixture(e,{isHot:t}),onSuccess:(e,{isHot:t})=>{J.invalidateQueries({queryKey:["fixtures"]}),ad.toast.success(`Fixture ${t?"marked as hot":"unmarked as hot"} successfully`)},onError:e=>{ad.toast.error(`Failed to update fixture hot status: ${e.message}`),console.error("Failed to update fixture hot status:",e.message)}}),el=[{key:"date",title:"Date & Time",sortable:!0,render:e=>u.jsx("div",{className:"text-sm",children:u.jsx(ar.U,{dateTime:e,showDate:!0,showTime:!0,isClickable:!0,onClick:()=>{B(new Date(e)),Q(!0)},className:"min-w-[100px]"})})},{key:"match",title:"Match",sortable:!1,headerClassName:"text-center",render:(e,t)=>(0,u.jsxs)("div",{className:"flex items-center justify-center space-x-4 py-3",children:[(0,u.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[t.homeTeamLogo&&u.jsx("img",{src:`http://172.31.213.61/${t.homeTeamLogo}`,alt:t.homeTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),u.jsx("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:t.homeTeamName})]}),u.jsx("div",{className:"px-2",children:u.jsx("span",{className:"text-gray-500 font-bold text-sm",children:"VS"})}),(0,u.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[t.awayTeamLogo&&u.jsx("img",{src:`http://172.31.213.61/${t.awayTeamLogo}`,alt:t.awayTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),u.jsx("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:t.awayTeamName})]})]})},{key:"score",title:"Score",align:"center",render:(e,t)=>(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsxs)("div",{className:"font-bold text-lg",children:[t.goalsHome??"-"," - ",t.goalsAway??"-"]}),null!==t.scoreHalftimeHome&&null!==t.scoreHalftimeAway&&(0,u.jsxs)("div",{className:"text-xs text-gray-500",children:["HT: ",t.scoreHalftimeHome," - ",t.scoreHalftimeAway]})]})},{key:"status",title:"Status",sortable:!0,filterable:!0,render:(e,t)=>u.jsx(v.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(e),children:((e,t)=>{switch(e){case"1H":case"2H":return`${t}'`;case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(e,t.elapsed)})},{key:"isHot",title:"Hot",sortable:!1,render:(e,t)=>K()||V()?u.jsx(b.Z,{checked:e||!1,onCheckedChange:e=>{let a=t.externalId||t.id;ei.mutate({fixtureId:a,isHot:e})},label:"",disabled:ei.isLoading,size:"sm",variant:"danger"}):e?u.jsx(v.C,{variant:"destructive",className:"text-xs",children:"Hot"}):null},{key:"leagueName",title:"League",sortable:!0,filterable:!0,render:e=>u.jsx("span",{className:"text-sm text-gray-600",children:e})},{key:"actions",title:"Actions",render:(e,t)=>(0,u.jsxs)("div",{className:"flex space-x-1",children:[u.jsx(g.z,{size:"sm",variant:"outline",title:"View Details",onClick:()=>ed(t),children:u.jsx(D.Z,{className:"h-4 w-4"})}),u.jsx(g.z,{size:"sm",variant:"outline",title:"Broadcast Links",onClick:()=>em(t),children:u.jsx(j.Z,{className:"h-4 w-4"})}),K()&&u.jsx(g.z,{size:"sm",variant:"outline",title:"Edit",onClick:()=>ec(t),children:u.jsx(C.Z,{className:"h-4 w-4"})}),V()&&u.jsx(g.z,{size:"sm",variant:"outline",title:"Delete",onClick:()=>eu(t),children:u.jsx(M.Z,{className:"h-4 w-4"})})]})}],ed=e=>{let t=e.externalId||e.id;window.open(`/dashboard/fixtures/${t}`,"_blank")},ec=e=>{let t=e.externalId||e.id;window.open(`/dashboard/fixtures/${t}/edit`,"_blank")},eu=e=>{I(e),$(!0)},em=e=>{R(e),H(!0)},eh=async()=>{try{console.log("Starting fixtures sync..."),console.log("Fixtures sync completed"),ea()}catch(e){console.error("Sync failed:",e.message)}};return et?(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),u.jsx("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),u.jsx(x.Zb,{children:u.jsx(x.aY,{className:"p-6",children:(0,u.jsxs)("div",{className:"text-center",children:[u.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,u.jsxs)(g.z,{onClick:()=>ea(),children:[u.jsx(T.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),u.jsx("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsxs)(g.z,{variant:"outline",onClick:()=>ea(),disabled:ee,children:[u.jsx(T.Z,{className:`mr-2 h-4 w-4 ${ee?"animate-spin":""}`}),"Refresh"]}),V()&&(0,u.jsxs)(g.z,{variant:"outline",onClick:eh,disabled:ee,children:[u.jsx(S.Z,{className:"mr-2 h-4 w-4"}),"Sync Data"]}),(0,u.jsxs)(g.z,{variant:"outline",onClick:()=>{console.log("Export feature coming soon")},children:[u.jsx(E.Z,{className:"mr-2 h-4 w-4"}),"Export"]}),K()&&(0,u.jsxs)(g.z,{onClick:()=>window.open("/dashboard/fixtures/create","_blank"),children:[u.jsx(O.Z,{className:"mr-2 h-4 w-4"}),"Add Fixture"]})]})]}),(0,u.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[u.jsx(x.Zb,{children:(0,u.jsxs)(x.aY,{className:"p-4",children:[u.jsx("div",{className:"text-2xl font-bold text-blue-600",children:X?.meta?.totalItems?.toLocaleString()||"Loading..."}),u.jsx("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})}),u.jsx(x.Zb,{children:(0,u.jsxs)(x.aY,{className:"p-4",children:[u.jsx("div",{className:"text-2xl font-bold text-green-600",children:X?.data?.filter(e=>["1H","2H","HT"].includes(e.status)).length||0}),u.jsx("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),u.jsx(x.Zb,{children:(0,u.jsxs)(x.aY,{className:"p-4",children:[u.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:X?.data?.filter(e=>"NS"===e.status).length||0}),u.jsx("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),u.jsx(x.Zb,{children:(0,u.jsxs)(x.aY,{className:"p-4",children:[u.jsx("div",{className:"text-2xl font-bold text-gray-600",children:X?.data?.filter(e=>"FT"===e.status).length||0}),u.jsx("p",{className:"text-sm text-gray-600",children:"Completed"})]})})]}),(0,u.jsxs)(x.Zb,{children:[u.jsx(x.Ol,{children:(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,u.jsxs)("div",{children:[(0,u.jsxs)(x.ll,{className:"flex items-center gap-2",children:[u.jsx(_.Z,{className:"mr-2 h-5 w-5"}),"All Fixtures",es&&u.jsx("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal",children:"Demo Mode"})]}),u.jsx(x.SZ,{children:es?"Showing demo data - API backend is not available":U?`Showing fixtures for ${U.toLocaleDateString()}`:`Showing fixtures for today (${new Date().toLocaleDateString()})`})]}),(0,u.jsxs)("div",{className:"flex items-center gap-2",children:[u.jsx(aa,{date:U,onDateChange:B,placeholder:"Today (click to change)",className:"w-[200px]"}),U&&u.jsx(g.z,{variant:"outline",size:"sm",onClick:()=>B(void 0),className:"px-2",title:"Reset to today",children:u.jsx(L.Z,{className:"h-4 w-4"})})]})]})}),u.jsx(x.aY,{children:ee?u.jsx(F.hM,{rows:10,columns:7}):u.jsx(w.w,{data:en?.data||[],columns:el,loading:ee&&!es,searchable:!0,searchPlaceholder:"Search fixtures...",showSearchButton:!0,searchValue:s,onSearchChange:n,onSearchSubmit:()=>{i(s.trim()),t(1)},onSearchClear:()=>{n(""),i(""),t(1)},pagination:{page:e,limit:a,total:en?.meta?.totalItems||en?.totalItems||0,onPageChange:t,onLimitChange:e=>{r(e),t(1)}},emptyMessage:"No fixtures found"})})]}),u.jsx(N.sm,{isOpen:A,onClose:()=>{$(!1),I(null)},onConfirm:()=>{Y&&eo.mutate(Y)},title:"Delete Fixture",message:Y?`Are you sure you want to delete the fixture "${Y.homeTeamName} vs ${Y.awayTeamName}"? This action cannot be undone.`:"Are you sure you want to delete this fixture?",confirmText:"Delete",cancelText:"Cancel",variant:"destructive",loading:eo.isLoading}),q&&u.jsx(P.A,{isOpen:z,onClose:()=>{H(!1),R(null)},fixture:q}),u.jsx(ai,{isOpen:G,onClose:()=>Q(!1),selectedDate:U,onDateSelect:B,onApplyFilter:e=>{B(e),t(1)},onResetFilter:()=>{B(void 0),t(1)}})]})}},13611:(e,t,a)=>{"use strict";a.d(t,{r:()=>i});var r=a(95344),s=a(3729),n=a(19655),o=a(11453);let i=s.forwardRef(({className:e,...t},a)=>r.jsx(n.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:r.jsx(n.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=n.fC.displayName},67999:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var r=a(95344);a(3729);var s=a(13611),n=a(7361);function o({checked:e,onCheckedChange:t,label:a,description:o,disabled:i=!1,size:l="md",variant:d="default"}){return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(s.r,{id:a,checked:e,onCheckedChange:t,disabled:i}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx(n._,{htmlFor:a,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[l]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[d]} ${i?"opacity-50":""}`,children:a}),o&&r.jsx("span",{className:`text-xs text-gray-500 ${i?"opacity-50":""}`,children:o})]})]})}},20255:(e,t,a)=>{"use strict";a.d(t,{L:()=>s});var r=a(50053);let s={getFixtures:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/fixtures?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`Failed to fetch fixtures: ${a.statusText}`);return await a.json()},getFixtureById:async e=>{let t=await fetch(`/api/fixtures/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture: ${t.statusText}`);return await t.json()},getUpcomingAndLive:async(e={})=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,a])=>{void 0!==a&&t.append(e,a.toString())});let a=await fetch(`/api/fixtures/live?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`Failed to fetch live fixtures: ${a.statusText}`);return await a.json()},getTeamSchedule:async(e,t={})=>{let a=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{void 0!==t&&a.append(e,t.toString())}),await r.x.get(`/football/fixtures/schedules/${e}?${a.toString()}`)},getFixtureStatistics:async e=>await r.x.get(`/football/fixtures/statistics/${e}`),triggerSeasonSync:async()=>{let e=(console.warn("❌ Season sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger season sync: ${t.statusText}`)}let a=await t.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let e=(console.warn("❌ Daily sync - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||`Failed to trigger daily sync: ${t.statusText}`)}let a=await t.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let e=(console.warn("❌ Sync status - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||`Failed to get sync status: ${t.statusText}`)}let a=await t.json();return console.log("✅ Sync status successful"),a},createFixture:async e=>{let t=(console.warn("❌ Create fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!t.Authorization,data:e});let a=await fetch("/api/fixtures",{method:"POST",headers:t,body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",a.status,a.statusText,e),Error(e.message||`Failed to create fixture: ${a.statusText}`)}let r=await a.json();return console.log("✅ Create fixture successful:",r.data?.id),r.data||r},updateFixture:async(e,t)=>{let a=(console.warn("❌ Update fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!a.Authorization,data:t});let r=await fetch(`/api/fixtures/${e}`,{method:"PUT",headers:a,body:JSON.stringify(t)});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",r.status,r.statusText,e),Error(e.message||`Failed to update fixture: ${r.statusText}`)}let s=await r.json();return console.log("✅ Update fixture successful:",e),s.data||s},deleteFixture:async e=>{let t=(console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"});console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch(`/api/fixtures/${e}`,{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||`Failed to delete fixture: ${a.statusText}`)}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch(`/api/fixtures/${e}/statistics`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture statistics: ${t.statusText}`);return await t.json()},getFixtureEvents:async e=>{let t=await fetch(`/api/fixtures/${e}/events`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`Failed to fetch fixture events: ${t.statusText}`);return await t.json()},getFixture:async e=>(await s.getFixtureById(e)).data}},22613:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>s,default:()=>o});let r=(0,a(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx`),{__esModule:s,$$typeof:n}=r,o=r.default},19655:(e,t,a)=>{"use strict";a.d(t,{bU:()=>k,fC:()=>N});var r=a(3729),s=a(85222),n=a(31405),o=a(98462),i=a(33183),l=a(92062),d=a(63085),c=a(62409),u=a(95344),m="Switch",[h,f]=(0,o.b)(m),[p,y]=h(m),x=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:o,checked:l,defaultChecked:d,required:h,disabled:f,value:y="on",onCheckedChange:x,form:g,...v}=e,[N,k]=r.useState(null),D=(0,n.e)(t,e=>k(e)),j=r.useRef(!1),C=!N||g||!!N.closest("form"),[M,T]=(0,i.T)({prop:l,defaultProp:d??!1,onChange:x,caller:m});return(0,u.jsxs)(p,{scope:a,checked:M,disabled:f,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":M,"aria-required":h,"data-state":w(M),"data-disabled":f?"":void 0,disabled:f,value:y,...v,ref:D,onClick:(0,s.M)(e.onClick,e=>{T(e=>!e),C&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),C&&(0,u.jsx)(b,{control:N,bubbles:!j.current,name:o,value:y,checked:M,required:h,disabled:f,form:g,style:{transform:"translateX(-100%)"}})]})});x.displayName=m;var g="SwitchThumb",v=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=y(g,a);return(0,u.jsx)(c.WV.span,{"data-state":w(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});v.displayName=g;var b=r.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:s=!0,...o},i)=>{let c=r.useRef(null),m=(0,n.e)(c,i),h=(0,l.D)(a),f=(0,d.t)(t);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[h,a,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...o,tabIndex:-1,ref:m,style:{...o.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var N=x,k=v},89520:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var r=a(84951);function s(e,t,a){let[s,n]=(0,r.d)(a?.in,e,t);return 12*(s.getFullYear()-n.getFullYear())+(s.getMonth()-n.getMonth())}},60631:(e,t,a)=>{"use strict";a.d(t,{V:()=>s});var r=a(98037);function s(e,t){let a=(0,r.Q)(e,t?.in),s=a.getMonth();return a.setFullYear(a.getFullYear(),s+1,0),a.setHours(23,59,59,999),a}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6126,337,2609,3649,732,7966,2527,2348,6614,6317,7833,7022,4216,155],()=>a(50619));module.exports=r})();