(()=>{var e={};e.id=823,e.ids=[823],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},62940:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var r=t(50482),a=t(69108),i=t(62563),n=t.n(i),l=t(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["fixtures",{children:["sync",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67127)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx"],o="/dashboard/fixtures/sync/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/fixtures/sync/page",pathname:"/dashboard/fixtures/sync",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},18444:(e,s,t)=>{Promise.resolve().then(t.bind(t,1519))},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},66262:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},99046:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},1519:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var r=t(95344),a=t(3729),i=t(11494),n=t(19738),l=t(14373),c=t(23673),d=t(5094),x=t(19591),o=t(86688),u=t(66262);let m=(0,t(97075).Z)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var h=t(65719),p=t(33733),y=t(25545),g=t(88534),j=t(99046),f=t(46064),N=t(55794),b=t(34755),v=t(20255),S=t(36487),w=t(55085);let Z=e=>{switch(e){case"daily":return"Daily Leagues Sync";case"season":return"Fixtures Status Sync";case"manual":return"Manual Sync";default:return"Unknown Sync"}};function D(){(0,i.NL)();let[e,s]=(0,a.useState)([]),[t,D]=(0,a.useState)(!0),{data:P,isLoading:k,refetch:q}=(0,n.a)({queryKey:["fixtures-sync-status"],queryFn:()=>v.L.getSyncStatus(),refetchInterval:!!t&&5e3}),L=(0,l.D)({mutationFn:()=>v.L.triggerSeasonSync(),onSuccess:e=>{b.toast.success("Fixtures status sync completed successfully!"),O({id:Date.now().toString(),type:"season",status:"success",startTime:new Date().toISOString(),endTime:new Date().toISOString(),duration:e.details?.duration||"N/A",fixturesProcessed:e.fixturesUpserted||0,errors:[],details:e.details}),q()},onError:e=>{b.toast.error(`Fixtures sync failed: ${e.message}`),O({id:Date.now().toString(),type:"season",status:"failed",startTime:new Date().toISOString(),endTime:new Date().toISOString(),fixturesProcessed:0,errors:[e.message]})}}),F=(0,l.D)({mutationFn:()=>v.L.triggerDailySync(),onSuccess:e=>{b.toast.success("Daily leagues sync completed successfully!"),O({id:Date.now().toString(),type:"daily",status:"success",startTime:new Date().toISOString(),endTime:new Date().toISOString(),duration:e.details?.duration||"N/A",fixturesProcessed:e.fixturesUpserted||0,errors:[],details:e.details}),q()},onError:e=>{b.toast.error(`Daily sync failed: ${e.message}`),O({id:Date.now().toString(),type:"daily",status:"failed",startTime:new Date().toISOString(),endTime:new Date().toISOString(),fixturesProcessed:0,errors:[e.message]})}}),O=e=>{s(s=>[e,...s.slice(0,9)])},z=e=>new Date(e).toLocaleString(),A=e=>{switch(e){case"success":return r.jsx(u.Z,{className:"h-4 w-4 text-green-600"});case"failed":return r.jsx(m,{className:"h-4 w-4 text-red-600"});case"warning":return r.jsx(h.Z,{className:"h-4 w-4 text-yellow-600"});case"running":return r.jsx(p.Z,{className:"h-4 w-4 text-blue-600 animate-spin"});default:return r.jsx(y.Z,{className:"h-4 w-4 text-gray-600"})}},M=e=>(0,r.jsxs)(x.C,{variant:{success:"default",failed:"destructive",warning:"secondary",running:"outline"}[e]||"outline",children:[A(e),r.jsx("span",{className:"ml-1 capitalize",children:e})]}),_=F.isLoading||L.isLoading;return r.jsx(S.a1,{requiredRole:"admin",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(w.Z,{variant:"detail"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[r.jsx(p.Z,{className:"mr-2 h-6 w-6"}),"Fixtures Sync Management"]}),r.jsx("p",{className:"text-gray-600 mt-1",children:"Synchronize fixture data from API Football service"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>D(!t),children:[r.jsx(g.Z,{className:`h-4 w-4 mr-2 ${t?"text-green-600":"text-gray-400"}`}),"Auto Refresh ",t?"ON":"OFF"]}),(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>q(),disabled:k,children:[r.jsx(p.Z,{className:`h-4 w-4 mr-2 ${k?"animate-spin":""}`}),"Refresh"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[r.jsx(c.Zb,{children:r.jsx(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(y.Z,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Last Sync"}),k?r.jsx(o.Od,{className:"h-4 w-20 mt-1"}):r.jsx("p",{className:"text-sm font-bold",children:P?.lastSync?z(P.lastSync):"Never"})]})]})})}),r.jsx(c.Zb,{children:r.jsx(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(j.Z,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Fixtures"}),k?r.jsx(o.Od,{className:"h-4 w-16 mt-1"}):r.jsx("p",{className:"text-lg font-bold",children:P?.fixtures?.toLocaleString()||0})]})]})})}),r.jsx(c.Zb,{children:r.jsx(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(h.Z,{className:"h-5 w-5 text-yellow-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sync Errors"}),k?r.jsx(o.Od,{className:"h-4 w-8 mt-1"}):r.jsx("p",{className:"text-lg font-bold text-red-600",children:P?.errors?.length||0})]})]})})}),r.jsx(c.Zb,{children:r.jsx(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(f.Z,{className:"h-5 w-5 text-purple-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Status"}),r.jsx("div",{className:"flex items-center mt-1",children:_?(0,r.jsxs)(r.Fragment,{children:[r.jsx(p.Z,{className:"h-4 w-4 text-blue-600 animate-spin mr-1"}),r.jsx("span",{className:"text-sm font-bold text-blue-600",children:"Syncing..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(u.Z,{className:"h-4 w-4 text-green-600 mr-1"}),r.jsx("span",{className:"text-sm font-bold text-green-600",children:"Ready"})]})})]})]})})})]}),P?.errors&&P.errors.length>0&&(0,r.jsxs)(c.Zb,{className:"border-red-200 bg-red-50",children:[r.jsx(c.Ol,{children:(0,r.jsxs)(c.ll,{className:"text-red-800 flex items-center",children:[r.jsx(h.Z,{className:"h-5 w-5 mr-2"}),"Recent Sync Errors"]})}),r.jsx(c.aY,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[P.errors.slice(0,3).map((e,s)=>r.jsx("div",{className:"text-sm text-red-700 bg-red-100 p-2 rounded",children:"string"==typeof e?e:JSON.stringify(e)},s)),P.errors.length>3&&(0,r.jsxs)("p",{className:"text-sm text-red-600",children:["... and ",P.errors.length-3," more errors"]})]})})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{children:"Quick Sync Actions"}),r.jsx(c.SZ,{children:"Trigger manual sync operations for active leagues"})]}),(0,r.jsxs)(c.aY,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"p-4 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("h3",{className:"font-medium text-gray-900",children:"Fixtures Status Sync"}),r.jsx(j.Z,{className:"h-5 w-5 text-blue-600"})]}),r.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Updates fixture status for active leagues (scores, match status, elapsed time)"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mb-3",children:[r.jsx("strong",{children:"API:"})," ",r.jsx("code",{children:"/football/fixtures/sync/fixtures"})]}),(0,r.jsxs)(d.z,{onClick:()=>L.mutate(),disabled:_,className:"w-full",size:"sm",children:[r.jsx(j.Z,{className:"mr-2 h-4 w-4"}),L.isLoading?"Syncing Fixtures...":"Sync Fixtures Status"]})]}),(0,r.jsxs)("div",{className:"p-4 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("h3",{className:"font-medium text-gray-900",children:"Daily Leagues Sync"}),r.jsx(N.Z,{className:"h-5 w-5 text-green-600"})]}),r.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Updates leagues data for active leagues (daily incremental sync)"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mb-3",children:[r.jsx("strong",{children:"API:"})," ",r.jsx("code",{children:"/football/fixtures/sync/daily"})]}),(0,r.jsxs)(d.z,{onClick:()=>F.mutate(),disabled:_,variant:"outline",className:"w-full",size:"sm",children:[r.jsx(N.Z,{className:"mr-2 h-4 w-4"}),F.isLoading?"Syncing Leagues...":"Sync Daily Leagues"]})]})]}),r.jsx("div",{className:"flex justify-center",children:(0,r.jsxs)(d.z,{onClick:()=>q(),variant:"outline",disabled:k,className:"flex items-center",size:"sm",children:[r.jsx(p.Z,{className:`mr-2 h-4 w-4 ${k?"animate-spin":""}`}),"Refresh Status"]})}),_&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(p.Z,{className:"h-4 w-4 text-blue-600 animate-spin mr-2"}),(0,r.jsxs)("span",{className:"text-blue-800 font-medium",children:[L.isLoading?"Fixtures Status Sync":"Daily Leagues Sync"," in progress..."]})]}),r.jsx("span",{className:"text-sm text-blue-600",children:new Date().toLocaleTimeString()})]}),r.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:r.jsx("div",{className:"bg-blue-600 h-2 rounded-full animate-pulse",style:{width:"45%"}})}),r.jsx("p",{className:"text-xs text-blue-700 mt-2",children:"Please do not close this page while sync is running."})]}),(0,r.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg border",children:[r.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCB API Endpoints Information:"}),(0,r.jsxs)("div",{className:"space-y-2 text-xs text-gray-600",children:[(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"1. Fixtures Status:"})," ",r.jsx("code",{children:"/football/fixtures/sync/fixtures"}),r.jsx("br",{}),r.jsx("span",{className:"ml-4",children:"→ Updates fixture status for active leagues (scores, match status, etc.)"})]}),(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"2. Daily Leagues:"})," ",r.jsx("code",{children:"/football/fixtures/sync/daily"}),r.jsx("br",{}),r.jsx("span",{className:"ml-4",children:"→ Updates leagues data for active leagues (daily incremental)"})]}),(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"3. Sync Status:"})," ",r.jsx("code",{children:"/football/fixtures/sync/status"}),r.jsx("br",{}),r.jsx("span",{className:"ml-4",children:"→ Gets current sync status and statistics"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,r.jsxs)("p",{children:["\uD83D\uDCA1 ",r.jsx("strong",{children:"Usage Tips:"})]}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[(0,r.jsxs)("li",{children:[r.jsx("strong",{children:"Fixtures Status Sync:"})," Use when you need to update match results and status"]}),(0,r.jsxs)("li",{children:[r.jsx("strong",{children:"Daily Leagues Sync:"})," Use for regular league data updates"]}),(0,r.jsxs)("li",{children:[r.jsx("strong",{children:"Auto Refresh:"})," Page updates every 5 seconds when enabled"]}),(0,r.jsxs)("li",{children:[r.jsx("strong",{children:"Active Leagues Only:"})," Both syncs only process currently active leagues"]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{children:"Recent Sync Operations"}),r.jsx(c.SZ,{children:"History of sync operations and their results"})]}),r.jsx(c.aY,{children:0===e.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[r.jsx(j.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),r.jsx("p",{children:"No sync operations yet. Start your first sync above."})]}):r.jsx("div",{className:"space-y-3",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[A(e.status),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:Z(e.type)}),r.jsx("p",{className:"text-sm text-gray-600",children:z(e.startTime)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.fixturesProcessed," fixtures"]}),e.duration&&r.jsx("p",{className:"text-xs text-gray-600",children:e.duration})]}),M(e.status)]})]},e.id))})})]})]})})}},67127:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,6126,337,2609,3649,732,6317,7833,6253],()=>t(62940));module.exports=r})();