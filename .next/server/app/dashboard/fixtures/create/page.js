(()=>{var e={};e.id=2622,e.ids=[2622],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},31502:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>o});var s=t(50482),l=t(69108),r=t(62563),i=t.n(r),n=t(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let o=["",{children:["dashboard",{children:["fixtures",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81408)),"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/create/page.tsx"],u="/dashboard/fixtures/create/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/dashboard/fixtures/create/page",pathname:"/dashboard/fixtures/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16336:(e,a,t)=>{Promise.resolve().then(t.bind(t,6592))},6592:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>N});var s=t(95344),l=t(3729),r=t(8428),i=t(19738),n=t(14373),d=t(23673),o=t(5094),c=t(25179),u=t(90187),m=t(86688),h=t(67999),x=t(20255),p=t(59836),g=t(73286),v=t(63024),y=t(55794),b=t(31498),j=t(34755),f=t(55085);let T=[{value:"TBD",label:"Time To Be Defined"},{value:"NS",label:"Not Started"},{value:"ST",label:"Scheduled"},{value:"1H",label:"First Half"},{value:"HT",label:"Halftime"},{value:"2H",label:"Second Half"},{value:"ET",label:"Extra Time"},{value:"BT",label:"Break Time"},{value:"P",label:"Penalty In Progress"},{value:"SUSP",label:"Match Suspended"},{value:"INT",label:"Match Interrupted"},{value:"FT",label:"Match Finished (Regular Time)"},{value:"AET",label:"Match Finished (After Extra Time)"},{value:"PEN",label:"Match Finished (After Penalty)"},{value:"PST",label:"Match Postponed"},{value:"CANC",label:"Match Cancelled"},{value:"ABD",label:"Match Abandoned"},{value:"AWD",label:"Technical Loss"},{value:"WO",label:"WalkOver"},{value:"LIVE",label:"In Progress"}];function N(){let e=(0,r.useRouter)(),[a,t]=(0,l.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:"",status:"NS",goalsHome:"",goalsAway:"",elapsed:"",isHot:!1}),[N,I]=(0,l.useState)({}),[S,C]=(0,l.useState)(""),[w,D]=(0,l.useState)(""),[P,q]=(0,l.useState)(""),{data:F,isLoading:A,error:M}=(0,i.a)({queryKey:["leagues","active","search",P],queryFn:()=>p.A.getLeagues({limit:100,active:!0,search:P||void 0})}),{data:L,isLoading:E,error:O}=(0,i.a)({queryKey:["teams","search",S,w],queryFn:()=>g.k.getTeams({limit:100,search:S||w||void 0})}),H=(0,n.D)({mutationFn:e=>x.L.createFixture(e),onSuccess:()=>{j.toast.success("Fixture created successfully"),e.push("/dashboard/fixtures")},onError:e=>{j.toast.error(e.message||"Failed to create fixture")}}),k=(0,l.useCallback)(e=>{C(e)},[]),_=(0,l.useCallback)(e=>{D(e)},[]),U=(0,l.useCallback)(e=>{q(e)},[]),V=(0,l.useMemo)(()=>F?.data?.map(e=>{let a="",t=e.country;return e.season_detail?.year?(a=`Season ${e.season_detail.year}`,e.season_detail.current&&(a+=" (Current)")):e.season&&(a=`Season ${e.season}`),a&&(t=`${e.country} • ${a}`),{value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:`league-${e.id}`,subtitle:t,externalId:e.externalId,season:e.season_detail?.year||e.season}})||[],[F]),$=(0,l.useMemo)(()=>L?.data?.map(e=>({value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:`home-team-${e.id}`,externalId:e.externalId}))||[],[L]),B=(0,l.useMemo)(()=>L?.data?.map(e=>({value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:`away-team-${e.id}`,externalId:e.externalId}))||[],[L]),Z=$.find(e=>e.value===a.homeTeamId),z=B.find(e=>e.value===a.awayTeamId),G=V.find(e=>e.value===a.leagueId),R=()=>{let e={};return console.log("\uD83D\uDD0D Form validation - Current formData:",{homeTeamId:a.homeTeamId,awayTeamId:a.awayTeamId,leagueId:a.leagueId,date:a.date,time:a.time,timeLength:a.time?.length,timeType:typeof a.time}),a.homeTeamId||(e.homeTeamId="Home team is required"),a.awayTeamId||(e.awayTeamId="Away team is required"),a.leagueId||(e.leagueId="League is required"),a.date||(e.date="Date is required"),a.time&&""!==a.time.trim()||(e.time="Time is required",console.log("❌ Time validation failed:",{time:a.time,isEmpty:!a.time})),a.homeTeamId===a.awayTeamId&&(e.awayTeamId="Away team must be different from home team"),console.log("\uD83D\uDD0D Validation errors:",e),I(e),0===Object.keys(e).length},W=(e,s)=>{console.log(`📝 Updating ${e}:`,{oldValue:a[e],newValue:s}),t(a=>({...a,[e]:s})),N[e]&&I(a=>({...a,[e]:void 0}))},K=({label:e,selectedOption:a,placeholder:t="Not selected"})=>(0,s.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-2",children:e}),a?(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[a.logo&&s.jsx("img",{src:`http://172.31.213.61/${a.logo}`,alt:a.label,className:"w-8 h-8 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium text-gray-900",children:a.label}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,s.jsxs)("div",{children:["Internal ID: ",a.value]}),a.externalId&&(0,s.jsxs)("div",{className:"text-green-600",children:["\uD83D\uDD17 External ID: ",a.externalId]}),a.season&&(0,s.jsxs)("div",{className:"text-purple-600",children:["\uD83D\uDCC5 Season: ",a.season]}),a.subtitle&&(0,s.jsxs)("div",{className:"text-blue-600",children:["\uD83D\uDCCD ",a.subtitle]})]})]})]}):s.jsx("div",{className:"text-gray-500 italic",children:t})]}),Y=A||E;return M||O?(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>e.back(),children:[s.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),s.jsx(d.Zb,{children:s.jsx(d.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[!!M&&s.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load leagues"}),!!O&&s.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load teams"}),s.jsx(o.z,{onClick:()=>e.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx(f.Z,{variant:"create",isLoading:H.isLoading}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Fixture"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Add a new football fixture to the system"})]})]}),(0,s.jsxs)(d.Zb,{children:[(0,s.jsxs)(d.Ol,{children:[(0,s.jsxs)(d.ll,{className:"flex items-center",children:[s.jsx(y.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),s.jsx(d.SZ,{children:"Fill in the details for the new fixture"})]}),s.jsx(d.aY,{children:Y?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(m.Od,{className:"h-6 w-48"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(m.Od,{className:"h-20 w-full"}),s.jsx(m.Od,{className:"h-10 w-full"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(m.Od,{className:"h-20 w-full"}),s.jsx(m.Od,{className:"h-10 w-full"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx(m.Od,{className:"h-20 w-full"}),s.jsx(m.Od,{className:"h-10 w-full"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(m.Od,{className:"h-6 w-32"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(m.Od,{className:"h-10 w-full"}),s.jsx(m.Od,{className:"h-10 w-full"})]})]})]}):(0,s.jsxs)("form",{onSubmit:e=>{var t;if(e.preventDefault(),!R()){j.toast.error("Please fix the form errors");return}let[s,l,r]=a.date.split("-").map(Number),[i,n]=a.time.split(":").map(Number),d=new Date(s,l-1,r,i,n,0,0),o=new Date(Date.UTC(s,l-1,r,i,n,0,0));console.log("\uD83D\uDD50 DateTime conversion:",{inputDate:a.date,inputTime:a.time,localDateTime:d.toISOString(),utcDateTime:o.toISOString(),userTimezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezoneOffset:d.getTimezoneOffset()});let c=V.find(e=>e.value===a.leagueId),u=$.find(e=>e.value===a.homeTeamId),m=B.find(e=>e.value===a.awayTeamId);if(!c||!u||!m){j.toast.error("Please ensure all teams and league are properly selected");return}let h={leagueId:c.externalId,season:c.season||2024,homeTeamId:u.externalId,awayTeamId:m.externalId,date:o.toISOString(),round:a.round||null,venueName:a.venueName||null,venueCity:a.venueCity||null,referee:a.referee||null,isHot:a.isHot,data:{homeTeamName:u.label,awayTeamName:m.label,status:a.status,statusLong:{TBD:"Time To Be Defined",NS:"Not Started",ST:"Scheduled","1H":"First Half",HT:"Halftime","2H":"Second Half",ET:"Extra Time",BT:"Break Time",P:"Penalty In Progress",SUSP:"Match Suspended",INT:"Match Interrupted",FT:"Match Finished",AET:"Match Finished After Extra Time",PEN:"Match Finished After Penalty",PST:"Match Postponed",CANC:"Match Cancelled",ABD:"Match Abandoned",AWD:"Technical Loss",WO:"WalkOver",LIVE:"In Progress"}[t=a.status]||t,statusExtra:0,elapsed:a.elapsed?parseInt(a.elapsed):0,goalsHome:a.goalsHome?parseInt(a.goalsHome):0,goalsAway:a.goalsAway?parseInt(a.goalsAway):0}};console.log("\uD83D\uDE80 Fixture Create Payload:",JSON.stringify(h,null,2)),H.mutate(h)},className:"space-y-6",children:[(0,s.jsxs)(c.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx(K,{label:"Selected Home Team",selectedOption:Z,placeholder:"No home team selected"}),s.jsx(u.L,{label:"Home Team",placeholder:E?"Loading teams...":"Select home team",searchPlaceholder:"Search teams...",required:!0,value:a.homeTeamId,onValueChange:e=>W("homeTeamId",e),options:$,error:N.homeTeamId,disabled:E,onSearch:k,isLoading:E})]}),(0,s.jsxs)("div",{children:[s.jsx(K,{label:"Selected Away Team",selectedOption:z,placeholder:"No away team selected"}),s.jsx(u.L,{label:"Away Team",placeholder:E?"Loading teams...":"Select away team",searchPlaceholder:"Search teams...",required:!0,value:a.awayTeamId,onValueChange:e=>W("awayTeamId",e),options:B.filter(e=>e.value!==a.homeTeamId),error:N.awayTeamId,disabled:E,onSearch:_,isLoading:E})]})]}),(0,s.jsxs)("div",{children:[s.jsx(K,{label:"Selected League",selectedOption:G,placeholder:"No league selected"}),s.jsx(u.L,{label:"League",placeholder:A?"Loading leagues...":"Select league",searchPlaceholder:"Search leagues...",required:!0,value:a.leagueId,onValueChange:e=>W("leagueId",e),options:V,error:N.leagueId,disabled:A,onSearch:U,isLoading:A})]})]}),(0,s.jsxs)(c.hj,{title:"Schedule",description:"Set the date and time",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(c.UP,{label:"Date *",type:"date",required:!0,value:a.date,onChange:e=>W("date",e.target.value),error:N.date,description:"Match date"}),s.jsx(c.UP,{label:"Time *",type:"time",required:!0,value:a.time,onChange:e=>W("time",e.target.value),error:N.time,description:`Local time (${Intl.DateTimeFormat().resolvedOptions().timeZone}) - will be converted to UTC`})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:[(0,s.jsxs)("p",{className:"flex items-center mb-2",children:[s.jsx("span",{className:"text-blue-600 mr-2",children:"\uD83C\uDF0D"}),s.jsx("strong",{children:"Timezone Conversion:"})," Enter time in your local timezone (",Intl.DateTimeFormat().resolvedOptions().timeZone,")."]}),(0,s.jsxs)("p",{className:"flex items-center text-xs",children:[s.jsx("span",{className:"text-green-600 mr-2",children:"✅"}),"The system will automatically convert to UTC for API storage. The asterisk (*) indicates required fields."]})]})]}),(0,s.jsxs)(c.hj,{title:"Match Status",description:"Set initial match status and score",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(c.mg,{label:"Status",placeholder:"Select status",required:!0,value:a.status,onValueChange:e=>W("status",e),options:T,error:N.status}),s.jsx(c.UP,{label:"Home Goals",type:"number",min:"0",value:a.goalsHome,onChange:e=>W("goalsHome",e.target.value),description:"Leave empty for scheduled matches"}),s.jsx(c.UP,{label:"Away Goals",type:"number",min:"0",value:a.goalsAway,onChange:e=>W("goalsAway",e.target.value),description:"Leave empty for scheduled matches"})]}),s.jsx(c.UP,{label:"Elapsed Time (minutes)",type:"number",min:"0",max:"120",value:a.elapsed,onChange:e=>W("elapsed",e.target.value),description:"Minutes played in the match (for live/finished matches)"})]}),s.jsx(c.hj,{title:"Fixture Settings",description:"Additional fixture settings",children:s.jsx(h.Z,{checked:a.isHot,onCheckedChange:e=>t(a=>({...a,isHot:e})),label:"Hot Fixture",description:"Mark this fixture as hot/featured",variant:"danger"})}),(0,s.jsxs)(c.hj,{title:"Venue & Match Information",description:"Venue details and match context",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(c.UP,{label:"Venue Name",placeholder:"Stadium name",value:a.venueName,onChange:e=>W("venueName",e.target.value)}),s.jsx(c.UP,{label:"Venue City",placeholder:"City",value:a.venueCity,onChange:e=>W("venueCity",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(c.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:a.round,onChange:e=>W("round",e.target.value)}),s.jsx(c.UP,{label:"Referee",placeholder:"Referee name",value:a.referee||"",onChange:e=>W("referee",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(c.UP,{label:"Temperature (\xb0C)",type:"number",placeholder:"e.g., 22",value:a.temperature||"",onChange:e=>W("temperature",e.target.value)}),s.jsx(c.UP,{label:"Weather",placeholder:"e.g., Sunny, Rainy",value:a.weather||"",onChange:e=>W("weather",e.target.value)}),s.jsx(c.UP,{label:"Attendance",type:"number",placeholder:"Number of spectators",value:a.attendance||"",onChange:e=>W("attendance",e.target.value)})]})]}),(0,s.jsxs)(c.iN,{children:[s.jsx(o.z,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:H.isLoading,children:"Cancel"}),(0,s.jsxs)(o.z,{type:"submit",disabled:H.isLoading,children:[s.jsx(b.Z,{className:"mr-2 h-4 w-4"}),H.isLoading?"Creating...":"Create Fixture"]})]})]})})]})]})}},81408:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>r,__esModule:()=>l,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/create/page.tsx`),{__esModule:l,$$typeof:r}=s,i=s.default}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,6126,337,2609,3649,732,7966,6419,6317,7833,6253,6278],()=>t(31502));module.exports=s})();