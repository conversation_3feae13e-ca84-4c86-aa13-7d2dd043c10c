(()=>{var e={};e.id=9726,e.ids=[9726],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},71790:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>l});var r=s(50482),a=s(69108),i=s(62563),o=s.n(i),n=s(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let l=["",{children:["dashboard",{children:["news",{children:["categories",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,19718)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/create/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/create/page.tsx"],u="/dashboard/news/categories/create/page",p={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/news/categories/create/page",pathname:"/dashboard/news/categories/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},25258:(e,t,s)=>{Promise.resolve().then(s.bind(s,60785))},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},36341:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},60785:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(95344),a=s(3729),i=s(8428),o=s(60708),n=s(85453),c=s(3389),l=s(63024),d=s(36341),u=s(53148),p=s(31498),g=s(5094),h=s(23673),m=s(46540),x=s(7361),y=s(2690),f=s(13611),b=s(50909),v=s(60339),w=s(23549);let j=c.z.object({name:c.z.string().min(1,"Category name is required").max(100,"Name must be less than 100 characters"),slug:c.z.string().min(1,"Slug is required").max(100,"Slug must be less than 100 characters"),description:c.z.string().optional(),isActive:c.z.boolean()});function N(){let e=(0,i.useRouter)(),{toast:t}=(0,v.p)(),{mutate:s,isLoading:c}=(0,w.HL)(),N=(0,o.cI)({resolver:(0,n.F)(j),defaultValues:{name:"",slug:"",description:"",isActive:!0}}),C=N.watch("name"),S=e=>e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"");(0,a.useState)(()=>{if(C){let e=N.getValues("slug"),t=S(C);e&&e!==S(N.getValues("name"))||N.setValue("slug",t)}});let q=()=>{e.push("/dashboard/news/categories")};return(0,r.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx(g.z,{variant:"ghost",size:"sm",onClick:q,className:"h-8 w-8 p-0",children:r.jsx(l.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Create News Category"}),r.jsx("p",{className:"text-muted-foreground",children:"Add a new category for organizing news articles"})]})]})}),(0,r.jsxs)("form",{onSubmit:N.handleSubmit(r=>{s({...r,description:r.description?.trim()||void 0},{onSuccess:()=>{t({title:"Category created",description:"News category has been successfully created."}),e.push("/dashboard/news/categories")},onError:e=>{t({title:"Error",description:e?.message||"Failed to create category.",variant:"destructive"})}})}),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-5 w-5"}),"Basic Information"]}),r.jsx(h.SZ,{children:"Enter the basic details for the news category"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(x._,{htmlFor:"name",children:"Category Name *"}),r.jsx(m.I,{id:"name",placeholder:"e.g., Sports News, Technology, Politics",...N.register("name"),className:N.formState.errors.name?"border-red-500":""}),N.formState.errors.name&&r.jsx("p",{className:"text-sm text-red-500",children:N.formState.errors.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(x._,{htmlFor:"slug",children:"URL Slug *"}),r.jsx(m.I,{id:"slug",placeholder:"e.g., sports-news, technology, politics",...N.register("slug"),className:N.formState.errors.slug?"border-red-500":""}),N.formState.errors.slug&&r.jsx("p",{className:"text-sm text-red-500",children:N.formState.errors.slug.message}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"URL-friendly version of the category name. Will be auto-generated from name."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(x._,{htmlFor:"description",children:"Description"}),r.jsx(y.g,{id:"description",placeholder:"Brief description of this category...",rows:4,...N.register("description")}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Optional description to help users understand this category"})]})]})]}),(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center gap-2",children:[r.jsx(u.Z,{className:"h-5 w-5"}),"Category Settings"]}),r.jsx(h.SZ,{children:"Configure visibility and status settings"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(x._,{htmlFor:"isActive",children:"Active Status"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Enable this category for use in news articles"})]}),r.jsx(f.r,{id:"isActive",checked:N.watch("isActive"),onCheckedChange:e=>N.setValue("isActive",e)})]}),r.jsx(b.Z,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-medium",children:"Preview"}),(0,r.jsxs)("div",{className:"rounded-lg border p-3 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"font-medium",children:N.watch("name")||"Category Name"}),!N.watch("isActive")&&r.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:"Inactive"})]}),N.watch("slug")&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["URL: /news/category/",N.watch("slug")]}),N.watch("description")&&r.jsx("p",{className:"text-sm text-muted-foreground",children:N.watch("description")})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[r.jsx(g.z,{type:"button",variant:"outline",onClick:q,disabled:c,children:"Cancel"}),(0,r.jsxs)(g.z,{type:"submit",disabled:c||!N.formState.isValid,className:"flex items-center gap-2",children:[r.jsx(p.Z,{className:"h-4 w-4"}),c?"Creating...":"Create Category"]})]})]})]})}},7361:(e,t,s)=>{"use strict";s.d(t,{_:()=>l});var r=s(95344),a=s(3729),i=s(14217),o=s(49247),n=s(11453);let c=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,n.cn)(c(),e),...t}));l.displayName=i.f.displayName},50909:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(95344),a=s(3729),i=s(62409),o="horizontal",n=["horizontal","vertical"],c=a.forwardRef((e,t)=>{let{decorative:s,orientation:a=o,...c}=e,l=n.includes(a)?a:o;return(0,r.jsx)(i.WV.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var l=s(11453);let d=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},i)=>r.jsx(c,{ref:i,decorative:s,orientation:t,className:(0,l.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));d.displayName=c.displayName},13611:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var r=s(95344),a=s(3729),i=s(19655),o=s(11453);let n=a.forwardRef(({className:e,...t},s)=>r.jsx(i.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:r.jsx(i.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},2690:(e,t,s)=>{"use strict";s.d(t,{g:()=>o});var r=s(95344),a=s(3729),i=s(11453);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));o.displayName="Textarea"},60339:(e,t,s)=>{"use strict";s.d(t,{p:()=>a});var r=s(34755);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},23549:(e,t,s)=>{"use strict";s.d(t,{L_:()=>u,b5:()=>p,HL:()=>h,P9:()=>x,dk:()=>g,fu:()=>y,Ny:()=>m});var r=s(19738),a=s(11494),i=s(14373),o=s(50053);class n{async getCategories(e={}){try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=t.toString(),r=s?`${this.baseUrl}?${s}`:this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let a=await o.x.get(r);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,s=e.limit||20,r=a.length,i=(t-1)*s;return{data:a.slice(i,i+s),meta:{currentPage:t,totalPages:Math.ceil(r/s),totalItems:r,limit:s}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await o.x.get(`${this.baseUrl}/${e}`);return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return o.x.post(this.baseUrl,e)}async updateCategory(e,t){return o.x.patch(`${this.baseUrl}/${e}`,t)}async deleteCategory(e){return o.x.delete(`${this.baseUrl}/${e}`)}async toggleCategoryStatus(e,t){return o.x.patch(`${this.baseUrl}/${e}`,{isActive:t})}async reorderCategories(e){return o.x.post(`${this.baseUrl}/reorder`,{categoryIds:e})}async getCategoryStats(){return o.x.get(`${this.baseUrl}/stats`)}constructor(){this.baseUrl="/api/news/categories"}}let c=new n;var l=s(34755);let d={all:["categories"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),e],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],public:()=>[...d.all,"public"]};function u(e={}){return(0,r.a)({queryKey:d.list(e),queryFn:()=>c.getCategories(e),staleTime:3e5})}function p(e){return(0,r.a)({queryKey:d.detail(e),queryFn:()=>c.getCategoryById(e),enabled:!!e,staleTime:6e5})}function g(){return(0,r.a)({queryKey:d.public(),queryFn:()=>c.getPublicCategories(),staleTime:9e5})}function h(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),l.toast.success(`Category "${t.name}" created successfully`)},onError:e=>{l.toast.error("Failed to create category: "+e.message)}})}function m(){let e=(0,a.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>c.updateCategory(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success(`Category "${t.name}" updated successfully`)},onError:e=>{l.toast.error("Failed to update category: "+e.message)}})}function x(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.deleteCategory(e),onSuccess:(t,s)=>{e.removeQueries({queryKey:d.detail(s)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success("Category deleted successfully")},onError:e=>{l.toast.error("Failed to delete category: "+e.message)}})}function y(){let e=(0,a.NL)();return(0,i.D)({mutationFn:({id:e,isActive:t})=>c.toggleCategoryStatus(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()});let s=t.isActive?"activated":"deactivated";l.toast.success(`Category "${t.name}" ${s} successfully`)},onError:e=>{l.toast.error("Failed to toggle category status: "+e.message)}})}},19718:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});let r=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/create/page.tsx`),{__esModule:a,$$typeof:i}=r,o=r.default},19655:(e,t,s)=>{"use strict";s.d(t,{bU:()=>N,fC:()=>j});var r=s(3729),a=s(85222),i=s(31405),o=s(98462),n=s(33183),c=s(92062),l=s(63085),d=s(62409),u=s(95344),p="Switch",[g,h]=(0,o.b)(p),[m,x]=g(p),y=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:c,defaultChecked:l,required:g,disabled:h,value:x="on",onCheckedChange:y,form:f,...b}=e,[j,N]=r.useState(null),C=(0,i.e)(t,e=>N(e)),S=r.useRef(!1),q=!j||f||!!j.closest("form"),[k,P]=(0,n.T)({prop:c,defaultProp:l??!1,onChange:y,caller:p});return(0,u.jsxs)(m,{scope:s,checked:k,disabled:h,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":k,"aria-required":g,"data-state":w(k),"data-disabled":h?"":void 0,disabled:h,value:x,...b,ref:C,onClick:(0,a.M)(e.onClick,e=>{P(e=>!e),q&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),q&&(0,u.jsx)(v,{control:j,bubbles:!S.current,name:o,value:x,checked:k,required:g,disabled:h,form:f,style:{transform:"translateX(-100%)"}})]})});y.displayName=p;var f="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,a=x(f,s);return(0,u.jsx)(d.WV.span,{"data-state":w(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});b.displayName=f;var v=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:a=!0,...o},n)=>{let d=r.useRef(null),p=(0,i.e)(d,n),g=(0,c.D)(s),h=(0,l.t)(t);return r.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(g!==s&&t){let r=new Event("click",{bubbles:a});t.call(e,s),e.dispatchEvent(r)}},[g,s,a]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...o,tabIndex:-1,ref:p,style:{...o.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var j=y,N=b},92062:(e,t,s)=>{"use strict";s.d(t,{D:()=>a});var r=s(3729);function a(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,6126,337,2609,3649,732,4932,6317,7833],()=>s(71790));module.exports=r})();