(()=>{var e={};e.id=5075,e.ids=[5075],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},39036:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o});var a=s(50482),r=s(69108),i=s(62563),l=s.n(i),c=s(68300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);s.d(t,n);let o=["",{children:["dashboard",{children:["news",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75549)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/page.tsx"],u="/dashboard/news/categories/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/news/categories/page",pathname:"/dashboard/news/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},59898:(e,t,s)=>{Promise.resolve().then(s.bind(s,95248))},71532:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37121:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},76394:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},51838:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},36341:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},95248:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(95344),r=s(3729),i=s(8428),l=s(11494),c=s(23673),n=s(5094),o=s(46540),d=s(19591),u=s(77022),x=s(73875),h=s(38157),m=s(36487),p=s(23549),g=s(53148),y=s(46327),v=s(97075);let j=(0,v.Z)("toggle-right",[["circle",{cx:"15",cy:"12",r:"3",key:"1afu0r"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]),f=(0,v.Z)("toggle-left",[["circle",{cx:"9",cy:"12",r:"3",key:"u3jwor"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]);var b=s(38271),N=s(65719),w=s(68219),C=s(51838),k=s(36341),q=s(37121),P=s(28765),A=s(76394),Z=s(56506);function S(){(0,i.useRouter)(),(0,l.NL)();let{isEditor:e,isAdmin:t}=(0,m.TE)(),[s,v]=(0,r.useState)({page:1,limit:20}),[S,F]=(0,r.useState)(""),[z,M]=(0,r.useState)(!1),[$,D]=(0,r.useState)(null),{data:_,isLoading:E,error:Q,refetch:T}=(0,p.L_)(s),L=(0,p.P9)(),O=(0,p.fu)(),K=()=>{v(e=>({...e,search:S,page:1}))},U=e=>{D(e),M(!0)},B=async()=>{if($)try{await L.mutateAsync($.id),M(!1),D(null)}catch(e){}},I=async e=>{try{await O.mutateAsync({id:e.id,isActive:!e.isActive})}catch(e){}},H=e=>e?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200",V=[{key:"name",title:"Category",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-4 h-4 rounded-full flex-shrink-0",style:{backgroundColor:t.color||"#6B7280"}}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:t.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["/",t.slug]})]})]})},{key:"description",title:"Description",render:e=>a.jsx("div",{className:"max-w-xs truncate text-gray-600",children:e||"No description"})},{key:"articleCount",title:"Articles",render:(e,t)=>(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"font-medium",children:t.publishedArticleCount||0}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t.articleCount||0," total"]})]})},{key:"isActive",title:"Status",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(d.C,{variant:"outline",className:H(e),children:e?"Active":"Inactive"}),t.isPublic&&a.jsx(d.C,{variant:"outline",className:"bg-blue-100 text-blue-800 border-blue-200",children:"Public"})]})},{key:"sortOrder",title:"Order",render:e=>a.jsx("div",{className:"text-center font-mono text-sm",children:e})},{key:"actions",title:"Actions",render:(s,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(Z.default,{href:`/dashboard/news/categories/${r.id}`,children:a.jsx(n.z,{variant:"ghost",size:"sm",children:a.jsx(g.Z,{className:"h-4 w-4"})})}),(e()||t())&&a.jsx(Z.default,{href:`/dashboard/news/categories/${r.id}/edit`,children:a.jsx(n.z,{variant:"ghost",size:"sm",children:a.jsx(y.Z,{className:"h-4 w-4"})})}),(e()||t())&&a.jsx(n.z,{variant:"ghost",size:"sm",onClick:()=>I(r),disabled:O.isLoading,children:r.isActive?a.jsx(j,{className:"h-4 w-4 text-green-600"}):a.jsx(f,{className:"h-4 w-4 text-gray-400"})}),t()&&a.jsx(n.z,{variant:"ghost",size:"sm",onClick:()=>U(r),disabled:L.isLoading,className:"text-red-600 hover:text-red-700",children:a.jsx(b.Z,{className:"h-4 w-4"})})]})}];return Q?a.jsx("div",{className:"container mx-auto px-4 py-8",children:a.jsx(c.Zb,{children:(0,a.jsxs)(c.aY,{className:"flex flex-col items-center justify-center py-12",children:[a.jsx(N.Z,{className:"h-12 w-12 text-red-500 mb-4"}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Failed to load categories"}),a.jsx("p",{className:"text-gray-600 mb-4",children:Q instanceof Error?Q.message:"An unexpected error occurred"}),a.jsx(n.z,{onClick:()=>T(),children:"Try Again"})]})})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[a.jsx(w.Z,{className:"h-8 w-8"}),"News Categories"]}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage news categories and their settings"})]}),(e()||t())&&a.jsx(Z.default,{href:"/dashboard/news/categories/create",children:(0,a.jsxs)(n.z,{children:[a.jsx(C.Z,{className:"h-4 w-4 mr-2"}),"Add Category"]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"flex items-center p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(k.Z,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Categories"}),a.jsx("p",{className:"text-2xl font-bold",children:_?.meta?.totalItems||0})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"flex items-center p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(j,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Active"}),a.jsx("p",{className:"text-2xl font-bold",children:_?.data?.filter(e=>e.isActive).length||0})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"flex items-center p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(g.Z,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Public"}),a.jsx("p",{className:"text-2xl font-bold",children:_?.data?.filter(e=>e.isPublic).length||0})]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"flex items-center p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(q.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Articles"}),a.jsx("p",{className:"text-2xl font-bold",children:_?.data?.reduce((e,t)=>e+(t.articleCount||0),0)||0})]})]})})})]}),a.jsx(c.Zb,{className:"mb-6",children:a.jsx(c.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(P.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(o.I,{placeholder:"Search categories...",value:S,onChange:e=>F(e.target.value),onKeyPress:e=>{"Enter"===e.key&&K()},className:"pl-10"})]})}),(0,a.jsxs)(h.Ph,{onValueChange:e=>{let t="all"===e?void 0:"active"===e;v(e=>({...e,isActive:t,page:1}))},children:[a.jsx(h.i4,{className:"w-full md:w-48",children:a.jsx(h.ki,{placeholder:"Filter by status"})}),(0,a.jsxs)(h.Bw,{children:[a.jsx(h.Ql,{value:"all",children:"All Status"}),a.jsx(h.Ql,{value:"active",children:"Active"}),a.jsx(h.Ql,{value:"inactive",children:"Inactive"})]})]}),(0,a.jsxs)(h.Ph,{onValueChange:e=>{let t="all"===e?void 0:"public"===e;v(e=>({...e,isPublic:t,page:1}))},children:[a.jsx(h.i4,{className:"w-full md:w-48",children:a.jsx(h.ki,{placeholder:"Filter by visibility"})}),(0,a.jsxs)(h.Bw,{children:[a.jsx(h.Ql,{value:"all",children:"All Visibility"}),a.jsx(h.Ql,{value:"public",children:"Public"}),a.jsx(h.Ql,{value:"private",children:"Private"})]})]}),(0,a.jsxs)(n.z,{onClick:K,className:"w-full md:w-auto",children:[a.jsx(A.Z,{className:"h-4 w-4 mr-2"}),"Search"]})]})})}),a.jsx(c.Zb,{children:a.jsx(c.aY,{className:"p-0",children:a.jsx(u.w,{data:_?.data||[],columns:V,loading:E,pagination:{page:s.page||1,limit:s.limit||20,total:_?.meta?.totalItems||0,onPageChange:e=>v(t=>({...t,page:e})),onLimitChange:e=>v(t=>({...t,limit:e,page:1}))}})})}),a.jsx(x.sm,{isOpen:z,onClose:()=>{M(!1),D(null)},onConfirm:B,title:"Delete Category",message:$?`Are you sure you want to delete "${$.name}"? This action cannot be undone and may affect associated articles.`:"",confirmText:"Delete",cancelText:"Cancel",loading:L.isLoading,variant:"destructive"})]})}},73875:(e,t,s)=>{"use strict";s.d(t,{sm:()=>x,uB:()=>h,u_:()=>u});var a=s(95344),r=s(3729),i=s(81021),l=s(13659),c=s(14513),n=s(5094),o=s(11453);let d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:t,title:s,description:u,children:x,size:h="md",showCloseButton:m=!0,closeOnOverlayClick:p=!0,className:g})=>a.jsx(i.u,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(l.Vq,{as:"div",className:"relative z-50",onClose:p?t:()=>{},children:[a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",d[h],g),children:[(s||m)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[s&&a.jsx(l.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:s}),u&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),m&&a.jsx(n.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:a.jsx(c.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"mt-2",children:x})]})})})})]})}),x=({isOpen:e,onClose:t,onConfirm:s,title:r="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:c="Cancel",variant:o="default",loading:d=!1})=>a.jsx(u,{isOpen:e,onClose:t,title:r,size:"sm",closeOnOverlayClick:!d,children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[a.jsx(n.z,{variant:"outline",onClick:t,disabled:d,children:c}),a.jsx(n.z,{variant:"destructive"===o?"destructive":"default",onClick:s,disabled:d,children:d?"Processing...":l})]})]})}),h=({isOpen:e,onClose:t,title:s,description:r,children:i,onSubmit:l,submitText:c="Save",cancelText:o="Cancel",loading:d=!1,size:x="md"})=>a.jsx(u,{isOpen:e,onClose:t,title:s,description:r,size:x,closeOnOverlayClick:!d,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l?.()},className:"space-y-4",children:[i,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[a.jsx(n.z,{type:"button",variant:"outline",onClick:t,disabled:d,children:o}),l&&a.jsx(n.z,{type:"submit",disabled:d,children:d?"Saving...":c})]})]})})},23549:(e,t,s)=>{"use strict";s.d(t,{L_:()=>u,b5:()=>x,HL:()=>m,P9:()=>g,dk:()=>h,fu:()=>y,Ny:()=>p});var a=s(19738),r=s(11494),i=s(14373),l=s(50053);class c{async getCategories(e={}){try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=t.toString(),a=s?`${this.baseUrl}?${s}`:this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",a);let r=await l.x.get(a);if(console.log("✅ Categories fetched successfully:",r),r.data&&Array.isArray(r.data))return r;if(Array.isArray(r)){let t=e.page||1,s=e.limit||20,a=r.length,i=(t-1)*s;return{data:r.slice(i,i+s),meta:{currentPage:t,totalPages:Math.ceil(a/s),totalItems:a,limit:s}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await l.x.get(`${this.baseUrl}/${e}`);return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return l.x.post(this.baseUrl,e)}async updateCategory(e,t){return l.x.patch(`${this.baseUrl}/${e}`,t)}async deleteCategory(e){return l.x.delete(`${this.baseUrl}/${e}`)}async toggleCategoryStatus(e,t){return l.x.patch(`${this.baseUrl}/${e}`,{isActive:t})}async reorderCategories(e){return l.x.post(`${this.baseUrl}/reorder`,{categoryIds:e})}async getCategoryStats(){return l.x.get(`${this.baseUrl}/stats`)}constructor(){this.baseUrl="/api/news/categories"}}let n=new c;var o=s(34755);let d={all:["categories"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),e],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],public:()=>[...d.all,"public"]};function u(e={}){return(0,a.a)({queryKey:d.list(e),queryFn:()=>n.getCategories(e),staleTime:3e5})}function x(e){return(0,a.a)({queryKey:d.detail(e),queryFn:()=>n.getCategoryById(e),enabled:!!e,staleTime:6e5})}function h(){return(0,a.a)({queryKey:d.public(),queryFn:()=>n.getPublicCategories(),staleTime:9e5})}function m(){let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>n.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),o.toast.success(`Category "${t.name}" created successfully`)},onError:e=>{o.toast.error("Failed to create category: "+e.message)}})}function p(){let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>n.updateCategory(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),o.toast.success(`Category "${t.name}" updated successfully`)},onError:e=>{o.toast.error("Failed to update category: "+e.message)}})}function g(){let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>n.deleteCategory(e),onSuccess:(t,s)=>{e.removeQueries({queryKey:d.detail(s)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),o.toast.success("Category deleted successfully")},onError:e=>{o.toast.error("Failed to delete category: "+e.message)}})}function y(){let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isActive:t})=>n.toggleCategoryStatus(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()});let s=t.isActive?"activated":"deactivated";o.toast.success(`Category "${t.name}" ${s} successfully`)},onError:e=>{o.toast.error("Failed to toggle category status: "+e.message)}})}},75549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,7966,2527,6317,7833,7022],()=>s(39036));module.exports=a})();