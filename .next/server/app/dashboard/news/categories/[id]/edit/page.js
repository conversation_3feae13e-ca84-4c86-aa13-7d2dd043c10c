(()=>{var e={};e.id=7141,e.ids=[7141],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=s(50482),a=s(69108),i=s(62563),o=s.n(i),c=s(68300),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);s.d(t,n);let l=["",{children:["dashboard",{children:["news",{children:["categories",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79827)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx"],u="/dashboard/news/categories/[id]/edit/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/news/categories/[id]/edit/page",pathname:"/dashboard/news/categories/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},81657:(e,t,s)=>{Promise.resolve().then(s.bind(s,18200))},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},36341:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},18200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(95344),a=s(3729),i=s(8428),o=s(60708),c=s(85453),n=s(3389),l=s(63024),d=s(36341),u=s(53148),m=s(31498),p=s(5094),h=s(23673),x=s(46540),g=s(7361),y=s(2690),f=s(13611),b=s(50909),v=s(60339),j=s(23549);let N=n.z.object({name:n.z.string().min(1,"Category name is required").max(100,"Name must be less than 100 characters"),slug:n.z.string().min(1,"Slug is required").max(100,"Slug must be less than 100 characters"),description:n.z.string().optional(),icon:n.z.string().optional(),color:n.z.string().optional(),sortOrder:n.z.number().min(0,"Sort order must be 0 or greater").optional(),isActive:n.z.boolean(),isPublic:n.z.boolean(),metaTitle:n.z.string().max(200,"Meta title must be less than 200 characters").optional(),metaDescription:n.z.string().max(500,"Meta description must be less than 500 characters").optional()});function w(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{toast:s}=(0,v.p)(),n=Number(e.id),{data:w,isLoading:C,error:S}=(0,j.b5)(n),{mutate:k,isLoading:P}=(0,j.Ny)(),[q,D]=(0,a.useState)(!1),F=(0,o.cI)({resolver:(0,c.F)(N),defaultValues:{name:"",slug:"",description:"",icon:"",color:"",sortOrder:0,isActive:!0,isPublic:!0,metaTitle:"",metaDescription:""}});(0,a.useEffect)(()=>{w&&F.reset({name:w.name||"",slug:w.slug||"",description:w.description||"",icon:w.icon||"",color:w.color||"",sortOrder:w.sortOrder||0,isActive:w.isActive,isPublic:w.isPublic,metaTitle:w.metaTitle||"",metaDescription:w.metaDescription||""})},[w,F]),(0,a.useEffect)(()=>{let e=F.watch(()=>{D(!0)});return()=>e.unsubscribe()},[F]),F.watch("name");let E=()=>{(!q||window.confirm("You have unsaved changes. Are you sure you want to leave?"))&&t.push(`/dashboard/news/categories/${n}`)};return C?(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[r.jsx(p.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:r.jsx(l.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"h-8 w-48 bg-gray-200 rounded animate-pulse"}),r.jsx("div",{className:"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2"})]})]}),r.jsx("div",{className:"grid gap-6 md:grid-cols-2",children:(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[r.jsx("div",{className:"h-6 w-32 bg-gray-200 rounded animate-pulse"}),r.jsx("div",{className:"h-4 w-48 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[r.jsx("div",{className:"h-4 w-full bg-gray-200 rounded animate-pulse"}),r.jsx("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),r.jsx("div",{className:"h-4 w-1/2 bg-gray-200 rounded animate-pulse"})]})]})})]}):S||!w?(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[r.jsx(p.z,{variant:"ghost",size:"sm",onClick:E,className:"h-8 w-8 p-0",children:r.jsx(l.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Category Not Found"}),r.jsx("p",{className:"text-muted-foreground",children:"The requested category could not be found."})]})]}),r.jsx(h.Zb,{children:r.jsx(h.aY,{className:"py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(d.Z,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),r.jsx("h3",{className:"text-lg font-medium mb-2",children:"Category Not Found"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"The category you're looking for doesn't exist or may have been deleted."}),r.jsx(p.z,{onClick:()=>t.push("/dashboard/news/categories"),children:"Go Back to Categories"})]})})})]}):(0,r.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx(p.z,{variant:"ghost",size:"sm",onClick:E,className:"h-8 w-8 p-0",children:r.jsx(l.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Edit Category"}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:['Update details for "',w.name,'"']})]})]})}),(0,r.jsxs)("form",{onSubmit:F.handleSubmit(e=>{let{slug:r,...a}=e;k({id:n,data:{...a,icon:a.icon?.trim()||void 0,color:a.color?.trim()||void 0,metaTitle:a.metaTitle?.trim()||void 0,metaDescription:a.metaDescription?.trim()||void 0,description:a.description?.trim()||void 0}},{onSuccess:()=>{s({title:"Category updated",description:"News category has been successfully updated."}),D(!1),t.push(`/dashboard/news/categories/${n}`)},onError:e=>{s({title:"Error",description:e?.message||"Failed to update category.",variant:"destructive"})}})}),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-5 w-5"}),"Basic Information"]}),r.jsx(h.SZ,{children:"Update the basic details for the news category"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"name",children:"Category Name *"}),r.jsx(x.I,{id:"name",placeholder:"e.g., Sports News, Technology, Politics",...F.register("name"),className:F.formState.errors.name?"border-red-500":""}),F.formState.errors.name&&r.jsx("p",{className:"text-sm text-red-500",children:F.formState.errors.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"slug",children:"URL Slug (Read-only)"}),r.jsx(x.I,{id:"slug",placeholder:"e.g., sports-news, technology, politics",...F.register("slug"),className:"bg-gray-50 text-gray-600",readOnly:!0,disabled:!0}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"URL slug cannot be changed after creation to maintain link integrity"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"description",children:"Description"}),r.jsx(y.g,{id:"description",placeholder:"Brief description of this category...",rows:4,...F.register("description")}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Optional description to help users understand this category"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"icon",children:"Icon"}),r.jsx(x.I,{id:"icon",placeholder:"e.g., sports, news, tech",...F.register("icon")}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Icon identifier for this category"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"color",children:"Color"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[r.jsx(x.I,{id:"color",type:"color",className:"w-16 h-10 p-1 border rounded",...F.register("color")}),r.jsx(x.I,{placeholder:"#FF0000",className:"flex-1",...F.register("color")})]}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Theme color for this category"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"sortOrder",children:"Sort Order"}),r.jsx(x.I,{id:"sortOrder",type:"number",min:"0",placeholder:"0",...F.register("sortOrder",{valueAsNumber:!0}),className:F.formState.errors.sortOrder?"border-red-500":""}),F.formState.errors.sortOrder&&r.jsx("p",{className:"text-sm text-red-500",children:F.formState.errors.sortOrder.message}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Display order (lower numbers appear first)"})]})]})]}),(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center gap-2",children:[r.jsx(u.Z,{className:"h-5 w-5"}),"Category Settings"]}),r.jsx(h.SZ,{children:"Configure visibility and status settings"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(g._,{htmlFor:"isActive",children:"Active Status"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Enable this category for use in news articles"})]}),r.jsx(f.r,{id:"isActive",checked:F.watch("isActive"),onCheckedChange:e=>F.setValue("isActive",e)})]}),r.jsx(b.Z,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(g._,{htmlFor:"isPublic",children:"Public Visibility"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Make this category visible to public users"})]}),r.jsx(f.r,{id:"isPublic",checked:F.watch("isPublic"),onCheckedChange:e=>F.setValue("isPublic",e)})]}),r.jsx(b.Z,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"metaTitle",children:"Meta Title (SEO)"}),r.jsx(x.I,{id:"metaTitle",placeholder:"SEO-optimized title for search engines",...F.register("metaTitle"),className:F.formState.errors.metaTitle?"border-red-500":""}),F.formState.errors.metaTitle&&r.jsx("p",{className:"text-sm text-red-500",children:F.formState.errors.metaTitle.message}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Title tag for search engines (max 200 characters)"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(g._,{htmlFor:"metaDescription",children:"Meta Description (SEO)"}),r.jsx(y.g,{id:"metaDescription",placeholder:"Brief description for search engine results",rows:3,...F.register("metaDescription"),className:F.formState.errors.metaDescription?"border-red-500":""}),F.formState.errors.metaDescription&&r.jsx("p",{className:"text-sm text-red-500",children:F.formState.errors.metaDescription.message}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Description for search engine results (max 500 characters)"})]}),r.jsx(b.Z,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-medium",children:"Preview"}),(0,r.jsxs)("div",{className:"rounded-lg border p-3 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-4 h-4 rounded border",style:{backgroundColor:F.watch("color")||"#6B7280"}}),r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"font-medium",children:F.watch("name")||"Category Name"}),F.watch("icon")&&r.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:F.watch("icon")}),!F.watch("isActive")&&r.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:"Inactive"}),!F.watch("isPublic")&&r.jsx("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded",children:"Private"})]}),F.watch("slug")&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["URL: /news/category/",F.watch("slug")]}),F.watch("description")&&r.jsx("p",{className:"text-sm text-muted-foreground",children:F.watch("description")}),void 0!==F.watch("sortOrder")&&0!==F.watch("sortOrder")&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Sort Order: ",F.watch("sortOrder")]}),F.watch("metaTitle")&&(0,r.jsxs)("div",{className:"text-xs",children:[r.jsx("span",{className:"font-medium",children:"SEO Title:"})," ",F.watch("metaTitle")]})]})]}),q&&r.jsx("div",{className:"text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200",children:"⚠️ You have unsaved changes"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[r.jsx(p.z,{type:"button",variant:"outline",onClick:E,disabled:P,children:"Cancel"}),(0,r.jsxs)(p.z,{type:"submit",disabled:P||!F.formState.isValid,className:"flex items-center gap-2",children:[r.jsx(m.Z,{className:"h-4 w-4"}),P?"Saving...":"Save Changes"]})]})]})]})}},7361:(e,t,s)=>{"use strict";s.d(t,{_:()=>l});var r=s(95344),a=s(3729),i=s(14217),o=s(49247),c=s(11453);let n=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,c.cn)(n(),e),...t}));l.displayName=i.f.displayName},50909:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(95344),a=s(3729),i=s(62409),o="horizontal",c=["horizontal","vertical"],n=a.forwardRef((e,t)=>{let{decorative:s,orientation:a=o,...n}=e,l=c.includes(a)?a:o;return(0,r.jsx)(i.WV.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...n,ref:t})});n.displayName="Separator";var l=s(11453);let d=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},i)=>r.jsx(n,{ref:i,decorative:s,orientation:t,className:(0,l.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));d.displayName=n.displayName},13611:(e,t,s)=>{"use strict";s.d(t,{r:()=>c});var r=s(95344),a=s(3729),i=s(19655),o=s(11453);let c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:r.jsx(i.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));c.displayName=i.fC.displayName},2690:(e,t,s)=>{"use strict";s.d(t,{g:()=>o});var r=s(95344),a=s(3729),i=s(11453);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));o.displayName="Textarea"},60339:(e,t,s)=>{"use strict";s.d(t,{p:()=>a});var r=s(34755);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},23549:(e,t,s)=>{"use strict";s.d(t,{L_:()=>u,b5:()=>m,HL:()=>h,P9:()=>g,dk:()=>p,fu:()=>y,Ny:()=>x});var r=s(19738),a=s(11494),i=s(14373),o=s(50053);class c{async getCategories(e={}){try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=t.toString(),r=s?`${this.baseUrl}?${s}`:this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let a=await o.x.get(r);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,s=e.limit||20,r=a.length,i=(t-1)*s;return{data:a.slice(i,i+s),meta:{currentPage:t,totalPages:Math.ceil(r/s),totalItems:r,limit:s}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await o.x.get(`${this.baseUrl}/${e}`);return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return o.x.post(this.baseUrl,e)}async updateCategory(e,t){return o.x.patch(`${this.baseUrl}/${e}`,t)}async deleteCategory(e){return o.x.delete(`${this.baseUrl}/${e}`)}async toggleCategoryStatus(e,t){return o.x.patch(`${this.baseUrl}/${e}`,{isActive:t})}async reorderCategories(e){return o.x.post(`${this.baseUrl}/reorder`,{categoryIds:e})}async getCategoryStats(){return o.x.get(`${this.baseUrl}/stats`)}constructor(){this.baseUrl="/api/news/categories"}}let n=new c;var l=s(34755);let d={all:["categories"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),e],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],public:()=>[...d.all,"public"]};function u(e={}){return(0,r.a)({queryKey:d.list(e),queryFn:()=>n.getCategories(e),staleTime:3e5})}function m(e){return(0,r.a)({queryKey:d.detail(e),queryFn:()=>n.getCategoryById(e),enabled:!!e,staleTime:6e5})}function p(){return(0,r.a)({queryKey:d.public(),queryFn:()=>n.getPublicCategories(),staleTime:9e5})}function h(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>n.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),l.toast.success(`Category "${t.name}" created successfully`)},onError:e=>{l.toast.error("Failed to create category: "+e.message)}})}function x(){let e=(0,a.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>n.updateCategory(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success(`Category "${t.name}" updated successfully`)},onError:e=>{l.toast.error("Failed to update category: "+e.message)}})}function g(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>n.deleteCategory(e),onSuccess:(t,s)=>{e.removeQueries({queryKey:d.detail(s)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success("Category deleted successfully")},onError:e=>{l.toast.error("Failed to delete category: "+e.message)}})}function y(){let e=(0,a.NL)();return(0,i.D)({mutationFn:({id:e,isActive:t})=>n.toggleCategoryStatus(e,t),onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()});let s=t.isActive?"activated":"deactivated";l.toast.success(`Category "${t.name}" ${s} successfully`)},onError:e=>{l.toast.error("Failed to toggle category status: "+e.message)}})}},79827:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});let r=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx`),{__esModule:a,$$typeof:i}=r,o=r.default},19655:(e,t,s)=>{"use strict";s.d(t,{bU:()=>w,fC:()=>N});var r=s(3729),a=s(85222),i=s(31405),o=s(98462),c=s(33183),n=s(92062),l=s(63085),d=s(62409),u=s(95344),m="Switch",[p,h]=(0,o.b)(m),[x,g]=p(m),y=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:n,defaultChecked:l,required:p,disabled:h,value:g="on",onCheckedChange:y,form:f,...b}=e,[N,w]=r.useState(null),C=(0,i.e)(t,e=>w(e)),S=r.useRef(!1),k=!N||f||!!N.closest("form"),[P,q]=(0,c.T)({prop:n,defaultProp:l??!1,onChange:y,caller:m});return(0,u.jsxs)(x,{scope:s,checked:P,disabled:h,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":p,"data-state":j(P),"data-disabled":h?"":void 0,disabled:h,value:g,...b,ref:C,onClick:(0,a.M)(e.onClick,e=>{q(e=>!e),k&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),k&&(0,u.jsx)(v,{control:N,bubbles:!S.current,name:o,value:g,checked:P,required:p,disabled:h,form:f,style:{transform:"translateX(-100%)"}})]})});y.displayName=m;var f="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,a=g(f,s);return(0,u.jsx)(d.WV.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});b.displayName=f;var v=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:a=!0,...o},c)=>{let d=r.useRef(null),m=(0,i.e)(d,c),p=(0,n.D)(s),h=(0,l.t)(t);return r.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==s&&t){let r=new Event("click",{bubbles:a});t.call(e,s),e.dispatchEvent(r)}},[p,s,a]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...o,tabIndex:-1,ref:m,style:{...o.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var N=y,w=b},92062:(e,t,s)=>{"use strict";s.d(t,{D:()=>a});var r=s(3729);function a(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,6126,337,2609,3649,732,4932,6317,7833],()=>s(8824));module.exports=r})();