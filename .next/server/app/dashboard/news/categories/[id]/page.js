(()=>{var e={};e.id=567,e.ids=[567],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},19114:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(50482),r=t(69108),i=t(62563),l=t.n(i),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["news",{children:["categories",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97003)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/page.tsx"],u="/dashboard/news/categories/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/news/categories/[id]/page",pathname:"/dashboard/news/categories/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90632:(e,s,t)=>{Promise.resolve().then(t.bind(t,14381))},63024:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37121:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},36341:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},14381:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(95344),r=t(8428),i=t(63024),l=t(36341),n=t(53148),c=t(1222),d=t(46327),o=t(38271),u=t(55794),m=t(37121),x=t(5094),h=t(23673),p=t(19591),g=t(50909),y=t(60339),j=t(23549),v=t(73875),f=t(3729);let N=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});function b(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),{toast:t}=(0,y.p)(),b=e.id,{data:C,isLoading:q,error:k}=(0,j.b5)(Number(b)),{mutate:S,isLoading:Z}=(0,j.P9)(),[P,A]=(0,f.useState)(!1),F=()=>{s.push("/dashboard/news/categories")};return q?(0,a.jsxs)("div",{className:"container mx-auto py-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[a.jsx(x.z,{variant:"ghost",size:"sm",onClick:F,className:"h-8 w-8 p-0",children:a.jsx(i.Z,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"h-8 w-48 bg-gray-200 rounded animate-pulse"}),a.jsx("div",{className:"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2"})]})]}),a.jsx("div",{className:"grid gap-6 md:grid-cols-2",children:(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[a.jsx("div",{className:"h-6 w-32 bg-gray-200 rounded animate-pulse"}),a.jsx("div",{className:"h-4 w-48 bg-gray-200 rounded animate-pulse"})]}),(0,a.jsxs)(h.aY,{className:"space-y-4",children:[a.jsx("div",{className:"h-4 w-full bg-gray-200 rounded animate-pulse"}),a.jsx("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),a.jsx("div",{className:"h-4 w-1/2 bg-gray-200 rounded animate-pulse"})]})]})})]}):k||!C?(0,a.jsxs)("div",{className:"container mx-auto py-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[a.jsx(x.z,{variant:"ghost",size:"sm",onClick:F,className:"h-8 w-8 p-0",children:a.jsx(i.Z,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold",children:"Category Not Found"}),a.jsx("p",{className:"text-muted-foreground",children:"The requested category could not be found."})]})]}),a.jsx(h.Zb,{children:a.jsx(h.aY,{className:"py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(l.Z,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Category Not Found"}),a.jsx("p",{className:"text-muted-foreground mb-4",children:"The category you're looking for doesn't exist or may have been deleted."}),a.jsx(x.z,{onClick:F,children:"Go Back to Categories"})]})})})]}):(0,a.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(x.z,{variant:"ghost",size:"sm",onClick:F,className:"h-8 w-8 p-0",children:a.jsx(i.Z,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("h1",{className:"text-2xl font-bold",children:C.name}),a.jsx(p.C,{variant:C.isActive?"default":"secondary",children:C.isActive?(0,a.jsxs)(a.Fragment,{children:[a.jsx(n.Z,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Z,{className:"h-3 w-3 mr-1"}),"Inactive"]})})]}),a.jsx("p",{className:"text-muted-foreground",children:"Category details and management"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(x.z,{variant:"outline",onClick:()=>{s.push(`/dashboard/news/categories/${b}/edit`)},children:[a.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,a.jsxs)(x.z,{variant:"destructive",onClick:()=>A(!0),disabled:Z,children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center gap-2",children:[a.jsx(l.Z,{className:"h-5 w-5"}),"Basic Information"]}),a.jsx(h.SZ,{children:"Category details and settings"})]}),a.jsx(h.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-3",children:[(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Name"}),a.jsx("p",{className:"text-sm font-medium",children:C.name})]}),(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Slug"}),a.jsx("p",{className:"text-sm font-mono bg-gray-50 px-2 py-1 rounded",children:C.slug})]}),C.description&&(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Description"}),a.jsx("p",{className:"text-sm text-gray-700",children:C.description})]}),(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Status"}),a.jsx("div",{className:"flex items-center gap-2",children:C.isActive?(0,a.jsxs)(p.C,{variant:"default",className:"text-xs",children:[a.jsx(n.Z,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,a.jsxs)(p.C,{variant:"secondary",className:"text-xs",children:[a.jsx(c.Z,{className:"h-3 w-3 mr-1"}),"Inactive"]})})]})]})})]}),(0,a.jsxs)(h.Zb,{children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center gap-2",children:[a.jsx(u.Z,{className:"h-5 w-5"}),"Meta Information"]}),a.jsx(h.SZ,{children:"Creation and modification details"})]}),a.jsx(h.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-3",children:[(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"ID"}),(0,a.jsxs)("p",{className:"text-sm font-mono",children:["#",C.id]})]}),C.createdAt&&(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Created"}),a.jsx("p",{className:"text-sm",children:N(C.createdAt)})]}),C.updatedAt&&(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"Last Modified"}),a.jsx("p",{className:"text-sm",children:N(C.updatedAt)})]}),a.jsx(g.Z,{}),(0,a.jsxs)("div",{children:[a.jsx(w,{className:"text-sm font-medium text-gray-600",children:"URL Preview"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground font-mono bg-gray-50 px-2 py-1 rounded",children:["/news/category/",C.slug]})]})]})})]}),(0,a.jsxs)(h.Zb,{className:"md:col-span-2",children:[(0,a.jsxs)(h.Ol,{children:[(0,a.jsxs)(h.ll,{className:"flex items-center gap-2",children:[a.jsx(m.Z,{className:"h-5 w-5"}),"Usage Statistics"]}),a.jsx(h.SZ,{children:"Articles and usage information for this category"})]}),a.jsx(h.aY,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C.articleCount||0}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Articles"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-green-600",children:C.isActive?"Yes":"No"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Available for New Articles"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-purple-600",children:C.slug.length}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Slug Characters"})]})]})})]})]}),a.jsx(v.sm,{isOpen:P,onClose:()=>A(!1),onConfirm:()=>{S(Number(b),{onSuccess:()=>{t({title:"Category deleted",description:"News category has been successfully deleted."}),s.push("/dashboard/news/categories")},onError:e=>{t({title:"Error",description:e?.message||"Failed to delete category.",variant:"destructive"})}})},title:"Delete Category",message:`Are you sure you want to delete "${C.name}"? This action cannot be undone.`,confirmText:"Delete",variant:"destructive",loading:Z})]})}function w({children:e,className:s}){return a.jsx("label",{className:s,children:e})}},73875:(e,s,t)=>{"use strict";t.d(s,{sm:()=>m,uB:()=>x,u_:()=>u});var a=t(95344),r=t(3729),i=t(81021),l=t(13659),n=t(14513),c=t(5094),d=t(11453);let o={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:s,title:t,description:u,children:m,size:x="md",showCloseButton:h=!0,closeOnOverlayClick:p=!0,className:g})=>a.jsx(i.u,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(l.Vq,{as:"div",className:"relative z-50",onClose:p?s:()=>{},children:[a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.Vq.Panel,{className:(0,d.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",o[x],g),children:[(t||h)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[t&&a.jsx(l.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:t}),u&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),h&&a.jsx(c.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:a.jsx(n.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"mt-2",children:m})]})})})})]})}),m=({isOpen:e,onClose:s,onConfirm:t,title:r="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:n="Cancel",variant:d="default",loading:o=!1})=>a.jsx(u,{isOpen:e,onClose:s,title:r,size:"sm",closeOnOverlayClick:!o,children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[a.jsx(c.z,{variant:"outline",onClick:s,disabled:o,children:n}),a.jsx(c.z,{variant:"destructive"===d?"destructive":"default",onClick:t,disabled:o,children:o?"Processing...":l})]})]})}),x=({isOpen:e,onClose:s,title:t,description:r,children:i,onSubmit:l,submitText:n="Save",cancelText:d="Cancel",loading:o=!1,size:m="md"})=>a.jsx(u,{isOpen:e,onClose:s,title:t,description:r,size:m,closeOnOverlayClick:!o,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l?.()},className:"space-y-4",children:[i,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[a.jsx(c.z,{type:"button",variant:"outline",onClick:s,disabled:o,children:d}),l&&a.jsx(c.z,{type:"submit",disabled:o,children:o?"Saving...":n})]})]})})},50909:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(95344),r=t(3729),i=t(62409),l="horizontal",n=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=l,...c}=e,d=n.includes(r)?r:l;return(0,a.jsx)(i.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=t(11453);let o=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>a.jsx(c,{ref:i,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));o.displayName=c.displayName},60339:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(34755);let r=()=>({toast:e=>{"destructive"===e.variant?a.toast.error(e.title||e.description||"Error occurred"):a.toast.success(e.title||e.description||"Success")}})},23549:(e,s,t)=>{"use strict";t.d(s,{L_:()=>u,b5:()=>m,HL:()=>h,P9:()=>g,dk:()=>x,fu:()=>y,Ny:()=>p});var a=t(19738),r=t(11494),i=t(14373),l=t(50053);class n{async getCategories(e={}){try{let s=new URLSearchParams;e.page&&s.append("page",e.page.toString()),e.limit&&s.append("limit",e.limit.toString()),e.search&&s.append("search",e.search),void 0!==e.isActive&&s.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&s.append("isPublic",e.isPublic.toString()),e.sortBy&&s.append("sortBy",e.sortBy),e.sortOrder&&s.append("sortOrder",e.sortOrder);let t=s.toString(),a=t?`${this.baseUrl}?${t}`:this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",a);let r=await l.x.get(a);if(console.log("✅ Categories fetched successfully:",r),r.data&&Array.isArray(r.data))return r;if(Array.isArray(r)){let s=e.page||1,t=e.limit||20,a=r.length,i=(s-1)*t;return{data:r.slice(i,i+t),meta:{currentPage:s,totalPages:Math.ceil(a/t),totalItems:a,limit:t}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let s=await l.x.get(`${this.baseUrl}/${e}`);return console.log("✅ Category fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return l.x.post(this.baseUrl,e)}async updateCategory(e,s){return l.x.patch(`${this.baseUrl}/${e}`,s)}async deleteCategory(e){return l.x.delete(`${this.baseUrl}/${e}`)}async toggleCategoryStatus(e,s){return l.x.patch(`${this.baseUrl}/${e}`,{isActive:s})}async reorderCategories(e){return l.x.post(`${this.baseUrl}/reorder`,{categoryIds:e})}async getCategoryStats(){return l.x.get(`${this.baseUrl}/stats`)}constructor(){this.baseUrl="/api/news/categories"}}let c=new n;var d=t(34755);let o={all:["categories"],lists:()=>[...o.all,"list"],list:e=>[...o.lists(),e],details:()=>[...o.all,"detail"],detail:e=>[...o.details(),e],public:()=>[...o.all,"public"]};function u(e={}){return(0,a.a)({queryKey:o.list(e),queryFn:()=>c.getCategories(e),staleTime:3e5})}function m(e){return(0,a.a)({queryKey:o.detail(e),queryFn:()=>c.getCategoryById(e),enabled:!!e,staleTime:6e5})}function x(){return(0,a.a)({queryKey:o.public(),queryFn:()=>c.getPublicCategories(),staleTime:9e5})}function h(){let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>c.createCategory(e),onSuccess:s=>{e.invalidateQueries({queryKey:o.all}),d.toast.success(`Category "${s.name}" created successfully`)},onError:e=>{d.toast.error("Failed to create category: "+e.message)}})}function p(){let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,data:s})=>c.updateCategory(e,s),onSuccess:s=>{e.setQueryData(o.detail(s.id),s),e.invalidateQueries({queryKey:o.lists()}),e.invalidateQueries({queryKey:o.public()}),d.toast.success(`Category "${s.name}" updated successfully`)},onError:e=>{d.toast.error("Failed to update category: "+e.message)}})}function g(){let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>c.deleteCategory(e),onSuccess:(s,t)=>{e.removeQueries({queryKey:o.detail(t)}),e.invalidateQueries({queryKey:o.lists()}),e.invalidateQueries({queryKey:o.public()}),d.toast.success("Category deleted successfully")},onError:e=>{d.toast.error("Failed to delete category: "+e.message)}})}function y(){let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isActive:s})=>c.toggleCategoryStatus(e,s),onSuccess:s=>{e.setQueryData(o.detail(s.id),s),e.invalidateQueries({queryKey:o.lists()}),e.invalidateQueries({queryKey:o.public()});let t=s.isActive?"activated":"deactivated";d.toast.success(`Category "${s.name}" ${t} successfully`)},onError:e=>{d.toast.error("Failed to toggle category status: "+e.message)}})}},97003:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,6126,337,2609,3649,732,2527,6317,7833],()=>t(19114));module.exports=a})();