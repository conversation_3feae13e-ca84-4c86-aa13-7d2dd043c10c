(()=>{var e={};e.id=1337,e.ids=[1337],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},84332:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=s(50482),r=s(69108),i=s(62563),l=s.n(i),n=s(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c=["",{children:["dashboard",{children:["news",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79172)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx"],u="/dashboard/news/[id]/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/news/[id]/page",pathname:"/dashboard/news/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2693:(e,t,s)=>{Promise.resolve().then(s.bind(s,92739))},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},30304:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},20016:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},36341:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},46064:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},92739:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(95344),r=s(3729),i=s(8428),l=s(23673),n=s(5094),d=s(19591),c=s(73875),o=s(67999),u=s(36487),m=s(52721),x=s(86688),h=s(63024),p=s(65719),y=s(46327),g=s(38271),f=s(46064),j=s(55794),v=s(18822);let w=(0,s(97075).Z)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var b=s(30304),N=s(53148),k=s(20016),F=s(36341),C=s(34755);function I(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{isEditor:s,isAdmin:I}=(0,u.TE)(),P=parseInt(e.id),[A,Z]=(0,r.useState)(!1),{data:q,isLoading:S,error:T}=(0,m.ZZ)(P),D=(0,m.po)(),E=(0,m.CC)(),$=(0,m.FF)(),O=async()=>{try{await D.mutateAsync(P),Z(!1),t.push("/dashboard/news")}catch(e){}};return S?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(x.Od,{className:"h-10 w-20"}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(x.Od,{className:"h-8 w-64"}),a.jsx(x.Od,{className:"h-4 w-48"})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[a.jsx(x.Od,{className:"h-6 w-48"}),a.jsx(x.Od,{className:"h-4 w-64"})]}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[a.jsx(x.Od,{className:"h-40 w-full"}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(x.Od,{className:"h-4 w-full"}),a.jsx(x.Od,{className:"h-4 w-3/4"}),a.jsx(x.Od,{className:"h-4 w-1/2"})]})]})]})]}):T||!q?(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>t.back(),children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(p.Z,{className:"mx-auto h-12 w-12 text-red-500 mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-red-600 mb-2",children:T?"Failed to load news":"News not found"}),"              ",a.jsx("p",{className:"text-gray-600 mb-4",children:T?.message||"The news article you are looking for does not exist or has been removed."}),a.jsx(n.z,{onClick:()=>t.push("/dashboard/news"),children:"Return to News"})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>t.back(),children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:q.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:"News Article Details"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[s()&&(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>{if(!s()&&!I()){C.toast.error("You do not have permission to edit news");return}t.push(`/dashboard/news/${P}/edit`)},children:[a.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),I()&&(0,a.jsxs)(n.z,{variant:"outline",onClick:()=>{if(!I()){C.toast.error("You do not have permission to delete news");return}Z(!0)},className:"text-red-600 hover:text-red-700",children:[a.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2 space-y-6",children:(0,a.jsxs)(l.Zb,{children:[(0,a.jsxs)(l.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(d.C,{variant:"published"===q.status?"default":"secondary",children:"published"===q.status?"Published":"draft"===q.status?"Draft":"Archived"}),q.isFeatured&&(0,a.jsxs)(d.C,{variant:"destructive",children:[a.jsx(f.Z,{className:"w-3 h-3 mr-1"}),"Featured"]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[a.jsx(j.Z,{className:"inline w-4 h-4 mr-1"}),new Date(q.publishedAt||q.createdAt).toLocaleDateString()]})]}),a.jsx(l.ll,{className:"text-2xl",children:q.title}),q.excerpt&&a.jsx(l.SZ,{className:"text-lg",children:q.excerpt})]}),(0,a.jsxs)(l.aY,{children:[q.featuredImage&&a.jsx("div",{className:"mb-6",children:a.jsx("img",{src:q.featuredImage,alt:q.title,className:"w-full h-64 object-cover rounded-lg",onError:e=>{e.currentTarget.style.display="none"}})}),a.jsx("div",{className:"prose max-w-none",children:a.jsx("div",{dangerouslySetInnerHTML:{__html:q.content}})})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:a.jsx(l.ll,{className:"text-lg",children:"Article Information"})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(v.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Author"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Author ",q.authorId]})]})]}),q.category&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(w,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Category"}),a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(d.C,{variant:"outline",style:{borderColor:q.category.color||"#6b7280",color:q.category.color||"#6b7280"},children:q.category.name})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(j.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Publish Date"}),a.jsx("p",{className:"text-sm text-gray-600",children:q.publishedAt?new Date(q.publishedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not published yet"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(b.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Created"}),a.jsx("p",{className:"text-sm text-gray-600",children:new Date(q.createdAt).toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(N.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Last Updated"}),a.jsx("p",{className:"text-sm text-gray-600",children:new Date(q.updatedAt).toLocaleDateString()})]})]}),q.featuredImage&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(k.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium",children:"Featured Image"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Available"})]})]})]})]}),q.tags&&q.tags.length>0&&(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"text-lg flex items-center",children:[a.jsx(F.Z,{className:"mr-2 h-5 w-5"}),"Tags"]})}),a.jsx(l.aY,{children:a.jsx("div",{className:"flex flex-wrap gap-2",children:q.tags.map((e,t)=>a.jsx(d.C,{variant:"outline",children:e},t))})})]}),(s()||I())&&(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:a.jsx(l.ll,{className:"text-lg",children:"Quick Actions"})}),a.jsx(l.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium",children:"Published Status"}),"                    ",a.jsx(o.Z,{checked:"published"===q.status,onCheckedChange:e=>{E.mutate({id:P,isPublished:e})},disabled:E.isLoading,size:"sm",label:"Published Status"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm font-medium",children:"Featured Article"}),"                    ",a.jsx(o.Z,{checked:q.isFeatured,onCheckedChange:e=>{$.mutate({id:P,isHot:e})},disabled:$.isLoading,size:"sm",variant:"danger",label:"Featured Article"})]})]})})]})]})]}),"      ",a.jsx(c.sm,{isOpen:A,onClose:()=>Z(!1),title:"Delete News Article",message:`Are you sure you want to delete "${q.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",onConfirm:O,loading:D.isLoading,variant:"destructive"})]})}},7361:(e,t,s)=>{"use strict";s.d(t,{_:()=>c});var a=s(95344),r=s(3729),i=s(14217),l=s(49247),n=s(11453);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},s)=>a.jsx(i.f,{ref:s,className:(0,n.cn)(d(),e),...t}));c.displayName=i.f.displayName},73875:(e,t,s)=>{"use strict";s.d(t,{sm:()=>m,uB:()=>x,u_:()=>u});var a=s(95344),r=s(3729),i=s(81021),l=s(13659),n=s(14513),d=s(5094),c=s(11453);let o={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:t,title:s,description:u,children:m,size:x="md",showCloseButton:h=!0,closeOnOverlayClick:p=!0,className:y})=>a.jsx(i.u,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(l.Vq,{as:"div",className:"relative z-50",onClose:p?t:()=>{},children:[a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.Vq.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",o[x],y),children:[(s||h)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[s&&a.jsx(l.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:s}),u&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),h&&a.jsx(d.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:a.jsx(n.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"mt-2",children:m})]})})})})]})}),m=({isOpen:e,onClose:t,onConfirm:s,title:r="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:n="Cancel",variant:c="default",loading:o=!1})=>a.jsx(u,{isOpen:e,onClose:t,title:r,size:"sm",closeOnOverlayClick:!o,children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[a.jsx(d.z,{variant:"outline",onClick:t,disabled:o,children:n}),a.jsx(d.z,{variant:"destructive"===c?"destructive":"default",onClick:s,disabled:o,children:o?"Processing...":l})]})]})}),x=({isOpen:e,onClose:t,title:s,description:r,children:i,onSubmit:l,submitText:n="Save",cancelText:c="Cancel",loading:o=!1,size:m="md"})=>a.jsx(u,{isOpen:e,onClose:t,title:s,description:r,size:m,closeOnOverlayClick:!o,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l?.()},className:"space-y-4",children:[i,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[a.jsx(d.z,{type:"button",variant:"outline",onClick:t,disabled:o,children:c}),l&&a.jsx(d.z,{type:"submit",disabled:o,children:o?"Saving...":n})]})]})})},13611:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var a=s(95344),r=s(3729),i=s(19655),l=s(11453);let n=r.forwardRef(({className:e,...t},s)=>a.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:a.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},67999:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(95344);s(3729);var r=s(13611),i=s(7361);function l({checked:e,onCheckedChange:t,label:s,description:l,disabled:n=!1,size:d="md",variant:c="default"}){return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(r.r,{id:s,checked:e,onCheckedChange:t,disabled:n}),(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx(i._,{htmlFor:s,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[d]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[c]} ${n?"opacity-50":""}`,children:s}),l&&a.jsx("span",{className:`text-xs text-gray-500 ${n?"opacity-50":""}`,children:l})]})]})}},52721:(e,t,s)=>{"use strict";s.d(t,{dR:()=>m,po:()=>h,Kg:()=>o,ZZ:()=>u,FF:()=>y,CC:()=>p,rj:()=>x});var a=s(19738),r=s(11494),i=s(14373);function l(e){return{...e,author:`Author ${e.authorId}`,summary:e.excerpt,imageUrl:e.featuredImage,isPublished:"published"===e.status,isHot:e.isFeatured,publishDate:e.publishedAt||e.createdAt}}let n=()=>null,d={getNews:async(e={})=>{let t=function(e){let t={...e};return void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft",delete t.isPublished),void 0!==e.isHot&&(t.isFeatured=e.isHot,delete t.isHot),t}(e),s=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&(Array.isArray(t)?t.forEach(t=>s.append(e,t.toString())):s.append(e,t.toString()))});let a=n(),r={"Content-Type":"application/json"};a&&(r.Authorization=`Bearer ${a}`);let i=await fetch(`/api/news?${s.toString()}`,{method:"GET",headers:r});if(!i.ok)throw Error((await i.json()).message||"Failed to fetch news");let d=await i.json();return d.data&&(d.data=d.data.map(l)),d},getNewsById:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=await fetch(`/api/news/${e}`,{method:"GET",headers:s});if(!a.ok)throw Error((await a.json()).message||`Failed to fetch news ${e}`);return l(await a.json())},createNews:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=function(e){return{title:e.title,content:e.content,categoryId:e.categoryId,...e.slug&&{slug:e.slug},...e.excerpt&&{excerpt:e.excerpt},...e.featuredImage&&{featuredImage:e.featuredImage},...e.tags&&{tags:e.tags},...e.status&&{status:e.status},...e.publishedAt&&{publishedAt:e.publishedAt},...e.metaTitle&&{metaTitle:e.metaTitle},...e.metaDescription&&{metaDescription:e.metaDescription},...e.relatedLeagueId&&{relatedLeagueId:e.relatedLeagueId},...e.relatedTeamId&&{relatedTeamId:e.relatedTeamId},...e.relatedPlayerId&&{relatedPlayerId:e.relatedPlayerId},...e.relatedFixtureId&&{relatedFixtureId:e.relatedFixtureId},...void 0!==e.isFeatured&&{isFeatured:e.isFeatured},...void 0!==e.priority&&{priority:e.priority},...e.summary&&!e.excerpt&&{excerpt:e.summary},...e.imageUrl&&!e.featuredImage&&{featuredImage:e.imageUrl},...void 0!==e.isPublished&&!e.status&&{status:e.isPublished?"published":"draft"},...void 0!==e.isHot&&void 0===e.isFeatured&&{isFeatured:e.isHot}}}(e),r=await fetch("/api/news",{method:"POST",headers:s,body:JSON.stringify(a)});if(!r.ok)throw Error((await r.json()).message||"Failed to create news");return l(await r.json())},updateNews:async(e,t)=>{let s=n(),a={"Content-Type":"application/json"};s&&(a.Authorization=`Bearer ${s}`);let r=function(e){let t={};return void 0!==e.title&&(t.title=e.title),void 0!==e.content&&(t.content=e.content),void 0!==e.excerpt&&(t.excerpt=e.excerpt),void 0!==e.featuredImage&&(t.featuredImage=e.featuredImage),void 0!==e.tags&&(t.tags=e.tags),void 0!==e.status&&(t.status=e.status),void 0!==e.categoryId&&(t.categoryId="string"==typeof e.categoryId?parseInt(e.categoryId):e.categoryId),void 0!==e.metaTitle&&(t.metaTitle=e.metaTitle),void 0!==e.metaDescription&&(t.metaDescription=e.metaDescription),void 0!==e.relatedLeagueId&&(t.relatedLeagueId=e.relatedLeagueId),void 0!==e.relatedTeamId&&(t.relatedTeamId=e.relatedTeamId),void 0!==e.relatedPlayerId&&(t.relatedPlayerId=e.relatedPlayerId),void 0!==e.relatedFixtureId&&(t.relatedFixtureId=e.relatedFixtureId),void 0!==e.isFeatured&&(t.isFeatured=e.isFeatured),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.publishedAt&&(t.publishedAt=e.publishedAt),void 0!==e.summary&&(t.excerpt=e.summary),void 0!==e.imageUrl&&(t.featuredImage=e.imageUrl),void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft"),void 0!==e.isHot&&(t.isFeatured=e.isHot),t}(t),i=await fetch(`/api/news/${e}`,{method:"PATCH",headers:a,body:JSON.stringify(r)});if(!i.ok)throw Error((await i.json()).message||`Failed to update news ${e}`);return l(await i.json())},deleteNews:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=await fetch(`/api/news/${e}`,{method:"DELETE",headers:s});if(!a.ok){let t=`Failed to delete news ${e}`;try{t=(await a.json()).message||t}catch(s){t=`Failed to delete news ${e} (Status: ${a.status})`}throw Error(t)}},getPublishedNews:async(e={})=>d.getNews({...e,status:"published"}),getHotNews:async(e={})=>d.getNews({...e,isFeatured:!0}),getNewsByAuthor:async(e,t={})=>d.getNews({...t,author:e}),toggleNewsStatus:async(e,t)=>d.updateNews(e,{status:t?"published":"draft"}),toggleHotStatus:async(e,t)=>d.updateNews(e,{isFeatured:t})};var c=s(34755);let o=(e={})=>(0,a.a)({queryKey:["news",e],queryFn:()=>d.getNews(e),staleTime:3e5}),u=(e,t=!0)=>(0,a.a)({queryKey:["news",e],queryFn:()=>d.getNewsById(e),enabled:!!e&&t,staleTime:6e5}),m=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>d.createNews(e),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News created successfully")},onError:e=>{c.toast.error(`Failed to create news: ${e.message}`)}})},x=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>d.updateNews(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success("News updated successfully")},onError:e=>{c.toast.error(`Failed to update news: ${e.message}`)}})},h=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>d.deleteNews(e),onSuccess:()=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News deleted successfully")},onError:e=>{c.toast.error(`Failed to delete news: ${e.message}`)}})},p=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isPublished:t})=>d.toggleNewsStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success(`News ${"published"===t.status?"published":"unpublished"} successfully`)},onError:e=>{c.toast.error(`Failed to toggle news status: ${e.message}`)}})},y=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isHot:t})=>d.toggleHotStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success(`News ${t.isFeatured?"marked as featured":"unmarked as featured"} successfully`)},onError:e=>{c.toast.error(`Failed to toggle hot status: ${e.message}`)}})}},79172:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},14217:(e,t,s)=>{"use strict";s.d(t,{f:()=>n});var a=s(3729),r=s(62409),i=s(95344),l=a.forwardRef((e,t)=>(0,i.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},19655:(e,t,s)=>{"use strict";s.d(t,{bU:()=>N,fC:()=>b});var a=s(3729),r=s(85222),i=s(31405),l=s(98462),n=s(33183),d=s(92062),c=s(63085),o=s(62409),u=s(95344),m="Switch",[x,h]=(0,l.b)(m),[p,y]=x(m),g=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:l,checked:d,defaultChecked:c,required:x,disabled:h,value:y="on",onCheckedChange:g,form:f,...j}=e,[b,N]=a.useState(null),k=(0,i.e)(t,e=>N(e)),F=a.useRef(!1),C=!b||f||!!b.closest("form"),[I,P]=(0,n.T)({prop:d,defaultProp:c??!1,onChange:g,caller:m});return(0,u.jsxs)(p,{scope:s,checked:I,disabled:h,children:[(0,u.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":I,"aria-required":x,"data-state":w(I),"data-disabled":h?"":void 0,disabled:h,value:y,...j,ref:k,onClick:(0,r.M)(e.onClick,e=>{P(e=>!e),C&&(F.current=e.isPropagationStopped(),F.current||e.stopPropagation())})}),C&&(0,u.jsx)(v,{control:b,bubbles:!F.current,name:l,value:y,checked:I,required:x,disabled:h,form:f,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var f="SwitchThumb",j=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,r=y(f,s);return(0,u.jsx)(o.WV.span,{"data-state":w(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});j.displayName=f;var v=a.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:r=!0,...l},n)=>{let o=a.useRef(null),m=(0,i.e)(o,n),x=(0,d.D)(s),h=(0,c.t)(t);return a.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==s&&t){let a=new Event("click",{bubbles:r});t.call(e,s),e.dispatchEvent(a)}},[x,s,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...l,tabIndex:-1,ref:m,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=g,N=j},92062:(e,t,s)=>{"use strict";s.d(t,{D:()=>r});var a=s(3729);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,2527,6317,7833],()=>s(84332));module.exports=a})();