(()=>{var e={};e.id=1317,e.ids=[1317],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},5896:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>o});var a=s(50482),r=s(69108),i=s(62563),l=s.n(i),n=s(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o=["",{children:["dashboard",{children:["news",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,53906)),"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94699)),"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx"],u="/dashboard/news/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/news/page",pathname:"/dashboard/news",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},57800:(e,t,s)=>{Promise.resolve().then(s.bind(s,27152))},71532:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},59768:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},64260:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76394:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},90586:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("newspaper",[["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2",key:"39pd36"}],["rect",{width:"8",height:"4",x:"10",y:"6",rx:"1",key:"aywv1n"}]])},51838:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(97075).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},27152:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(95344),r=s(3729),i=s(8428),l=s(11494),n=s(23673),d=s(5094),o=s(46540),c=s(19591),u=s(77022),m=s(73875),h=s(67999),p=s(36487),x=s(52721),y=s(34755),g=s(18822),f=s(55794),v=s(53148),w=s(46327),b=s(38271),j=s(65719),N=s(90586),k=s(51838),F=s(76394),C=s(28765),I=s(56506);function P(){let e=(0,i.useRouter)(),t=(0,l.NL)(),{isEditor:s,isAdmin:P}=(0,p.TE)(),[q,S]=(0,r.useState)({page:1,limit:20}),[T,A]=(0,r.useState)(""),[E,Z]=(0,r.useState)(!1),[$,z]=(0,r.useState)(null),{data:D,isLoading:M,error:L}=(0,x.Kg)(q),_=(0,x.po)(),H=(0,x.CC)(),O=(0,x.FF)(),K=(e,t)=>{S(s=>({...s,[e]:t,page:1}))},R=t=>{e.push(`/dashboard/news/${t.id}`)},B=t=>{if(!s()&&!P()){y.toast.error("You do not have permission to edit news");return}e.push(`/dashboard/news/${t.id}/edit`)},Q=e=>{if(!P()){y.toast.error("You do not have permission to delete news");return}z(e),Z(!0)},G=async()=>{if($)try{await _.mutateAsync($.id),Z(!1),z(null)}catch(e){}};return L?(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"News Management"})}),a.jsx(n.Zb,{children:(0,a.jsxs)(n.aY,{className:"p-6",children:["            ",(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(j.Z,{className:"mx-auto h-12 w-12 text-red-500 mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-red-600 mb-2",children:"Failed to load news"}),a.jsx("p",{className:"text-gray-600 mb-4",children:L?.message}),a.jsx(d.z,{onClick:()=>t.invalidateQueries({queryKey:["news"]}),children:"Try Again"})]})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[a.jsx(N.Z,{className:"mr-3 h-8 w-8 text-blue-600"}),"News Management"]}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Manage news articles and announcements"})]}),s()&&a.jsx(I.default,{href:"/dashboard/news/create",passHref:!0,children:(0,a.jsxs)(d.z,{className:"bg-blue-600 hover:bg-blue-700",children:[a.jsx(k.Z,{className:"mr-2 h-4 w-4"}),"Create News"]})})]}),(0,a.jsxs)(n.Zb,{children:[a.jsx(n.Ol,{children:(0,a.jsxs)(n.ll,{className:"flex items-center",children:[a.jsx(F.Z,{className:"mr-2 h-5 w-5"}),"Filters & Search"]})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),S(e=>({...e,search:T.trim()||void 0,page:1}))},className:"flex gap-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx(o.I,{placeholder:"Search by title, content, or author...",value:T,onChange:e=>A(e.target.value),className:"w-full"})}),(0,a.jsxs)(d.z,{type:"submit",variant:"outline",children:[a.jsx(C.Z,{className:"mr-2 h-4 w-4"}),"Search"]}),q.search&&a.jsx(d.z,{type:"button",variant:"ghost",onClick:()=>{A(""),K("search",void 0)},children:"Clear"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("label",{className:"text-sm font-medium",children:"Status:"}),(0,a.jsxs)("select",{value:q.status||"all",onChange:e=>{let t=e.target.value;K("status","all"===t?void 0:t)},className:"px-3 py-1 border rounded-md text-sm",children:[a.jsx("option",{value:"all",children:"All"}),a.jsx("option",{value:"published",children:"Published"}),a.jsx("option",{value:"draft",children:"Draft"}),a.jsx("option",{value:"archived",children:"Archived"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("label",{className:"text-sm font-medium",children:"Featured:"}),(0,a.jsxs)("select",{value:q.isFeatured?.toString()||"all",onChange:e=>{let t=e.target.value;K("isFeatured","all"===t?void 0:"true"===t)},className:"px-3 py-1 border rounded-md text-sm",children:[a.jsx("option",{value:"all",children:"All"}),a.jsx("option",{value:"true",children:"Featured Only"}),a.jsx("option",{value:"false",children:"Regular"})]})]})]})]})]}),a.jsx(n.Zb,{children:a.jsx(n.aY,{className:"p-0",children:a.jsx(u.w,{columns:[{key:"title",title:"Title",sortable:!0,render:(e,t)=>(0,a.jsxs)("div",{className:"max-w-md",children:[a.jsx("div",{className:"font-medium text-gray-900 truncate",children:e}),t.summary&&a.jsx("div",{className:"text-sm text-gray-500 truncate",children:t.summary})]})},{key:"author",title:"Author",sortable:!0,filterable:!0,render:e=>(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),a.jsx("span",{className:"text-sm text-gray-600",children:e})]})},{key:"status",title:"Status",sortable:!0,render:(e,t)=>{let r="published"===e;return s()||P()?a.jsx(h.Z,{checked:r,onCheckedChange:e=>{H.mutate({id:t.id,isPublished:e})},label:"",disabled:H.isLoading,size:"sm",variant:r?"success":"warning"}):a.jsx(c.C,{variant:r?"default":"secondary",children:"published"===e?"Published":"draft"===e?"Draft":"Archived"})}},{key:"isFeatured",title:"Featured",sortable:!1,render:(e,t)=>s()||P()?a.jsx(h.Z,{checked:e||!1,onCheckedChange:e=>{O.mutate({id:t.id,isHot:e})},label:"",disabled:O.isLoading,size:"sm",variant:"danger"}):e?a.jsx(c.C,{variant:"destructive",className:"text-xs",children:"Featured"}):null},{key:"publishDate",title:"Publish Date",sortable:!0,render:e=>(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx(f.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),new Date(e).toLocaleDateString()]})},{key:"actions",title:"Actions",render:(e,t)=>(0,a.jsxs)("div",{className:"flex space-x-1",children:[a.jsx(d.z,{size:"sm",variant:"outline",title:"View Details",onClick:()=>R(t),children:a.jsx(v.Z,{className:"h-4 w-4"})}),s()&&a.jsx(d.z,{size:"sm",variant:"outline",title:"Edit News",onClick:()=>B(t),children:a.jsx(w.Z,{className:"h-4 w-4"})}),P()&&a.jsx(d.z,{size:"sm",variant:"outline",title:"Delete News",onClick:()=>Q(t),className:"text-red-600 hover:text-red-700",children:a.jsx(b.Z,{className:"h-4 w-4"})})]})}],data:D?.data||[],loading:M,pagination:{page:q.page||1,limit:q.limit||20,total:D?.meta?.totalItems||0,onPageChange:e=>K("page",e),onLimitChange:e=>K("limit",e)},emptyMessage:"No news found. Create your first news article!"})})}),"      ",a.jsx(m.sm,{isOpen:E,onClose:()=>Z(!1),title:"Delete News",message:`Are you sure you want to delete "${$?.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",onConfirm:G,loading:_.isLoading,variant:"destructive"})]})}},7361:(e,t,s)=>{"use strict";s.d(t,{_:()=>o});var a=s(95344),r=s(3729),i=s(14217),l=s(49247),n=s(11453);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...t},s)=>a.jsx(i.f,{ref:s,className:(0,n.cn)(d(),e),...t}));o.displayName=i.f.displayName},73875:(e,t,s)=>{"use strict";s.d(t,{sm:()=>m,uB:()=>h,u_:()=>u});var a=s(95344),r=s(3729),i=s(81021),l=s(13659),n=s(14513),d=s(5094),o=s(11453);let c={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=({isOpen:e,onClose:t,title:s,description:u,children:m,size:h="md",showCloseButton:p=!0,closeOnOverlayClick:x=!0,className:y})=>a.jsx(i.u,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(l.Vq,{as:"div",className:"relative z-50",onClose:x?t:()=>{},children:[a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(i.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",c[h],y),children:[(s||p)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[s&&a.jsx(l.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:s}),u&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),p&&a.jsx(d.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:a.jsx(n.Z,{className:"h-4 w-4"})})]}),a.jsx("div",{className:"mt-2",children:m})]})})})})]})}),m=({isOpen:e,onClose:t,onConfirm:s,title:r="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:n="Cancel",variant:o="default",loading:c=!1})=>a.jsx(u,{isOpen:e,onClose:t,title:r,size:"sm",closeOnOverlayClick:!c,children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("p",{className:"text-sm text-gray-600",children:i}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[a.jsx(d.z,{variant:"outline",onClick:t,disabled:c,children:n}),a.jsx(d.z,{variant:"destructive"===o?"destructive":"default",onClick:s,disabled:c,children:c?"Processing...":l})]})]})}),h=({isOpen:e,onClose:t,title:s,description:r,children:i,onSubmit:l,submitText:n="Save",cancelText:o="Cancel",loading:c=!1,size:m="md"})=>a.jsx(u,{isOpen:e,onClose:t,title:s,description:r,size:m,closeOnOverlayClick:!c,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l?.()},className:"space-y-4",children:[i,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[a.jsx(d.z,{type:"button",variant:"outline",onClick:t,disabled:c,children:o}),l&&a.jsx(d.z,{type:"submit",disabled:c,children:c?"Saving...":n})]})]})})},13611:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var a=s(95344),r=s(3729),i=s(19655),l=s(11453);let n=r.forwardRef(({className:e,...t},s)=>a.jsx(i.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:a.jsx(i.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));n.displayName=i.fC.displayName},67999:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(95344);s(3729);var r=s(13611),i=s(7361);function l({checked:e,onCheckedChange:t,label:s,description:l,disabled:n=!1,size:d="md",variant:o="default"}){return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(r.r,{id:s,checked:e,onCheckedChange:t,disabled:n}),(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx(i._,{htmlFor:s,className:`font-medium cursor-pointer ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[d]} ${{default:e?"text-blue-700":"text-gray-700",success:e?"text-green-700":"text-gray-700",warning:e?"text-yellow-700":"text-gray-700",danger:e?"text-red-700":"text-gray-700"}[o]} ${n?"opacity-50":""}`,children:s}),l&&a.jsx("span",{className:`text-xs text-gray-500 ${n?"opacity-50":""}`,children:l})]})]})}},52721:(e,t,s)=>{"use strict";s.d(t,{dR:()=>m,po:()=>p,Kg:()=>c,ZZ:()=>u,FF:()=>y,CC:()=>x,rj:()=>h});var a=s(19738),r=s(11494),i=s(14373);function l(e){return{...e,author:`Author ${e.authorId}`,summary:e.excerpt,imageUrl:e.featuredImage,isPublished:"published"===e.status,isHot:e.isFeatured,publishDate:e.publishedAt||e.createdAt}}let n=()=>null,d={getNews:async(e={})=>{let t=function(e){let t={...e};return void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft",delete t.isPublished),void 0!==e.isHot&&(t.isFeatured=e.isHot,delete t.isHot),t}(e),s=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&(Array.isArray(t)?t.forEach(t=>s.append(e,t.toString())):s.append(e,t.toString()))});let a=n(),r={"Content-Type":"application/json"};a&&(r.Authorization=`Bearer ${a}`);let i=await fetch(`/api/news?${s.toString()}`,{method:"GET",headers:r});if(!i.ok)throw Error((await i.json()).message||"Failed to fetch news");let d=await i.json();return d.data&&(d.data=d.data.map(l)),d},getNewsById:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=await fetch(`/api/news/${e}`,{method:"GET",headers:s});if(!a.ok)throw Error((await a.json()).message||`Failed to fetch news ${e}`);return l(await a.json())},createNews:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=function(e){return{title:e.title,content:e.content,categoryId:e.categoryId,...e.slug&&{slug:e.slug},...e.excerpt&&{excerpt:e.excerpt},...e.featuredImage&&{featuredImage:e.featuredImage},...e.tags&&{tags:e.tags},...e.status&&{status:e.status},...e.publishedAt&&{publishedAt:e.publishedAt},...e.metaTitle&&{metaTitle:e.metaTitle},...e.metaDescription&&{metaDescription:e.metaDescription},...e.relatedLeagueId&&{relatedLeagueId:e.relatedLeagueId},...e.relatedTeamId&&{relatedTeamId:e.relatedTeamId},...e.relatedPlayerId&&{relatedPlayerId:e.relatedPlayerId},...e.relatedFixtureId&&{relatedFixtureId:e.relatedFixtureId},...void 0!==e.isFeatured&&{isFeatured:e.isFeatured},...void 0!==e.priority&&{priority:e.priority},...e.summary&&!e.excerpt&&{excerpt:e.summary},...e.imageUrl&&!e.featuredImage&&{featuredImage:e.imageUrl},...void 0!==e.isPublished&&!e.status&&{status:e.isPublished?"published":"draft"},...void 0!==e.isHot&&void 0===e.isFeatured&&{isFeatured:e.isHot}}}(e),r=await fetch("/api/news",{method:"POST",headers:s,body:JSON.stringify(a)});if(!r.ok)throw Error((await r.json()).message||"Failed to create news");return l(await r.json())},updateNews:async(e,t)=>{let s=n(),a={"Content-Type":"application/json"};s&&(a.Authorization=`Bearer ${s}`);let r=function(e){let t={};return void 0!==e.title&&(t.title=e.title),void 0!==e.content&&(t.content=e.content),void 0!==e.excerpt&&(t.excerpt=e.excerpt),void 0!==e.featuredImage&&(t.featuredImage=e.featuredImage),void 0!==e.tags&&(t.tags=e.tags),void 0!==e.status&&(t.status=e.status),void 0!==e.categoryId&&(t.categoryId="string"==typeof e.categoryId?parseInt(e.categoryId):e.categoryId),void 0!==e.metaTitle&&(t.metaTitle=e.metaTitle),void 0!==e.metaDescription&&(t.metaDescription=e.metaDescription),void 0!==e.relatedLeagueId&&(t.relatedLeagueId=e.relatedLeagueId),void 0!==e.relatedTeamId&&(t.relatedTeamId=e.relatedTeamId),void 0!==e.relatedPlayerId&&(t.relatedPlayerId=e.relatedPlayerId),void 0!==e.relatedFixtureId&&(t.relatedFixtureId=e.relatedFixtureId),void 0!==e.isFeatured&&(t.isFeatured=e.isFeatured),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.publishedAt&&(t.publishedAt=e.publishedAt),void 0!==e.summary&&(t.excerpt=e.summary),void 0!==e.imageUrl&&(t.featuredImage=e.imageUrl),void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft"),void 0!==e.isHot&&(t.isFeatured=e.isHot),t}(t),i=await fetch(`/api/news/${e}`,{method:"PATCH",headers:a,body:JSON.stringify(r)});if(!i.ok)throw Error((await i.json()).message||`Failed to update news ${e}`);return l(await i.json())},deleteNews:async e=>{let t=n(),s={"Content-Type":"application/json"};t&&(s.Authorization=`Bearer ${t}`);let a=await fetch(`/api/news/${e}`,{method:"DELETE",headers:s});if(!a.ok){let t=`Failed to delete news ${e}`;try{t=(await a.json()).message||t}catch(s){t=`Failed to delete news ${e} (Status: ${a.status})`}throw Error(t)}},getPublishedNews:async(e={})=>d.getNews({...e,status:"published"}),getHotNews:async(e={})=>d.getNews({...e,isFeatured:!0}),getNewsByAuthor:async(e,t={})=>d.getNews({...t,author:e}),toggleNewsStatus:async(e,t)=>d.updateNews(e,{status:t?"published":"draft"}),toggleHotStatus:async(e,t)=>d.updateNews(e,{isFeatured:t})};var o=s(34755);let c=(e={})=>(0,a.a)({queryKey:["news",e],queryFn:()=>d.getNews(e),staleTime:3e5}),u=(e,t=!0)=>(0,a.a)({queryKey:["news",e],queryFn:()=>d.getNewsById(e),enabled:!!e&&t,staleTime:6e5}),m=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>d.createNews(e),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),o.toast.success("News created successfully")},onError:e=>{o.toast.error(`Failed to create news: ${e.message}`)}})},h=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,data:t})=>d.updateNews(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),o.toast.success("News updated successfully")},onError:e=>{o.toast.error(`Failed to update news: ${e.message}`)}})},p=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:e=>d.deleteNews(e),onSuccess:()=>{e.invalidateQueries({queryKey:["news"]}),o.toast.success("News deleted successfully")},onError:e=>{o.toast.error(`Failed to delete news: ${e.message}`)}})},x=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isPublished:t})=>d.toggleNewsStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),o.toast.success(`News ${"published"===t.status?"published":"unpublished"} successfully`)},onError:e=>{o.toast.error(`Failed to toggle news status: ${e.message}`)}})},y=()=>{let e=(0,r.NL)();return(0,i.D)({mutationFn:({id:e,isHot:t})=>d.toggleHotStatus(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),o.toast.success(`News ${t.isFeatured?"marked as featured":"unmarked as featured"} successfully`)},onError:e=>{o.toast.error(`Failed to toggle hot status: ${e.message}`)}})}},53906:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,s(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default},14217:(e,t,s)=>{"use strict";s.d(t,{f:()=>n});var a=s(3729),r=s(62409),i=s(95344),l=a.forwardRef((e,t)=>(0,i.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},19655:(e,t,s)=>{"use strict";s.d(t,{bU:()=>N,fC:()=>j});var a=s(3729),r=s(85222),i=s(31405),l=s(98462),n=s(33183),d=s(92062),o=s(63085),c=s(62409),u=s(95344),m="Switch",[h,p]=(0,l.b)(m),[x,y]=h(m),g=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:l,checked:d,defaultChecked:o,required:h,disabled:p,value:y="on",onCheckedChange:g,form:f,...v}=e,[j,N]=a.useState(null),k=(0,i.e)(t,e=>N(e)),F=a.useRef(!1),C=!j||f||!!j.closest("form"),[I,P]=(0,n.T)({prop:d,defaultProp:o??!1,onChange:g,caller:m});return(0,u.jsxs)(x,{scope:s,checked:I,disabled:p,children:[(0,u.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":I,"aria-required":h,"data-state":b(I),"data-disabled":p?"":void 0,disabled:p,value:y,...v,ref:k,onClick:(0,r.M)(e.onClick,e=>{P(e=>!e),C&&(F.current=e.isPropagationStopped(),F.current||e.stopPropagation())})}),C&&(0,u.jsx)(w,{control:j,bubbles:!F.current,name:l,value:y,checked:I,required:h,disabled:p,form:f,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var f="SwitchThumb",v=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,r=y(f,s);return(0,u.jsx)(c.WV.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});v.displayName=f;var w=a.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:r=!0,...l},n)=>{let c=a.useRef(null),m=(0,i.e)(c,n),h=(0,d.D)(s),p=(0,o.t)(t);return a.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==s&&t){let a=new Event("click",{bubbles:r});t.call(e,s),e.dispatchEvent(a)}},[h,s,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...l,tabIndex:-1,ref:m,style:{...l.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var j=g,N=v}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,6126,337,2609,3649,732,7966,2527,6317,7833,7022],()=>s(5896));module.exports=a})();