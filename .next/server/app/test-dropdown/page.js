(()=>{var e={};e.id=5596,e.ids=[5596],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},51091:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(50482),a=t(69108),n=t(62563),o=t.n(n),l=t(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["test-dropdown",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59946)),"/home/<USER>/FECMS-sport/src/app/test-dropdown/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/test-dropdown/page.tsx"],u="/test-dropdown/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-dropdown/page",pathname:"/test-dropdown",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97342:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},53622:(e,r,t)=>{Promise.resolve().then(t.bind(t,34755)),Promise.resolve().then(t.bind(t,64792)),Promise.resolve().then(t.bind(t,89284)),Promise.resolve().then(t.bind(t,11503))},39285:(e,r,t)=>{Promise.resolve().then(t.bind(t,40875))},40875:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(95344),a=t(3729),n=t(25179);function o(){let[e,r]=(0,a.useState)("league"),t=[{value:"league",label:"League"},{value:"cup",label:"Cup"},{value:"playoffs",label:"Playoffs"},{value:"friendly",label:"Friendly"},{value:"qualification",label:"Qualification"}];return(0,a.useEffect)(()=>{console.log("Test dropdown value:",e)},[e]),(0,s.jsxs)("div",{className:"p-8 max-w-md",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"League Type Dropdown Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(n.mg,{label:"League Type (Test)",placeholder:"Select league type",value:e,onValueChange:e=>{console.log("Value changed to:",e),r(e)},options:t}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Current Value:"}),' "',e,'"']}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Selected Option:"})," ",t.find(r=>r.value===e)?.label||"None"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("p",{className:"text-sm font-medium",children:"Test buttons:"}),t.map(e=>(0,s.jsxs)("button",{onClick:()=>r(e.value),className:"block px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 rounded",children:['Set to "',e.value,'"']},e.value))]})]})]})}},5094:(e,r,t)=>{"use strict";t.d(r,{d:()=>i,z:()=>d});var s=t(95344),a=t(3729),n=t(32751),o=t(49247),l=t(11453);let i=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},d)=>{let c=a?n.g7:"button";return s.jsx(c,{className:(0,l.cn)(i({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>l,SZ:()=>d,Zb:()=>o,aY:()=>c,ll:()=>i});var s=t(95344),a=t(3729),n=t(11453);let o=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));o.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...r}));i.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},64792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DefaultErrorFallback:()=>u,ErrorBoundary:()=>c,useErrorHandler:()=>p});var s=t(95344),a=t(3729),n=t.n(a),o=t(5094),l=t(23673),i=t(65719),d=t(33733);class c extends n().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return s.jsx(e,{error:this.state.error,resetError:this.resetError})}return s.jsx(u,{error:this.state.error,resetError:this.resetError})}return this.props.children}}let u=({error:e,resetError:r})=>s.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(l.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(l.Ol,{className:"text-center",children:[s.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:s.jsx(i.Z,{className:"h-6 w-6 text-red-600"})}),s.jsx(l.ll,{className:"text-red-900",children:"Something went wrong"}),s.jsx(l.SZ,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[!1,(0,s.jsxs)(o.z,{onClick:r,className:"w-full",variant:"outline",children:[s.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}),p=()=>{let[e,r]=n().useState(null),t=n().useCallback(()=>{r(null)},[]),s=n().useCallback(e=>{r(e)},[]);return n().useEffect(()=>{if(e)throw e},[e]),{captureError:s,resetError:t}}},25179:(e,r,t)=>{"use strict";t.d(r,{ji:()=>b,iN:()=>g,hj:()=>v,UP:()=>x,mg:()=>y,XL:()=>h});var s=t(95344),a=t(3729),n=t(7361),o=t(46540),l=t(2690),i=t(38157),d=t(85684),c=t(62312),u=t(11453);let p=a.forwardRef(({className:e,...r},t)=>s.jsx(d.fC,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:s.jsx(d.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:s.jsx(c.Z,{className:"h-4 w-4"})})}));p.displayName=d.fC.displayName;var m=t(68065);let f=(0,a.forwardRef)(({label:e,description:r,error:t,required:a,className:o,children:l},i)=>(0,s.jsxs)("div",{ref:i,className:(0,u.cn)("space-y-2",o),children:[e&&(0,s.jsxs)(n._,{className:(0,u.cn)("text-sm font-medium",t&&"text-red-600"),children:[e,a&&s.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),l,r&&!t&&s.jsx("p",{className:"text-sm text-gray-500",children:r}),t&&s.jsx("p",{className:"text-sm text-red-600",children:t})]}));f.displayName="FormField";let x=(0,a.forwardRef)(({label:e,description:r,error:t,required:a,className:n,...l},i)=>s.jsx(f,{label:e,description:r,error:t,required:a,children:s.jsx(o.I,{ref:i,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",n),...l})}));x.displayName="InputField";let h=(0,a.forwardRef)(({label:e,description:r,error:t,required:a,className:n,...o},i)=>s.jsx(f,{label:e,description:r,error:t,required:a,children:s.jsx(l.g,{ref:i,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",n),...o})}));h.displayName="TextareaField";let y=(0,a.forwardRef)(({label:e,description:r,error:t,required:a,placeholder:n,value:o,onValueChange:l,options:d,className:c,disabled:p},m)=>{let x=d.find(e=>e.value===o),h="http://*************";return s.jsx(f,{label:e,description:r,error:t,required:a,children:(0,s.jsxs)(i.Ph,{value:o,onValueChange:l,disabled:p,children:[s.jsx(i.i4,{ref:m,className:(0,u.cn)(t&&"border-red-500 focus:border-red-500",c),children:s.jsx("div",{className:"flex items-center justify-between w-full",children:s.jsx("div",{className:"flex items-center space-x-2 flex-1",children:x?x?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[x.logo&&s.jsx("img",{src:`${h}/${x.logo}`,alt:x.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),s.jsx("span",{children:x.label})]}):n:s.jsx("span",{className:"text-muted-foreground",children:n})})})}),s.jsx(i.Bw,{children:d.map(e=>s.jsx(i.Ql,{value:e.value,disabled:e.disabled,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&s.jsx("img",{src:`${h}/${e.logo}`,alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),s.jsx("span",{children:e.label})]})},e.value))})]})})});y.displayName="SelectField";let b=(0,a.forwardRef)(({label:e,description:r,error:t,checked:a,onCheckedChange:o,className:l},i)=>s.jsx(f,{description:r,error:t,className:l,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(p,{ref:i,checked:a,onCheckedChange:o,className:(0,u.cn)(t&&"border-red-500")}),e&&s.jsx(n._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",t&&"text-red-600"),children:e})]})}));b.displayName="CheckboxField",(0,a.forwardRef)(({label:e,description:r,error:t,required:a,value:o,onValueChange:l,options:i,orientation:d="vertical",className:c},p)=>s.jsx(f,{label:e,description:r,error:t,required:a,className:c,children:s.jsx(m.E,{ref:p,value:o,onValueChange:l,className:(0,u.cn)("horizontal"===d?"flex flex-row space-x-4":"space-y-2"),children:i.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(m.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(t&&"border-red-500")}),s.jsx(n._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})).displayName="RadioField";let v=({title:e,description:r,children:t,className:a})=>(0,s.jsxs)("div",{className:(0,u.cn)("space-y-4",a),children:[(e||r)&&(0,s.jsxs)("div",{className:"space-y-1",children:[e&&s.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e}),r&&s.jsx("p",{className:"text-sm text-gray-600",children:r})]}),s.jsx("div",{className:"space-y-4",children:t})]}),g=({children:e,className:r,align:t="right"})=>s.jsx("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===t&&"justify-start","center"===t&&"justify-center","right"===t&&"justify-end",r),children:e})},46540:(e,r,t)=>{"use strict";t.d(r,{I:()=>o});var s=t(95344),a=t(3729),n=t(11453);let o=a.forwardRef(({className:e,type:r,...t},a)=>s.jsx("input",{type:r,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Input"},7361:(e,r,t)=>{"use strict";t.d(r,{_:()=>d});var s=t(95344),a=t(3729),n=t(14217),o=t(49247),l=t(11453);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>s.jsx(n.f,{ref:t,className:(0,l.cn)(i(),e),...r}));d.displayName=n.f.displayName},68065:(e,r,t)=>{"use strict";t.d(r,{E:()=>i,m:()=>d});var s=t(95344),a=t(3729),n=t(9913),o=t(82958),l=t(11453);let i=a.forwardRef(({className:e,...r},t)=>s.jsx(n.fC,{className:(0,l.cn)("grid gap-2",e),...r,ref:t}));i.displayName=n.fC.displayName;let d=a.forwardRef(({className:e,...r},t)=>s.jsx(n.ck,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:s.jsx(n.z$,{className:"flex items-center justify-center",children:s.jsx(o.Z,{className:"h-3.5 w-3.5 fill-primary"})})}));d.displayName=n.ck.displayName},38157:(e,r,t)=>{"use strict";t.d(r,{Bw:()=>x,Ph:()=>c,Ql:()=>h,i4:()=>p,ki:()=>u});var s=t(95344),a=t(3729),n=t(32116),o=t(25390),l=t(12704),i=t(62312),d=t(11453);let c=n.fC;n.ZA;let u=n.B4,p=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(n.xz,{ref:a,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[r,s.jsx(n.JO,{asChild:!0,children:s.jsx(o.Z,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=n.xz.displayName;let m=a.forwardRef(({className:e,...r},t)=>s.jsx(n.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:s.jsx(l.Z,{className:"h-4 w-4"})}));m.displayName=n.u_.displayName;let f=a.forwardRef(({className:e,...r},t)=>s.jsx(n.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:s.jsx(o.Z,{className:"h-4 w-4"})}));f.displayName=n.$G.displayName;let x=a.forwardRef(({className:e,children:r,position:t="popper",...a},o)=>s.jsx(n.h_,{children:(0,s.jsxs)(n.VY,{ref:o,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[s.jsx(m,{}),s.jsx(n.l_,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),s.jsx(f,{})]})}));x.displayName=n.VY.displayName,a.forwardRef(({className:e,...r},t)=>s.jsx(n.__,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",e),...r})).displayName=n.__.displayName;let h=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(n.ck,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[s.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(n.wU,{children:s.jsx(i.Z,{className:"h-4 w-4"})})}),s.jsx(n.eT,{children:r})]}));h.displayName=n.ck.displayName,a.forwardRef(({className:e,...r},t)=>s.jsx(n.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=n.Z0.displayName},2690:(e,r,t)=>{"use strict";t.d(r,{g:()=>o});var s=t(95344),a=t(3729),n=t(11453);let o=a.forwardRef(({className:e,...r},t)=>s.jsx("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...r}));o.displayName="Textarea"},89284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryProvider:()=>l});var s=t(95344),a=t(86166),n=t(11494),o=t(3729);let l=({children:e})=>{let[r]=(0,o.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,s.jsxs)(n.aH,{client:r,children:[e,!1]})}},11503:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ThemeProvider:()=>o,useTheme:()=>l});var s=t(95344),a=t(3729);let n=(0,a.createContext)({theme:"system",setTheme:()=>null});function o({children:e,defaultTheme:r="system",storageKey:t="cms-theme",...o}){let[l,i]=(0,a.useState)(r),[d,c]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{c(!0);let e=localStorage?.getItem(t);e&&i(e)},[t]),(0,a.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===l){let r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(r);return}e.classList.add(l)},[l]),d)?s.jsx(n.Provider,{...o,value:{theme:l,setTheme:e=>{localStorage?.setItem(t,e),i(e)}},children:e}):null}let l=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},11453:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(56815),a=t(79377);function n(...e){return(0,a.m6)((0,s.W)(e))}},32417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j,metadata:()=>g});var s=t(25036),a=t(42195),n=t.n(a);t(5023);var o=t(86843);let l=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx`),{__esModule:i,$$typeof:d}=l;l.default;let c=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx#QueryProvider`),u=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx`),{__esModule:p,$$typeof:m}=u;u.default;let f=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#ThemeProvider`);(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#useTheme`);let x=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx`),{__esModule:h,$$typeof:y}=x;x.default;let b=(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#ErrorBoundary`);(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#DefaultErrorFallback`),(0,o.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#useErrorHandler`);var v=t(27171);let g={title:"APISportsGame CMS",description:"Content Management System for APISportsGame API"};function j({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:s.jsx(f,{defaultTheme:"system",children:s.jsx(b,{children:(0,s.jsxs)(c,{children:[e,s.jsx(v.x7,{position:"top-right",richColors:!0})]})})})})})}},59946:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>a,default:()=>o});let s=(0,t(86843).createProxy)(String.raw`/home/<USER>/FECMS-sport/src/app/test-dropdown/page.tsx`),{__esModule:a,$$typeof:n}=s,o=s.default},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(70337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{},85684:(e,r,t)=>{"use strict";t.d(r,{fC:()=>j,z$:()=>N});var s=t(3729),a=t(31405),n=t(98462),o=t(85222),l=t(33183),i=t(92062),d=t(63085),c=t(43234),u=t(62409),p=t(95344),m="Checkbox",[f,x]=(0,n.b)(m),[h,y]=f(m);function b(e){let{__scopeCheckbox:r,checked:t,children:a,defaultChecked:n,disabled:o,form:i,name:d,onCheckedChange:c,required:u,value:f="on",internal_do_not_use_render:x}=e,[y,b]=(0,l.T)({prop:t,defaultProp:n??!1,onChange:c,caller:m}),[v,g]=s.useState(null),[j,w]=s.useState(null),N=s.useRef(!1),k=!v||!!i||!!v.closest("form"),C={checked:y,disabled:o,setChecked:b,control:v,setControl:g,name:d,form:i,value:f,hasConsumerStoppedPropagationRef:N,required:u,defaultChecked:!E(n)&&n,isFormControl:k,bubbleInput:j,setBubbleInput:w};return(0,p.jsx)(h,{scope:r,...C,children:"function"==typeof x?x(C):a})}var v="CheckboxTrigger",g=s.forwardRef(({__scopeCheckbox:e,onKeyDown:r,onClick:t,...n},l)=>{let{control:i,value:d,disabled:c,checked:m,required:f,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:b,isFormControl:g,bubbleInput:j}=y(v,e),w=(0,a.e)(l,x),N=s.useRef(m);return s.useEffect(()=>{let e=i?.form;if(e){let r=()=>h(N.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[i,h]),(0,p.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":E(m)?"mixed":m,"aria-required":f,"data-state":P(m),"data-disabled":c?"":void 0,disabled:c,value:d,...n,ref:w,onKeyDown:(0,o.M)(r,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(t,e=>{h(e=>!!E(e)||!e),j&&g&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});g.displayName=v;var j=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,name:s,checked:a,defaultChecked:n,required:o,disabled:l,value:i,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(b,{__scopeCheckbox:t,checked:a,defaultChecked:n,disabled:l,required:o,onCheckedChange:d,name:s,form:c,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(g,{...u,ref:r,__scopeCheckbox:t}),e&&(0,p.jsx)(C,{__scopeCheckbox:t})]})})});j.displayName=m;var w="CheckboxIndicator",N=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,forceMount:s,...a}=e,n=y(w,t);return(0,p.jsx)(c.z,{present:s||E(n.checked)||!0===n.checked,children:(0,p.jsx)(u.WV.span,{"data-state":P(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:r,style:{pointerEvents:"none",...e.style}})})});N.displayName=w;var k="CheckboxBubbleInput",C=s.forwardRef(({__scopeCheckbox:e,...r},t)=>{let{control:n,hasConsumerStoppedPropagationRef:o,checked:l,defaultChecked:c,required:m,disabled:f,name:x,value:h,form:b,bubbleInput:v,setBubbleInput:g}=y(k,e),j=(0,a.e)(t,g),w=(0,i.D)(l),N=(0,d.t)(n);s.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,r=!o.current;if(w!==l&&e){let t=new Event("click",{bubbles:r});v.indeterminate=E(l),e.call(v,!E(l)&&l),v.dispatchEvent(t)}},[v,w,l,o]);let C=s.useRef(!E(l)&&l);return(0,p.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??C.current,required:m,disabled:f,name:x,value:h,form:b,...r,tabIndex:-1,ref:j,style:{...r.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function E(e){return"indeterminate"===e}function P(e){return E(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=k},14217:(e,r,t)=>{"use strict";t.d(r,{f:()=>l});var s=t(3729),a=t(62409),n=t(95344),o=s.forwardRef((e,r)=>(0,n.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var l=o},9913:(e,r,t)=>{"use strict";t.d(r,{ck:()=>V,fC:()=>z,z$:()=>L});var s=t(3729),a=t(85222),n=t(31405),o=t(98462),l=t(62409),i=t(34504),d=t(33183),c=t(3975),u=t(63085),p=t(92062),m=t(43234),f=t(95344),x="Radio",[h,y]=(0,o.b)(x),[b,v]=h(x),g=s.forwardRef((e,r)=>{let{__scopeRadio:t,name:o,checked:i=!1,required:d,disabled:c,value:u="on",onCheck:p,form:m,...x}=e,[h,y]=s.useState(null),v=(0,n.e)(r,e=>y(e)),g=s.useRef(!1),j=!h||m||!!h.closest("form");return(0,f.jsxs)(b,{scope:t,checked:i,disabled:c,children:[(0,f.jsx)(l.WV.button,{type:"button",role:"radio","aria-checked":i,"data-state":k(i),"data-disabled":c?"":void 0,disabled:c,value:u,...x,ref:v,onClick:(0,a.M)(e.onClick,e=>{i||p?.(),j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),j&&(0,f.jsx)(N,{control:h,bubbles:!g.current,name:o,value:u,checked:i,required:d,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=x;var j="RadioIndicator",w=s.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:s,...a}=e,n=v(j,t);return(0,f.jsx)(m.z,{present:s||n.checked,children:(0,f.jsx)(l.WV.span,{"data-state":k(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:r})})});w.displayName=j;var N=s.forwardRef(({__scopeRadio:e,control:r,checked:t,bubbles:a=!0,...o},i)=>{let d=s.useRef(null),c=(0,n.e)(d,i),m=(0,p.D)(t),x=(0,u.t)(r);return s.useEffect(()=>{let e=d.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&r){let s=new Event("click",{bubbles:a});r.call(e,t),e.dispatchEvent(s)}},[m,t,a]),(0,f.jsx)(l.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:t,...o,tabIndex:-1,ref:c,style:{...o.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}N.displayName="RadioBubbleInput";var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],E="RadioGroup",[P,R]=(0,o.b)(E,[i.Pc,y]),S=(0,i.Pc)(),_=y(),[M,T]=P(E),F=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:s,defaultValue:a,value:n,required:o=!1,disabled:u=!1,orientation:p,dir:m,loop:x=!0,onValueChange:h,...y}=e,b=S(t),v=(0,c.gm)(m),[g,j]=(0,d.T)({prop:n,defaultProp:a??null,onChange:h,caller:E});return(0,f.jsx)(M,{scope:t,name:s,required:o,disabled:u,value:g,onValueChange:j,children:(0,f.jsx)(i.fC,{asChild:!0,...b,orientation:p,dir:v,loop:x,children:(0,f.jsx)(l.WV.div,{role:"radiogroup","aria-required":o,"aria-orientation":p,"data-disabled":u?"":void 0,dir:v,...y,ref:r})})})});F.displayName=E;var q="RadioGroupItem",D=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:o,...l}=e,d=T(q,t),c=d.disabled||o,u=S(t),p=_(t),m=s.useRef(null),x=(0,n.e)(r,m),h=d.value===l.value,y=s.useRef(!1);return s.useEffect(()=>{let e=e=>{C.includes(e.key)&&(y.current=!0)},r=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,f.jsx)(i.ck,{asChild:!0,...u,focusable:!c,active:h,children:(0,f.jsx)(g,{disabled:c,required:d.required,checked:h,...p,...l,name:d.name,ref:x,onCheck:()=>d.onValueChange(l.value),onKeyDown:(0,a.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,a.M)(l.onFocus,()=>{y.current&&m.current?.click()})})})});D.displayName=q;var I=s.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...s}=e,a=_(t);return(0,f.jsx)(w,{...a,...s,ref:r})});I.displayName="RadioGroupIndicator";var z=F,V=D,L=I}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6126,337,3649,7966],()=>t(51091));module.exports=s})();