"use strict";(()=>{var e={};e.id=9791,e.ids=[9791],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53459:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>m,originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>x});var r={};a.r(r),a.d(r,{GET:()=>c,dynamic:()=>i});var s=a(95419),o=a(69108),n=a(99678),u=a(98984);let i="force-dynamic",p=process.env.DOMAIN_CDN_PICTURE||"http://172.31.213.61";async function c(e,{params:t}){try{let a=t.path.join("/"),r=`${p}/${a}`;console.log("\uD83D\uDD04 Proxying image request:",r);let s=await fetch(r,{method:"GET",headers:{...e.headers.get("accept")&&{Accept:e.headers.get("accept")},...e.headers.get("user-agent")&&{"User-Agent":e.headers.get("user-agent")}}});if(!s.ok)return console.error("❌ Image Error:",s.status,s.statusText),new u.NextResponse(null,{status:404});let o=await s.arrayBuffer(),n=s.headers.get("content-type")||"image/png";return console.log("✅ Image served successfully:",a),new u.NextResponse(o,{status:200,headers:{"Content-Type":n,"Cache-Control":"public, max-age=86400"}})}catch(e){return console.error("❌ Image Proxy Error:",e),new u.NextResponse(null,{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/images/[...path]/route",pathname:"/api/images/[...path]",filename:"route",bundlePath:"app/api/images/[...path]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/images/[...path]/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:m,staticGenerationBailout:x}=l,y="/api/images/[...path]/route";function f(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>a(53459));module.exports=r})();