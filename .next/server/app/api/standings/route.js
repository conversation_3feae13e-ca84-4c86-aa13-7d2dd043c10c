"use strict";(()=>{var e={};e.id=3735,e.ids=[3735],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2563:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d,dynamic:()=>u});var o=r(95419),a=r(69108),n=r(99678),i=r(98984);let u="force-dynamic";async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("league"),s=t.get("season"),o=t.get("format");if(!r)return i.NextResponse.json({error:"League parameter is required"},{status:400});let a=`http://localhost:3000/football/standings?league=${r}`;s&&(a+=`&season=${s}`),o&&(a+=`&format=${o}`),console.log("\uD83D\uDD04 Proxying standings request to:",a);let n=await fetch(a,{method:"GET",headers:{"Content-Type":"application/json"}});if(!n.ok)return console.error("❌ Backend API error:",n.status,n.statusText),i.NextResponse.json({error:`Backend API error: ${n.status}`},{status:n.status});let u=await n.json();return console.log("✅ Standings API response received"),i.NextResponse.json(u)}catch(e){return console.error("❌ Standings API proxy error:",e),i.NextResponse.json({error:"Failed to fetch standings"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/standings/route",pathname:"/api/standings",filename:"route",bundlePath:"app/api/standings/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/standings/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:c,serverHooks:g,headerHooks:h,staticGenerationBailout:m}=p,f="/api/standings/route";function x(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(2563));module.exports=s})();