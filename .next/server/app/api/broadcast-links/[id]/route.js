"use strict";(()=>{var e={};e.id=6891,e.ids=[6891],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93047:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>y,originalPathname:()=>f,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>m});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>d,PUT:()=>c,dynamic:()=>u});var a=s(95419),o=s(69108),n=s(99678),i=s(98984);let u="force-dynamic",l="http://localhost:3000";async function d(e,{params:t}){try{let s=t.id;console.log("\uD83D\uDD04 Proxying broadcast link detail request:",`${l}/broadcast-links/${s}`);let r=await fetch(`${l}/broadcast-links/${s}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!r.ok)return console.error("❌ API Error:",r.status,r.statusText),i.NextResponse.json({error:"Failed to fetch broadcast link",status:r.status,message:r.statusText},{status:r.status});let a=await r.json();return console.log("✅ Broadcast link detail fetched successfully:",a.data?.id),i.NextResponse.json(a)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function c(e,{params:t}){try{let s=parseInt(t.id),r=await e.json();console.log("\uD83D\uDD04 Proxying broadcast link update request:",`${l}/broadcast-links/${s}`);let a=await fetch(`${l}/broadcast-links/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(r)});if(!a.ok)return console.error("❌ API Error:",a.status,a.statusText),i.NextResponse.json({error:"Failed to update broadcast link",status:a.status,message:a.statusText},{status:a.status});let o=await a.json();return console.log("✅ Broadcast link updated successfully:",o.data?.id),i.NextResponse.json(o)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function p(e,{params:t}){try{let s=parseInt(t.id);console.log("\uD83D\uDD04 Proxying broadcast link delete request:",`${l}/broadcast-links/${s}`);let r=await fetch(`${l}/broadcast-links/${s}`,{method:"DELETE",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!r.ok)return console.error("❌ API Error:",r.status,r.statusText),i.NextResponse.json({error:"Failed to delete broadcast link",status:r.status,message:r.statusText},{status:r.status});return console.log("✅ Broadcast link deleted successfully:",s),i.NextResponse.json({success:!0,message:"Broadcast link deleted successfully"})}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/broadcast-links/[id]/route",pathname:"/api/broadcast-links/[id]",filename:"route",bundlePath:"app/api/broadcast-links/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/[id]/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:k,serverHooks:x,headerHooks:y,staticGenerationBailout:m}=h,f="/api/broadcast-links/[id]/route";function b(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:k})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>s(93047));module.exports=r})();