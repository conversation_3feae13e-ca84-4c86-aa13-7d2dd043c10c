"use strict";(()=>{var e={};e.id=6384,e.ids=[6384],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14997:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>k});var o={};r.r(o),r.d(o,{GET:()=>l,dynamic:()=>u});var s=r(95419),a=r(69108),n=r(99678),i=r(98984);let u="force-dynamic",c="http://localhost:3000";async function l(e,{params:t}){try{let r=t.fixtureId;console.log("\uD83D\uDD04 Proxying broadcast links for fixture request:",`${c}/broadcast-links/fixture/${r}`);let o=e.headers.get("authorization");console.log("\uD83D\uDD11 Authorization header received:",o?"Present":"Missing"),console.log("\uD83D\uDD11 Token preview:",o?o.substring(0,30)+"...":"No token");try{let e=await fetch(`${c}/broadcast-links/fixture/${r}`,{method:"GET",headers:{"Content-Type":"application/json",...o&&{Authorization:o}}});if(!e.ok)return console.error("❌ API Error:",e.status,e.statusText),i.NextResponse.json({error:"Failed to fetch broadcast links for fixture",status:e.status,message:e.statusText},{status:e.status});let t=await e.json();return console.log("✅ Broadcast links for fixture fetched successfully:",t.data?.length||0,"links"),i.NextResponse.json(t)}catch(e){return console.error("❌ Network Error:",e),i.NextResponse.json({error:"Network error occurred",message:e?.message||"Unknown network error"},{status:500})}}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/broadcast-links/fixture/[fixtureId]/route",pathname:"/api/broadcast-links/fixture/[fixtureId]",filename:"route",bundlePath:"app/api/broadcast-links/fixture/[fixtureId]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/fixture/[fixtureId]/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:f,serverHooks:x,headerHooks:h,staticGenerationBailout:k}=d,g="/api/broadcast-links/fixture/[fixtureId]/route";function m(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>r(14997));module.exports=o})();