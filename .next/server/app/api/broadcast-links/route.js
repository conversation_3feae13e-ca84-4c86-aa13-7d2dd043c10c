"use strict";(()=>{var e={};e.id=3503,e.ids=[3503],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},26133:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>k});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>d,dynamic:()=>u});var a=r(95419),o=r(69108),n=r(99678),i=r(98984);let u="force-dynamic",c="http://localhost:3000";async function l(e){try{let{searchParams:t}=new URL(e.url),r=new URLSearchParams;t.forEach((e,t)=>{r.append(t,e)}),console.log("\uD83D\uDD04 Proxying broadcast links request:",`${c}/broadcast-links?${r.toString()}`);let s=await fetch(`${c}/broadcast-links?${r.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!s.ok)return console.error("❌ API Error:",s.status,s.statusText),i.NextResponse.json({error:"Failed to fetch broadcast links",status:s.status,message:s.statusText},{status:s.status});let a=await s.json();return console.log("✅ Broadcast links fetched successfully:",a.meta),i.NextResponse.json(a)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function d(e){try{let t=await e.json();console.log("\uD83D\uDD04 Proxying create broadcast link request:",t);let r=await fetch(`${c}/broadcast-links`,{method:"POST",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(t)});if(!r.ok){console.error("❌ API Error:",r.status,r.statusText);let e=await r.text();return i.NextResponse.json({error:"Failed to create broadcast link",status:r.status,message:r.statusText,details:e},{status:r.status})}let s=await r.json();return console.log("✅ Broadcast link created successfully:",s.data?.id),i.NextResponse.json(s)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/broadcast-links/route",pathname:"/api/broadcast-links",filename:"route",bundlePath:"app/api/broadcast-links/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:x,staticGenerationBailout:k}=p,y="/api/broadcast-links/route";function f(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(26133));module.exports=s})();