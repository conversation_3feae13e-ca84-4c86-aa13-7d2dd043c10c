"use strict";(()=>{var e={};e.id=5156,e.ids=[5156],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55304:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>w,originalPathname:()=>x,patchFetch:()=>N,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>j,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>m});var r={};o.r(r),o.d(r,{DELETE:()=>h,GET:()=>d,PATCH:()=>p,PUT:()=>l,dynamic:()=>c});var n=o(95419),s=o(69108),a=o(99678),i=o(98984);let c="force-dynamic",u="http://localhost:3000";async function d(e,{params:t}){try{let{id:o}=t,r=e.headers.get("authorization");if(console.log("\uD83D\uDD04 Fetching category with ID:",o,"Auth:",!!r),r){let e=await fetch(`${u}/admin/news/categories/${o}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(e.ok){let t=await e.json();return console.log("✅ Category fetched from admin endpoint"),i.NextResponse.json(t)}console.log("❌ Admin endpoint failed:",e.status)}let n=await fetch(`${u}/news/categories`,{method:"GET",headers:{"Content-Type":"application/json"}});if(n.ok){let e=(await n.json()).find(e=>e.id.toString()===o);if(e)return console.log("✅ Category found in public list:",e.slug),i.NextResponse.json(e)}return console.log("❌ Category not found in any endpoint"),i.NextResponse.json({error:"Category not found"},{status:404})}catch(e){return console.error("Error fetching category:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e,{params:t}){try{let{id:o}=t,r=e.headers.get("authorization");if(!r)return i.NextResponse.json({error:"Authorization required for category update"},{status:401});let n=await e.json(),s=await fetch(`${u}/admin/news/categories/${o}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify(n)});if(!s.ok)return i.NextResponse.json({error:"Failed to update category"},{status:s.status});let a=await s.json();return i.NextResponse.json(a)}catch(e){return console.error("Error updating category:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:t}){try{let{id:o}=t,r=e.headers.get("authorization");if(!r)return i.NextResponse.json({error:"Authorization required for category patch"},{status:401});let n=await e.json(),s=await fetch(`${u}/admin/news/categories/${o}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify(n)});if(!s.ok){let e=await s.text();return console.error("Backend error response:",e),i.NextResponse.json({error:"Failed to patch category",details:e},{status:s.status})}let a=await s.json();return i.NextResponse.json(a)}catch(e){return console.error("Error patching category:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:t}){try{let{id:o}=t,r=e.headers.get("authorization");if(!r)return i.NextResponse.json({error:"Authorization required for category deletion"},{status:401});let n=await fetch(`${u}/admin/news/categories/${o}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:r}});if(!n.ok)return i.NextResponse.json({error:"Failed to delete category"},{status:n.status});let s=null,a=n.headers.get("content-type");if(a&&a.includes("application/json"))try{s=await n.json()}catch(e){console.info("DELETE response has no JSON body, which is expected")}return i.NextResponse.json(s||{message:"Category deleted successfully"})}catch(e){return console.error("Error deleting category:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/news/categories/[id]/route",pathname:"/api/news/categories/[id]",filename:"route",bundlePath:"app/api/news/categories/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/news/categories/[id]/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:y,staticGenerationAsyncStorage:f,serverHooks:j,headerHooks:w,staticGenerationBailout:m}=g,x="/api/news/categories/[id]/route";function N(){return(0,a.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:f})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>o(55304));module.exports=r})();