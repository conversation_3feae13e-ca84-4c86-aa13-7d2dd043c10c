"use strict";(()=>{var e={};e.id=2482,e.ids=[2482],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},98601:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>y,patchFetch:()=>k,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>w,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>f});var o={};r.r(o),r.d(o,{GET:()=>d,POST:()=>p,dynamic:()=>c});var a=r(95419),s=r(69108),n=r(99678),i=r(98984);let c="force-dynamic",u="http://localhost:3000";async function d(e){try{console.info("Fetching news categories from backend");let t=new URL(e.url).searchParams.toString(),r=e.headers.get("authorization"),o=r?`/admin/news/categories${t?"?"+t:""}`:`/news/categories${t?"?"+t:""}`,a=`${u}${o}`,s=await fetch(a,{method:"GET",headers:{"Content-Type":"application/json",...r&&{Authorization:r}}});if(!s.ok){let e=await s.text();throw console.error("Backend error:",e),Error(`Backend responded with status ${s.status}: ${e}`)}let n=await s.json();return i.NextResponse.json(n)}catch(e){return console.error("Error fetching news categories:",e),i.NextResponse.json({message:"Failed to fetch news categories",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let t=await e.json(),r=e.headers.get("authorization");if(!r)return i.NextResponse.json({message:"Authorization required for category creation"},{status:401});let o=`${u}/admin/news/categories`,a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw console.error("Backend error:",e),Error(`Backend responded with status ${a.status}: ${e}`)}let s=await a.json();return i.NextResponse.json(s,{status:a.status})}catch(e){return console.error("Error creating news category:",e),i.NextResponse.json({message:"Failed to create news category",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/news/categories/route",pathname:"/api/news/categories",filename:"route",bundlePath:"app/api/news/categories/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/news/categories/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:l,serverHooks:w,headerHooks:m,staticGenerationBailout:f}=h,y="/api/news/categories/route";function k(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>r(98601));module.exports=o})();