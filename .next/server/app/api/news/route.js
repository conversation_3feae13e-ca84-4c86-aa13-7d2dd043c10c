"use strict";(()=>{var e={};e.id=7634,e.ids=[7634],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},40027:e=>{e.exports=require("node:console")},64819:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>w,routeModule:()=>l,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>k});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>h,dynamic:()=>c});var n=r(95419),o=r(69108),s=r(99678),i=r(98984),u=r(40027);let c="force-dynamic",d="http://localhost:3000";async function p(e){try{u.info("Fetching news list from backend");let t=new URL(e.url).searchParams.toString(),r=e.headers.get("authorization"),a=r?"admin/news/articles":"news",n=`${d}/${a}${t?"?"+t:""}`,o=await fetch(n,{method:"GET",headers:{"Content-Type":"application/json",...r&&{Authorization:r}}});if(!o.ok){let e=await o.text();throw u.error("Backend error:",e),Error(`Backend responded with status ${o.status}: ${e}`)}let s=await o.json();return i.NextResponse.json(s)}catch(e){return u.error("Error fetching news:",e),i.NextResponse.json({message:"Failed to fetch news",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function h(e){try{let t=await e.json(),r=`${d}/admin/news/articles`,a=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw u.error("Backend error:",e),Error(`Backend responded with status ${a.status}: ${e}`)}let n=await a.json();return i.NextResponse.json(n,{status:a.status})}catch(e){return u.error("Error creating news:",e),i.NextResponse.json({message:"Failed to create news",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/news/route",pathname:"/api/news",filename:"route",bundlePath:"app/api/news/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/news/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:g,staticGenerationBailout:k}=l,x="/api/news/route";function y(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2791],()=>r(64819));module.exports=a})();