"use strict";(()=>{var e={};e.id=9175,e.ids=[9175],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},40027:e=>{e.exports=require("node:console")},90984:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>$,originalPathname:()=>x,patchFetch:()=>k,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>E,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>j});var n={};r.r(n),r.d(n,{DELETE:()=>f,GET:()=>l,PATCH:()=>w,PUT:()=>p,dynamic:()=>u});var s=r(95419),a=r(69108),o=r(99678),i=r(98984),c=r(40027);async function d(e,t="Deleted successfully"){if(!e.ok){let t=await e.text();throw console.error("Backend error:",t),Error(`Backend responded with status ${e.status}: ${t}`)}let r=null,n=e.headers.get("content-type");if(n&&n.includes("application/json"))try{r=await e.json()}catch(e){console.info("DELETE response has no JSON body, which is expected")}return i.NextResponse.json(r||{message:t},{status:e.status||200})}let u="force-dynamic",h="http://localhost:3000";async function l(e,{params:t}){try{let r;let{id:n}=t;c.info(`Fetching news with id: ${n}`);let s=e.headers.get("authorization"),a={"Content-Type":"application/json"};s?(r=`${h}/admin/news/articles/${n}`,a.Authorization=s):r=`${h}/admin/news/articles/${n}`;let o=await fetch(r,{method:"GET",headers:a});if(!o.ok){let e=await o.text();throw c.error("Backend error:",e),Error(`Backend responded with status ${o.status}: ${e}`)}let d=await o.json();return i.NextResponse.json(d)}catch(e){return c.error("Error fetching news:",e),i.NextResponse.json({message:"Failed to fetch news",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=t,n=await e.json();c.info(`Updating news with id: ${r}`,n);let s=`${h}/admin/news/articles/${r}`,a=await fetch(s,{method:"PUT",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(n)});if(!a.ok){let e=await a.text();throw c.error("Backend error:",e),Error(`Backend responded with status ${a.status}: ${e}`)}let o=await a.json();return i.NextResponse.json(o,{status:a.status})}catch(e){return c.error("Error updating news:",e),i.NextResponse.json({message:"Failed to update news",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function w(e,{params:t}){try{let{id:r}=t,n=await e.json();c.info(`Patching news with id: ${r}`,n);let s=`${h}/admin/news/articles/${r}`,a=await fetch(s,{method:"PATCH",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(n)});if(!a.ok){let e=await a.text();throw c.error("Backend error:",e),Error(`Backend responded with status ${a.status}: ${e}`)}let o=await a.json();return i.NextResponse.json(o,{status:a.status})}catch(e){return c.error("Error patching news:",e),i.NextResponse.json({message:"Failed to patch news",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function f(e,{params:t}){try{let{id:r}=t;c.info(`Deleting news with id: ${r}`);let n=`${h}/admin/news/articles/${r}`,s=await fetch(n,{method:"DELETE",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});return await d(s,"News deleted successfully")}catch(e){return function(e,t="Internal server error",r=500){console.error("API Error:",e);let n=e instanceof Error?e.message:t;return i.NextResponse.json({message:t,error:n},{status:r})}(e,"Failed to delete news")}}let g=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/news/[id]/route",pathname:"/api/news/[id]",filename:"route",bundlePath:"app/api/news/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/news/[id]/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:E,headerHooks:$,staticGenerationBailout:j}=g,x="/api/news/[id]/route";function k(){return(0,o.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,2791],()=>r(90984));module.exports=n})();