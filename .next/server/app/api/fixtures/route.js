"use strict";(()=>{var e={};e.id=7110,e.ids=[7110],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},28460:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>y,patchFetch:()=>P,requestAsyncStorage:()=>h,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>p,dynamic:()=>i});var o=r(95419),a=r(69108),n=r(99678),u=r(98984);let i="force-dynamic",l="http://localhost:3000";async function c(e){try{let{searchParams:t}=new URL(e.url),r=new URLSearchParams;t.forEach((e,t)=>{r.append(t,e)}),console.log("\uD83D\uDD04 Proxying fixtures request:",`${l}/football/fixtures?${r.toString()}`);let s=await fetch(`${l}/football/fixtures?${r.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!s.ok)return console.error("❌ API Error:",s.status,s.statusText),u.NextResponse.json({error:"Failed to fetch fixtures",status:s.status,message:s.statusText},{status:s.status});let o=await s.json();return console.log("✅ Fixtures fetched successfully:",o.meta),u.NextResponse.json(o)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function p(e){try{let t=await e.json();console.log("\uD83D\uDD04 Proxying fixture create request:",`${l}/football/fixtures`);let r=await fetch(`${l}/football/fixtures`,{method:"POST",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(t)});if(!r.ok)return console.error("❌ API Error:",r.status,r.statusText),u.NextResponse.json({error:"Failed to create fixture",status:r.status,message:r.statusText},{status:r.status});let s=await r.json();return console.log("✅ Fixture created successfully:",s.data?.id),u.NextResponse.json(s)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/fixtures/route",pathname:"/api/fixtures",filename:"route",bundlePath:"app/api/fixtures/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:f,headerHooks:g,staticGenerationBailout:m}=d,y="/api/fixtures/route";function P(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:x})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(28460));module.exports=s})();