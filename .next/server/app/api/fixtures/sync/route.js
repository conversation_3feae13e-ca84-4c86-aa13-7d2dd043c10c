"use strict";(()=>{var e={};e.id=929,e.ids=[929],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5881:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>f,originalPathname:()=>m,patchFetch:()=>j,requestAsyncStorage:()=>d,routeModule:()=>y,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>x});var r={};s.r(r),s.d(r,{GET:()=>l,POST:()=>p,dynamic:()=>i});var o=s(95419),a=s(69108),n=s(99678),u=s(98984);let i="force-dynamic",c="http://localhost:3000";async function l(e){try{console.log("\uD83D\uDD04 Proxying sync status request:",`${c}/football/fixtures/sync/status`);let t=await fetch(`${c}/football/fixtures/sync/status`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!t.ok)return console.error("❌ Sync status API Error:",t.status,t.statusText),u.NextResponse.json({error:"Failed to fetch sync status",status:t.status,message:t.statusText},{status:t.status});let s=await t.json();return console.log("✅ Sync status fetched successfully"),u.NextResponse.json(s)}catch(e){return console.error("❌ Sync status proxy error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function p(e){try{let{type:t}=await e.json(),s="";if("daily"===t)s=`${c}/football/fixtures/sync/daily`;else{if("season"!==t)return u.NextResponse.json({error:'Invalid sync type. Must be "daily" or "season"'},{status:400});s=`${c}/football/fixtures/sync/fixtures`}console.log("\uD83D\uDD04 Proxying sync trigger request:",s);let r=await fetch(s,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!r.ok)return console.error("❌ Sync trigger API Error:",r.status,r.statusText),u.NextResponse.json({error:`Failed to trigger ${t} sync`,status:r.status,message:r.statusText},{status:r.status});let o=await r.json();return console.log(`✅ ${t} sync triggered successfully:`,o.message),u.NextResponse.json(o)}catch(e){return console.error("❌ Sync trigger proxy error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let y=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/fixtures/sync/route",pathname:"/api/fixtures/sync",filename:"route",bundlePath:"app/api/fixtures/sync/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/sync/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:f,staticGenerationBailout:x}=y,m="/api/fixtures/sync/route";function j(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>s(5881));module.exports=r})();