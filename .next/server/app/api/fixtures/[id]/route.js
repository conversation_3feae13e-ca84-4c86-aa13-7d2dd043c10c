"use strict";(()=>{var e={};e.id=9170,e.ids=[9170],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30598:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>y,originalPathname:()=>j,patchFetch:()=>P,requestAsyncStorage:()=>f,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>x,GET:()=>d,PUT:()=>c,dynamic:()=>i});var o=r(95419),a=r(69108),u=r(99678),n=r(98984);let i="force-dynamic",l="http://localhost:3000";async function d(e,{params:t}){try{let r=t.id;console.log("\uD83D\uDD04 Proxying fixture detail request:",`${l}/football/fixtures/${r}`);let s=await fetch(`${l}/football/fixtures/${r}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!s.ok)return console.error("❌ API Error:",s.status,s.statusText),n.NextResponse.json({error:"Failed to fetch fixture",status:s.status,message:s.statusText},{status:s.status});let o=await s.json();return console.log("✅ Fixture detail fetched successfully:",o.data?.id),n.NextResponse.json(o)}catch(e){return console.error("❌ Proxy Error:",e),n.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function c(e,{params:t}){try{let r=t.id,s=await e.json();console.log("\uD83D\uDD04 Proxying fixture update request:",`${l}/football/fixtures/${r}`);let o=await fetch(`${l}/football/fixtures/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(s)});if(!o.ok)return console.error("❌ API Error:",o.status,o.statusText),n.NextResponse.json({error:"Failed to update fixture",status:o.status,message:o.statusText},{status:o.status});let a=await o.json();return console.log("✅ Fixture updated successfully:",a.data?.id),n.NextResponse.json(a)}catch(e){return console.error("❌ Proxy Error:",e),n.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function x(e,{params:t}){try{let r=t.id;console.log("\uD83D\uDD04 Proxying fixture delete request:",`${l}/football/fixtures/${r}`);let s=await fetch(`${l}/football/fixtures/${r}`,{method:"DELETE",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!s.ok)return console.error("❌ API Error:",s.status,s.statusText),n.NextResponse.json({error:"Failed to delete fixture",status:s.status,message:s.statusText},{status:s.status});return console.log("✅ Fixture deleted successfully:",r),n.NextResponse.json({success:!0,message:"Fixture deleted successfully"})}catch(e){return console.error("❌ Proxy Error:",e),n.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/fixtures/[id]/route",pathname:"/api/fixtures/[id]",filename:"route",bundlePath:"app/api/fixtures/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/[id]/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:g,headerHooks:y,staticGenerationBailout:m}=p,j="/api/fixtures/[id]/route";function P(){return(0,u.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(30598));module.exports=s})();