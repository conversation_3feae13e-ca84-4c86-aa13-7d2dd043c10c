"use strict";(()=>{var e={};e.id=1238,e.ids=[1238],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},86485:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>x,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>v});var s={};r.r(s),r.d(s,{GET:()=>d,dynamic:()=>u});var o=r(95419),a=r(69108),n=r(99678),i=r(98984);let u="force-dynamic",l="http://localhost:3000";async function d(e,{params:t}){try{let r=t.id;console.log("\uD83D\uDD04 Proxying fixture events request:",`${l}/football/fixtures/events/${r}`);let s=await fetch(`${l}/football/fixtures/events/${r}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!s.ok)return console.error("❌ API Error:",s.status,s.statusText),i.NextResponse.json({error:"Failed to fetch fixture events",status:s.status,message:s.statusText},{status:s.status});let o=await s.json();return console.log("✅ Fixture events fetched successfully:",o.data?.events?.length||0,"events"),i.NextResponse.json(o)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/fixtures/[id]/events/route",pathname:"/api/fixtures/[id]/events",filename:"route",bundlePath:"app/api/fixtures/[id]/events/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/[id]/events/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:x,headerHooks:f,staticGenerationBailout:v}=c,g="/api/fixtures/[id]/events/route";function m(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:h})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(86485));module.exports=s})();