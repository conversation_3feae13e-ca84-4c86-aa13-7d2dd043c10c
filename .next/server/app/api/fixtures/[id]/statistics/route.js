"use strict";(()=>{var t={};t.id=2672,t.ids=[2672],t.modules={30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},74953:(t,e,s)=>{s.r(e),s.d(e,{headerHooks:()=>f,originalPathname:()=>g,patchFetch:()=>y,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var r={};s.r(r),s.d(r,{GET:()=>l,dynamic:()=>n});var a=s(95419),i=s(69108),o=s(99678),u=s(98984);let n="force-dynamic",c="http://localhost:3000";async function l(t,{params:e}){try{let s=e.id;console.log("\uD83D\uDD04 Proxying fixture statistics request:",`${c}/football/fixtures/statistics/${s}`);let r=await fetch(`${c}/football/fixtures/statistics/${s}`,{method:"GET",headers:{"Content-Type":"application/json",...t.headers.get("authorization")&&{Authorization:t.headers.get("authorization")}}});if(!r.ok)return console.error("❌ API Error:",r.status,r.statusText),u.NextResponse.json({error:"Failed to fetch fixture statistics",status:r.status,message:r.statusText},{status:r.status});let a=await r.json();return console.log("✅ Fixture statistics fetched successfully:",a.data?.length||0,"teams"),u.NextResponse.json(a)}catch(t){return console.error("❌ Proxy Error:",t),u.NextResponse.json({error:"Internal server error",message:t.message},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/fixtures/[id]/statistics/route",pathname:"/api/fixtures/[id]/statistics",filename:"route",bundlePath:"app/api/fixtures/[id]/statistics/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/[id]/statistics/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:x,headerHooks:f,staticGenerationBailout:m}=d,g="/api/fixtures/[id]/statistics/route";function y(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:h})}}};var e=require("../../../../../webpack-runtime.js");e.C(t);var s=t=>e(e.s=t),r=e.X(0,[1638,2791],()=>s(74953));module.exports=r})();