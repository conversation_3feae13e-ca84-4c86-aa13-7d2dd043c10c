"use strict";(()=>{var e={};e.id=7873,e.ids=[7873],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55126:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var o={};r.r(o),r.d(o,{GET:()=>c,dynamic:()=>u});var s=r(95419),a=r(69108),i=r(99678),n=r(98984);let u="force-dynamic",l="http://localhost:3000";async function c(e){try{let{searchParams:t}=new URL(e.url),r=new URLSearchParams;t.forEach((e,t)=>{r.append(t,e)}),console.log("\uD83D\uDD04 Proxying live fixtures request:",`${l}/football/fixtures/upcoming-and-live?${r.toString()}`);let o=await fetch(`${l}/football/fixtures/upcoming-and-live?${r.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!o.ok)return console.error("❌ API Error:",o.status,o.statusText),n.NextResponse.json({error:"Failed to fetch live fixtures",status:o.status,message:o.statusText},{status:o.status});let s=await o.json();return console.log("✅ Live fixtures fetched successfully:",s.meta),n.NextResponse.json(s)}catch(e){return console.error("❌ Proxy Error:",e),n.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/fixtures/live/route",pathname:"/api/fixtures/live",filename:"route",bundlePath:"app/api/fixtures/live/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/fixtures/live/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:f,headerHooks:x,staticGenerationBailout:m}=p,v="/api/fixtures/live/route";function g(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>r(55126));module.exports=o})();