"use strict";(()=>{var e={};e.id=651,e.ids=[651],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29220:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>y,originalPathname:()=>j,patchFetch:()=>P,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>m});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>d,PATCH:()=>c,dynamic:()=>l});var a=s(95419),o=s(69108),n=s(99678),u=s(98984);let l="force-dynamic",i="http://localhost:3000";async function d(e,{params:t}){try{let s,r;let{searchParams:a}=new URL(e.url),o=t.id;if(o.includes("-")){let[e,t]=o.split("-");s=e,r=t}else s=o;let n=new URLSearchParams;a.forEach((e,t)=>{n.append(t,e)}),r&&!n.has("season")&&n.append("season",r);let l=n.toString(),d=`${i}/football/leagues/${s}${l?`?${l}`:""}`;console.log("\uD83D\uDD04 Proxying league detail request:",d);let c=await fetch(d,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!c.ok)return console.error("❌ API Error:",c.status,c.statusText),u.NextResponse.json({error:"Failed to fetch league",status:c.status,message:c.statusText},{status:c.status});let p=await c.json();if(console.log("✅ League detail fetched successfully:",p),p.data&&Array.isArray(p.data)){let e=p.data;if(r){let t=parseInt(r),s=e.find(e=>e.season===t);if(s)return u.NextResponse.json(s);return u.NextResponse.json({error:`No league data found for season ${r}`},{status:404})}let t=e.find(e=>e.season_detail?.current===!0);if(!t&&e.length>0&&(t=e.reduce((e,t)=>t.season>e.season?t:e)),t)return u.NextResponse.json(t);return u.NextResponse.json({error:"No league data found"},{status:404})}return u.NextResponse.json(p)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function c(e,{params:t}){try{let s;let r=t.id,a=await e.json();s=r.includes("-")?r.split("-")[0]:r,console.log("\uD83D\uDD04 Proxying league update request to internal ID:",s);let o=await fetch(`${i}/football/leagues/${s}`,{method:"PATCH",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}},body:JSON.stringify(a)});if(!o.ok)return console.error("❌ API Error:",o.status,o.statusText),u.NextResponse.json({error:"Failed to update league",status:o.status,message:o.statusText},{status:o.status});let n=await o.json();return console.log("✅ League updated successfully:",n.id||n.externalId),u.NextResponse.json(n)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}async function p(e,{params:t}){try{let s=t.id,r=s.includes("-")?s.split("-")[0]:s;console.log("\uD83D\uDD04 Proxying league delete request:",r);let a=await fetch(`${i}/football/leagues/${r}`,{method:"DELETE",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!a.ok)return console.error("❌ API Error:",a.status,a.statusText),u.NextResponse.json({error:"Failed to delete league",status:a.status,message:a.statusText},{status:a.status});return console.log("✅ League deleted successfully:",r),u.NextResponse.json({success:!0})}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leagues/[id]/route",pathname:"/api/leagues/[id]",filename:"route",bundlePath:"app/api/leagues/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/leagues/[id]/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:f,serverHooks:x,headerHooks:y,staticGenerationBailout:m}=g,j="/api/leagues/[id]/route";function P(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>s(29220));module.exports=r})();