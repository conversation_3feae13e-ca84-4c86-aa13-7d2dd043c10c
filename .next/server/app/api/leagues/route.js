"use strict";(()=>{var e={};e.id=2115,e.ids=[2115],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60606:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{GET:()=>c,dynamic:()=>i});var s=r(95419),o=r(69108),n=r(99678),u=r(98984);let i="force-dynamic",l="http://localhost:3000";async function c(e){try{let{searchParams:t}=new URL(e.url),r=new URLSearchParams;t.forEach((e,t)=>{r.append(t,e)}),console.log("\uD83D\uDD04 Proxying leagues request:",`${l}/football/leagues?${r.toString()}`);let a=await fetch(`${l}/football/leagues?${r.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!a.ok)return console.error("❌ API Error:",a.status,a.statusText),u.NextResponse.json({error:"Failed to fetch leagues",status:a.status,message:a.statusText},{status:a.status});let s=await a.json();return console.log("✅ Leagues fetched successfully:",s.meta),u.NextResponse.json(s)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leagues/route",pathname:"/api/leagues",filename:"route",bundlePath:"app/api/leagues/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/leagues/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:m,staticGenerationBailout:f}=p,x="/api/leagues/route";function y(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2791],()=>r(60606));module.exports=a})();