"use strict";(()=>{var e={};e.id=9621,e.ids=[9621],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},20556:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,dynamic:()=>i});var a=r(95419),o=r(69108),n=r(99678),u=r(98984);let i="force-dynamic";async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("season"),s=t.get("newdb");if(!r)return u.NextResponse.json({error:"Season parameter is required"},{status:400});let a=process.env.BACKEND_URL||"http://localhost:3000",o=new URL(`${a}/football/leagues`);o.searchParams.set("season",r),"true"===s&&o.searchParams.set("newdb","true"),console.log("\uD83D\uDD17 Syncing leagues from:",o.toString());let n=await fetch(o.toString(),{method:"GET",headers:{"Content-Type":"application/json"}});if(!n.ok)throw Error(`Backend API error: ${n.status} ${n.statusText}`);let i=await n.json();return console.log("✅ Leagues sync successful:",i?.response?.length||0,"leagues"),u.NextResponse.json(i)}catch(e){return console.error("❌ Leagues sync error:",e),u.NextResponse.json({error:"Failed to sync leagues",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/leagues/sync/route",pathname:"/api/leagues/sync",filename:"route",bundlePath:"app/api/leagues/sync/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/leagues/sync/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:g,headerHooks:h,staticGenerationBailout:m}=l,y="/api/leagues/sync/route";function f(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(20556));module.exports=s})();