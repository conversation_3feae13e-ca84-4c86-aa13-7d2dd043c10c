"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leagues/sync/route";
exports.ids = ["app/api/leagues/sync/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Fsync%2Froute&page=%2Fapi%2Fleagues%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Fsync%2Froute&page=%2Fapi%2Fleagues%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_leagues_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/leagues/sync/route.ts */ \"(rsc)/./src/app/api/leagues/sync/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leagues/sync/route\",\n        pathname: \"/api/leagues/sync\",\n        filename: \"route\",\n        bundlePath: \"app/api/leagues/sync/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/leagues/sync/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_leagues_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/leagues/sync/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Fsync%2Froute&page=%2Fapi%2Fleagues%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/leagues/sync/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/leagues/sync/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const season = searchParams.get(\"season\");\n        const newdb = searchParams.get(\"newdb\");\n        if (!season) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Season parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Build the backend API URL\n        const backendUrl = process.env.BACKEND_URL || \"http://localhost:3000\";\n        const apiUrl = new URL(`${backendUrl}/football/leagues`);\n        // Add query parameters\n        apiUrl.searchParams.set(\"season\", season);\n        if (newdb === \"true\") {\n            apiUrl.searchParams.set(\"newdb\", \"true\");\n        }\n        console.log(\"\\uD83D\\uDD17 Syncing leagues from:\", apiUrl.toString());\n        // Make request to backend API\n        const response = await fetch(apiUrl.toString(), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Backend API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log(\"✅ Leagues sync successful:\", data?.response?.length || 0, \"leagues\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Leagues sync error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to sync leagues\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/leagues/sync/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Fsync%2Froute&page=%2Fapi%2Fleagues%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();