"use strict";(()=>{var e={};e.id=418,e.ids=[418],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27075:(e,s,a)=>{a.r(s),a.d(s,{headerHooks:()=>P,originalPathname:()=>h,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>g});var t={};a.r(t),a.d(t,{POST:()=>i,dynamic:()=>u});var r=a(95419),o=a(69108),n=a(99678),l=a(98984);let u="force-dynamic";async function i(e){try{let{leagueId:s,season:a}=await e.json();if(!s||!a)return l.NextResponse.json({error:"League ID and season are required"},{status:400});console.log("\uD83D\uDD04 Starting players sync for league:",s,"season:",a);let t=`http://localhost:3000/football/players/sync?league=${s}&season=${a}&limit=100&page=1`;console.log("\uD83D\uDD04 Calling players sync endpoint:",t);let r=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!r.ok)return console.error("❌ Players Sync API Error:",r.status,r.statusText),l.NextResponse.json({error:"Failed to sync players",status:r.status,message:r.statusText},{status:r.status});let o=await r.json();console.log("✅ Players sync completed successfully:",{status:o.status,totalPlayers:o.data?.totalPlayers||0,newPlayers:o.data?.newPlayers||0,updatedPlayers:o.data?.updatedPlayers||0,leagueId:s,season:a});let n={success:"success"===o.status,count:o.data?.totalPlayers||0,newPlayers:o.data?.newPlayers||0,updatedPlayers:o.data?.updatedPlayers||0,leagueId:s,season:a,message:o.message||`Successfully synced ${o.data?.totalPlayers||0} players`,details:o.data};return l.NextResponse.json(n)}catch(e){return console.error("❌ Players sync proxy error:",e),l.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let c=new r.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/players/sync/route",pathname:"/api/players/sync",filename:"route",bundlePath:"app/api/players/sync/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/players/sync/route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:p,staticGenerationAsyncStorage:y,serverHooks:d,headerHooks:P,staticGenerationBailout:g}=c,h="/api/players/sync/route";function m(){return(0,n.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:y})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,2791],()=>a(27075));module.exports=t})();