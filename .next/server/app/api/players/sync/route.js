"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/players/sync/route";
exports.ids = ["app/api/players/sync/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fplayers%2Fsync%2Froute&page=%2Fapi%2Fplayers%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fplayers%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fplayers%2Fsync%2Froute&page=%2Fapi%2Fplayers%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fplayers%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_players_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/players/sync/route.ts */ \"(rsc)/./src/app/api/players/sync/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/players/sync/route\",\n        pathname: \"/api/players/sync\",\n        filename: \"route\",\n        bundlePath: \"app/api/players/sync/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/players/sync/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_players_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/players/sync/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fplayers%2Fsync%2Froute&page=%2Fapi%2Fplayers%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fplayers%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/players/sync/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/players/sync/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { leagueId, season } = body;\n        // Validate required parameters\n        if (!leagueId || !season) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"League ID and season are required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"\\uD83D\\uDD04 Starting players sync for league:\", leagueId, \"season:\", season);\n        // Fetch all players from all pages\n        let allPlayers = [];\n        let currentPage = 1;\n        let totalPages = 1;\n        let totalItems = 0;\n        do {\n            const url = `${API_BASE_URL}/football/players/sync?league=${leagueId}&season=${season}&limit=50&page=${currentPage}`;\n            console.log(\"\\uD83D\\uDD04 Fetching players page:\", currentPage, \"URL:\", url);\n            const response = await fetch(url, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    ...request.headers.get(\"authorization\") && {\n                        \"Authorization\": request.headers.get(\"authorization\")\n                    }\n                }\n            });\n            if (!response.ok) {\n                console.error(\"❌ Players API Error:\", response.status, response.statusText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to fetch players\",\n                    status: response.status,\n                    message: response.statusText\n                }, {\n                    status: response.status\n                });\n            }\n            const pageData = await response.json();\n            // Extract players from current page\n            if (pageData.data && Array.isArray(pageData.data)) {\n                allPlayers = allPlayers.concat(pageData.data);\n            }\n            // Update pagination info\n            if (pageData.meta) {\n                totalPages = pageData.meta.totalPages || 1;\n                totalItems = pageData.meta.totalItems || 0;\n            }\n            console.log(`✅ Page ${currentPage}/${totalPages} fetched: ${pageData.data?.length || 0} players`);\n            currentPage++;\n        }while (currentPage <= totalPages);\n        console.log(\"✅ Players sync completed successfully:\", {\n            totalPlayers: allPlayers.length,\n            totalPages: totalPages,\n            leagueId,\n            season\n        });\n        // Return summary of sync operation\n        const syncResult = {\n            success: true,\n            count: allPlayers.length,\n            totalPages: totalPages,\n            leagueId,\n            season,\n            players: allPlayers,\n            message: `Successfully synced ${allPlayers.length} players from ${totalPages} pages`\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(syncResult);\n    } catch (error) {\n        console.error(\"❌ Players sync proxy error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9wbGF5ZXJzL3N5bmMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdEO0FBRWpELE1BQU1DLFVBQVUsZ0JBQWdCO0FBRXZDLE1BQU1DLGVBQWVDLHVCQUFvQyxJQUFJO0FBRXRELGVBQWVHLEtBQUtDLE9BQW9CO0lBQ3ZDLElBQUk7UUFDSSxNQUFNQyxPQUFPLE1BQU1ELFFBQVFFLElBQUk7UUFDL0IsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRSxHQUFHSDtRQUU3QiwrQkFBK0I7UUFDL0IsSUFBSSxDQUFDRSxZQUFZLENBQUNDLFFBQVE7WUFDbEIsT0FBT1gscURBQVlBLENBQUNTLElBQUksQ0FDaEI7Z0JBQUVHLE9BQU87WUFBb0MsR0FDN0M7Z0JBQUVDLFFBQVE7WUFBSTtRQUU5QjtRQUVBQyxRQUFRQyxHQUFHLENBQUMsa0RBQXdDTCxVQUFVLFdBQVdDO1FBRXpFLG1DQUFtQztRQUNuQyxJQUFJSyxhQUFvQixFQUFFO1FBQzFCLElBQUlDLGNBQWM7UUFDbEIsSUFBSUMsYUFBYTtRQUNqQixJQUFJQyxhQUFhO1FBRWpCLEdBQUc7WUFDSyxNQUFNQyxNQUFNLENBQUMsRUFBRWxCLGFBQWEsOEJBQThCLEVBQUVRLFNBQVMsUUFBUSxFQUFFQyxPQUFPLGVBQWUsRUFBRU0sWUFBWSxDQUFDO1lBQ3BISCxRQUFRQyxHQUFHLENBQUMsdUNBQTZCRSxhQUFhLFFBQVFHO1lBRTlELE1BQU1DLFdBQVcsTUFBTUMsTUFBTUYsS0FBSztnQkFDMUJHLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ0QsZ0JBQWdCO29CQUNoQixHQUFJakIsUUFBUWlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQjt3QkFDcEMsaUJBQWlCbEIsUUFBUWlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO29CQUM3QyxDQUFDO2dCQUNUO1lBQ1I7WUFFQSxJQUFJLENBQUNKLFNBQVNLLEVBQUUsRUFBRTtnQkFDVlosUUFBUUYsS0FBSyxDQUFDLHdCQUF3QlMsU0FBU1IsTUFBTSxFQUFFUSxTQUFTTSxVQUFVO2dCQUMxRSxPQUFPM0IscURBQVlBLENBQUNTLElBQUksQ0FDaEI7b0JBQ1FHLE9BQU87b0JBQ1BDLFFBQVFRLFNBQVNSLE1BQU07b0JBQ3ZCZSxTQUFTUCxTQUFTTSxVQUFVO2dCQUNwQyxHQUNBO29CQUFFZCxRQUFRUSxTQUFTUixNQUFNO2dCQUFDO1lBRTFDO1lBRUEsTUFBTWdCLFdBQVcsTUFBTVIsU0FBU1osSUFBSTtZQUVwQyxvQ0FBb0M7WUFDcEMsSUFBSW9CLFNBQVNDLElBQUksSUFBSUMsTUFBTUMsT0FBTyxDQUFDSCxTQUFTQyxJQUFJLEdBQUc7Z0JBQzNDZCxhQUFhQSxXQUFXaUIsTUFBTSxDQUFDSixTQUFTQyxJQUFJO1lBQ3BEO1lBRUEseUJBQXlCO1lBQ3pCLElBQUlELFNBQVNLLElBQUksRUFBRTtnQkFDWGhCLGFBQWFXLFNBQVNLLElBQUksQ0FBQ2hCLFVBQVUsSUFBSTtnQkFDekNDLGFBQWFVLFNBQVNLLElBQUksQ0FBQ2YsVUFBVSxJQUFJO1lBQ2pEO1lBRUFMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLE9BQU8sRUFBRUUsWUFBWSxDQUFDLEVBQUVDLFdBQVcsVUFBVSxFQUFFVyxTQUFTQyxJQUFJLEVBQUVLLFVBQVUsRUFBRSxRQUFRLENBQUM7WUFDaEdsQjtRQUVSLFFBQVNBLGVBQWVDLFlBQVk7UUFFcENKLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEM7WUFDOUNxQixjQUFjcEIsV0FBV21CLE1BQU07WUFDL0JqQixZQUFZQTtZQUNaUjtZQUNBQztRQUNSO1FBRUEsbUNBQW1DO1FBQ25DLE1BQU0wQixhQUFhO1lBQ1hDLFNBQVM7WUFDVEMsT0FBT3ZCLFdBQVdtQixNQUFNO1lBQ3hCakIsWUFBWUE7WUFDWlI7WUFDQUM7WUFDQTZCLFNBQVN4QjtZQUNUWSxTQUFTLENBQUMsb0JBQW9CLEVBQUVaLFdBQVdtQixNQUFNLENBQUMsY0FBYyxFQUFFakIsV0FBVyxNQUFNLENBQUM7UUFDNUY7UUFFQSxPQUFPbEIscURBQVlBLENBQUNTLElBQUksQ0FBQzRCO0lBQ2pDLEVBQUUsT0FBT3pCLE9BQVk7UUFDYkUsUUFBUUYsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBT1oscURBQVlBLENBQUNTLElBQUksQ0FDaEI7WUFDUUcsT0FBTztZQUNQZ0IsU0FBU2hCLE1BQU1nQixPQUFPO1FBQzlCLEdBQ0E7WUFBRWYsUUFBUTtRQUFJO0lBRTlCO0FBQ1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbXMtYXBpc3BvcnRzZ2FtZS8uL3NyYy9hcHAvYXBpL3BsYXllcnMvc3luYy9yb3V0ZS50cz8xZjQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5cbmV4cG9ydCBjb25zdCBkeW5hbWljID0gJ2ZvcmNlLWR5bmFtaWMnO1xuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgbGVhZ3VlSWQsIHNlYXNvbiB9ID0gYm9keTtcblxuICAgICAgICAgICAgICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIHBhcmFtZXRlcnNcbiAgICAgICAgICAgICAgICBpZiAoIWxlYWd1ZUlkIHx8ICFzZWFzb24pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBlcnJvcjogJ0xlYWd1ZSBJRCBhbmQgc2Vhc29uIGFyZSByZXF1aXJlZCcgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFN0YXJ0aW5nIHBsYXllcnMgc3luYyBmb3IgbGVhZ3VlOicsIGxlYWd1ZUlkLCAnc2Vhc29uOicsIHNlYXNvbik7XG5cbiAgICAgICAgICAgICAgICAvLyBGZXRjaCBhbGwgcGxheWVycyBmcm9tIGFsbCBwYWdlc1xuICAgICAgICAgICAgICAgIGxldCBhbGxQbGF5ZXJzOiBhbnlbXSA9IFtdO1xuICAgICAgICAgICAgICAgIGxldCBjdXJyZW50UGFnZSA9IDE7XG4gICAgICAgICAgICAgICAgbGV0IHRvdGFsUGFnZXMgPSAxO1xuICAgICAgICAgICAgICAgIGxldCB0b3RhbEl0ZW1zID0gMDtcblxuICAgICAgICAgICAgICAgIGRvIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVybCA9IGAke0FQSV9CQVNFX1VSTH0vZm9vdGJhbGwvcGxheWVycy9zeW5jP2xlYWd1ZT0ke2xlYWd1ZUlkfSZzZWFzb249JHtzZWFzb259JmxpbWl0PTUwJnBhZ2U9JHtjdXJyZW50UGFnZX1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgRmV0Y2hpbmcgcGxheWVycyBwYWdlOicsIGN1cnJlbnRQYWdlLCAnVVJMOicsIHVybCk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLihyZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJykgJiYge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJykhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFBsYXllcnMgQVBJIEVycm9yOicsIHJlc3BvbnNlLnN0YXR1cywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBwbGF5ZXJzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2Uuc3RhdHVzVGV4dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhZ2VEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBFeHRyYWN0IHBsYXllcnMgZnJvbSBjdXJyZW50IHBhZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwYWdlRGF0YS5kYXRhICYmIEFycmF5LmlzQXJyYXkocGFnZURhdGEuZGF0YSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxsUGxheWVycyA9IGFsbFBsYXllcnMuY29uY2F0KHBhZ2VEYXRhLmRhdGEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgcGFnaW5hdGlvbiBpbmZvXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFnZURhdGEubWV0YSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3RhbFBhZ2VzID0gcGFnZURhdGEubWV0YS50b3RhbFBhZ2VzIHx8IDE7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvdGFsSXRlbXMgPSBwYWdlRGF0YS5tZXRhLnRvdGFsSXRlbXMgfHwgMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBQYWdlICR7Y3VycmVudFBhZ2V9LyR7dG90YWxQYWdlc30gZmV0Y2hlZDogJHtwYWdlRGF0YS5kYXRhPy5sZW5ndGggfHwgMH0gcGxheWVyc2ApO1xuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFBhZ2UrKztcblxuICAgICAgICAgICAgICAgIH0gd2hpbGUgKGN1cnJlbnRQYWdlIDw9IHRvdGFsUGFnZXMpO1xuXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBQbGF5ZXJzIHN5bmMgY29tcGxldGVkIHN1Y2Nlc3NmdWxseTonLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0b3RhbFBsYXllcnM6IGFsbFBsYXllcnMubGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICAgICAgdG90YWxQYWdlczogdG90YWxQYWdlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxlYWd1ZUlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2Vhc29uXG4gICAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgICAvLyBSZXR1cm4gc3VtbWFyeSBvZiBzeW5jIG9wZXJhdGlvblxuICAgICAgICAgICAgICAgIGNvbnN0IHN5bmNSZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY291bnQ6IGFsbFBsYXllcnMubGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICAgICAgdG90YWxQYWdlczogdG90YWxQYWdlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxlYWd1ZUlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2Vhc29uLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGxheWVyczogYWxsUGxheWVycywgLy8gSW5jbHVkZSBhbGwgcGxheWVycyBkYXRhXG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgU3VjY2Vzc2Z1bGx5IHN5bmNlZCAke2FsbFBsYXllcnMubGVuZ3RofSBwbGF5ZXJzIGZyb20gJHt0b3RhbFBhZ2VzfSBwYWdlc2BcbiAgICAgICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHN5bmNSZXN1bHQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFBsYXllcnMgc3luYyBwcm94eSBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICAgICAgICAgICApO1xuICAgICAgICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZHluYW1pYyIsIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwiLCJQT1NUIiwicmVxdWVzdCIsImJvZHkiLCJqc29uIiwibGVhZ3VlSWQiLCJzZWFzb24iLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiLCJsb2ciLCJhbGxQbGF5ZXJzIiwiY3VycmVudFBhZ2UiLCJ0b3RhbFBhZ2VzIiwidG90YWxJdGVtcyIsInVybCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiZ2V0Iiwib2siLCJzdGF0dXNUZXh0IiwibWVzc2FnZSIsInBhZ2VEYXRhIiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsImNvbmNhdCIsIm1ldGEiLCJsZW5ndGgiLCJ0b3RhbFBsYXllcnMiLCJzeW5jUmVzdWx0Iiwic3VjY2VzcyIsImNvdW50IiwicGxheWVycyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/players/sync/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fplayers%2Fsync%2Froute&page=%2Fapi%2Fplayers%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fplayers%2Fsync%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();