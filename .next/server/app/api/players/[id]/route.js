"use strict";(()=>{var e={};e.id=7353,e.ids=[7353],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},43292:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>y,originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>m});var a={};r.r(a),r.d(a,{GET:()=>l,dynamic:()=>u});var o=r(95419),s=r(69108),n=r(99678),i=r(98984);let u="force-dynamic";async function l(e,{params:t}){try{let r=t.id,{searchParams:a}=new URL(e.url),o=new URLSearchParams;a.forEach((e,t)=>{o.append(t,e)});let s=o.toString(),n=`http://localhost:3000/football/players/${r}${s?`?${s}`:""}`;console.log("\uD83D\uDD04 Proxying player detail request:",n);let u=await fetch(n,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!u.ok)return console.error("❌ API Error:",u.status,u.statusText),i.NextResponse.json({error:"Failed to fetch player",status:u.status,message:u.statusText},{status:u.status});let l=await u.json();return console.log("✅ Player detail fetched successfully:",l.name||`Player ID: ${r}`),i.NextResponse.json(l)}catch(e){return console.error("❌ Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/players/[id]/route",pathname:"/api/players/[id]",filename:"route",bundlePath:"app/api/players/[id]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/players/[id]/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:h,headerHooks:y,staticGenerationBailout:m}=p,g="/api/players/[id]/route";function f(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:c})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2791],()=>r(43292));module.exports=a})();