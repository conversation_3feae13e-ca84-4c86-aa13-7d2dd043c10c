"use strict";(()=>{var e={};e.id=1838,e.ids=[1838],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},72506:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p,dynamic:()=>i});var s=r(95419),o=r(69108),n=r(99678),u=r(98984);let i="force-dynamic",l="http://localhost:3000";async function p(e){try{let{searchParams:t}=new URL(e.url),r=new URLSearchParams;t.forEach((e,t)=>{r.append(t,e)}),console.log("\uD83D\uDD04 Proxying players request:",`${l}/football/players?${r.toString()}`);let a=await fetch(`${l}/football/players?${r.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!a.ok)return console.error("❌ API Error:",a.status,a.statusText),u.NextResponse.json({error:"Failed to fetch players",status:a.status,message:a.statusText},{status:a.status});let s=await a.json();return console.log("✅ Players fetched successfully:",s.meta||`${s.data?.length||0} players`),u.NextResponse.json(s)}catch(e){return console.error("❌ Proxy Error:",e),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/players/route",pathname:"/api/players",filename:"route",bundlePath:"app/api/players/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/players/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:y,headerHooks:g,staticGenerationBailout:m}=c,f="/api/players/route";function x(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2791],()=>r(72506));module.exports=a})();