"use strict";(()=>{var e={};e.id=5575,e.ids=[5575],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93651:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>m,originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>g});var o={};t.r(o),t.d(o,{GET:()=>c,dynamic:()=>u});var s=t(95419),a=t(69108),n=t(99678),p=t(98984);let u="force-dynamic",i="http://localhost:3000";async function c(e){try{let{searchParams:r}=new URL(e.url),t=new URLSearchParams;r.forEach((e,r)=>{t.append(r,e)}),console.log("\uD83D\uDD04 Proxying topscorers request:",`${i}/football/players/topscorers?${t.toString()}`);let o=await fetch(`${i}/football/players/topscorers?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers.get("authorization")&&{Authorization:e.headers.get("authorization")}}});if(!o.ok)return console.error("❌ API Error:",o.status,o.statusText),p.NextResponse.json({error:"Failed to fetch top scorers",status:o.status,message:o.statusText},{status:o.status});let s=await o.json();return console.log("✅ Top scorers fetched successfully:",s.meta),p.NextResponse.json(s)}catch(e){return console.error("❌ Top scorers proxy error:",e),p.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/players/topscorers/route",pathname:"/api/players/topscorers",filename:"route",bundlePath:"app/api/players/topscorers/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/players/topscorers/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:y,headerHooks:m,staticGenerationBailout:g}=l,f="/api/players/topscorers/route";function x(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638,2791],()=>t(93651));module.exports=o})();