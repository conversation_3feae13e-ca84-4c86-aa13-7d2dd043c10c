"use strict";(()=>{var e={};e.id=1495,e.ids=[1495],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44701:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>m,patchFetch:()=>P,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>d,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>g});var o={};r.r(o),r.d(o,{GET:()=>l,dynamic:()=>u});var s=r(95419),a=r(69108),n=r(99678),i=r(98984);let u="force-dynamic";async function l(e){try{let t=e.headers.get("authorization");console.log("\uD83D\uDD04 Proxying profile request, auth:",t?"Present":"Missing"),console.log("\uD83D\uDD11 Profile token preview:",t?t.substring(0,30)+"...":"No token");let r=await fetch("http://localhost:3000/system-auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:t}}});if(!r.ok)return console.error("❌ Profile API Error:",r.status,r.statusText),i.NextResponse.json({error:"Failed to fetch profile",status:r.status,message:r.statusText},{status:r.status});let o=await r.json();return console.log("✅ Profile fetched successfully"),i.NextResponse.json(o)}catch(e){return console.error("❌ Profile Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/profile/route",pathname:"/api/auth/profile",filename:"route",bundlePath:"app/api/auth/profile/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/auth/profile/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:h,serverHooks:d,headerHooks:f,staticGenerationBailout:g}=p,m="/api/auth/profile/route";function P(){return(0,n.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>r(44701));module.exports=o})();