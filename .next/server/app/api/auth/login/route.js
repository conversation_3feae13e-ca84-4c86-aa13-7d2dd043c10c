"use strict";(()=>{var e={};e.id=8873,e.ids=[8873],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},37521:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>m});var o={};r.r(o),r.d(o,{POST:()=>l,dynamic:()=>u});var s=r(95419),a=r(69108),n=r(99678),i=r(98984);let u="force-dynamic";async function l(e){try{let t=await e.json();console.log("\uD83D\uDD04 Proxying login request:",t.username);let r=await fetch("http://localhost:3000/system-auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){console.error("❌ Login API Error:",r.status,r.statusText);let e=await r.text();return i.NextResponse.json({error:"Login failed",status:r.status,message:r.statusText,details:e},{status:r.status})}let o=await r.json();return console.log("✅ Login successful for user:",t.username),i.NextResponse.json(o)}catch(e){return console.error("❌ Login Proxy Error:",e),i.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/auth/login/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:g,headerHooks:h,staticGenerationBailout:m}=p,x="/api/auth/login/route";function y(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>r(37521));module.exports=o})();