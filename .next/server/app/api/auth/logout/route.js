"use strict";(()=>{var t={};t.id=7716,t.ids=[7716],t.modules={30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57327:(t,e,o)=>{o.r(e),o.d(e,{headerHooks:()=>g,originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>d,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>m});var r={};o.r(r),o.d(r,{POST:()=>l,dynamic:()=>i});var s=o(95419),a=o(69108),u=o(99678),n=o(98984);let i="force-dynamic";async function l(t){try{let e=await t.json(),o=t.headers.get("authorization");console.log("\uD83D\uDD04 Proxying logout request");let r=await fetch("http://localhost:3000/system-auth/logout",{method:"POST",headers:{"Content-Type":"application/json",...o&&{Authorization:o}},body:JSON.stringify(e)});if(!r.ok)return console.error("❌ Logout API Error:",r.status,r.statusText),n.NextResponse.json({error:"Logout failed",status:r.status,message:r.statusText},{status:r.status});let s=await r.json();return console.log("✅ Logout successful"),n.NextResponse.json(s)}catch(t){return console.error("❌ Logout Proxy Error:",t),n.NextResponse.json({error:"Internal server error",message:t.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/auth/logout/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:c,staticGenerationAsyncStorage:h,serverHooks:d,headerHooks:g,staticGenerationBailout:m}=p,x="/api/auth/logout/route";function y(){return(0,u.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:h})}}};var e=require("../../../../webpack-runtime.js");e.C(t);var o=t=>e(e.s=t),r=e.X(0,[1638,2791],()=>o(57327));module.exports=r})();