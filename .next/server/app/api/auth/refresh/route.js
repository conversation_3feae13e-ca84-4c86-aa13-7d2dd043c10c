"use strict";(()=>{var e={};e.id=9575,e.ids=[9575],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},95690:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>x,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>d,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{POST:()=>h,dynamic:()=>i});var o=r(95419),a=r(69108),n=r(99678),u=r(98984);let i="force-dynamic";async function h(e){try{let t=await e.json();console.log("\uD83D\uDD04 Proxying refresh token request");let r=await fetch("http://localhost:3000/system-auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){console.error("❌ Refresh Token API Error:",r.status,r.statusText);let e=await r.text();return u.NextResponse.json({error:"Token refresh failed",status:r.status,message:r.statusText,details:e},{status:r.status})}let s=await r.json();return console.log("✅ Token refresh successful via proxy"),u.NextResponse.json(s)}catch(e){return console.error("❌ Refresh token proxy error:",e.message),u.NextResponse.json({error:"Internal server error",message:e.message},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/refresh/route",pathname:"/api/auth/refresh",filename:"route",bundlePath:"app/api/auth/refresh/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/auth/refresh/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:c,serverHooks:d,headerHooks:f,staticGenerationBailout:m}=p,x="/api/auth/refresh/route";function g(){return(0,n.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:c})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,2791],()=>r(95690));module.exports=s})();