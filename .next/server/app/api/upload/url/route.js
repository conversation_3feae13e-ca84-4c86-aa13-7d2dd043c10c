"use strict";(()=>{var e={};e.id=3935,e.ids=[3935],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},72707:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>g});var a={};r.r(a),r.d(a,{POST:()=>l,dynamic:()=>i});var o=r(95419),s=r(69108),n=r(99678),u=r(98984);let i="force-dynamic";async function l(e){try{console.info("\uD83D\uDD17 Proxying URL upload request to backend");let t=e.headers.get("authorization");if(!t)return console.error("❌ No authorization header found"),u.NextResponse.json({message:"Authorization header required"},{status:401});let r=await e.json();console.log("\uD83D\uDD17 URL Upload Details:",{imageUrl:r.imageUrl,category:r.category,description:r.description,filename:r.filename});let a=await fetch("http://localhost:3000/upload/url",{method:"POST",headers:{"Content-Type":"application/json",Authorization:t},body:JSON.stringify(r)});if(!a.ok){let e=await a.text();console.error("❌ Backend URL upload error:",{status:a.status,statusText:a.statusText,error:e});let t="URL upload failed";try{t=JSON.parse(e).message||t}catch{t=e||t}return u.NextResponse.json({message:t,status:a.status,details:e},{status:a.status})}let o=await a.json();return u.NextResponse.json(o)}catch(e){return console.error("❌ URL upload proxy error:",e),u.NextResponse.json({message:"Internal server error during URL upload",error:e.message},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/upload/url/route",pathname:"/api/upload/url",filename:"route",bundlePath:"app/api/upload/url/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/upload/url/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:h,headerHooks:m,staticGenerationBailout:g}=p,f="/api/upload/url/route";function x(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:c})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,2791],()=>r(72707));module.exports=a})();