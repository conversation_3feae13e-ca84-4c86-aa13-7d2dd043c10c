"use strict";(()=>{var e={};e.id=5998,e.ids=[5998],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},96903:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>h,originalPathname:()=>x,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>g});var r={};a.r(r),a.d(r,{GET:()=>l,dynamic:()=>i});var o=a(95419),s=a(69108),n=a(99678),u=a(98984);let i="force-dynamic";async function l(e){try{console.info("\uD83D\uDCCB Proxying get uploaded images request to backend");let t=e.headers.get("authorization");if(!t)return console.error("❌ No authorization header found"),u.NextResponse.json({message:"Authorization header required"},{status:401});let a=new URL(e.url).searchParams.toString();console.log("\uD83D\uDCCB List Images Params:",a);let r=await fetch(`http://localhost:3000/upload${a?"?"+a:""}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:t}});if(!r.ok){let e=await r.text();return console.error("❌ Backend list images error:",{status:r.status,statusText:r.statusText,error:e}),u.NextResponse.json({message:"Failed to fetch uploaded images",status:r.status,details:e},{status:r.status})}let o=await r.json();return console.log("✅ List images successful:",{totalItems:o.meta?.totalItems,currentPage:o.meta?.currentPage,itemsCount:o.data?.length}),u.NextResponse.json(o)}catch(e){return console.error("❌ List images proxy error:",e),u.NextResponse.json({message:"Internal server error while fetching images",error:e.message},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/upload/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:c,serverHooks:m,headerHooks:h,staticGenerationBailout:g}=d,x="/api/upload/route";function f(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>a(96903));module.exports=r})();