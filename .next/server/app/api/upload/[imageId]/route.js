"use strict";(()=>{var e={};e.id=8623,e.ids=[8623],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},65849:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>f,originalPathname:()=>y,patchFetch:()=>j,requestAsyncStorage:()=>g,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>x});var r={};a.r(r),a.d(r,{DELETE:()=>d,GET:()=>c,dynamic:()=>u});var s=a(95419),o=a(69108),i=a(99678),n=a(98984);let u="force-dynamic",l="http://localhost:3000";async function d(e,{params:t}){try{let{imageId:a}=t;console.info(`🗑️ Proxying delete image request for ID: ${a}`);let r=e.headers.get("authorization");if(!r)return console.error("❌ No authorization header found"),n.NextResponse.json({message:"Authorization header required"},{status:401});let s=await fetch(`${l}/upload/${a}`,{method:"DELETE",headers:{Authorization:r}});if(!s.ok){let e=await s.text();console.error("❌ Backend delete image error:",{imageId:a,status:s.status,statusText:s.statusText,error:e});let t="Failed to delete image";try{t=JSON.parse(e).message||t}catch{t=e||t}return n.NextResponse.json({message:t,status:s.status,details:e},{status:s.status})}return console.log("✅ Image deleted successfully:",a),new n.NextResponse(null,{status:204})}catch(e){return console.error("❌ Delete image proxy error:",e),n.NextResponse.json({message:"Internal server error while deleting image",error:e.message},{status:500})}}async function c(e,{params:t}){try{let{imageId:a}=t;console.info(`📷 Proxying get image details request for ID: ${a}`);let r=e.headers.get("authorization");if(!r)return console.error("❌ No authorization header found"),n.NextResponse.json({message:"Authorization header required"},{status:401});let s=await fetch(`${l}/upload/${a}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(!s.ok){let e=await s.text();return console.error("❌ Backend get image error:",{imageId:a,status:s.status,statusText:s.statusText,error:e}),n.NextResponse.json({message:"Failed to fetch image details",status:s.status,details:e},{status:s.status})}let o=await s.json();return console.log("✅ Get image details successful:",{id:o.data?.id,filename:o.data?.filename}),n.NextResponse.json(o)}catch(e){return console.error("❌ Get image details proxy error:",e),n.NextResponse.json({message:"Internal server error while fetching image details",error:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/upload/[imageId]/route",pathname:"/api/upload/[imageId]",filename:"route",bundlePath:"app/api/upload/[imageId]/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/upload/[imageId]/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:h,headerHooks:f,staticGenerationBailout:x}=p,y="/api/upload/[imageId]/route";function j(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,2791],()=>a(65849));module.exports=r})();