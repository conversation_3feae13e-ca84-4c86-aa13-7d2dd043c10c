"use strict";(()=>{var e={};e.id=4693,e.ids=[4693],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},86982:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>h,originalPathname:()=>k,patchFetch:()=>D,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var o={};a.r(o),a.d(o,{POST:()=>d,dynamic:()=>l});var s=a(95419),r=a(69108),n=a(99678),i=a(98984);let l="force-dynamic",u="http://localhost:3000";async function d(e){try{console.info("\uD83D\uDCE4 Proxying file upload request to backend");let t=e.headers.get("authorization");if(!t)return console.error("❌ No authorization header found"),i.NextResponse.json({message:"Authorization header required"},{status:401});let a=await e.formData(),o=a.get("file"),s=a.get("category"),r=a.get("description");if(console.log("\uD83D\uDCC1 Upload Details:",{fileName:o?.name,fileSize:o?.size,fileType:o?.type,category:s,description:r,authHeader:t?"Present":"Missing",backendUrl:`${u}/upload/file`}),!o)return console.error("❌ No file found in form data"),i.NextResponse.json({message:"No file provided"},{status:400});console.log("\uD83D\uDD0D Testing backend connectivity...");try{let e=await fetch(`${u}/health`,{method:"GET",signal:AbortSignal.timeout(5e3)});console.log("\uD83C\uDFE5 Backend health check:",{status:e.status,ok:e.ok})}catch(e){return console.error("❌ Backend connectivity issue:",e.message),i.NextResponse.json({message:"Backend service unavailable",details:`Cannot connect to backend at ${u}`,error:e.message},{status:503})}console.log("\uD83D\uDE80 Sending request to backend...");let n=await fetch(`${u}/upload/file`,{method:"POST",headers:{Authorization:t},body:a,signal:AbortSignal.timeout(3e4)});if(console.log("\uD83D\uDCE1 Backend response received:",{status:n.status,statusText:n.statusText,ok:n.ok,headers:Object.fromEntries(n.headers.entries())}),!n.ok){let e=await n.text();console.error("❌ Backend upload error:",{status:n.status,statusText:n.statusText,error:e,url:`${u}/upload/file`});let t="Upload failed",a=e;try{let o=JSON.parse(e);t=o.message||t,a=o}catch(a){console.warn("⚠️ Could not parse error response as JSON:",a),t=e||t}return i.NextResponse.json({message:t,status:n.status,details:a,backendUrl:`${u}/upload/file`},{status:n.status})}let l=await n.json();return i.NextResponse.json(l)}catch(a){console.error("❌ Upload proxy error:",{name:a.name,message:a.message,stack:a.stack,cause:a.cause});let e="Internal server error during upload",t=500;return"AbortError"===a.name?(e="Upload timeout - please try again with a smaller file",t=408):"TypeError"===a.name&&a.message.includes("fetch")&&(e="Cannot connect to backend service",t=503),i.NextResponse.json({message:e,error:a.message,type:a.name,backendUrl:`${u}/upload/file`,timestamp:new Date().toISOString()},{status:t})}}let c=new s.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/upload/file/route",pathname:"/api/upload/file",filename:"route",bundlePath:"app/api/upload/file/route"},resolvedPagePath:"/home/<USER>/FECMS-sport/src/app/api/upload/file/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:g,headerHooks:h,staticGenerationBailout:f}=c,k="/api/upload/file/route";function D(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[1638,2791],()=>a(86982));module.exports=o})();