(()=>{var e={};e.id=9165,e.ids=[9165],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13961:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>l});var s=t(50482),o=t(69108),n=t(62563),a=t.n(n),i=t(68300),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,32417)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=[],u="/_not-found",m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},97342:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},53622:(e,r,t)=>{Promise.resolve().then(t.bind(t,34755)),Promise.resolve().then(t.bind(t,64792)),Promise.resolve().then(t.bind(t,89284)),Promise.resolve().then(t.bind(t,11503))},5094:(e,r,t)=>{"use strict";t.d(r,{d:()=>d,z:()=>l});var s=t(95344),o=t(3729),n=t(32751),a=t(49247),i=t(11453);let d=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef(({className:e,variant:r,size:t,asChild:o=!1,...a},l)=>{let c=o?n.g7:"button";return s.jsx(c,{className:(0,i.cn)(d({variant:r,size:t,className:e})),ref:l,...a})});l.displayName="Button"},23673:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>i,SZ:()=>l,Zb:()=>a,aY:()=>c,ll:()=>d});var s=t(95344),o=t(3729),n=t(11453);let a=o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));a.displayName="Card";let i=o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let d=o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",o.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},64792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DefaultErrorFallback:()=>u,ErrorBoundary:()=>c,useErrorHandler:()=>m});var s=t(95344),o=t(3729),n=t.n(o),a=t(5094),i=t(23673),d=t(65719),l=t(33733);class c extends n().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return s.jsx(e,{error:this.state.error,resetError:this.resetError})}return s.jsx(u,{error:this.state.error,resetError:this.resetError})}return this.props.children}}let u=({error:e,resetError:r})=>s.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(i.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.Ol,{className:"text-center",children:[s.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:s.jsx(d.Z,{className:"h-6 w-6 text-red-600"})}),s.jsx(i.ll,{className:"text-red-900",children:"Something went wrong"}),s.jsx(i.SZ,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,s.jsxs)(i.aY,{className:"space-y-4",children:[!1,(0,s.jsxs)(a.z,{onClick:r,className:"w-full",variant:"outline",children:[s.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}),m=()=>{let[e,r]=n().useState(null),t=n().useCallback(()=>{r(null)},[]),s=n().useCallback(e=>{r(e)},[]);return n().useEffect(()=>{if(e)throw e},[e]),{captureError:s,resetError:t}}},89284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryProvider:()=>i});var s=t(95344),o=t(86166),n=t(11494),a=t(3729);let i=({children:e})=>{let[r]=(0,a.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,s.jsxs)(n.aH,{client:r,children:[e,!1]})}},11503:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ThemeProvider:()=>a,useTheme:()=>i});var s=t(95344),o=t(3729);let n=(0,o.createContext)({theme:"system",setTheme:()=>null});function a({children:e,defaultTheme:r="system",storageKey:t="cms-theme",...a}){let[i,d]=(0,o.useState)(r),[l,c]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{c(!0);let e=localStorage?.getItem(t);e&&d(e)},[t]),(0,o.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===i){let r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(r);return}e.classList.add(i)},[i]),l)?s.jsx(n.Provider,{...a,value:{theme:i,setTheme:e=>{localStorage?.setItem(t,e),d(e)}},children:e}):null}let i=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},11453:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(56815),o=t(79377);function n(...e){return(0,o.m6)((0,s.W)(e))}},32417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w,metadata:()=>b});var s=t(25036),o=t(42195),n=t.n(o);t(5023);var a=t(86843);let i=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx`),{__esModule:d,$$typeof:l}=i;i.default;let c=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx#QueryProvider`),u=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx`),{__esModule:m,$$typeof:h}=u;u.default;let p=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#ThemeProvider`);(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx#useTheme`);let x=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx`),{__esModule:f,$$typeof:v}=x;x.default;let y=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#ErrorBoundary`);(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#DefaultErrorFallback`),(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#useErrorHandler`);var g=t(27171);let b={title:"APISportsGame CMS",description:"Content Management System for APISportsGame API"};function w({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:s.jsx(p,{defaultTheme:"system",children:s.jsx(y,{children:(0,s.jsxs)(c,{children:[e,s.jsx(g.x7,{position:"top-right",richColors:!0})]})})})})})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6126],()=>t(13961));module.exports=s})();