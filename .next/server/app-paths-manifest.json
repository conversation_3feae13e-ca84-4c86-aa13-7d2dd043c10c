{"/_not-found": "app/_not-found.js", "/page": "app/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/test-dropdown/page": "app/test-dropdown/page.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/profile/route": "app/api/auth/profile/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/broadcast-links/fixture/[fixtureId]/route": "app/api/broadcast-links/fixture/[fixtureId]/route.js", "/api/broadcast-links/route": "app/api/broadcast-links/route.js", "/api/fixtures/[id]/events/route": "app/api/fixtures/[id]/events/route.js", "/api/auth/refresh/route": "app/api/auth/refresh/route.js", "/api/broadcast-links/[id]/route": "app/api/broadcast-links/[id]/route.js", "/api/fixtures/[id]/statistics/route": "app/api/fixtures/[id]/statistics/route.js", "/api/fixtures/[id]/route": "app/api/fixtures/[id]/route.js", "/api/fixtures/route": "app/api/fixtures/route.js", "/api/fixtures/sync/route": "app/api/fixtures/sync/route.js", "/api/images/[...path]/route": "app/api/images/[...path]/route.js", "/api/fixtures/live/route": "app/api/fixtures/live/route.js", "/api/leagues/sync/route": "app/api/leagues/sync/route.js", "/api/leagues/route": "app/api/leagues/route.js", "/api/leagues/[id]/route": "app/api/leagues/[id]/route.js", "/api/news/categories/route": "app/api/news/categories/route.js", "/api/news/route": "app/api/news/route.js", "/api/news/categories/[id]/route": "app/api/news/categories/[id]/route.js", "/api/players/[id]/route": "app/api/players/[id]/route.js", "/api/players/sync/route": "app/api/players/sync/route.js", "/api/players/route": "app/api/players/route.js", "/api/standings/route": "app/api/standings/route.js", "/api/players/topscorers/route": "app/api/players/topscorers/route.js", "/api/teams/route": "app/api/teams/route.js", "/api/upload/[imageId]/route": "app/api/upload/[imageId]/route.js", "/api/news/[id]/route": "app/api/news/[id]/route.js", "/api/upload/url/route": "app/api/upload/url/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/upload/file/route": "app/api/upload/file/route.js", "/auth/login/page": "app/auth/login/page.js", "/dashboard/api-test/page": "app/dashboard/api-test/page.js", "/dashboard/components-demo/page": "app/dashboard/components-demo/page.js", "/dashboard/fixtures/live/page": "app/dashboard/fixtures/live/page.js", "/dashboard/fixtures/[id]/edit/page": "app/dashboard/fixtures/[id]/edit/page.js", "/dashboard/fixtures/create/page": "app/dashboard/fixtures/create/page.js", "/dashboard/fixtures/[id]/page": "app/dashboard/fixtures/[id]/page.js", "/dashboard/fixtures/sync/page": "app/dashboard/fixtures/sync/page.js", "/dashboard/fixtures/page": "app/dashboard/fixtures/page.js", "/dashboard/leagues/[id]/edit/page": "app/dashboard/leagues/[id]/edit/page.js", "/dashboard/leagues/create/page": "app/dashboard/leagues/create/page.js", "/dashboard/leagues/[id]/page": "app/dashboard/leagues/[id]/page.js", "/dashboard/leagues/page": "app/dashboard/leagues/page.js", "/dashboard/news/[id]/page": "app/dashboard/news/[id]/page.js", "/dashboard/news/[id]/edit/page": "app/dashboard/news/[id]/edit/page.js", "/dashboard/news/categories/[id]/page": "app/dashboard/news/categories/[id]/page.js", "/dashboard/news/categories/[id]/edit/page": "app/dashboard/news/categories/[id]/edit/page.js", "/dashboard/news/categories/create/page": "app/dashboard/news/categories/create/page.js", "/dashboard/news/page": "app/dashboard/news/page.js", "/dashboard/news/create/page": "app/dashboard/news/create/page.js", "/dashboard/news/categories/page": "app/dashboard/news/categories/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/players/page": "app/dashboard/players/page.js", "/dashboard/players/[id]/page": "app/dashboard/players/[id]/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js", "/dashboard/teams/[id]/edit/page": "app/dashboard/teams/[id]/edit/page.js", "/dashboard/teams/[id]/page": "app/dashboard/teams/[id]/page.js", "/dashboard/teams/[id]/statistics/page": "app/dashboard/teams/[id]/statistics/page.js", "/dashboard/teams/page": "app/dashboard/teams/page.js", "/dashboard/users/registered/[id]/edit/page": "app/dashboard/users/registered/[id]/edit/page.js", "/dashboard/users/registered/[id]/page": "app/dashboard/users/registered/[id]/page.js", "/dashboard/users/registered/page": "app/dashboard/users/registered/page.js", "/dashboard/users/system/[id]/edit/page": "app/dashboard/users/system/[id]/edit/page.js", "/dashboard/users/system/[id]/page": "app/dashboard/users/system/[id]/page.js", "/dashboard/users/system/create/page": "app/dashboard/users/system/create/page.js", "/dashboard/users/system/page": "app/dashboard/users/system/page.js", "/dashboard/users/tiers/page": "app/dashboard/users/tiers/page.js"}