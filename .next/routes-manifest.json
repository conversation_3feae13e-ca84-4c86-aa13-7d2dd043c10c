{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/broadcast-links/fixture/[fixtureId]", "regex": "^/api/broadcast\\-links/fixture/([^/]+?)(?:/)?$", "routeKeys": {"nxtPfixtureId": "nxtPfixtureId"}, "namedRegex": "^/api/broadcast\\-links/fixture/(?<nxtPfixtureId>[^/]+?)(?:/)?$"}, {"page": "/api/broadcast-links/[id]", "regex": "^/api/broadcast\\-links/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/broadcast\\-links/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/fixtures/[id]", "regex": "^/api/fixtures/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/fixtures/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/fixtures/[id]/events", "regex": "^/api/fixtures/([^/]+?)/events(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/fixtures/(?<nxtPid>[^/]+?)/events(?:/)?$"}, {"page": "/api/fixtures/[id]/statistics", "regex": "^/api/fixtures/([^/]+?)/statistics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/fixtures/(?<nxtPid>[^/]+?)/statistics(?:/)?$"}, {"page": "/api/images/[...path]", "regex": "^/api/images/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/images/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/api/leagues/[id]", "regex": "^/api/leagues/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/leagues/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/news/categories/[id]", "regex": "^/api/news/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/news/categories/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/news/[id]", "regex": "^/api/news/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/news/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/players/[id]", "regex": "^/api/players/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/players/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/upload/[imageId]", "regex": "^/api/upload/([^/]+?)(?:/)?$", "routeKeys": {"nxtPimageId": "nxtPimageId"}, "namedRegex": "^/api/upload/(?<nxtPimageId>[^/]+?)(?:/)?$"}, {"page": "/dashboard/fixtures/[id]", "regex": "^/dashboard/fixtures/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/fixtures/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/fixtures/[id]/edit", "regex": "^/dashboard/fixtures/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/fixtures/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/leagues/[id]", "regex": "^/dashboard/leagues/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/leagues/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/leagues/[id]/edit", "regex": "^/dashboard/leagues/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/leagues/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/news/categories/[id]", "regex": "^/dashboard/news/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/news/categories/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/news/categories/[id]/edit", "regex": "^/dashboard/news/categories/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/news/categories/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/news/[id]", "regex": "^/dashboard/news/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/news/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/news/[id]/edit", "regex": "^/dashboard/news/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/news/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/players/[id]", "regex": "^/dashboard/players/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/players/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/teams/[id]", "regex": "^/dashboard/teams/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teams/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/teams/[id]/edit", "regex": "^/dashboard/teams/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teams/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/teams/[id]/statistics", "regex": "^/dashboard/teams/([^/]+?)/statistics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teams/(?<nxtPid>[^/]+?)/statistics(?:/)?$"}, {"page": "/dashboard/users/registered/[id]", "regex": "^/dashboard/users/registered/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/users/registered/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/users/registered/[id]/edit", "regex": "^/dashboard/users/registered/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/users/registered/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/users/system/[id]", "regex": "^/dashboard/users/system/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/users/system/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/users/system/[id]/edit", "regex": "^/dashboard/users/system/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/users/system/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/api-test", "regex": "^/dashboard/api\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/api\\-test(?:/)?$"}, {"page": "/dashboard/components-demo", "regex": "^/dashboard/components\\-demo(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/components\\-demo(?:/)?$"}, {"page": "/dashboard/fixtures", "regex": "^/dashboard/fixtures(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/fixtures(?:/)?$"}, {"page": "/dashboard/fixtures/create", "regex": "^/dashboard/fixtures/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/fixtures/create(?:/)?$"}, {"page": "/dashboard/fixtures/live", "regex": "^/dashboard/fixtures/live(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/fixtures/live(?:/)?$"}, {"page": "/dashboard/fixtures/sync", "regex": "^/dashboard/fixtures/sync(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/fixtures/sync(?:/)?$"}, {"page": "/dashboard/leagues", "regex": "^/dashboard/leagues(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/leagues(?:/)?$"}, {"page": "/dashboard/leagues/create", "regex": "^/dashboard/leagues/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/leagues/create(?:/)?$"}, {"page": "/dashboard/news", "regex": "^/dashboard/news(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/news(?:/)?$"}, {"page": "/dashboard/news/categories", "regex": "^/dashboard/news/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/news/categories(?:/)?$"}, {"page": "/dashboard/news/categories/create", "regex": "^/dashboard/news/categories/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/news/categories/create(?:/)?$"}, {"page": "/dashboard/news/create", "regex": "^/dashboard/news/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/news/create(?:/)?$"}, {"page": "/dashboard/players", "regex": "^/dashboard/players(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/players(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/dashboard/teams", "regex": "^/dashboard/teams(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teams(?:/)?$"}, {"page": "/dashboard/users/registered", "regex": "^/dashboard/users/registered(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users/registered(?:/)?$"}, {"page": "/dashboard/users/system", "regex": "^/dashboard/users/system(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users/system(?:/)?$"}, {"page": "/dashboard/users/system/create", "regex": "^/dashboard/users/system/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users/system/create(?:/)?$"}, {"page": "/dashboard/users/tiers", "regex": "^/dashboard/users/tiers(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users/tiers(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/test-dropdown", "regex": "^/test\\-dropdown(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-dropdown(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}