"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/fixtures.ts":
/*!*********************************!*\
  !*** ./src/lib/api/fixtures.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixturesApi: function() { return /* binding */ fixturesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst fixturesApi = {\n    // Public endpoints - Using Next.js API proxy\n    getFixtures: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch fixtures: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    getFixtureById: async (externalId)=>{\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Upcoming and Live fixtures (Public) - Using Next.js API proxy\n    getUpcomingAndLive: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures/live?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch live fixtures: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Team schedule (Requires auth)\n    getTeamSchedule: async function(teamId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/schedules/\".concat(teamId, \"?\").concat(params.toString()));\n        return response;\n    },\n    // Fixture statistics (Requires auth)\n    getFixtureStatistics: async (externalId)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/statistics/\".concat(externalId));\n        return response;\n    },\n    // Admin only - Sync operations - Using Next.js API proxy\n    triggerSeasonSync: async ()=>{\n        // Get token from auth store (same pattern as other operations)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Season sync - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Season sync - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Season sync - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Season sync request via proxy\");\n        const response = await fetch(\"/api/fixtures/sync\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                type: \"season\"\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Season sync failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to trigger season sync: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Season sync successful\");\n        return result;\n    },\n    triggerDailySync: async ()=>{\n        // Get token from auth store (same pattern as other operations)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Daily sync - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Daily sync - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Daily sync - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Daily sync request via proxy\");\n        const response = await fetch(\"/api/fixtures/sync\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                type: \"daily\"\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Daily sync failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to trigger daily sync: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Daily sync successful\");\n        return result;\n    },\n    // Editor+ - Sync status - Using Next.js API proxy\n    getSyncStatus: async ()=>{\n        // Get token from auth store (same pattern as other operations)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Sync status - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Sync status - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Sync status - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Sync status request via proxy\");\n        const response = await fetch(\"/api/fixtures/sync\", {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Sync status failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to get sync status: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Sync status successful\");\n        return result;\n    },\n    // CRUD operations - Using Next.js API proxy\n    createFixture: async (data)=>{\n        var _result_data;\n        // Get token from auth store (same pattern as update/delete)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Create fixture - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Create fixture - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Create fixture - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Create fixture request:\", {\n            hasAuth: !!headers.Authorization,\n            data\n        });\n        const response = await fetch(\"/api/fixtures\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Create fixture failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to create fixture: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Create fixture successful:\", (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.id);\n        return result.data || result;\n    },\n    updateFixture: async (externalId, data)=>{\n        // Get token from auth store (same pattern as delete)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Update fixture - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Update fixture - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Update fixture - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Update fixture request:\", {\n            externalId,\n            hasAuth: !!headers.Authorization,\n            data\n        });\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"PUT\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Update fixture failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to update fixture: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Update fixture successful:\", externalId);\n        return result.data || result;\n    },\n    deleteFixture: async (externalId)=>{\n        // Get token from auth store (same pattern as broadcast-links.ts)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Delete fixture - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Delete fixture - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Delete fixture - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Delete fixture request:\", {\n            externalId,\n            hasAuth: !!headers.Authorization\n        });\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Delete fixture failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to delete fixture: \".concat(response.statusText));\n        }\n        console.log(\"✅ Delete fixture successful:\", externalId);\n    },\n    // Get fixture statistics (Public)\n    getFixtureStatistics: async (externalId)=>{\n        const response = await fetch(\"/api/fixtures/\".concat(externalId, \"/statistics\"), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch fixture statistics: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Aliases for consistency\n    getFixture: async (externalId)=>{\n        const response = await fixturesApi.getFixtureById(externalId);\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/fixtures.ts\n"));

/***/ })

});