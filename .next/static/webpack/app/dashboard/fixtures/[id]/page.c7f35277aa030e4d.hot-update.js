"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/components/fixtures/FixtureStats.tsx":
/*!**************************************************!*\
  !*** ./src/components/fixtures/FixtureStats.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FixtureStats: function() { return /* binding */ FixtureStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Clock,Flag,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Clock,Flag,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Clock,Flag,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Clock,Flag,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Clock,Flag,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ FixtureStats auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction FixtureStats(param) {\n    let { fixture } = param;\n    _s();\n    // Fetch real statistics from API\n    const { data: statisticsData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)({\n        queryKey: [\n            \"fixture-statistics\",\n            fixture.externalId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_4__.fixturesApi.getFixtureStatistics(fixture.externalId),\n        enabled: !!fixture.externalId,\n        staleTime: 5 * 60 * 1000\n    });\n    // Debug logging\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (statisticsData) {\n            var _statisticsData_data;\n            console.log(\"\\uD83D\\uDD0D Statistics Data Received:\", {\n                fullData: statisticsData,\n                dataArray: statisticsData.data,\n                dataType: typeof statisticsData.data,\n                isArray: Array.isArray(statisticsData.data),\n                length: (_statisticsData_data = statisticsData.data) === null || _statisticsData_data === void 0 ? void 0 : _statisticsData_data.length\n            });\n        }\n        if (error) {\n            console.error(\"❌ Statistics Error:\", error);\n        }\n    }, [\n        statisticsData,\n        error\n    ]);\n    // Parse API data or use mock data as fallback\n    const parseStatistics = ()=>{\n        // Check if we have valid API data\n        if (!(statisticsData === null || statisticsData === void 0 ? void 0 : statisticsData.data) || !Array.isArray(statisticsData.data) || statisticsData.data.length < 2) {\n            var _statisticsData_data;\n            console.log(\"Using mock data - API data not available or insufficient:\", {\n                hasData: !!(statisticsData === null || statisticsData === void 0 ? void 0 : statisticsData.data),\n                isArray: Array.isArray(statisticsData === null || statisticsData === void 0 ? void 0 : statisticsData.data),\n                length: statisticsData === null || statisticsData === void 0 ? void 0 : (_statisticsData_data = statisticsData.data) === null || _statisticsData_data === void 0 ? void 0 : _statisticsData_data.length\n            });\n            // Use mock data when API fails or no data\n            return {\n                possession: {\n                    home: 65,\n                    away: 35\n                },\n                shots: {\n                    home: 12,\n                    away: 8\n                },\n                shotsOnTarget: {\n                    home: 6,\n                    away: 3\n                },\n                corners: {\n                    home: 7,\n                    away: 4\n                },\n                fouls: {\n                    home: 11,\n                    away: 14\n                },\n                yellowCards: {\n                    home: 2,\n                    away: 3\n                },\n                redCards: {\n                    home: 0,\n                    away: 1\n                },\n                offsides: {\n                    home: 3,\n                    away: 2\n                },\n                isRealData: false\n            };\n        }\n        // API returns data in format: { teamName, statistics: { shotsOnGoal, totalShots, ... } }\n        const homeTeamData = statisticsData.data[0];\n        const awayTeamData = statisticsData.data[1];\n        console.log(\"Parsing real API data:\", {\n            homeTeam: homeTeamData === null || homeTeamData === void 0 ? void 0 : homeTeamData.teamName,\n            awayTeam: awayTeamData === null || awayTeamData === void 0 ? void 0 : awayTeamData.teamName,\n            homeStats: homeTeamData === null || homeTeamData === void 0 ? void 0 : homeTeamData.statistics,\n            awayStats: awayTeamData === null || awayTeamData === void 0 ? void 0 : awayTeamData.statistics\n        });\n        const getStatValue = (teamData, statKey)=>{\n            if (!(teamData === null || teamData === void 0 ? void 0 : teamData.statistics)) {\n                console.warn(\"getStatValue: no statistics found for team:\", teamData === null || teamData === void 0 ? void 0 : teamData.teamName);\n                return 0;\n            }\n            const value = teamData.statistics[statKey];\n            if (value === undefined || value === null) return 0;\n            // Handle percentage values\n            if (typeof value === \"string\" && value.includes(\"%\")) {\n                return parseInt(value.replace(\"%\", \"\"));\n            }\n            return parseInt(value) || 0;\n        };\n        return {\n            possession: {\n                home: getStatValue(homeTeamData, \"possession\"),\n                away: getStatValue(awayTeamData, \"possession\")\n            },\n            shots: {\n                home: getStatValue(homeTeamData, \"totalShots\"),\n                away: getStatValue(awayTeamData, \"totalShots\")\n            },\n            shotsOnTarget: {\n                home: getStatValue(homeTeamData, \"shotsOnGoal\"),\n                away: getStatValue(awayTeamData, \"shotsOnGoal\")\n            },\n            corners: {\n                home: getStatValue(homeTeamData, \"corners\"),\n                away: getStatValue(awayTeamData, \"corners\")\n            },\n            fouls: {\n                home: getStatValue(homeTeamData, \"fouls\") || 0,\n                away: getStatValue(awayTeamData, \"fouls\") || 0\n            },\n            yellowCards: {\n                home: getStatValue(homeTeamData, \"yellowCards\"),\n                away: getStatValue(awayTeamData, \"yellowCards\")\n            },\n            redCards: {\n                home: getStatValue(homeTeamData, \"redCards\"),\n                away: getStatValue(awayTeamData, \"redCards\")\n            },\n            offsides: {\n                home: getStatValue(homeTeamData, \"offsides\"),\n                away: getStatValue(awayTeamData, \"offsides\")\n            },\n            isRealData: true\n        };\n    };\n    const stats = parseStatistics();\n    const StatRow = (param)=>{\n        let { label, homeValue, awayValue, icon: Icon, isPercentage = false } = param;\n        const total = homeValue + awayValue;\n        const homePercentage = total > 0 ? homeValue / total * 100 : 50;\n        const awayPercentage = total > 0 ? awayValue / total * 100 : 50;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-right w-12\",\n                            children: [\n                                homeValue,\n                                isPercentage ? \"%\" : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: label\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-left w-12\",\n                            children: [\n                                awayValue,\n                                isPercentage ? \"%\" : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-2 bg-gray-200 rounded-full overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(homePercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-500 transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(awayPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    };\n    // Loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Match Statistics\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                                className: \"h-4 w-8\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                                className: \"h-4 w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                                className: \"h-4 w-8\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                                        className: \"h-2 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Match Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        !stats.isRealData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 text-orange-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm font-medium text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-12 text-right\",\n                                children: fixture.homeTeamName\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 text-center\",\n                                children: \"Statistics\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-12 text-left\",\n                                children: fixture.awayTeamName\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Possession\",\n                                homeValue: stats.possession.home,\n                                awayValue: stats.possession.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                isPercentage: true\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Shots\",\n                                homeValue: stats.shots.home,\n                                awayValue: stats.shots.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Shots on Target\",\n                                homeValue: stats.shotsOnTarget.home,\n                                awayValue: stats.shotsOnTarget.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Corners\",\n                                homeValue: stats.corners.home,\n                                awayValue: stats.corners.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Fouls\",\n                                homeValue: stats.fouls.home,\n                                awayValue: stats.fouls.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Yellow Cards\",\n                                homeValue: stats.yellowCards.home,\n                                awayValue: stats.yellowCards.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Red Cards\",\n                                homeValue: stats.redCards.home,\n                                awayValue: stats.redCards.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatRow, {\n                                label: \"Offsides\",\n                                homeValue: stats.offsides.home,\n                                awayValue: stats.offsides.away,\n                                icon: _barrel_optimize_names_AlertCircle_BarChart3_Clock_Flag_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 text-center pt-4 border-t\",\n                        children: \"* Statistics are updated in real-time during the match\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureStats.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(FixtureStats, \"+t5et6hwai5Bb+Cn3F6/hZDsjls=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery\n    ];\n});\n_c = FixtureStats;\nvar _c;\n$RefreshReg$(_c, \"FixtureStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fixtures/FixtureStats.tsx\n"));

/***/ })

});