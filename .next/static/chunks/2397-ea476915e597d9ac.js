"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2397],{37805:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(57977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},37501:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(57977).Z)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},28670:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(57977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},34059:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},81100:function(e,n,r){r.d(n,{oC:function(){return e8},VY:function(){return e4},ZA:function(){return e2},ck:function(){return e3},wU:function(){return ne},__:function(){return e6},Uv:function(){return e7},Ee:function(){return e5},Rk:function(){return e9},fC:function(){return e0},Z0:function(){return nn},Tr:function(){return nr},tu:function(){return no},fF:function(){return nt},xz:function(){return e1}});var t=r(2265),o=r(44991),a=r(61266),u=r(84104),l=r(9310),i=r(29586),d=r(5528),c=r(12275),s=r(1260),p=r(46165),f=r(78082),v=r(38687),h=r(12338),g=r(37881),m=r(12642),w=r(23715),x=r(59143),y=r(39830),M=r(66674),C=r(47225),j=r(57437),b=["Enter"," "],k=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...k],D={ltr:[...b,"ArrowRight"],rtl:[...b,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[E,I,T]=(0,d.B)(P),[N,O]=(0,u.b)(P,[T,h.D7,w.Pc]),S=(0,h.D7)(),F=(0,w.Pc)(),[L,A]=N(P),[K,V]=N(P),Z=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=S(n),[d,s]=t.useState(null),p=t.useRef(!1),f=(0,y.W)(u),v=(0,c.gm)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,j.jsx)(h.fC,{...i,children:(0,j.jsx)(L,{scope:n,open:r,onOpenChange:f,content:d,onContentChange:s,children:(0,j.jsx)(K,{scope:n,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:l,children:o})})})};Z.displayName=P;var W=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=S(r);return(0,j.jsx)(h.ee,{...o,...t,ref:n})});W.displayName="MenuAnchor";var G="MenuPortal",[U,B]=N(G,{forceMount:void 0}),z=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=A(G,n);return(0,j.jsx)(U,{scope:n,forceMount:r,children:(0,j.jsx)(m.z,{present:r||a.open,children:(0,j.jsx)(g.h,{asChild:!0,container:o,children:t})})})};z.displayName=G;var X="MenuContent",[Y,H]=N(X),q=t.forwardRef((e,n)=>{let r=B(X,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=A(X,e.__scopeMenu),u=V(X,e.__scopeMenu);return(0,j.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(m.z,{present:t||a.open,children:(0,j.jsx)(E.Slot,{scope:e.__scopeMenu,children:u.modal?(0,j.jsx)(J,{...o,ref:n}):(0,j.jsx)(Q,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=A(X,e.__scopeMenu),u=t.useRef(null),l=(0,a.e)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,M.Ry)(e)},[]),(0,j.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let r=A(X,e.__scopeMenu);return(0,j.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,x.Z8)("MenuContent.ScrollLock"),ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:c,onEntryFocus:v,onEscapeKeyDown:g,onPointerDownOutside:m,onFocusOutside:x,onInteractOutside:y,onDismiss:M,disableOutsideScroll:b,...D}=e,_=A(X,r),P=V(X,r),E=S(r),T=F(r),N=I(r),[O,L]=t.useState(null),K=t.useRef(null),Z=(0,a.e)(n,K,_.onContentChange),W=t.useRef(0),G=t.useRef(""),U=t.useRef(0),B=t.useRef(null),z=t.useRef("right"),H=t.useRef(0),q=b?C.Z:t.Fragment,J=e=>{var n,r;let t=G.current+e,o=N().filter(e=>!e.disabled),a=document.activeElement,u=null===(n=o.find(e=>e.ref.current===a))||void 0===n?void 0:n.textValue,l=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=(t=Math.max(r?e.indexOf(r):-1,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(o.map(e=>e.textValue),t,u),i=null===(r=o.find(e=>e.textValue===l))||void 0===r?void 0:r.ref.current;!function e(n){G.current=n,window.clearTimeout(W.current),""!==n&&(W.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(W.current),[]),(0,p.EW)();let Q=t.useCallback(e=>{var n,r,t;return z.current===(null===(n=B.current)||void 0===n?void 0:n.side)&&!!(t=null===(r=B.current)||void 0===r?void 0:r.area)&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],l=n[a],i=u.x,d=u.y,c=l.x,s=l.y;d>t!=s>t&&r<(c-i)*(t-d)/(s-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,j.jsx)(Y,{scope:r,searchRef:G,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var n;Q(e)||(null===(n=K.current)||void 0===n||n.focus(),L(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:U,onPointerGraceIntentChange:t.useCallback(e=>{B.current=e},[]),children:(0,j.jsx)(q,{...b?{as:$,allowPinchZoom:!0}:void 0,children:(0,j.jsx)(f.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(i,e=>{var n;e.preventDefault(),null===(n=K.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,j.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:m,onFocusOutside:x,onInteractOutside:y,onDismiss:M,children:(0,j.jsx)(w.fC,{asChild:!0,...T,dir:P.dir,orientation:"vertical",loop:u,currentTabStopId:O,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.M)(v,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,j.jsx)(h.VY,{role:"menu","aria-orientation":"vertical","data-state":e_(_.open),"data-radix-menu-content":"",dir:P.dir,...E,...D,ref:Z,style:{outline:"none",...D.style},onKeyDown:(0,o.M)(D.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&J(e.key));let o=K.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);k.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(W.current),G.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eI(e=>{let n=e.target,r=H.current!==e.clientX;if(e.currentTarget.contains(n)&&r){let n=e.clientX>H.current?"right":"left";z.current=n,H.current=e.clientX}}))})})})})})})});q.displayName=X;var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,j.jsx)(i.WV.div,{role:"group",...t,ref:n})});en.displayName="MenuGroup";var er=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,j.jsx)(i.WV.div,{...t,ref:n})});er.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...l}=e,d=t.useRef(null),c=V(et,e.__scopeMenu),s=H(et,e.__scopeMenu),p=(0,a.e)(n,d),f=t.useRef(!1);return(0,j.jsx)(eu,{...l,ref:p,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=d.current;if(!r&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,i.jH)(e,n),n.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:n=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,n),f.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var n;f.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=""!==s.searchRef.current;!r&&(!n||" "!==e.key)&&b.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var eu=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:l,...d}=e,c=H(et,r),s=F(r),p=t.useRef(null),f=(0,a.e)(n,p),[v,h]=t.useState(!1),[g,m]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var n;m((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[d.children]),(0,j.jsx)(E.ItemSlot,{scope:r,disabled:u,textValue:null!=l?l:g,children:(0,j.jsx)(w.ck,{asChild:!0,...s,focusable:!u,children:(0,j.jsx)(i.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...d,ref:f,onPointerMove:(0,o.M)(e.onPointerMove,eI(e=>{u?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eI(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>h(!0)),onBlur:(0,o.M)(e.onBlur,()=>h(!1))})})})}),el=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,j.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,j.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eP(r)?"mixed":r,...a,ref:n,"data-state":eE(r),onSelect:(0,o.M)(a.onSelect,()=>null==t?void 0:t(!!eP(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,ec]=N(ei,{value:void 0,onValueChange:()=>{}}),es=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,y.W)(t);return(0,j.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,j.jsx)(en,{...o,ref:n})})});es.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ec(ep,e.__scopeMenu),u=r===a.value;return(0,j.jsx)(eh,{scope:e.__scopeMenu,checked:u,children:(0,j.jsx)(ea,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eE(u),onSelect:(0,o.M)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[eh,eg]=N(ev,{checked:!1}),em=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=eg(ev,r);return(0,j.jsx)(m.z,{present:t||eP(a.checked)||!0===a.checked,children:(0,j.jsx)(i.WV.span,{...o,ref:n,"data-state":eE(a.checked)})})});em.displayName=ev;var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,j.jsx)(i.WV.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});ew.displayName="MenuSeparator";var ex=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=S(r);return(0,j.jsx)(h.Eh,{...o,...t,ref:n})});ex.displayName="MenuArrow";var ey="MenuSub",[eM,eC]=N(ey),ej=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,u=A(ey,n),l=S(n),[i,d]=t.useState(null),[c,s]=t.useState(null),p=(0,y.W)(a);return t.useEffect(()=>(!1===u.open&&p(!1),()=>p(!1)),[u.open,p]),(0,j.jsx)(h.fC,{...l,children:(0,j.jsx)(L,{scope:n,open:o,onOpenChange:p,content:c,onContentChange:s,children:(0,j.jsx)(eM,{scope:n,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:i,onTriggerChange:d,children:r})})})};ej.displayName=ey;var eb="MenuSubTrigger",ek=t.forwardRef((e,n)=>{let r=A(eb,e.__scopeMenu),u=V(eb,e.__scopeMenu),l=eC(eb,e.__scopeMenu),i=H(eb,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:s}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),s(null)}},[c,s]),(0,j.jsx)(W,{asChild:!0,...p,children:(0,j.jsx)(eu,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":e_(r.open),...e,ref:(0,a.F)(n,l.onTriggerChange),onClick:n=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eI(n=>{i.onItemEnter(n),n.defaultPrevented||e.disabled||r.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eI(e=>{var n,t;f();let o=null===(n=r.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(t=r.content)||void 0===t?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&D[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),n.preventDefault()}})})})});ek.displayName=eb;var eR="MenuSubContent",eD=t.forwardRef((e,n)=>{let r=B(X,e.__scopeMenu),{forceMount:u=r.forceMount,...l}=e,i=A(X,e.__scopeMenu),d=V(X,e.__scopeMenu),c=eC(eR,e.__scopeMenu),s=t.useRef(null),p=(0,a.e)(n,s);return(0,j.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(m.z,{present:u||i.open,children:(0,j.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,j.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;d.isUsingKeyboardRef.current&&(null===(n=s.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=_[d.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null===(t=c.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function e_(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eE(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eI(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=eR;var eT="DropdownMenu",[eN,eO]=(0,u.b)(eT,[O]),eS=O(),[eF,eL]=eN(eT),eA=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:d=!0}=e,c=eS(n),s=t.useRef(null),[p,f]=(0,l.T)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eT});return(0,j.jsx)(eF,{scope:n,triggerId:(0,v.M)(),triggerRef:s,contentId:(0,v.M)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,j.jsx)(Z,{...c,open:p,onOpenChange:f,dir:o,modal:d,children:r})})};eA.displayName=eT;var eK="DropdownMenuTrigger",eV=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,l=eL(eK,r),d=eS(r);return(0,j.jsx)(W,{asChild:!0,...d,children:(0,j.jsx)(i.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.F)(n,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eV.displayName=eK;var eZ=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eS(n);return(0,j.jsx)(z,{...t,...r})};eZ.displayName="DropdownMenuPortal";var eW="DropdownMenuContent",eG=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eL(eW,r),l=eS(r),i=t.useRef(!1);return(0,j.jsx)(q,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:n,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;i.current||null===(n=u.triggerRef.current)||void 0===n||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eW;var eU=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(en,{...o,...t,ref:n})});eU.displayName="DropdownMenuGroup";var eB=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(er,{...o,...t,ref:n})});eB.displayName="DropdownMenuLabel";var ez=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(ea,{...o,...t,ref:n})});ez.displayName="DropdownMenuItem";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(el,{...o,...t,ref:n})});eX.displayName="DropdownMenuCheckboxItem";var eY=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(es,{...o,...t,ref:n})});eY.displayName="DropdownMenuRadioGroup";var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(ef,{...o,...t,ref:n})});eH.displayName="DropdownMenuRadioItem";var eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(em,{...o,...t,ref:n})});eq.displayName="DropdownMenuItemIndicator";var eJ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(ew,{...o,...t,ref:n})});eJ.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(ex,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(ek,{...o,...t,ref:n})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eS(r);return(0,j.jsx)(eD,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eA,e1=eV,e7=eZ,e4=eG,e2=eU,e6=eB,e3=ez,e8=eX,e5=eY,e9=eH,ne=eq,nn=eJ,nr=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,u=eS(n),[i,d]=(0,l.T)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,j.jsx)(ej,{...u,open:i,onOpenChange:d,children:r})},nt=eQ,no=e$}}]);