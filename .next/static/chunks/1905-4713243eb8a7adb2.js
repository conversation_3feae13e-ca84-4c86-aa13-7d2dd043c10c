"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1905],{33277:function(e,t,r){r.d(t,{C:function(){return l}});var a=r(57437);r(2265);var s=r(49769),n=r(22169);let o=(0,s.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:r}),t),...s})}},575:function(e,t,r){r.d(t,{d:function(){return i},z:function(){return c}});var a=r(57437),s=r(2265),n=r(59143),o=r(49769),l=r(22169);let i=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:s,size:o,className:r})),ref:t,...d})});c.displayName="Button"},15671:function(e,t,r){r.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return o},aY:function(){return d},ll:function(){return i}});var a=r(57437),s=r(2265),n=r(22169);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...s})});o.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},85713:function(e,t,r){r.d(t,{U:function(){return N},Z:function(){return k}});var a=r(57437),s=r(2265),n=r(57277),o=r(10632),l=r(52235),i=r(79580),c=r(79382),d=r(65561),u=r(62985),f=r(575),h=r(22782),m=r(12647),p=r(99497),g=r(15671),x=r(33277),v=r(22169),y=r(56288);let b=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},w=e=>{if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e;return t.startsWith("/")&&(t=t.slice(1)),"".concat("http://*************","/").concat(t)},j={uploadFile:async function(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"general",a=arguments.length>2?arguments[2]:void 0,s=b();if(!s)throw Error("Authentication required for file upload");let n=new FormData;n.append("file",e),n.append("category",r),a&&n.append("description",a);let o=await fetch("/api/upload/file",{method:"POST",headers:{Authorization:"Bearer ".concat(s)},body:n});if(!o.ok){let e="Upload failed";try{e=(await o.json()).message||e}catch(t){console.warn("Failed to parse error response:",t),e="Upload failed with status ".concat(o.status)}throw Error(e)}try{t=await o.json()}catch(e){throw console.error("Failed to parse upload response:",e),Error("Invalid response from upload service")}let l=t.data||t;if(!l||!l.path)throw console.error("Invalid upload response:",t),Error("Upload response missing required data");let i=w(l.path);if(!i)throw Error("Failed to generate CDN URL from upload response");return{...l,url:i}},uploadFromUrl:async e=>{let t=b();if(!t)throw Error("Authentication required for URL upload");let r=await fetch("/api/upload/url",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!r.ok)throw Error((await r.json().catch(()=>({message:"Upload failed"}))).message||"Upload failed with status ".concat(r.status));let a=await r.json(),s=a.data||a,n=w(s.path);return{...s,url:n}},getUploadedImages:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=arguments.length>2?arguments[2]:void 0,a=b();if(!a)throw Error("Authentication required");let s=new URLSearchParams({page:e.toString(),limit:t.toString()});r&&s.append("category",r);let n=await fetch("/api/upload?".concat(s.toString()),{method:"GET",headers:{Authorization:"Bearer ".concat(a)}});if(!n.ok)throw Error("Failed to fetch images: ".concat(n.status));let o=await n.json(),l=o.data.map(e=>({...e,url:w(e.path)}));return{...o,data:l}},deleteUploadedImage:async e=>{let t=b();if(!t)throw Error("Authentication required");let r=await fetch("/api/upload/".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok)throw Error("Failed to delete image: ".concat(r.status))},buildDirectCdnUrl:w},N=s.forwardRef((e,t)=>{let{label:r,description:b,value:w,onChange:N,onFileSelect:k,accept:R="image/*",maxSize:U=5,className:T,error:L,required:C,preview:I=!0,placeholder:S="Enter image URL or drag & drop a file",category:E="general",uploadDescription:A}=e,[z,P]=(0,s.useState)(!1),[F,D]=(0,s.useState)("url"),[B,Z]=(0,s.useState)(w||""),[O,_]=(0,s.useState)(null),[Q,M]=(0,s.useState)(!1),[W,q]=(0,s.useState)(0),J=(0,s.useRef)(null),V=(0,s.useRef)(null);(0,s.useEffect)(()=>()=>{if(V.current&&clearInterval(V.current),B&&B.startsWith("blob:"))try{URL.revokeObjectURL(B)}catch(e){console.warn("Failed to revoke object URL on unmount:",e)}},[B]),(0,s.useEffect)(()=>{w!==B&&Z(w||"")},[w]);let G=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?P(!0):"dragleave"===e.type&&P(!1)},[]),Y=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),P(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&H(e.dataTransfer.files[0])},[]),H=(0,s.useCallback)(async e=>{if(!e.type.startsWith("image/")){y.toast.error("Please select a valid image file");return}if(e.size>1048576*U){y.toast.error("File size must be less than ".concat(U,"MB"));return}_(e),M(!0),q(0);let t=URL.createObjectURL(e);Z(t);try{V.current=setInterval(()=>{q(e=>Math.min(e+10,90))},200);let r=await j.uploadFile(e,E,A);if(V.current&&(clearInterval(V.current),V.current=null),q(100),!r||!r.url)throw Error("Invalid upload response - missing URL");let a=r.url;if(!a||!a.startsWith("http://")&&!a.startsWith("https://"))throw Error("Invalid CDN URL format received");Z(a),N(a),k&&k(e),y.toast.success("Image uploaded successfully!");try{URL.revokeObjectURL(t)}catch(e){console.warn("Failed to revoke object URL:",e)}}catch(r){console.error("Upload failed:",r),V.current&&(clearInterval(V.current),V.current=null);let e=(null==r?void 0:r.message)||"Upload failed. Please try again.";y.toast.error(e),Z(w||""),_(null);try{URL.revokeObjectURL(t)}catch(e){console.warn("Failed to revoke object URL:",e)}}finally{V.current&&(clearInterval(V.current),V.current=null),M(!1),q(0)}},[U,N,k,E,A,w]),K=(0,s.useCallback)(e=>{e.target.files&&e.target.files[0]&&H(e.target.files[0])},[H]),$=(0,s.useCallback)(e=>{try{if(Z(e),_(null),e&&e.trim())try{new URL(e)}catch(t){console.warn("Invalid URL format:",e),y.toast.error("Please enter a valid URL");return}N(e)}catch(e){console.error("Error handling URL change:",e),y.toast.error("Failed to update image URL")}},[N]),X=(0,s.useCallback)(()=>{Z(""),_(null),N(""),J.current&&(J.current.value="")},[N]),ee=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,a.jsxs)("div",{ref:t,className:(0,v.cn)("space-y-3",T),children:[r&&(0,a.jsxs)(m._,{className:(0,v.cn)("text-sm font-medium",L&&"text-red-600"),children:[r,C&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)(p.mQ,{value:F,onValueChange:e=>D(e),className:"w-full",children:[(0,a.jsxs)(p.dr,{className:"grid w-full grid-cols-2",children:[(0,a.jsxs)(p.SP,{value:"url",className:"flex items-center gap-2",children:[(0,a.jsx)(n.Z,{className:"w-4 h-4"}),"URL"]}),(0,a.jsxs)(p.SP,{value:"upload",className:"flex items-center gap-2",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4"}),"Upload"]})]}),(0,a.jsx)(p.nU,{value:"url",className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.I,{type:"url",placeholder:S,value:O?"File: ".concat(O.name):B,onChange:e=>$(e.target.value),disabled:!!O,className:(0,v.cn)(L&&"border-red-500 focus:border-red-500")}),(B||O)&&(0,a.jsx)(f.z,{type:"button",variant:"ghost",size:"sm",onClick:X,className:"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0",children:(0,a.jsx)(l.Z,{className:"w-4 h-4"})})]})}),(0,a.jsx)(p.nU,{value:"upload",className:"space-y-3",children:(0,a.jsxs)("div",{className:(0,v.cn)("relative border-2 border-dashed rounded-lg p-6 transition-colors",z?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400",L&&"border-red-500","cursor-pointer"),onDragEnter:G,onDragLeave:G,onDragOver:G,onDrop:Y,onClick:()=>{var e;return null===(e=J.current)||void 0===e?void 0:e.click()},children:[(0,a.jsx)("input",{ref:J,type:"file",accept:R,onChange:K,className:"hidden"}),(0,a.jsx)("div",{className:"flex flex-col items-center justify-center text-center",children:Q?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.Z,{className:"w-12 h-12 text-blue-500 mb-3 animate-spin"}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-700",children:"Uploading image..."}),(0,a.jsxs)("div",{className:"w-full max-w-xs mt-3",children:[(0,a.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(W,"%")}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[W,"% complete"]})]})]}):O?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.Z,{className:"w-12 h-12 text-green-500 mb-3"}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-700",children:O.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:ee(O.size)}),(0,a.jsxs)(f.z,{type:"button",variant:"outline",size:"sm",onClick:e=>{e.stopPropagation(),X()},className:"mt-3",disabled:Q,children:[(0,a.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Remove"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z,{className:(0,v.cn)("w-12 h-12 mb-3",z?"text-blue-500":"text-gray-400")}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Drag & drop an image here, or click to select"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Supports PNG, JPG, JPEG (max ",U,"MB)"]}),(0,a.jsx)("p",{className:"text-xs text-blue-600 mt-1 font-medium",children:"Images will be uploaded to CDN automatically"})]})})]})})]}),I&&(B||O)&&(0,a.jsx)(g.Zb,{className:"overflow-hidden",children:(0,a.jsx)(g.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)("div",{className:"relative w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border",children:[(0,a.jsx)("img",{src:B,alt:"Preview",className:"w-full h-full object-cover",onError:e=>{let t=e.target;t.src="",t.style.display="none",console.warn("Failed to load image preview:",B)},onLoad:()=>{console.log("Image preview loaded successfully")}}),!B&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-gray-400"})})]})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:O?O.name:"Image Preview"}),O&&(0,a.jsx)("p",{className:"text-xs text-gray-500",children:ee(O.size)}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)(x.C,{variant:"secondary",className:"text-xs",children:O?"Local File":"Remote URL"}),O&&(0,a.jsx)(x.C,{variant:"outline",className:"text-xs",children:O.type})]})]})]})})}),b&&!L&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:b}),L&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-red-600",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),L]})]})});N.displayName="ImageUpload";var k=N},22782:function(e,t,r){r.d(t,{I:function(){return o}});var a=r(57437),s=r(2265),n=r(22169);let o=s.forwardRef((e,t)=>{let{className:r,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...o})});o.displayName="Input"},12647:function(e,t,r){r.d(t,{_:function(){return c}});var a=r(57437),s=r(2265),n=r(24602),o=r(49769),l=r(22169);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.f,{ref:t,className:(0,l.cn)(i(),r),...s})});c.displayName=n.f.displayName},31657:function(e,t,r){r.d(t,{E:function(){return i},m:function(){return c}});var a=r(57437),s=r(2265),n=r(68928),o=r(37501),l=r(22169);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.fC,{className:(0,l.cn)("grid gap-2",r),...s,ref:t})});i.displayName=n.fC.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.ck,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),...s,children:(0,a.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,a.jsx)(o.Z,{className:"h-3.5 w-3.5 fill-primary"})})})});c.displayName=n.ck.displayName},99497:function(e,t,r){r.d(t,{SP:function(){return c},dr:function(){return i},mQ:function(){return l},nU:function(){return d}});var a=r(57437),s=r(2265),n=r(64694),o=r(22169);let l=n.fC,i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.aV,{ref:t,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...s})});i.displayName=n.aV.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.xz,{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...s})});c.displayName=n.xz.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...s})});d.displayName=n.VY.displayName},3549:function(e,t,r){r.d(t,{g:function(){return o}});var a=r(57437),s=r(2265),n=r(22169);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});o.displayName="Textarea"},74921:function(e,t,r){r.d(t,{x:function(){return o}});var a=r(73107),s=r(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=s.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:a}=await r.json(),s=e.user;if(s)return e.setAuth(s,a,t),this.setAuthToken(a),console.log("✅ Token refreshed successfully"),a}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:a,reject:s}=r;e?s(e):a(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let o=new n},48763:function(e,t,r){r.d(t,{t:function(){return o}});var a=r(12574),s=r(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},o=(0,a.U)()((0,s.tJ)((e,t)=>({...n,setAuth:(t,r,a)=>{e({user:t,accessToken:r,refreshToken:a,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let a=t().user;a&&e({user:{...a,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let a=Array.isArray(e)?e:[e];return"admin"===r.role||(a.includes("editor")?["admin","editor"].includes(r.role):a.includes("moderator")?["admin","editor","moderator"].includes(r.role):a.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){r.d(t,{cn:function(){return n}});var a=r(75504),s=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}}}]);