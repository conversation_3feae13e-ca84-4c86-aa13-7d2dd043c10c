(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{15700:function(t,e,s){Promise.resolve().then(s.bind(s,48836))},79580:function(t,e,s){"use strict";s.d(e,{Z:function(){return i}});let i=(0,s(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},47907:function(t,e,s){"use strict";var i=s(15313);s.o(i,"useParams")&&s.d(e,{useParams:function(){return i.useParams}}),s.o(i,"usePathname")&&s.d(e,{usePathname:function(){return i.usePathname}}),s.o(i,"useRouter")&&s.d(e,{useRouter:function(){return i.useRouter}})},48836:function(t,e,s){"use strict";s.r(e),s.d(e,{default:function(){return u}});var i=s(57437),a=s(2265),r=s(47907),n=s(27786),o=s(96146);function u(){let t=(0,r.useRouter)(),{isAuthenticated:e,isLoading:s}=(0,n.a)();return(0,a.useEffect)(()=>{s||(e?t.push("/dashboard"):t.push("/auth/login"))},[e,s,t]),(0,i.jsx)(o.SX,{message:"Redirecting..."})}},95899:function(t,e,s){"use strict";s.d(e,{_:function(){return i}});let i=console},34654:function(t,e,s){"use strict";s.d(e,{R:function(){return u},m:function(){return o}});var i=s(95899),a=s(79522),r=s(3864),n=s(34500);class o extends r.F{constructor(t){super(),this.defaultOptions=t.defaultOptions,this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.logger=t.logger||i._,this.observers=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(t){this.dispatch({type:"setState",state:t})}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.observers=this.observers.filter(e=>e!==t),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var t,e;return null!=(t=null==(e=this.retryer)?void 0:e.continue())?t:this.execute()}async execute(){var t,e,s,i,a,r,o,u,l,h,c,d,p,v,m,f,b,y,R,g;let C="loading"===this.state.status;try{if(!C){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(l=(h=this.mutationCache.config).onMutate)?void 0:l.call(h,this.state.variables,this));let t=await (null==(c=(d=this.options).onMutate)?void 0:c.call(d,this.state.variables));t!==this.state.context&&this.dispatch({type:"loading",context:t,variables:this.state.variables})}let p=await (()=>{var t;return this.retryer=(0,n.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(t=(e=this.mutationCache.config).onSuccess)?void 0:t.call(e,p,this.state.variables,this.state.context,this)),await (null==(s=(i=this.options).onSuccess)?void 0:s.call(i,p,this.state.variables,this.state.context)),await (null==(a=(r=this.mutationCache.config).onSettled)?void 0:a.call(r,p,null,this.state.variables,this.state.context,this)),await (null==(o=(u=this.options).onSettled)?void 0:o.call(u,p,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:p}),p}catch(t){try{throw await (null==(p=(v=this.mutationCache.config).onError)?void 0:p.call(v,t,this.state.variables,this.state.context,this)),await (null==(m=(f=this.options).onError)?void 0:m.call(f,t,this.state.variables,this.state.context)),await (null==(b=(y=this.mutationCache.config).onSettled)?void 0:b.call(y,void 0,t,this.state.variables,this.state.context,this)),await (null==(R=(g=this.options).onSettled)?void 0:R.call(g,void 0,t,this.state.variables,this.state.context)),t}finally{this.dispatch({type:"error",error:t})}}}dispatch(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"loading":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.Kw)(this.options.networkMode),status:"loading",variables:t.variables};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"};case"setState":return{...e,...t.state}}})(this.state),a.V.batch(()=>{this.observers.forEach(e=>{e.onMutationUpdate(t)}),this.mutationCache.notify({mutation:this,type:"updated",action:t})})}}function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},3864:function(t,e,s){"use strict";s.d(e,{F:function(){return a}});var i=s(31678);class a{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(t){this.cacheTime=Math.max(this.cacheTime||0,null!=t?t:i.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},8186:function(t,e,s){"use strict";s.d(e,{D:function(){return d}});var i=s(2265),a=s(31678),r=s(34654),n=s(79522),o=s(6761);class u extends o.l{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;let s=this.options;this.options=this.client.defaultMutationOptions(t),(0,a.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var t;null==(t=this.currentMutation)||t.removeObserver(this)}}onMutationUpdate(t){this.updateResult();let e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let t=this.currentMutation?this.currentMutation.state:(0,r.R)(),e={...t,isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=e}notify(t){n.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var e,s,i,a,r,n,o,u;t.onSuccess?(null==(e=(s=this.mutateOptions).onSuccess)||e.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(i=(a=this.mutateOptions).onSettled)||i.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):t.onError&&(null==(r=(n=this.mutateOptions).onError)||r.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(u=this.mutateOptions).onSettled)||o.call(u,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}t.listeners&&this.listeners.forEach(({listener:t})=>{t(this.currentResult)})})}}var l=s(97536),h=s(64095),c=s(3439);function d(t,e,s){let r=(0,a.lV)(t,e,s),o=(0,h.NL)({context:r.context}),[d]=i.useState(()=>new u(o,r));i.useEffect(()=>{d.setOptions(r)},[d,r]);let v=(0,l.$)(i.useCallback(t=>d.subscribe(n.V.batchCalls(t)),[d]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),m=i.useCallback((t,e)=>{d.mutate(t,e).catch(p)},[d]);if(v.error&&(0,c.L)(d.options.useErrorBoundary,[v.error]))throw v.error;return{...v,mutate:m,mutateAsync:v.mutate}}function p(){}}},function(t){t.O(0,[2150,9101,8939,1346,6877,2971,8069,1744],function(){return t(t.s=15700)}),_N_E=t.O()}]);