(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{60694:function(e,t,a){Promise.resolve().then(a.bind(a,67736))},67736:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return y}});var s=a(57437),o=a(27786),r=a(11546),n=a(31346),i=a(15671),l=a(33277),c=a(97307),u=a(75462),d=a(66260),g=a(26490),f=a(77326),h=a(34059),x=a(2975),m=a(47011),p=a(8792);function y(){var e,t,a,y,v,j,w,D,b,S;let{user:N}=(0,o.a)(),{isAdmin:T,isEditor:k,isModerator:L}=(0,r.TE)(),{data:A}=(0,n.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>x.L.getUpcomingAndLive({limit:5}),refetchInterval:3e4}),{data:F}=(0,n.a)({queryKey:["fixtures","all"],queryFn:()=>x.L.getFixtures({limit:1})}),{data:E}=(0,n.a)({queryKey:["leagues","all"],queryFn:()=>m.A.getLeagues({limit:1})}),C=[{title:"Total Fixtures",value:(null==F?void 0:null===(t=F.meta)||void 0===t?void 0:null===(e=t.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading...",icon:c.Z,description:"Active fixtures in database",loading:!F},{title:"Live Matches",value:(null==A?void 0:null===(a=A.data)||void 0===a?void 0:a.filter(e=>["1H","2H","HT"].includes(e.status)).length)||"0",icon:u.Z,description:"Currently live matches",loading:!A},{title:"Active Leagues",value:(null==E?void 0:null===(v=E.meta)||void 0===v?void 0:null===(y=v.totalItems)||void 0===y?void 0:y.toLocaleString())||"Loading...",icon:d.Z,description:"Currently active leagues",loading:!E},{title:"Upcoming Today",value:(null==A?void 0:null===(j=A.data)||void 0===j?void 0:j.filter(e=>"NS"===e.status).length)||"0",icon:g.Z,description:"Matches scheduled today",loading:!A}];return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[(()=>{let e=new Date().getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"})(),", ",(null==N?void 0:N.fullName)||(null==N?void 0:N.username),"!"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Welcome to the APISportsGame Content Management System"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(l.C,{className:"".concat((e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"editor":return"bg-blue-100 text-blue-800";case"moderator":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})((null==N?void 0:N.role)||"")," flex items-center space-x-1"),children:[(0,s.jsx)(f.Z,{className:"h-3 w-3"}),(0,s.jsx)("span",{className:"capitalize",children:null==N?void 0:N.role})]}),(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"Last login"}),(0,s.jsx)("p",{className:"font-medium",children:(null==N?void 0:N.lastLoginAt)?new Date(N.lastLoginAt).toLocaleDateString():"First time"})]})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:C.map((e,t)=>{let a=e.icon;return(0,s.jsx)(i.Zb,{children:(0,s.jsx)(i.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,s.jsx)("p",{className:"text-2xl font-bold ".concat(e.loading?"animate-pulse text-gray-400":"text-gray-900"),children:e.value}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,s.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,s.jsx)(a,{className:"h-6 w-6 text-blue-600"})})]})})},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsxs)(i.ll,{className:"flex items-center",children:[(0,s.jsx)(g.Z,{className:"mr-2 h-5 w-5"}),"Recent Activities"]}),(0,s.jsx)(i.SZ,{children:"Latest system activities and updates"})]}),(0,s.jsx)(i.aY,{children:(0,s.jsx)("div",{className:"space-y-4",children:[{action:"Fixture sync completed",time:"2 minutes ago",type:"sync"},{action:"New broadcast link added",time:"15 minutes ago",type:"broadcast"},{action:"League updated: Premier League",time:"1 hour ago",type:"league"},{action:"User tier upgraded",time:"2 hours ago",type:"user"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]})]},t))})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[(0,s.jsx)(i.ll,{children:"Quick Actions"}),(0,s.jsx)(i.SZ,{children:"Common tasks and shortcuts"})]}),(0,s.jsx)(i.aY,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(p.default,{href:"/dashboard/fixtures/live",className:"block",children:(0,s.jsx)("div",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.Z,{className:"h-5 w-5 text-blue-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"View Live Fixtures"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Check ongoing matches"})]})]}),((null==A?void 0:null===(D=A.data)||void 0===D?void 0:null===(w=D.filter(e=>["1H","2H","HT"].includes(e.status)))||void 0===w?void 0:w.length)||0)>0&&(0,s.jsxs)(l.C,{className:"bg-green-100 text-green-800",children:[(null==A?void 0:null===(S=A.data)||void 0===S?void 0:null===(b=S.filter(e=>["1H","2H","HT"].includes(e.status)))||void 0===b?void 0:b.length)||0," Live"]})]})})}),k()&&(0,s.jsx)("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(u.Z,{className:"h-5 w-5 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Add Broadcast Link"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Add new streaming link"})]})]})}),T()&&(0,s.jsx)("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(h.Z,{className:"h-5 w-5 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Manage Users"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"User administration"})]})]})})]})})]})]})]})}},33277:function(e,t,a){"use strict";a.d(t,{C:function(){return i}});var s=a(57437);a(2265);var o=a(49769),r=a(22169);let n=(0,o.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...o}=e;return(0,s.jsx)("div",{className:(0,r.cn)(n({variant:a}),t),...o})}},2975:function(e,t,a){"use strict";a.d(t,{L:function(){return o}});var s=a(74921);let o={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;void 0!==s&&a.append(t,s.toString())}),await s.x.get("/football/fixtures/schedules/".concat(e,"?").concat(a.toString()))},getFixtureStatistics:async e=>await s.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Season sync - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Season sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Season sync - No token found!"),e})();console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger season sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Daily sync - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Daily sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Daily sync - No token found!"),e})();console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger daily sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Sync status - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Sync status - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Sync status - No token found!"),e})();console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||"Failed to get sync status: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Sync status successful"),a},createFixture:async e=>{var t;let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Create fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Create fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Create fixture - No token found!"),e})();console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!a.Authorization,data:e});let s=await fetch("/api/fixtures",{method:"POST",headers:a,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",s.status,s.statusText,e),Error(e.message||"Failed to create fixture: ".concat(s.statusText))}let o=await s.json();return console.log("✅ Create fixture successful:",null===(t=o.data)||void 0===t?void 0:t.id),o.data||o},updateFixture:async(e,t)=>{let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Update fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Update fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Update fixture - No token found!"),e})();console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!a.Authorization,data:t});let s=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:a,body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",s.status,s.statusText,e),Error(e.message||"Failed to update fixture: ".concat(s.statusText))}let o=await s.json();return console.log("✅ Update fixture successful:",e),o.data||o},deleteFixture:async e=>{let t=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Delete fixture - No token found!"),e})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/statistics"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture statistics: ".concat(t.statusText));return await t.json()},getFixtureEvents:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/events"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture events: ".concat(t.statusText));return await t.json()},getFixture:async e=>(await o.getFixtureById(e)).data}},47011:function(e,t,a){"use strict";a.d(t,{A:function(){return r}});var s=a(74921);let o=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},r={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?"".concat(e,"-").concat(t):e.toString(),s=await fetch("/api/leagues/".concat(a),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch league ".concat(e));return await s.json()},createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let s=o(),n={"Content-Type":"application/json"};s&&(n.Authorization="Bearer ".concat(s));let i=await r.getLeagueById(e,a);if(!i||!i.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));let l=await fetch("/api/leagues/".concat(i.id),{method:"PATCH",headers:n,body:JSON.stringify(t)});if(!l.ok)throw Error((await l.json()).message||"Failed to update league ".concat(e));return await l.json()},deleteLeague:async(e,t)=>{let a=await r.getLeagueById(e,t);if(!a||!a.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await s.x.delete("/football/leagues/".concat(a.id))},getActiveLeagues:async()=>r.getLeagues({active:!0}),getLeaguesByCountry:async e=>r.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>r.updateLeague(e,{active:t},a)}},11546:function(e,t,a){"use strict";a.d(t,{TE:function(){return c},a1:function(){return l}});var s=a(57437),o=a(2265),r=a(47907),n=a(27786),i=a(96146);let l=e=>{let{children:t,requiredRole:a,fallbackUrl:l="/auth/login"}=e,c=(0,r.useRouter)(),{isAuthenticated:u,user:d,isLoading:g}=(0,n.a)();if((0,o.useEffect)(()=>{if(!g){if(!u||!d){c.push(l);return}if(a&&!(Array.isArray(a)?a:[a]).includes(d.role)){c.push("/dashboard?error=unauthorized");return}}},[u,d,g,a,c,l]),g)return(0,s.jsx)(i.SX,{message:"Verifying authentication..."});if(!u||!d)return(0,s.jsx)(i.SX,{message:"Redirecting to login..."});if(a){let e=Array.isArray(a)?a:[a];if(!e.includes(d.role))return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",d.role]})]})})}return(0,s.jsx)(s.Fragment,{children:t})},c=()=>{let{user:e}=(0,n.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),a=()=>t("admin"),s=()=>t(["admin","editor"]),o=()=>t(["admin","editor","moderator"]),r=()=>a(),i=()=>s(),l=()=>o(),c=()=>a();return{user:e,hasRole:t,isAdmin:a,isEditor:s,isModerator:o,canManageUsers:r,canManageContent:i,canModerate:l,canSync:c,can:e=>{switch(e){case"manage-users":return r();case"manage-content":return i();case"moderate":return l();case"sync":return c();default:return!1}}}}}},function(e){e.O(0,[2150,9101,8939,1346,8792,3027,6877,2971,8069,1744],function(){return e(e.s=60694)}),_N_E=e.O()}]);