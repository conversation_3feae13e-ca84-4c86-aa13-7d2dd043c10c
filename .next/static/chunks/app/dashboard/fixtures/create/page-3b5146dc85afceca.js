(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2622],{41514:function(e,a,l){Promise.resolve().then(l.bind(l,20160))},20160:function(e,a,l){"use strict";l.r(a),l.d(a,{default:function(){return T}});var s=l(57437),t=l(2265),r=l(47907),i=l(31346),d=l(8186),n=l(15671),o=l(575),c=l(17818),u=l(78807),m=l(77625),h=l(85110),g=l(2975),x=l(47011),v=l(33016),p=l(53879),j=l(97307),b=l(70699),y=l(56288),f=l(78789);let N=[{value:"TBD",label:"Time To Be Defined"},{value:"NS",label:"Not Started"},{value:"ST",label:"Scheduled"},{value:"1H",label:"First Half"},{value:"HT",label:"Halftime"},{value:"2H",label:"Second Half"},{value:"ET",label:"Extra Time"},{value:"BT",label:"Break Time"},{value:"P",label:"Penalty In Progress"},{value:"SUSP",label:"Match Suspended"},{value:"INT",label:"Match Interrupted"},{value:"FT",label:"Match Finished (Regular Time)"},{value:"AET",label:"Match Finished (After Extra Time)"},{value:"PEN",label:"Match Finished (After Penalty)"},{value:"PST",label:"Match Postponed"},{value:"CANC",label:"Match Cancelled"},{value:"ABD",label:"Match Abandoned"},{value:"AWD",label:"Technical Loss"},{value:"WO",label:"WalkOver"},{value:"LIVE",label:"In Progress"}];function T(){let e=(0,r.useRouter)(),[a,l]=(0,t.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:"",status:"NS",goalsHome:"",goalsAway:"",elapsed:"",isHot:!1}),[T,I]=(0,t.useState)({}),[D,S]=(0,t.useState)(""),[C,w]=(0,t.useState)(""),[P,F]=(0,t.useState)(""),{data:A,isLoading:L,error:O}=(0,i.a)({queryKey:["leagues","active","search",P],queryFn:()=>x.A.getLeagues({limit:100,active:!0,search:P||void 0})}),{data:H,isLoading:M,error:k}=(0,i.a)({queryKey:["teams","search",D,C],queryFn:()=>v.k.getTeams({limit:100,search:D||C||void 0})}),E=(0,d.D)({mutationFn:e=>g.L.createFixture(e),onSuccess:()=>{y.toast.success("Fixture created successfully"),e.push("/dashboard/fixtures")},onError:e=>{y.toast.error(e.message||"Failed to create fixture")}}),q=(0,t.useCallback)(e=>{S(e)},[]),U=(0,t.useCallback)(e=>{w(e)},[]),V=(0,t.useCallback)(e=>{F(e)},[]),B=(0,t.useMemo)(()=>{var e;return(null==A?void 0:null===(e=A.data)||void 0===e?void 0:e.map(e=>{var a,l;let s="",t=e.country;return(null===(a=e.season_detail)||void 0===a?void 0:a.year)?(s="Season ".concat(e.season_detail.year),e.season_detail.current&&(s+=" (Current)")):e.season&&(s="Season ".concat(e.season)),s&&(t="".concat(e.country," • ").concat(s)),{value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:"league-".concat(e.id),subtitle:t,externalId:e.externalId,season:(null===(l=e.season_detail)||void 0===l?void 0:l.year)||e.season}}))||[]},[A]),Z=(0,t.useMemo)(()=>{var e;return(null==H?void 0:null===(e=H.data)||void 0===e?void 0:e.map(e=>({value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:"home-team-".concat(e.id),externalId:e.externalId})))||[]},[H]),_=(0,t.useMemo)(()=>{var e;return(null==H?void 0:null===(e=H.data)||void 0===e?void 0:e.map(e=>({value:e.id.toString(),label:e.name,logo:e.logo,uniqueKey:"away-team-".concat(e.id),externalId:e.externalId})))||[]},[H]),z=Z.find(e=>e.value===a.homeTeamId),R=_.find(e=>e.value===a.awayTeamId),W=B.find(e=>e.value===a.leagueId),K=()=>{var e;let l={};return console.log("\uD83D\uDD0D Form validation - Current formData:",{homeTeamId:a.homeTeamId,awayTeamId:a.awayTeamId,leagueId:a.leagueId,date:a.date,time:a.time,timeLength:null===(e=a.time)||void 0===e?void 0:e.length,timeType:typeof a.time}),a.homeTeamId||(l.homeTeamId="Home team is required"),a.awayTeamId||(l.awayTeamId="Away team is required"),a.leagueId||(l.leagueId="League is required"),a.date||(l.date="Date is required"),a.time&&""!==a.time.trim()||(l.time="Time is required",console.log("❌ Time validation failed:",{time:a.time,isEmpty:!a.time})),a.homeTeamId===a.awayTeamId&&(l.awayTeamId="Away team must be different from home team"),console.log("\uD83D\uDD0D Validation errors:",l),I(l),0===Object.keys(l).length},G=(e,s)=>{console.log("\uD83D\uDCDD Updating ".concat(e,":"),{oldValue:a[e],newValue:s}),l(a=>({...a,[e]:s})),T[e]&&I(a=>({...a,[e]:void 0}))},Y=e=>{let{label:a,selectedOption:l,placeholder:t="Not selected"}=e;return(0,s.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:a}),l?(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[l.logo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(l.logo),alt:l.label,className:"w-8 h-8 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:l.label}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,s.jsxs)("div",{children:["Internal ID: ",l.value]}),l.externalId&&(0,s.jsxs)("div",{className:"text-green-600",children:["\uD83D\uDD17 External ID: ",l.externalId]}),l.season&&(0,s.jsxs)("div",{className:"text-purple-600",children:["\uD83D\uDCC5 Season: ",l.season]}),l.subtitle&&(0,s.jsxs)("div",{className:"text-blue-600",children:["\uD83D\uDCCD ",l.subtitle]})]})]})]}):(0,s.jsx)("div",{className:"text-gray-500 italic",children:t})]})},J=L||M;return O||k?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(o.z,{variant:"outline",onClick:()=>e.back(),children:[(0,s.jsx)(p.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,s.jsx)(n.Zb,{children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[!!O&&(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load leagues"}),!!k&&(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load teams"}),(0,s.jsx)(o.z,{onClick:()=>e.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(f.Z,{variant:"create",isLoading:E.isLoading}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Fixture"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Add a new football fixture to the system"})]})]}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsxs)(n.Ol,{children:[(0,s.jsxs)(n.ll,{className:"flex items-center",children:[(0,s.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),(0,s.jsx)(n.SZ,{children:"Fill in the details for the new fixture"})]}),(0,s.jsx)(n.aY,{children:J?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(m.Od,{className:"h-6 w-48"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(m.Od,{className:"h-20 w-full"}),(0,s.jsx)(m.Od,{className:"h-10 w-full"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(m.Od,{className:"h-20 w-full"}),(0,s.jsx)(m.Od,{className:"h-10 w-full"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(m.Od,{className:"h-20 w-full"}),(0,s.jsx)(m.Od,{className:"h-10 w-full"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(m.Od,{className:"h-6 w-32"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(m.Od,{className:"h-10 w-full"}),(0,s.jsx)(m.Od,{className:"h-10 w-full"})]})]})]}):(0,s.jsxs)("form",{onSubmit:e=>{var l;if(e.preventDefault(),!K()){y.toast.error("Please fix the form errors");return}let[s,t,r]=a.date.split("-").map(Number),[i,d]=a.time.split(":").map(Number),n=new Date(s,t-1,r,i,d,0,0),o=new Date(Date.UTC(s,t-1,r,i,d,0,0));console.log("\uD83D\uDD50 DateTime conversion:",{inputDate:a.date,inputTime:a.time,localDateTime:n.toISOString(),utcDateTime:o.toISOString(),userTimezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezoneOffset:n.getTimezoneOffset()});let c=B.find(e=>e.value===a.leagueId),u=Z.find(e=>e.value===a.homeTeamId),m=_.find(e=>e.value===a.awayTeamId);if(!c||!u||!m){y.toast.error("Please ensure all teams and league are properly selected");return}let h={leagueId:c.externalId,season:c.season||2024,homeTeamId:u.externalId,awayTeamId:m.externalId,date:o.toISOString(),round:a.round||null,venueName:a.venueName||null,venueCity:a.venueCity||null,referee:a.referee||null,isHot:a.isHot,data:{homeTeamName:u.label,awayTeamName:m.label,status:a.status,statusLong:{TBD:"Time To Be Defined",NS:"Not Started",ST:"Scheduled","1H":"First Half",HT:"Halftime","2H":"Second Half",ET:"Extra Time",BT:"Break Time",P:"Penalty In Progress",SUSP:"Match Suspended",INT:"Match Interrupted",FT:"Match Finished",AET:"Match Finished After Extra Time",PEN:"Match Finished After Penalty",PST:"Match Postponed",CANC:"Match Cancelled",ABD:"Match Abandoned",AWD:"Technical Loss",WO:"WalkOver",LIVE:"In Progress"}[l=a.status]||l,statusExtra:0,elapsed:a.elapsed?parseInt(a.elapsed):0,goalsHome:a.goalsHome?parseInt(a.goalsHome):0,goalsAway:a.goalsAway?parseInt(a.goalsAway):0}};console.log("\uD83D\uDE80 Fixture Create Payload:",JSON.stringify(h,null,2)),E.mutate(h)},className:"space-y-6",children:[(0,s.jsxs)(c.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(Y,{label:"Selected Home Team",selectedOption:z,placeholder:"No home team selected"}),(0,s.jsx)(u.L,{label:"Home Team",placeholder:M?"Loading teams...":"Select home team",searchPlaceholder:"Search teams...",required:!0,value:a.homeTeamId,onValueChange:e=>G("homeTeamId",e),options:Z,error:T.homeTeamId,disabled:M,onSearch:q,isLoading:M})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(Y,{label:"Selected Away Team",selectedOption:R,placeholder:"No away team selected"}),(0,s.jsx)(u.L,{label:"Away Team",placeholder:M?"Loading teams...":"Select away team",searchPlaceholder:"Search teams...",required:!0,value:a.awayTeamId,onValueChange:e=>G("awayTeamId",e),options:_.filter(e=>e.value!==a.homeTeamId),error:T.awayTeamId,disabled:M,onSearch:U,isLoading:M})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(Y,{label:"Selected League",selectedOption:W,placeholder:"No league selected"}),(0,s.jsx)(u.L,{label:"League",placeholder:L?"Loading leagues...":"Select league",searchPlaceholder:"Search leagues...",required:!0,value:a.leagueId,onValueChange:e=>G("leagueId",e),options:B,error:T.leagueId,disabled:L,onSearch:V,isLoading:L})]})]}),(0,s.jsxs)(c.hj,{title:"Schedule",description:"Set the date and time",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.UP,{label:"Date *",type:"date",required:!0,value:a.date,onChange:e=>G("date",e.target.value),error:T.date,description:"Match date"}),(0,s.jsx)(c.UP,{label:"Time *",type:"time",required:!0,value:a.time,onChange:e=>G("time",e.target.value),error:T.time,description:"Local time (".concat(Intl.DateTimeFormat().resolvedOptions().timeZone,") - will be converted to UTC")})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:[(0,s.jsxs)("p",{className:"flex items-center mb-2",children:[(0,s.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83C\uDF0D"}),(0,s.jsx)("strong",{children:"Timezone Conversion:"})," Enter time in your local timezone (",Intl.DateTimeFormat().resolvedOptions().timeZone,")."]}),(0,s.jsxs)("p",{className:"flex items-center text-xs",children:[(0,s.jsx)("span",{className:"text-green-600 mr-2",children:"✅"}),"The system will automatically convert to UTC for API storage. The asterisk (*) indicates required fields."]})]})]}),(0,s.jsxs)(c.hj,{title:"Match Status",description:"Set initial match status and score",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(c.mg,{label:"Status",placeholder:"Select status",required:!0,value:a.status,onValueChange:e=>G("status",e),options:N,error:T.status}),(0,s.jsx)(c.UP,{label:"Home Goals",type:"number",min:"0",value:a.goalsHome,onChange:e=>G("goalsHome",e.target.value),description:"Leave empty for scheduled matches"}),(0,s.jsx)(c.UP,{label:"Away Goals",type:"number",min:"0",value:a.goalsAway,onChange:e=>G("goalsAway",e.target.value),description:"Leave empty for scheduled matches"})]}),(0,s.jsx)(c.UP,{label:"Elapsed Time (minutes)",type:"number",min:"0",max:"120",value:a.elapsed,onChange:e=>G("elapsed",e.target.value),description:"Minutes played in the match (for live/finished matches)"})]}),(0,s.jsx)(c.hj,{title:"Fixture Settings",description:"Additional fixture settings",children:(0,s.jsx)(h.Z,{checked:a.isHot,onCheckedChange:e=>l(a=>({...a,isHot:e})),label:"Hot Fixture",description:"Mark this fixture as hot/featured",variant:"danger"})}),(0,s.jsxs)(c.hj,{title:"Venue & Match Information",description:"Venue details and match context",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.UP,{label:"Venue Name",placeholder:"Stadium name",value:a.venueName,onChange:e=>G("venueName",e.target.value)}),(0,s.jsx)(c.UP,{label:"Venue City",placeholder:"City",value:a.venueCity,onChange:e=>G("venueCity",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:a.round,onChange:e=>G("round",e.target.value)}),(0,s.jsx)(c.UP,{label:"Referee",placeholder:"Referee name",value:a.referee||"",onChange:e=>G("referee",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(c.UP,{label:"Temperature (\xb0C)",type:"number",placeholder:"e.g., 22",value:a.temperature||"",onChange:e=>G("temperature",e.target.value)}),(0,s.jsx)(c.UP,{label:"Weather",placeholder:"e.g., Sunny, Rainy",value:a.weather||"",onChange:e=>G("weather",e.target.value)}),(0,s.jsx)(c.UP,{label:"Attendance",type:"number",placeholder:"Number of spectators",value:a.attendance||"",onChange:e=>G("attendance",e.target.value)})]})]}),(0,s.jsxs)(c.iN,{children:[(0,s.jsx)(o.z,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:E.isLoading,children:"Cancel"}),(0,s.jsxs)(o.z,{type:"submit",disabled:E.isLoading,children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),E.isLoading?"Creating...":"Create Fixture"]})]})]})})]})]})}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,8041,174,1564,6389,2971,8069,1744],function(){return e(e.s=41514)}),_N_E=e.O()}]);