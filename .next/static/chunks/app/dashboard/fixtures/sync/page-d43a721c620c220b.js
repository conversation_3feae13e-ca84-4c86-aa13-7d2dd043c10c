(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[823],{51386:function(e,s,t){Promise.resolve().then(t.bind(t,40575))},95032:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},53879:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},97307:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},34187:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26490:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},93276:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},79580:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},40834:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},29733:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},69724:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47907:function(e,s,t){"use strict";var r=t(15313);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},40575:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return k}});var r=t(57437),a=t(2265),n=t(64095),i=t(31346),l=t(8186),c=t(15671),d=t(575),u=t(33277),o=t(77625),x=t(34187);let h=(0,t(57977).Z)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=t(69724),f=t(40834),y=t(26490),g=t(95032),j=t(93276),p=t(29733),v=t(97307),b=t(56288),N=t(2975),S=t(11546),w=t(78789);let Z=e=>{switch(e){case"daily":return"Daily Leagues Sync";case"season":return"Fixtures Status Sync";case"manual":return"Manual Sync";default:return"Unknown Sync"}};function k(){var e,s;(0,n.NL)();let[t,k]=(0,a.useState)([]),[R,O]=(0,a.useState)(!0),{data:M,isLoading:D,refetch:L}=(0,i.a)({queryKey:["fixtures-sync-status"],queryFn:()=>N.L.getSyncStatus(),refetchInterval:!!R&&5e3}),A=(0,l.D)({mutationFn:()=>N.L.triggerSeasonSync(),onSuccess:e=>{var s;b.toast.success("Fixtures status sync completed successfully!"),C({id:Date.now().toString(),type:"season",status:"success",startTime:new Date().toISOString(),endTime:new Date().toISOString(),duration:(null===(s=e.details)||void 0===s?void 0:s.duration)||"N/A",fixturesProcessed:e.fixturesUpserted||0,errors:[],details:e.details}),L()},onError:e=>{b.toast.error("Fixtures sync failed: ".concat(e.message)),C({id:Date.now().toString(),type:"season",status:"failed",startTime:new Date().toISOString(),endTime:new Date().toISOString(),fixturesProcessed:0,errors:[e.message]})}}),z=(0,l.D)({mutationFn:()=>N.L.triggerDailySync(),onSuccess:e=>{var s;b.toast.success("Daily leagues sync completed successfully!"),C({id:Date.now().toString(),type:"daily",status:"success",startTime:new Date().toISOString(),endTime:new Date().toISOString(),duration:(null===(s=e.details)||void 0===s?void 0:s.duration)||"N/A",fixturesProcessed:e.fixturesUpserted||0,errors:[],details:e.details}),L()},onError:e=>{b.toast.error("Daily sync failed: ".concat(e.message)),C({id:Date.now().toString(),type:"daily",status:"failed",startTime:new Date().toISOString(),endTime:new Date().toISOString(),fixturesProcessed:0,errors:[e.message]})}}),C=e=>{k(s=>[e,...s.slice(0,9)])},F=e=>new Date(e).toLocaleString(),P=e=>{switch(e){case"success":return(0,r.jsx)(x.Z,{className:"h-4 w-4 text-green-600"});case"failed":return(0,r.jsx)(h,{className:"h-4 w-4 text-red-600"});case"warning":return(0,r.jsx)(m.Z,{className:"h-4 w-4 text-yellow-600"});case"running":return(0,r.jsx)(f.Z,{className:"h-4 w-4 text-blue-600 animate-spin"});default:return(0,r.jsx)(y.Z,{className:"h-4 w-4 text-gray-600"})}},E=e=>(0,r.jsxs)(u.C,{variant:{success:"default",failed:"destructive",warning:"secondary",running:"outline"}[e]||"outline",children:[P(e),(0,r.jsx)("span",{className:"ml-1 capitalize",children:e})]}),I=z.isLoading||A.isLoading;return(0,r.jsx)(S.a1,{requiredRole:"admin",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(w.Z,{variant:"detail"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(f.Z,{className:"mr-2 h-6 w-6"}),"Fixtures Sync Management"]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Synchronize fixture data from API Football service"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>O(!R),children:[(0,r.jsx)(g.Z,{className:"h-4 w-4 mr-2 ".concat(R?"text-green-600":"text-gray-400")}),"Auto Refresh ",R?"ON":"OFF"]}),(0,r.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>L(),disabled:D,children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 mr-2 ".concat(D?"animate-spin":"")}),"Refresh"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(c.Zb,{children:(0,r.jsx)(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.Z,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Last Sync"}),D?(0,r.jsx)(o.Od,{className:"h-4 w-20 mt-1"}):(0,r.jsx)("p",{className:"text-sm font-bold",children:(null==M?void 0:M.lastSync)?F(M.lastSync):"Never"})]})]})})}),(0,r.jsx)(c.Zb,{children:(0,r.jsx)(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.Z,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Fixtures"}),D?(0,r.jsx)(o.Od,{className:"h-4 w-16 mt-1"}):(0,r.jsx)("p",{className:"text-lg font-bold",children:(null==M?void 0:null===(e=M.fixtures)||void 0===e?void 0:e.toLocaleString())||0})]})]})})}),(0,r.jsx)(c.Zb,{children:(0,r.jsx)(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-yellow-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Sync Errors"}),D?(0,r.jsx)(o.Od,{className:"h-4 w-8 mt-1"}):(0,r.jsx)("p",{className:"text-lg font-bold text-red-600",children:(null==M?void 0:null===(s=M.errors)||void 0===s?void 0:s.length)||0})]})]})})}),(0,r.jsx)(c.Zb,{children:(0,r.jsx)(c.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.Z,{className:"h-5 w-5 text-purple-600"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Status"}),(0,r.jsx)("div",{className:"flex items-center mt-1",children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 text-blue-600 animate-spin mr-1"}),(0,r.jsx)("span",{className:"text-sm font-bold text-blue-600",children:"Syncing..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.Z,{className:"h-4 w-4 text-green-600 mr-1"}),(0,r.jsx)("span",{className:"text-sm font-bold text-green-600",children:"Ready"})]})})]})]})})})]}),(null==M?void 0:M.errors)&&M.errors.length>0&&(0,r.jsxs)(c.Zb,{className:"border-red-200 bg-red-50",children:[(0,r.jsx)(c.Ol,{children:(0,r.jsxs)(c.ll,{className:"text-red-800 flex items-center",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 mr-2"}),"Recent Sync Errors"]})}),(0,r.jsx)(c.aY,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[M.errors.slice(0,3).map((e,s)=>(0,r.jsx)("div",{className:"text-sm text-red-700 bg-red-100 p-2 rounded",children:"string"==typeof e?e:JSON.stringify(e)},s)),M.errors.length>3&&(0,r.jsxs)("p",{className:"text-sm text-red-600",children:["... and ",M.errors.length-3," more errors"]})]})})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[(0,r.jsx)(c.ll,{children:"Quick Sync Actions"}),(0,r.jsx)(c.SZ,{children:"Trigger manual sync operations for active leagues"})]}),(0,r.jsxs)(c.aY,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"p-4 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Fixtures Status Sync"}),(0,r.jsx)(j.Z,{className:"h-5 w-5 text-blue-600"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Updates fixture status for active leagues (scores, match status, elapsed time)"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mb-3",children:[(0,r.jsx)("strong",{children:"API:"})," ",(0,r.jsx)("code",{children:"/football/fixtures/sync/fixtures"})]}),(0,r.jsxs)(d.z,{onClick:()=>A.mutate(),disabled:I,className:"w-full",size:"sm",children:[(0,r.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),A.isLoading?"Syncing Fixtures...":"Sync Fixtures Status"]})]}),(0,r.jsxs)("div",{className:"p-4 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Daily Leagues Sync"}),(0,r.jsx)(v.Z,{className:"h-5 w-5 text-green-600"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Updates leagues data for active leagues (daily incremental sync)"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mb-3",children:[(0,r.jsx)("strong",{children:"API:"})," ",(0,r.jsx)("code",{children:"/football/fixtures/sync/daily"})]}),(0,r.jsxs)(d.z,{onClick:()=>z.mutate(),disabled:I,variant:"outline",className:"w-full",size:"sm",children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),z.isLoading?"Syncing Leagues...":"Sync Daily Leagues"]})]})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)(d.z,{onClick:()=>L(),variant:"outline",disabled:D,className:"flex items-center",size:"sm",children:[(0,r.jsx)(f.Z,{className:"mr-2 h-4 w-4 ".concat(D?"animate-spin":"")}),"Refresh Status"]})}),I&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 text-blue-600 animate-spin mr-2"}),(0,r.jsxs)("span",{className:"text-blue-800 font-medium",children:[A.isLoading?"Fixtures Status Sync":"Daily Leagues Sync"," in progress..."]})]}),(0,r.jsx)("span",{className:"text-sm text-blue-600",children:new Date().toLocaleTimeString()})]}),(0,r.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full animate-pulse",style:{width:"45%"}})}),(0,r.jsx)("p",{className:"text-xs text-blue-700 mt-2",children:"Please do not close this page while sync is running."})]}),(0,r.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg border",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCB API Endpoints Information:"}),(0,r.jsxs)("div",{className:"space-y-2 text-xs text-gray-600",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"1. Fixtures Status:"})," ",(0,r.jsx)("code",{children:"/football/fixtures/sync/fixtures"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"ml-4",children:"→ Updates fixture status for active leagues (scores, match status, etc.)"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"2. Daily Leagues:"})," ",(0,r.jsx)("code",{children:"/football/fixtures/sync/daily"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"ml-4",children:"→ Updates leagues data for active leagues (daily incremental)"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"3. Sync Status:"})," ",(0,r.jsx)("code",{children:"/football/fixtures/sync/status"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"ml-4",children:"→ Gets current sync status and statistics"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,r.jsxs)("p",{children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"Usage Tips:"})]}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Fixtures Status Sync:"})," Use when you need to update match results and status"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Daily Leagues Sync:"})," Use for regular league data updates"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Auto Refresh:"})," Page updates every 5 seconds when enabled"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Active Leagues Only:"})," Both syncs only process currently active leagues"]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[(0,r.jsx)(c.ll,{children:"Recent Sync Operations"}),(0,r.jsx)(c.SZ,{children:"History of sync operations and their results"})]}),(0,r.jsx)(c.aY,{children:0===t.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(j.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"No sync operations yet. Start your first sync above."})]}):(0,r.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[P(e.status),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:Z(e.type)}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:F(e.startTime)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.fixturesProcessed," fixtures"]}),e.duration&&(0,r.jsx)("p",{className:"text-xs text-gray-600",children:e.duration})]}),E(e.status)]})]},e.id))})})]})]})})}},33277:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(57437);t(2265);var a=t(49769),n=t(22169);let i=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...a})}},11546:function(e,s,t){"use strict";t.d(s,{TE:function(){return d},a1:function(){return c}});var r=t(57437),a=t(2265),n=t(47907),i=t(27786),l=t(96146);let c=e=>{let{children:s,requiredRole:t,fallbackUrl:c="/auth/login"}=e,d=(0,n.useRouter)(),{isAuthenticated:u,user:o,isLoading:x}=(0,i.a)();if((0,a.useEffect)(()=>{if(!x){if(!u||!o){d.push(c);return}if(t&&!(Array.isArray(t)?t:[t]).includes(o.role)){d.push("/dashboard?error=unauthorized");return}}},[u,o,x,t,d,c]),x)return(0,r.jsx)(l.SX,{message:"Verifying authentication..."});if(!u||!o)return(0,r.jsx)(l.SX,{message:"Redirecting to login..."});if(t){let e=Array.isArray(t)?t:[t];if(!e.includes(o.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",o.role]})]})})}return(0,r.jsx)(r.Fragment,{children:s})},d=()=>{let{user:e}=(0,i.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),t=()=>s("admin"),r=()=>s(["admin","editor"]),a=()=>s(["admin","editor","moderator"]),n=()=>t(),l=()=>r(),c=()=>a(),d=()=>t();return{user:e,hasRole:s,isAdmin:t,isEditor:r,isModerator:a,canManageUsers:n,canManageContent:l,canModerate:c,canSync:d,can:e=>{switch(e){case"manage-users":return n();case"manage-content":return l();case"moderate":return c();case"sync":return d();default:return!1}}}}},8186:function(e,s,t){"use strict";t.d(s,{D:function(){return x}});var r=t(2265),a=t(31678),n=t(34654),i=t(79522),l=t(6761);class c extends l.l{constructor(e,s){super(),this.client=e,this.setOptions(s),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var s;let t=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(t,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(s=this.currentMutation)||s.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let s={listeners:!0};"success"===e.type?s.onSuccess=!0:"error"===e.type&&(s.onError=!0),this.notify(s)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,s){return this.mutateOptions=s,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,n.R)(),s={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=s}notify(e){i.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var s,t,r,a,n,i,l,c;e.onSuccess?(null==(s=(t=this.mutateOptions).onSuccess)||s.call(t,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(a=this.mutateOptions).onSettled)||r.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(n=(i=this.mutateOptions).onError)||n.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(l=(c=this.mutateOptions).onSettled)||l.call(c,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var d=t(97536),u=t(64095),o=t(3439);function x(e,s,t){let n=(0,a.lV)(e,s,t),l=(0,u.NL)({context:n.context}),[x]=r.useState(()=>new c(l,n));r.useEffect(()=>{x.setOptions(n)},[x,n]);let m=(0,d.$)(r.useCallback(e=>x.subscribe(i.V.batchCalls(e)),[x]),()=>x.getCurrentResult(),()=>x.getCurrentResult()),f=r.useCallback((e,s)=>{x.mutate(e,s).catch(h)},[x]);if(m.error&&(0,o.L)(x.options.useErrorBoundary,[m.error]))throw m.error;return{...m,mutate:f,mutateAsync:m.mutate}}function h(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,6877,1564,2971,8069,1744],function(){return e(e.s=51386)}),_N_E=e.O()}]);