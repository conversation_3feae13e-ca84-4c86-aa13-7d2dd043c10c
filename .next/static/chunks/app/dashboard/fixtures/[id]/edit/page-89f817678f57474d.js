(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3660],{60340:function(e,a,t){Promise.resolve().then(t.bind(t,56710))},56710:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return N}});var l=t(57437),s=t(2265),r=t(47907),i=t(64095),n=t(31346),d=t(8186),o=t(15671),c=t(575),u=t(17818),m=t(78807),h=t(77625),g=t(85110),x=t(2975),v=t(47011),p=t(33016),y=t(53879),j=t(97307),b=t(70699),f=t(56288),T=t(78789);function N(){let e=(0,r.useParams)(),a=(0,r.useRouter)(),t=(0,i.NL)(),N=parseInt(e.id),[I,w]=(0,s.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:"",status:"",goalsHome:"",goalsAway:"",elapsed:"",isHot:!1}),[S,C]=(0,s.useState)({}),{data:P,isLoading:H}=(0,n.a)({queryKey:["fixture",N],queryFn:()=>x.L.getFixture(N),enabled:!!N}),[A,F]=(0,s.useState)(""),[L,O]=(0,s.useState)(""),[k,E]=(0,s.useState)([]),[M,q]=(0,s.useState)(""),[U,D]=(0,s.useState)([]),{data:B,isLoading:Z,error:K}=(0,n.a)({queryKey:["leagues"],queryFn:()=>v.A.getLeagues({limit:100})}),{data:V,isLoading:z,error:R}=(0,n.a)({queryKey:["teams"],queryFn:()=>p.k.getTeams({limit:100})});(0,s.useEffect)(()=>{if(!L.trim()){E([]);return}let e=setTimeout(async()=>{try{let e=await p.k.getTeams({limit:100,search:L});(null==e?void 0:e.data)&&E(e.data)}catch(e){console.error("❌ Home team search error:",e),E([])}},3e3);return()=>clearTimeout(e)},[L]),(0,s.useEffect)(()=>{if(!M.trim()){D([]);return}let e=setTimeout(async()=>{try{let e=await p.k.getTeams({limit:100,search:M});(null==e?void 0:e.data)&&D(e.data)}catch(e){console.error("❌ Away team search error:",e),D([])}},3e3);return()=>clearTimeout(e)},[M]);let W=(0,d.D)({mutationFn:e=>x.L.updateFixture(N,e),onSuccess:()=>{t.invalidateQueries({queryKey:["fixture",N]}),t.invalidateQueries({queryKey:["fixtures"]}),f.toast.success("Fixture updated successfully"),a.push("/dashboard/fixtures/".concat(N))},onError:e=>{f.toast.error(e.message||"Failed to update fixture")}});(0,s.useEffect)(()=>{if(P){var e,a,t,l,s,r,i,n,d,o;let c=new Date(P.date);w({homeTeamId:(null===(e=P.homeTeamId)||void 0===e?void 0:e.toString())||"",awayTeamId:(null===(a=P.awayTeamId)||void 0===a?void 0:a.toString())||"",leagueId:(null===(t=P.leagueId)||void 0===t?void 0:t.toString())||"",date:c.toISOString().split("T")[0],time:c.toTimeString().slice(0,5),venueName:(null===(l=P.venue)||void 0===l?void 0:l.name)||P.venueName||"",venueCity:(null===(s=P.venue)||void 0===s?void 0:s.city)||P.venueCity||"",round:P.round||"",status:P.status||"",goalsHome:(null===(r=P.goalsHome)||void 0===r?void 0:r.toString())||"",goalsAway:(null===(i=P.goalsAway)||void 0===i?void 0:i.toString())||"",elapsed:(null===(n=P.elapsed)||void 0===n?void 0:n.toString())||"",referee:P.referee||"",temperature:(null===(d=P.temperature)||void 0===d?void 0:d.toString())||"",weather:P.weather||"",attendance:(null===(o=P.attendance)||void 0===o?void 0:o.toString())||"",isHot:P.isHot||!1})}},[P]),(0,s.useEffect)(()=>{P&&P.status&&!I.status&&w(e=>({...e,status:P.status}))},[P,I.status]);let _=()=>{let e={};return I.homeTeamId||(e.homeTeamId="Home team is required"),I.awayTeamId||(e.awayTeamId="Away team is required"),I.leagueId||(e.leagueId="League is required"),I.date||(e.date="Date is required"),I.time||(e.time="Time is required"),I.status||(e.status="Status is required"),I.homeTeamId===I.awayTeamId&&(e.awayTeamId="Away team must be different from home team"),C(e),0===Object.keys(e).length},Q=(e,a)=>{w(t=>({...t,[e]:a})),S[e]&&C(a=>({...a,[e]:void 0}))},Y=(0,s.useCallback)(e=>{F(e)},[]),G=(0,s.useCallback)(e=>{O(e)},[]),J=(0,s.useCallback)(e=>{q(e)},[]),X=(0,s.useMemo)(()=>{var e;return(null==B?void 0:null===(e=B.data)||void 0===e?void 0:e.map((e,a)=>({value:e.externalId.toString(),label:"".concat(e.name).concat(e.season?" (".concat(e.season,")"):""),logo:e.logo,season:e.season,uniqueKey:"league-".concat(e.id||e.externalId,"-").concat(a)})))||[]},[null==B?void 0:B.data]),$=(0,s.useMemo)(()=>{var e;return(null==V?void 0:null===(e=V.data)||void 0===e?void 0:e.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"team-".concat(e.id||e.externalId,"-").concat(a)})))||[]},[null==V?void 0:V.data]),ee=(0,s.useMemo)(()=>k.length>0?k.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"search-home-team-".concat(e.id||e.externalId,"-").concat(a)})):$,[$,k]),ea=(0,s.useMemo)(()=>U.length>0?U.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"search-away-team-".concat(e.id||e.externalId,"-").concat(a)})):$,[$,U]),{league:et,homeTeam:el,awayTeam:es}=(()=>{if(!P)return{league:null,homeTeam:null,awayTeam:null};let e=X.find(e=>e.value===I.leagueId),a=ee.find(e=>e.value===I.homeTeamId),t=ea.find(e=>e.value===I.awayTeamId);return{league:e||{value:I.leagueId,label:P.leagueName,logo:""},homeTeam:a||{value:I.homeTeamId,label:P.homeTeamName,logo:P.homeTeamLogo},awayTeam:t||{value:I.awayTeamId,label:P.awayTeamName,logo:P.awayTeamLogo}}})(),[er,ei]=(0,s.useState)(0);(0,s.useEffect)(()=>{ee.length>0&&ea.length>0&&X.length>0&&I.homeTeamId&&ei(e=>e+1)},[ee.length,ea.length,X.length,I.homeTeamId,I.awayTeamId,I.leagueId]);let en=e=>{let{label:a,selectedOption:t,placeholder:s="Not selected"}=e;return(0,l.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:a}),t?(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[t.logo&&(0,l.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(t.logo),alt:t.label,className:"w-8 h-8 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:t.label})]}):(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-gray-400 text-xs",children:"?"})}),(0,l.jsx)("span",{className:"text-gray-500 italic",children:s})]})]})};return H||Z||z?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)(h.Od,{className:"h-10 w-20"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(h.Od,{className:"h-8 w-64"}),(0,l.jsx)(h.Od,{className:"h-4 w-48"})]})]}),(0,l.jsxs)(o.Zb,{children:[(0,l.jsxs)(o.Ol,{children:[(0,l.jsx)(h.Od,{className:"h-6 w-48"}),(0,l.jsx)(h.Od,{className:"h-4 w-64"})]}),(0,l.jsxs)(o.aY,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(h.Od,{className:"h-4 w-32"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(h.Od,{className:"h-10"}),(0,l.jsx)(h.Od,{className:"h-10"})]}),(0,l.jsx)(h.Od,{className:"h-10"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(h.Od,{className:"h-4 w-24"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(h.Od,{className:"h-10"}),(0,l.jsx)(h.Od,{className:"h-10"})]})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)(h.Od,{className:"h-10 w-20"}),(0,l.jsx)(h.Od,{className:"h-10 w-32"})]})]})]})]}):!P||K||R?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"flex items-center space-x-4",children:(0,l.jsxs)(c.z,{variant:"outline",onClick:()=>a.back(),children:[(0,l.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,l.jsx)(o.Zb,{children:(0,l.jsx)(o.aY,{className:"p-6",children:(0,l.jsxs)("div",{className:"text-center",children:[!P&&(0,l.jsx)("p",{className:"text-red-600 mb-4",children:"Fixture not found"}),!!K&&(0,l.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load leagues"}),!!R&&(0,l.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load teams"}),(0,l.jsx)(c.z,{onClick:()=>a.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)(T.Z,{variant:"edit",fixtureId:N,isLoading:W.isLoading}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Edit Fixture: ",P.homeTeamName," vs ",P.awayTeamName]}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"Update fixture details and match information"})]})]}),(0,l.jsxs)(o.Zb,{children:[(0,l.jsxs)(o.Ol,{children:[(0,l.jsxs)(o.ll,{className:"flex items-center",children:[(0,l.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),(0,l.jsx)(o.SZ,{children:"Update the fixture information"})]}),(0,l.jsx)(o.aY,{children:(0,l.jsxs)("form",{onSubmit:e=>{var a;if(e.preventDefault(),!_()){f.toast.error("Please fix the form errors");return}let t=new Date("".concat(I.date,"T").concat(I.time)),l={homeTeamId:parseInt(I.homeTeamId),awayTeamId:parseInt(I.awayTeamId),leagueId:parseInt(I.leagueId),date:t.toISOString(),venueName:I.venueName||null,venueCity:I.venueCity||null,round:I.round||null,referee:I.referee||null,isHot:I.isHot,data:{status:I.status,statusLong:{TBD:"Time To Be Defined",NS:"Not Started",ST:"Scheduled","1H":"First Half",HT:"Halftime","2H":"Second Half",ET:"Extra Time",BT:"Break Time",P:"Penalty In Progress",SUSP:"Match Suspended",INT:"Match Interrupted",FT:"Match Finished",AET:"Match Finished After Extra Time",PEN:"Match Finished After Penalty",PST:"Match Postponed",CANC:"Match Cancelled",ABD:"Match Abandoned",AWD:"Technical Loss",WO:"WalkOver",LIVE:"In Progress"}[a=I.status]||a,statusExtra:0,elapsed:I.elapsed?parseInt(I.elapsed):null,goalsHome:I.goalsHome?parseInt(I.goalsHome):null,goalsAway:I.goalsAway?parseInt(I.goalsAway):null}};W.mutate(l)},className:"space-y-6",children:[(0,l.jsxs)(u.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(en,{label:"Selected Home Team",selectedOption:el,placeholder:"No home team selected"},"home-".concat(er)),(0,l.jsx)(m.L,{label:"Home Team",placeholder:z?"Loading teams...":"Select home team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:I.homeTeamId,onValueChange:e=>Q("homeTeamId",e),options:ee,error:S.homeTeamId,disabled:z,onSearch:G,isLoading:z},"home-team-search-stable")]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(en,{label:"Selected Away Team",selectedOption:es,placeholder:"No away team selected"},"away-".concat(er)),(0,l.jsx)(m.L,{label:"Away Team",placeholder:z?"Loading teams...":"Select away team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:I.awayTeamId,onValueChange:e=>Q("awayTeamId",e),options:ea.filter(e=>e.value!==I.homeTeamId),error:S.awayTeamId,disabled:z,onSearch:J,isLoading:z},"away-team-search-stable")]})]}),(0,l.jsx)("div",{children:(0,l.jsx)(()=>(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"flex items-center space-x-3 min-w-0 flex-1",children:et?(0,l.jsxs)(l.Fragment,{children:[et.logo&&(0,l.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(et.logo),alt:et.label,className:"w-8 h-8 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 truncate",children:et.label})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0",children:(0,l.jsx)("span",{className:"text-gray-400 text-xs",children:"?"})}),(0,l.jsx)("span",{className:"text-gray-500 italic",children:"No league selected"})]})}),(0,l.jsxs)("div",{className:"flex-shrink-0 w-64",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"League*"}),(0,l.jsx)(m.L,{placeholder:Z?"Loading leagues...":"Select league",searchPlaceholder:"Search leagues...",required:!0,value:I.leagueId,onValueChange:e=>Q("leagueId",e),options:X,error:S.leagueId,disabled:Z,onSearch:Y,isLoading:Z},"league-search-stable")]})]}),{})})]}),(0,l.jsxs)(u.hj,{title:"Schedule",description:"Set the date and time (local timezone)",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(u.UP,{label:"Date *",type:"date",required:!0,value:I.date,onChange:e=>Q("date",e.target.value),error:S.date,description:"Match date"}),(0,l.jsx)(u.UP,{label:"Time *",type:"time",required:!0,value:I.time,onChange:e=>Q("time",e.target.value),error:S.time,description:"Local time (".concat(Intl.DateTimeFormat().resolvedOptions().timeZone,")")})]}),(0,l.jsx)("div",{className:"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:(0,l.jsxs)("p",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-blue-600 mr-2",children:"ℹ️"}),(0,l.jsx)("strong",{children:"Timezone Info:"})," Times are displayed in your local timezone (",Intl.DateTimeFormat().resolvedOptions().timeZone,"). The asterisk (*) indicates required fields."]})})]}),(0,l.jsxs)(u.hj,{title:"Match Status",description:"Update match status and score",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsx)(u.mg,{label:"Status",placeholder:"Select status",required:!0,value:I.status,onValueChange:e=>Q("status",e),options:[{value:"TBD",label:"Time To Be Defined"},{value:"NS",label:"Not Started"},{value:"ST",label:"Scheduled"},{value:"1H",label:"First Half, Kick Off"},{value:"HT",label:"Halftime"},{value:"2H",label:"Second Half, 2nd Half Started"},{value:"ET",label:"Extra Time"},{value:"BT",label:"Break Time (in Extra Time)"},{value:"P",label:"Penalty In Progress"},{value:"LIVE",label:"In Progress"},{value:"FT",label:"Match Finished (Regular Time)"},{value:"AET",label:"Match Finished After Extra Time"},{value:"PEN",label:"Match Finished After Penalty"},{value:"SUSP",label:"Match Suspended"},{value:"INT",label:"Match Interrupted"},{value:"PST",label:"Match Postponed"},{value:"CANC",label:"Match Cancelled"},{value:"ABD",label:"Match Abandoned"},{value:"AWD",label:"Technical Loss"},{value:"WO",label:"WalkOver"}],error:S.status}),(0,l.jsx)(u.UP,{label:"Home Goals",type:"number",min:"0",value:I.goalsHome,onChange:e=>Q("goalsHome",e.target.value)}),(0,l.jsx)(u.UP,{label:"Away Goals",type:"number",min:"0",value:I.goalsAway,onChange:e=>Q("goalsAway",e.target.value)})]}),(0,l.jsx)(u.UP,{label:"Elapsed Time (minutes)",type:"number",min:"0",max:"120",value:I.elapsed,onChange:e=>Q("elapsed",e.target.value),description:"Minutes played in the match"})]}),(0,l.jsx)(u.hj,{title:"Fixture Settings",description:"Additional fixture settings",children:(0,l.jsx)(g.Z,{checked:I.isHot,onCheckedChange:e=>w(a=>({...a,isHot:e})),label:"Hot Fixture",description:"Mark this fixture as hot/featured",variant:"danger"})}),(0,l.jsxs)(u.hj,{title:"Venue & Match Information",description:"Venue details and match context",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(u.UP,{label:"Venue Name",placeholder:"Stadium name",value:I.venueName,onChange:e=>Q("venueName",e.target.value)}),(0,l.jsx)(u.UP,{label:"Venue City",placeholder:"City",value:I.venueCity,onChange:e=>Q("venueCity",e.target.value)})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(u.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:I.round,onChange:e=>Q("round",e.target.value)}),(0,l.jsx)(u.UP,{label:"Referee",placeholder:"Referee name",value:I.referee||"",onChange:e=>Q("referee",e.target.value)})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsx)(u.UP,{label:"Temperature (\xb0C)",type:"number",placeholder:"e.g., 22",value:I.temperature||"",onChange:e=>Q("temperature",e.target.value)}),(0,l.jsx)(u.UP,{label:"Weather",placeholder:"e.g., Sunny, Rainy",value:I.weather||"",onChange:e=>Q("weather",e.target.value)}),(0,l.jsx)(u.UP,{label:"Attendance",type:"number",placeholder:"Number of spectators",value:I.attendance||"",onChange:e=>Q("attendance",e.target.value)})]})]}),(0,l.jsxs)(u.iN,{children:[(0,l.jsx)(c.z,{type:"button",variant:"outline",onClick:()=>a.back(),disabled:W.isLoading,children:"Cancel"}),(0,l.jsxs)(c.z,{type:"submit",disabled:W.isLoading,children:[(0,l.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),W.isLoading?"Updating...":"Update Fixture"]})]})]})})]})]})}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,8041,174,1564,6389,2971,8069,1744],function(){return e(e.s=60340)}),_N_E=e.O()}]);