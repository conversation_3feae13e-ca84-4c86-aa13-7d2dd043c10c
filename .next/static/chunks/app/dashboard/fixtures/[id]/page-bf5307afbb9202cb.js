(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9447],{22073:function(e,s,a){Promise.resolve().then(a.bind(a,93902))},53879:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5835:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62985:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},26490:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58943:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},62457:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},56227:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},69724:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},66260:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},58106:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]])},11213:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},34059:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},93902:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return q}});var t=a(57437),r=a(2265),n=a(64095),l=a(31346),i=a(8186),c=a(47907),d=a(15671),o=a(575),u=a(77625),m=a(2975),x=a(53879),h=a(75462),f=a(29295),y=a(50489),j=a(97307),p=a(26490),N=a(66260),w=a(62457),v=a(69724),g=a(40834),b=a(11546),k=a(33277),Z=a(11213),C=a(52569);function T(e){var s,a;let{fixture:r,className:n}=e;return(0,t.jsx)(d.Zb,{className:n,children:(0,t.jsxs)(d.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-600",children:r.leagueName}),r.round&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["• ",r.round]})]}),(0,t.jsx)(k.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(r.status),children:((e,s)=>{switch(e){case"1H":case"2H":return"".concat(s,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(r.status,r.elapsed)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[r.homeTeamLogo&&(0,t.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(r.homeTeamLogo),alt:r.homeTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:r.homeTeamName}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Home"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[null!==(s=r.goalsHome)&&void 0!==s?s:"-"," - ",null!==(a=r.goalsAway)&&void 0!==a?a:"-"]}),null!==r.scoreHalftimeHome&&null!==r.scoreHalftimeAway&&(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["HT: ",r.scoreHalftimeHome," - ",r.scoreHalftimeAway]}),(0,t.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"VS"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[r.awayTeamLogo&&(0,t.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(r.awayTeamLogo),alt:r.awayTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:r.awayTeamName}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Away"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.Z,{className:"h-4 w-4"}),(0,t.jsx)(C.U,{dateTime:r.date,showDate:!0,showTime:!0,className:"font-medium"})]}),r.venue&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["string"==typeof r.venue?r.venue:r.venue.name,"object"==typeof r.venue&&r.venue.city&&(0,t.jsxs)("span",{className:"text-gray-500",children:[", ",r.venue.city]})]})]}),r.referee&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(Z.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Referee: ",r.referee]})]}),r.elapsed&&"NS"!==r.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[r.elapsed,"' minutes played"]})]})]})]})})}var M=a(5835),O=a(62985),S=a(56227),I=a(58943);function V(e){let{fixture:s}=e,{data:a,isLoading:r,error:n}=(0,l.a)({queryKey:["fixture-statistics",s.externalId],queryFn:()=>m.L.getFixtureStatistics(s.externalId),enabled:!!s.externalId,staleTime:3e5}),i=(()=>{if(!(null==a?void 0:a.data)||!Array.isArray(a.data)||a.data.length<2)return{possession:{home:65,away:35},shots:{home:12,away:8},shotsOnTarget:{home:6,away:3},corners:{home:7,away:4},fouls:{home:11,away:14},yellowCards:{home:2,away:3},redCards:{home:0,away:1},offsides:{home:3,away:2},isRealData:!1};let e=a.data[0],s=a.data[1],t=(e,s)=>{if(!(null==e?void 0:e.statistics))return 0;let a=e.statistics[s];return null==a?0:"string"==typeof a&&a.includes("%")?parseInt(a.replace("%","")):parseInt(a)||0};return{possession:{home:t(e,"possession"),away:t(s,"possession")},shots:{home:t(e,"totalShots"),away:t(s,"totalShots")},shotsOnTarget:{home:t(e,"shotsOnGoal"),away:t(s,"shotsOnGoal")},corners:{home:t(e,"corners"),away:t(s,"corners")},fouls:{home:t(e,"fouls")||0,away:t(s,"fouls")||0},yellowCards:{home:t(e,"yellowCards"),away:t(s,"yellowCards")},redCards:{home:t(e,"redCards"),away:t(s,"redCards")},offsides:{home:t(e,"offsides"),away:t(s,"offsides")},isRealData:!0}})(),c=e=>{let{label:s,homeValue:a,awayValue:r,icon:n,isPercentage:l=!1}=e,i=a+r,c=i>0?a/i*100:50,d=i>0?r/i*100:50;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"font-medium text-right w-12",children:[a,l?"%":""]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-1 justify-center",children:[(0,t.jsx)(n,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:s})]}),(0,t.jsxs)("span",{className:"font-medium text-left w-12",children:[r,l?"%":""]})]}),(0,t.jsxs)("div",{className:"flex h-2 bg-gray-200 rounded-full overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-blue-500 transition-all duration-300",style:{width:"".concat(c,"%")}}),(0,t.jsx)("div",{className:"bg-red-500 transition-all duration-300",style:{width:"".concat(d,"%")}})]})]})};return r?(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Statistics"})]})}),(0,t.jsx)(d.aY,{className:"space-y-6",children:(0,t.jsx)("div",{className:"space-y-4",children:[...Array(6)].map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(u.Od,{className:"h-4 w-8"}),(0,t.jsx)(u.Od,{className:"h-4 w-20"}),(0,t.jsx)(u.Od,{className:"h-4 w-8"})]}),(0,t.jsx)(u.Od,{className:"h-2 w-full"})]},s))})})]}):(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Statistics"}),!i.isRealData&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-orange-600",children:[(0,t.jsx)(O.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-xs",children:"Demo Data"})]})]})}),(0,t.jsxs)(d.aY,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm font-medium text-gray-600",children:[(0,t.jsx)("span",{className:"w-12 text-right",children:s.homeTeamName}),(0,t.jsx)("span",{className:"flex-1 text-center",children:"Statistics"}),(0,t.jsx)("span",{className:"w-12 text-left",children:s.awayTeamName})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(c,{label:"Possession",homeValue:i.possession.home,awayValue:i.possession.away,icon:M.Z,isPercentage:!0}),(0,t.jsx)(c,{label:"Shots",homeValue:i.shots.home,awayValue:i.shots.away,icon:S.Z}),(0,t.jsx)(c,{label:"Shots on Target",homeValue:i.shotsOnTarget.home,awayValue:i.shotsOnTarget.away,icon:S.Z}),(0,t.jsx)(c,{label:"Corners",homeValue:i.corners.home,awayValue:i.corners.away,icon:I.Z}),(0,t.jsx)(c,{label:"Fouls",homeValue:i.fouls.home,awayValue:i.fouls.away,icon:p.Z}),(0,t.jsx)(c,{label:"Yellow Cards",homeValue:i.yellowCards.home,awayValue:i.yellowCards.away,icon:I.Z}),(0,t.jsx)(c,{label:"Red Cards",homeValue:i.redCards.home,awayValue:i.redCards.away,icon:I.Z}),(0,t.jsx)(c,{label:"Offsides",homeValue:i.offsides.home,awayValue:i.offsides.away,icon:I.Z})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500 text-center pt-4 border-t",children:"* Statistics are updated in real-time during the match"})]})]})}let _=(0,a(57977).Z)("goal",[["path",{d:"M12 13V2l8 4-8 4",key:"5wlwwj"}],["path",{d:"M20.561 10.222a9 9 0 1 1-12.55-5.29",key:"1c0wjv"}],["path",{d:"M8.002 9.997a5 5 0 1 0 8.9 2.02",key:"gb1g7m"}]]);var H=a(58106),D=a(53348);function L(e){var s;let{fixture:a}=e,{data:r,isLoading:n,error:i}=(0,l.a)({queryKey:["fixture-events",a.externalId],queryFn:()=>m.L.getFixtureEvents(a.externalId),enabled:!!a.externalId,staleTime:3e5});if(n)return(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Timeline"})]})}),(0,t.jsx)(d.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)(u.Od,{className:"h-6 w-12"}),(0,t.jsx)(u.Od,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(u.Od,{className:"h-4 w-24"}),(0,t.jsx)(u.Od,{className:"h-4 w-32"}),(0,t.jsx)(u.Od,{className:"h-3 w-20"})]})]},s))})})]});let{events:c,isRealData:o}=(null==r?void 0:null===(s=r.data)||void 0===s?void 0:s.events)&&Array.isArray(r.data.events)?{events:r.data.events.map((e,s)=>{let t=e.team.name===a.homeTeamName,r=e.time.elapsed+(e.time.extra||0),n="";return e.assist.name?n="Assist: ".concat(e.assist.name):"subst"===e.type&&e.assist.name&&(n="In: ".concat(e.assist.name)),{id:s+1,minute:r,type:((e,s)=>{switch(e.toLowerCase()){case"goal":if(s.toLowerCase().includes("own"))return"own_goal";if(s.toLowerCase().includes("penalty"))return"penalty";return"goal";case"card":return s.toLowerCase().includes("red")?"red_card":"yellow_card";case"subst":return"substitution";default:return"goal"}})(e.type,e.detail),team:t?"home":"away",player:e.player.name,description:e.detail,additionalInfo:n}}).sort((e,s)=>e.minute-s.minute),isRealData:!0}:{events:[{id:1,minute:15,type:"goal",team:"home",player:"Marcus Rashford",description:"Goal",additionalInfo:"Assist: Bruno Fernandes"},{id:2,minute:23,type:"yellow_card",team:"away",player:"Virgil van Dijk",description:"Yellow Card",additionalInfo:"Foul"},{id:3,minute:45,type:"substitution",team:"home",player:"Anthony Martial",description:"Substitution",additionalInfo:"Out: Jadon Sancho"},{id:4,minute:67,type:"goal",team:"away",player:"Mohamed Salah",description:"Goal",additionalInfo:"Assist: Sadio Man\xe9"},{id:5,minute:89,type:"goal",team:"home",player:"Mason Greenwood",description:"Goal",additionalInfo:"Penalty"}],isRealData:!1},x=e=>{switch(e){case"goal":case"penalty":case"own_goal":return(0,t.jsx)(_,{className:"h-4 w-4"});case"yellow_card":return(0,t.jsx)(v.Z,{className:"h-4 w-4 text-yellow-500"});case"red_card":return(0,t.jsx)(H.Z,{className:"h-4 w-4 text-red-500"});case"substitution":return(0,t.jsx)(D.Z,{className:"h-4 w-4 text-blue-500"});default:return(0,t.jsx)(p.Z,{className:"h-4 w-4"})}},h=e=>{switch(e){case"goal":case"penalty":return"bg-green-100 text-green-800";case"own_goal":return"bg-orange-100 text-orange-800";case"yellow_card":return"bg-yellow-100 text-yellow-800";case"red_card":return"bg-red-100 text-red-800";case"substitution":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},f=e=>{switch(e){case"goal":return"Goal";case"penalty":return"Penalty Goal";case"own_goal":return"Own Goal";case"yellow_card":return"Yellow Card";case"red_card":return"Red Card";case"substitution":return"Substitution";default:return e}};return 0===c.length?(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Timeline"})]})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(p.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No events recorded for this match yet."})]})})]}):(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Timeline"}),!o&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-orange-600",children:[(0,t.jsx)(O.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-xs",children:"Demo Data"})]})]})}),(0,t.jsxs)(d.aY,{children:[(0,t.jsx)("div",{className:"space-y-4",children:c.map((e,s)=>(0,t.jsxs)("div",{className:"relative",children:[s<c.length-1&&(0,t.jsx)("div",{className:"absolute left-6 top-12 w-0.5 h-8 bg-gray-200"}),(0,t.jsxs)("div",{className:"flex items-start space-x-4 ".concat("away"===e.team?"flex-row-reverse space-x-reverse":""),children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsxs)(k.C,{variant:"outline",className:"font-mono",children:[e.minute,"'"]})}),(0,t.jsx)("div",{className:"flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ".concat("home"===e.team?"bg-blue-100":"bg-red-100"),children:x(e.type)}),(0,t.jsxs)("div",{className:"flex-1 ".concat("away"===e.team?"text-right":""),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.C,{className:h(e.type),children:f(e.type)}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"home"===e.team?a.homeTeamName:a.awayTeamName})]}),(0,t.jsx)("p",{className:"font-medium text-gray-900 mt-1",children:e.player}),e.additionalInfo&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.additionalInfo})]})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-8 pt-6 border-t",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-8 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{children:"1st Half: 0-45'"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,t.jsx)("span",{children:"2nd Half: 45-90'"})]}),a.elapsed&&a.elapsed>90&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{children:"Extra Time: 90'+"})]})]})})]})]})}var F=a(72632),A=a(78789),R=a(34059);let E=e=>{let{fixture:s,onBroadcastLinks:a,className:r=""}=e,n=(0,c.useRouter)(),{isEditor:l}=(0,b.TE)();return(0,t.jsxs)("div",{className:"space-y-4 ".concat(r),children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Actions"}),l()&&(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{let e=s.externalId||s.id;n.push("/dashboard/fixtures/".concat(e,"/edit"))},children:[(0,t.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),"Edit Fixture"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:a,children:[(0,t.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),"Manage Broadcast Links"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Related Data"}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{s.homeTeamId&&window.open("/dashboard/teams/".concat(s.homeTeamId),"_blank")},disabled:!s.homeTeamId,children:[(0,t.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"View Home Team"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{s.awayTeamId&&window.open("/dashboard/teams/".concat(s.awayTeamId),"_blank")},disabled:!s.awayTeamId,children:[(0,t.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"View Away Team"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>{s.leagueId&&window.open("/dashboard/leagues/".concat(s.leagueId),"_blank")},disabled:!s.leagueId,children:[(0,t.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"View League"]})]})]})};var z=a(4133),P=a(56288);function q(){let e=(0,c.useParams)(),s=(0,c.useRouter)(),a=(0,n.NL)(),k=parseInt(e.id),[Z,C]=(0,r.useState)(!1),[M,O]=(0,r.useState)(!1),{isEditor:S,isAdmin:I}=(0,b.TE)(),{data:_,isLoading:H,error:D,refetch:R}=(0,l.a)({queryKey:["fixture",k],queryFn:()=>m.L.getFixture(k),enabled:!!k}),q=(0,i.D)({mutationFn:()=>m.L.deleteFixture(k),onSuccess:()=>{a.invalidateQueries({queryKey:["fixtures"]}),P.toast.success("Fixture deleted successfully"),O(!1),s.push("/dashboard/fixtures")},onError:e=>{P.toast.error(e.message||"Failed to delete fixture"),O(!1)}}),Y=()=>{C(!0)};return H?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(u.Od,{className:"h-10 w-10"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.Od,{className:"h-8 w-64"}),(0,t.jsx)(u.Od,{className:"h-4 w-48"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(u.Od,{className:"h-64"}),(0,t.jsx)(u.Od,{className:"h-48"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(u.Od,{className:"h-32"}),(0,t.jsx)(u.Od,{className:"h-48"})]})]})]}):D||!_?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>s.back(),children:[(0,t.jsx)(x.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,t.jsx)(d.Zb,{children:(0,t.jsx)(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixture details"}),(0,t.jsx)(o.z,{onClick:()=>s.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(A.Z,{variant:"detail",fixtureId:k,onRefresh:R,isLoading:H}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[_.homeTeamName," vs ",_.awayTeamName]}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:[_.leagueName," • Fixture Details"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[S()&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o.z,{variant:"outline",onClick:Y,children:[(0,t.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Broadcast Links"]}),(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{s.push("/dashboard/fixtures/".concat(k,"/edit"))},children:[(0,t.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Edit"]})]}),I()&&(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{O(!0)},children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(T,{fixture:_}),(0,t.jsx)(L,{fixture:_})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(V,{fixture:_}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center",children:[(0,t.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Match Information"]})}),(0,t.jsxs)(d.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Date"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:new Date(_.date).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Time"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:new Date(_.date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"League"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_.leagueName})]})]}),_.venueName&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(w.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Venue"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_.venueName})]})]})]})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{children:"Quick Actions"})}),(0,t.jsx)(d.aY,{children:(0,t.jsx)(E,{fixture:_,onBroadcastLinks:Y})})]})]})]}),(0,t.jsx)(F.A,{isOpen:Z,onClose:()=>C(!1),fixture:_}),(0,t.jsx)(z.u_,{isOpen:M,onClose:()=>O(!1),title:"Delete Fixture",description:"Are you sure you want to delete this fixture? This action cannot be undone.",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[(0,t.jsx)(v.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the fixture:"}),(0,t.jsx)("p",{className:"text-sm text-red-700 mt-1",children:(0,t.jsxs)("strong",{children:[_.homeTeamName," vs ",_.awayTeamName]})}),(0,t.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:[new Date(_.date).toLocaleDateString()," • ",_.leagueName]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)(o.z,{variant:"outline",onClick:()=>O(!1),disabled:q.isLoading,children:"Cancel"}),(0,t.jsx)(o.z,{variant:"destructive",onClick:()=>{q.mutate()},disabled:q.isLoading,children:q.isLoading?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"Delete Fixture"]})})]})]})})]})}},33277:function(e,s,a){"use strict";a.d(s,{C:function(){return i}});var t=a(57437);a(2265);var r=a(49769),n=a(22169);let l=(0,r.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:a}),s),...r})}},22782:function(e,s,a){"use strict";a.d(s,{I:function(){return l}});var t=a(57437),r=a(2265),n=a(22169);let l=r.forwardRef((e,s)=>{let{className:a,type:r,...l}=e;return(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...l})});l.displayName="Input"},44991:function(e,s,a){"use strict";function t(e,s,{checkForDefaultPrevented:a=!0}={}){return function(t){if(e?.(t),!1===a||!t.defaultPrevented)return s?.(t)}}a.d(s,{M:function(){return t}})},84104:function(e,s,a){"use strict";a.d(s,{b:function(){return l},k:function(){return n}});var t=a(2265),r=a(57437);function n(e,s){let a=t.createContext(s),n=e=>{let{children:s,...n}=e,l=t.useMemo(()=>n,Object.values(n));return(0,r.jsx)(a.Provider,{value:l,children:s})};return n.displayName=e+"Provider",[n,function(r){let n=t.useContext(a);if(n)return n;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${e}\``)}]}function l(e,s=[]){let a=[],n=()=>{let s=a.map(e=>t.createContext(e));return function(a){let r=a?.[e]||s;return t.useMemo(()=>({[`__scope${e}`]:{...a,[e]:r}}),[a,r])}};return n.scopeName=e,[function(s,n){let l=t.createContext(n),i=a.length;a=[...a,n];let c=s=>{let{scope:a,children:n,...c}=s,d=a?.[e]?.[i]||l,o=t.useMemo(()=>c,Object.values(c));return(0,r.jsx)(d.Provider,{value:o,children:n})};return c.displayName=s+"Provider",[c,function(a,r){let c=r?.[e]?.[i]||l,d=t.useContext(c);if(d)return d;if(void 0!==n)return n;throw Error(`\`${a}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=a.reduce((s,{useScope:a,scopeName:t})=>{let r=a(e)[`__scope${t}`];return{...s,...r}},{});return t.useMemo(()=>({[`__scope${s.scopeName}`]:r}),[r])}};return a.scopeName=s.scopeName,a}(n,...s)]}},38687:function(e,s,a){"use strict";a.d(s,{M:function(){return c}});var t,r=a(2265),n=a(32618),l=(t||(t=a.t(r,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[s,a]=r.useState(l());return(0,n.b)(()=>{e||a(e=>e??String(i++))},[e]),e||(s?`radix-${s}`:"")}},29586:function(e,s,a){"use strict";a.d(s,{WV:function(){return i},jH:function(){return c}});var t=a(2265),r=a(54887),n=a(59143),l=a(57437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let a=(0,n.Z8)(`Primitive.${s}`),r=t.forwardRef((e,t)=>{let{asChild:r,...n}=e,i=r?a:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...n,ref:t})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{});function c(e,s){e&&r.flushSync(()=>e.dispatchEvent(s))}},39830:function(e,s,a){"use strict";a.d(s,{W:function(){return r}});var t=a(2265);function r(e){let s=t.useRef(e);return t.useEffect(()=>{s.current=e}),t.useMemo(()=>(...e)=>s.current?.(...e),[])}},9310:function(e,s,a){"use strict";a.d(s,{T:function(){return i}});var t,r=a(2265),n=a(32618),l=(t||(t=a.t(r,2)))[" useInsertionEffect ".trim().toString()]||n.b;function i({prop:e,defaultProp:s,onChange:a=()=>{},caller:t}){let[n,i,c]=function({defaultProp:e,onChange:s}){let[a,t]=r.useState(e),n=r.useRef(a),i=r.useRef(s);return l(()=>{i.current=s},[s]),r.useEffect(()=>{n.current!==a&&(i.current?.(a),n.current=a)},[a,n]),[a,t,i]}({defaultProp:s,onChange:a}),d=void 0!==e,o=d?e:n;{let s=r.useRef(void 0!==e);r.useEffect(()=>{let e=s.current;if(e!==d){let s=d?"controlled":"uncontrolled";console.warn(`${t} is changing from ${e?"controlled":"uncontrolled"} to ${s}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}s.current=d},[d,t])}return[o,r.useCallback(s=>{if(d){let a="function"==typeof s?s(e):s;a!==e&&c.current?.(a)}else i(s)},[d,e,i,c])]}Symbol("RADIX:SYNC_STATE")},32618:function(e,s,a){"use strict";a.d(s,{b:function(){return r}});var t=a(2265),r=globalThis?.document?t.useLayoutEffect:()=>{}},11780:function(e,s,a){"use strict";a.d(s,{C2:function(){return l},fC:function(){return c}});var t=a(2265),r=a(29586),n=a(57437),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=t.forwardRef((e,s)=>(0,n.jsx)(r.WV.span,{...e,ref:s,style:{...l,...e.style}}));i.displayName="VisuallyHidden";var c=i}},function(e){e.O(0,[2150,9101,8939,1346,2341,4216,1953,9918,1715,6877,1564,8942,2971,8069,1744],function(){return e(e.s=22073)}),_N_E=e.O()}]);