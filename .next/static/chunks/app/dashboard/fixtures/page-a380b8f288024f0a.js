(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[266],{54240:function(e,t,a){Promise.resolve().then(a.bind(a,15257))},15257:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return J}});var s=a(57437),o=a(2265),r=a(64095),n=a(31346),l=a(8186),i=a(15671),c=a(575),d=a(33277),u=a(85110),m=a(22632),x=a(4133),g=a(2975),h=a(37841),f=a(75462),p=a(29295),y=a(50489),b=a(40834),v=a(10632),w=a(44715),N=a(70094),j=a(97307),D=a(53348),T=a(77625),k=a(11546),S=a(72632),C=a(98594),F=a(22169),z=a(49108),E=a(37805),L=a(15514);function A(e){let{className:t,classNames:a,showOutsideDays:o=!0,...r}=e,n=new Date;return n.setHours(0,0,0,0),(0,s.jsx)(L._,{showOutsideDays:o,className:(0,F.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,F.cn)((0,c.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,F.cn)((0,c.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-red-500 text-white font-bold hover:bg-red-600",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},modifiers:{past:e=>{let t=new Date(e);return t.setHours(0,0,0,0),t<n},future:e=>{let t=new Date(e);return t.setHours(0,0,0,0),t>n}},modifiersClassNames:{past:"text-black bg-gray-200 hover:bg-gray-300",future:"text-blue-600 bg-blue-50 hover:bg-blue-100"},components:{Chevron:e=>{let{orientation:t,...a}=e,o="left"===t?z.Z:E.Z;return(0,s.jsx)(o,{className:"h-4 w-4"})}},...r})}A.displayName="Calendar";var Z=a(57427);let P=Z.fC,U=Z.xz,_=o.forwardRef((e,t)=>{let{className:a,align:o="center",sideOffset:r=4,...n}=e;return(0,s.jsx)(Z.h_,{children:(0,s.jsx)(Z.VY,{ref:t,align:o,sideOffset:r,className:(0,F.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});function I(e){let{date:t,onDateChange:a,placeholder:o="Pick a date",className:r}=e;return(0,s.jsxs)(P,{children:[(0,s.jsx)(U,{asChild:!0,children:(0,s.jsxs)(c.z,{variant:"outline",className:(0,F.cn)("w-[280px] justify-start text-left font-normal",!t&&"text-muted-foreground",r),children:[(0,s.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),t?(0,C.WU)(t,"PPP"):(0,s.jsx)("span",{children:o})]})}),(0,s.jsx)(_,{className:"w-auto p-0",children:(0,s.jsx)(A,{mode:"single",selected:t,onSelect:a,initialFocus:!0})})]})}_.displayName=Z.VY.displayName;var H=a(52569),O=a(52235),B=a(80037),M=a(9208);function R(e){let{isOpen:t,onClose:a,selectedDate:r,onDateSelect:n,onApplyFilter:l,onResetFilter:i}=e,[d,u]=o.useState(r);o.useEffect(()=>{u(r)},[r,t]);let m=e=>{try{u(e)}catch(e){console.error("Error selecting date:",e)}};return(0,s.jsx)(M.Vq,{open:t,onOpenChange:a,children:(0,s.jsxs)(M.cZ,{className:"sm:max-w-[480px] p-0 overflow-hidden bg-white rounded-xl shadow-2xl",children:[(0,s.jsxs)(M.fK,{className:"px-6 py-5 bg-gradient-to-r from-blue-600 to-blue-700 text-white",children:[(0,s.jsxs)(M.$N,{className:"flex items-center gap-3 text-xl font-bold",children:[(0,s.jsx)(j.Z,{className:"h-6 w-6"}),"Select Date Filter"]}),(0,s.jsx)(M.Be,{className:"text-blue-100 mt-2 text-sm",children:"Choose a specific date to filter fixtures, or reset to show today's fixtures."})]}),(0,s.jsxs)("div",{className:"px-6 py-6",children:[d&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-1",children:"Selected Date:"}),(0,s.jsx)("div",{className:"text-xl font-bold text-blue-900",children:(0,C.WU)(d,"EEEE, MMMM d, yyyy")}),(0,s.jsx)("div",{className:"text-xs text-blue-600 mt-1",children:d<new Date?"Past date":d.toDateString()===new Date().toDateString()?"Today":"Future date"})]}),(0,s.jsx)(c.z,{variant:"ghost",size:"sm",onClick:()=>m(void 0),className:"text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-lg",children:(0,s.jsx)(O.Z,{className:"h-5 w-5"})})]})}),(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"bg-white rounded-xl border border-gray-200 shadow-lg p-2",children:(0,s.jsx)(A,{mode:"single",selected:d,onSelect:m,initialFocus:!0,className:"rounded-lg"})})}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 border border-gray-200",children:[(0,s.jsxs)("div",{className:"text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Quick Actions"]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>m(new Date),className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Today"}),(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+1),m(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Tomorrow"}),(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+7),m(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Next Week"})]})]})]}),(0,s.jsxs)(M.cN,{className:"px-6 py-5 bg-gray-50 border-t border-gray-200 flex-col sm:flex-row gap-3",children:[(0,s.jsxs)("div",{className:"flex gap-3 w-full sm:w-auto",children:[(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>{try{u(void 0),i(),n(void 0),a()}catch(e){console.error("Error resetting date filter:",e)}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:[(0,s.jsx)(D.Z,{className:"h-4 w-4 mr-2"}),"Reset to Today"]}),(0,s.jsx)(c.z,{variant:"outline",onClick:()=>{try{u(r),a()}catch(e){console.error("Error canceling date filter:",e),a()}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:"Cancel"})]}),(0,s.jsxs)(c.z,{onClick:()=>{try{l(d),n(d),a()}catch(e){console.error("Error applying date filter:",e)}},disabled:!d,className:"w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(B.Z,{className:"h-4 w-4 mr-2"}),"Apply Filter"]})]})]})})}var q=a(80928),V=a(56288);function J(){var e,t,a,C,F,z;let[E,L]=(0,o.useState)(1),[A,Z]=(0,o.useState)(25),[P,U]=(0,o.useState)(""),[_,O]=(0,o.useState)(""),[B,M]=(0,o.useState)(""),[J,Y]=(0,o.useState)(""),[G,K]=(0,o.useState)(!1),[Q,W]=(0,o.useState)(null),[$,X]=(0,o.useState)(!1),[ee,et]=(0,o.useState)(null),[ea,es]=(0,o.useState)(void 0),[eo,er]=(0,o.useState)(!1),{isAdmin:en,isEditor:el}=(0,k.TE)(),ei=(0,r.NL)(),{data:ec,isLoading:ed,error:eu,refetch:em}=(0,n.a)({queryKey:["fixtures","all",E,A,_,B,J,ea],queryFn:()=>{let e={page:E,limit:A};_&&_.trim()&&(e.search=_.trim()),B&&(e.status=B),J&&(e.league=J);let t=ea||new Date;return e.date=(0,q.PM)(t),g.L.getFixtures(e)},staleTime:3e4,retry:!1,onError:e=>{console.log("API is down, using mock data:",(null==e?void 0:e.message)||"Unknown error")}}),ex=ec||{data:[{id:1,homeTeamName:"Manchester United",awayTeamName:"Liverpool",homeTeamLogo:"/images/teams/1.png",awayTeamLogo:"/images/teams/2.png",date:"2024-12-19T14:30:00Z",status:"NS",leagueName:"Premier League",venue:"Old Trafford"},{id:2,homeTeamName:"Arsenal",awayTeamName:"Chelsea",homeTeamLogo:"/images/teams/3.png",awayTeamLogo:"/images/teams/4.png",date:"2024-12-20T16:00:00Z",status:"NS",leagueName:"Premier League",venue:"Emirates Stadium"},{id:3,homeTeamName:"Barcelona",awayTeamName:"Real Madrid",homeTeamLogo:"/images/teams/5.png",awayTeamLogo:"/images/teams/6.png",date:"2024-12-21T20:00:00Z",status:"LIVE",leagueName:"La Liga",venue:"Camp Nou"},{id:4,homeTeamName:"Bayern Munich",awayTeamName:"Borussia Dortmund",homeTeamLogo:"/images/teams/7.png",awayTeamLogo:"/images/teams/8.png",date:"2024-12-18T18:30:00Z",status:"FT",leagueName:"Bundesliga",venue:"Allianz Arena"},{id:5,homeTeamName:"PSG",awayTeamName:"Marseille",homeTeamLogo:"/images/teams/9.png",awayTeamLogo:"/images/teams/10.png",date:"2024-12-22T21:00:00Z",status:"NS",leagueName:"Ligue 1",venue:"Parc des Princes"}],totalItems:5,totalPages:1,currentPage:1,limit:25},eg=!ec,eh=o.useMemo(()=>{if(!eg||!_.trim())return ex;let e=ex.data.filter(e=>{var t,a,s,o,r;let n=_.toLowerCase();return(null===(t=e.homeTeamName)||void 0===t?void 0:t.toLowerCase().includes(n))||(null===(a=e.awayTeamName)||void 0===a?void 0:a.toLowerCase().includes(n))||(null===(s=e.leagueName)||void 0===s?void 0:s.toLowerCase().includes(n))||(null===(o=e.venue)||void 0===o?void 0:o.toLowerCase().includes(n))||(null===(r=e.status)||void 0===r?void 0:r.toLowerCase().includes(n))});return{...ex,data:e,meta:{...ex.meta,totalItems:e.length,totalPages:Math.ceil(e.length/A)},totalItems:e.length,totalPages:Math.ceil(e.length/A)}},[ex,_,eg,A]),ef=(0,l.D)({mutationFn:e=>{let t=e.externalId||e.id;return g.L.deleteFixture(t)},onSuccess:()=>{ei.invalidateQueries({queryKey:["fixtures"]}),console.log("Fixture deleted successfully"),K(!1),W(null)},onError:e=>{console.error("Failed to delete fixture:",e.message)}}),ep=(0,l.D)({mutationFn:e=>{let{fixtureId:t,isHot:a}=e;return g.L.updateFixture(t,{isHot:a})},onSuccess:(e,t)=>{let{isHot:a}=t;ei.invalidateQueries({queryKey:["fixtures"]}),V.toast.success("Fixture ".concat(a?"marked as hot":"unmarked as hot"," successfully"))},onError:e=>{V.toast.error("Failed to update fixture hot status: ".concat(e.message)),console.error("Failed to update fixture hot status:",e.message)}}),ey=[{key:"date",title:"Date & Time",sortable:!0,render:e=>(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(H.U,{dateTime:e,showDate:!0,showTime:!0,isClickable:!0,onClick:()=>{es(new Date(e)),er(!0)},className:"min-w-[100px]"})})},{key:"match",title:"Match",sortable:!1,headerClassName:"text-center",render:(e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 py-3",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[t.homeTeamLogo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(t.homeTeamLogo),alt:t.homeTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:t.homeTeamName})]}),(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)("span",{className:"text-gray-500 font-bold text-sm",children:"VS"})}),(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[t.awayTeamLogo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(t.awayTeamLogo),alt:t.awayTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:t.awayTeamName})]})]})},{key:"score",title:"Score",align:"center",render:(e,t)=>{var a,o;return(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"font-bold text-lg",children:[null!==(a=t.goalsHome)&&void 0!==a?a:"-"," - ",null!==(o=t.goalsAway)&&void 0!==o?o:"-"]}),null!==t.scoreHalftimeHome&&null!==t.scoreHalftimeAway&&(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["HT: ",t.scoreHalftimeHome," - ",t.scoreHalftimeAway]})]})}},{key:"status",title:"Status",sortable:!0,filterable:!0,render:(e,t)=>(0,s.jsx)(d.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(e),children:((e,t)=>{switch(e){case"1H":case"2H":return"".concat(t,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(e,t.elapsed)})},{key:"isHot",title:"Hot",sortable:!1,render:(e,t)=>el()||en()?(0,s.jsx)(u.Z,{checked:e||!1,onCheckedChange:e=>{let a=t.externalId||t.id;ep.mutate({fixtureId:a,isHot:e})},label:"",disabled:ep.isLoading,size:"sm",variant:"danger"}):e?(0,s.jsx)(d.C,{variant:"destructive",className:"text-xs",children:"Hot"}):null},{key:"leagueName",title:"League",sortable:!0,filterable:!0,render:e=>(0,s.jsx)("span",{className:"text-sm text-gray-600",children:e})},{key:"actions",title:"Actions",render:(e,t)=>(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)(c.z,{size:"sm",variant:"outline",title:"View Details",onClick:()=>eb(t),children:(0,s.jsx)(h.Z,{className:"h-4 w-4"})}),(0,s.jsx)(c.z,{size:"sm",variant:"outline",title:"Broadcast Links",onClick:()=>eN(t),children:(0,s.jsx)(f.Z,{className:"h-4 w-4"})}),el()&&(0,s.jsx)(c.z,{size:"sm",variant:"outline",title:"Edit",onClick:()=>ev(t),children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})}),en()&&(0,s.jsx)(c.z,{size:"sm",variant:"outline",title:"Delete",onClick:()=>ew(t),children:(0,s.jsx)(y.Z,{className:"h-4 w-4"})})]})}],eb=e=>{let t=e.externalId||e.id;window.open("/dashboard/fixtures/".concat(t),"_blank")},ev=e=>{let t=e.externalId||e.id;window.open("/dashboard/fixtures/".concat(t,"/edit"),"_blank")},ew=e=>{W(e),K(!0)},eN=e=>{et(e),X(!0)},ej=async()=>{try{console.log("Starting fixtures sync..."),console.log("Fixtures sync completed"),em()}catch(e){console.error("Sync failed:",e.message)}};return eu?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),(0,s.jsx)(i.Zb,{children:(0,s.jsx)(i.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,s.jsxs)(c.z,{onClick:()=>em(),children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>em(),disabled:ed,children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4 ".concat(ed?"animate-spin":"")}),"Refresh"]}),en()&&(0,s.jsxs)(c.z,{variant:"outline",onClick:ej,disabled:ed,children:[(0,s.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Sync Data"]}),(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>{console.log("Export feature coming soon")},children:[(0,s.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"Export"]}),el()&&(0,s.jsxs)(c.z,{onClick:()=>window.open("/dashboard/fixtures/create","_blank"),children:[(0,s.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Add Fixture"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(i.Zb,{children:(0,s.jsxs)(i.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==ec?void 0:null===(t=ec.meta)||void 0===t?void 0:null===(e=t.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading..."}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})}),(0,s.jsx)(i.Zb,{children:(0,s.jsxs)(i.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==ec?void 0:null===(a=ec.data)||void 0===a?void 0:a.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),(0,s.jsx)(i.Zb,{children:(0,s.jsxs)(i.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:(null==ec?void 0:null===(C=ec.data)||void 0===C?void 0:C.filter(e=>"NS"===e.status).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),(0,s.jsx)(i.Zb,{children:(0,s.jsxs)(i.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:(null==ec?void 0:null===(F=ec.data)||void 0===F?void 0:F.filter(e=>"FT"===e.status).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Completed"})]})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsx)(i.Ol,{children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(i.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"All Fixtures",eg&&(0,s.jsx)("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal",children:"Demo Mode"})]}),(0,s.jsx)(i.SZ,{children:eg?"Showing demo data - API backend is not available":ea?"Showing fixtures for ".concat(ea.toLocaleDateString()):"Showing fixtures for today (".concat(new Date().toLocaleDateString(),")")})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(I,{date:ea,onDateChange:es,placeholder:"Today (click to change)",className:"w-[200px]"}),ea&&(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:()=>es(void 0),className:"px-2",title:"Reset to today",children:(0,s.jsx)(D.Z,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)(i.aY,{children:ed?(0,s.jsx)(T.hM,{rows:10,columns:7}):(0,s.jsx)(m.w,{data:(null==eh?void 0:eh.data)||[],columns:ey,loading:ed&&!eg,searchable:!0,searchPlaceholder:"Search fixtures...",showSearchButton:!0,searchValue:P,onSearchChange:U,onSearchSubmit:()=>{O(P.trim()),L(1)},onSearchClear:()=>{U(""),O(""),L(1)},pagination:{page:E,limit:A,total:(null==eh?void 0:null===(z=eh.meta)||void 0===z?void 0:z.totalItems)||(null==eh?void 0:eh.totalItems)||0,onPageChange:L,onLimitChange:e=>{Z(e),L(1)}},emptyMessage:"No fixtures found"})})]}),(0,s.jsx)(x.sm,{isOpen:G,onClose:()=>{K(!1),W(null)},onConfirm:()=>{Q&&ef.mutate(Q)},title:"Delete Fixture",message:Q?'Are you sure you want to delete the fixture "'.concat(Q.homeTeamName," vs ").concat(Q.awayTeamName,'"? This action cannot be undone.'):"Are you sure you want to delete this fixture?",confirmText:"Delete",cancelText:"Cancel",variant:"destructive",loading:ef.isLoading}),ee&&(0,s.jsx)(S.A,{isOpen:$,onClose:()=>{X(!1),et(null)},fixture:ee}),(0,s.jsx)(R,{isOpen:eo,onClose:()=>er(!1),selectedDate:ea,onDateSelect:es,onApplyFilter:e=>{es(e),L(1)},onResetFilter:()=>{es(void 0),L(1)}})]})}},9208:function(e,t,a){"use strict";a.d(t,{$N:function(){return g},Be:function(){return h},Vq:function(){return i},cN:function(){return x},cZ:function(){return u},fK:function(){return m}});var s=a(57437),o=a(2265),r=a(72936),n=a(52235),l=a(22169);let i=r.fC;r.xz;let c=r.h_;r.x8;let d=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...o})});d.displayName=r.aV.displayName;let u=o.forwardRef((e,t)=>{let{className:a,children:o,...i}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.VY,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...i,children:[o,(0,s.jsxs)(r.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.VY.displayName;let m=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};m.displayName="DialogHeader";let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};x.displayName="DialogFooter";let g=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...o})});g.displayName=r.Dx.displayName;let h=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...o})});h.displayName=r.dk.displayName},86468:function(e,t,a){"use strict";a.d(t,{r:function(){return l}});var s=a(57437),o=a(2265),r=a(94845),n=a(22169);let l=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...o,ref:t,children:(0,s.jsx)(r.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=r.fC.displayName},85110:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});var s=a(57437);a(2265);var o=a(86468),r=a(12647);function n(e){let{checked:t,onCheckedChange:a,label:n,description:l,disabled:i=!1,size:c="md",variant:d="default"}=e;return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(o.r,{id:n,checked:t,onCheckedChange:a,disabled:i}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)(r._,{htmlFor:n,className:"font-medium cursor-pointer ".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[c]," ").concat({default:t?"text-blue-700":"text-gray-700",success:t?"text-green-700":"text-gray-700",warning:t?"text-yellow-700":"text-gray-700",danger:t?"text-red-700":"text-gray-700"}[d]," ").concat(i?"opacity-50":""),children:n}),l&&(0,s.jsx)("span",{className:"text-xs text-gray-500 ".concat(i?"opacity-50":""),children:l})]})]})}},2975:function(e,t,a){"use strict";a.d(t,{L:function(){return o}});var s=a(74921);let o={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;void 0!==s&&a.append(t,s.toString())}),await s.x.get("/football/fixtures/schedules/".concat(e,"?").concat(a.toString()))},getFixtureStatistics:async e=>await s.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Season sync - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Season sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Season sync - No token found!"),e})();console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger season sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Daily sync - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Daily sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Daily sync - No token found!"),e})();console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger daily sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Sync status - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Sync status - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Sync status - No token found!"),e})();console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||"Failed to get sync status: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Sync status successful"),a},createFixture:async e=>{var t;let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Create fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Create fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Create fixture - No token found!"),e})();console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!a.Authorization,data:e});let s=await fetch("/api/fixtures",{method:"POST",headers:a,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",s.status,s.statusText,e),Error(e.message||"Failed to create fixture: ".concat(s.statusText))}let o=await s.json();return console.log("✅ Create fixture successful:",null===(t=o.data)||void 0===t?void 0:t.id),o.data||o},updateFixture:async(e,t)=>{let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Update fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Update fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Update fixture - No token found!"),e})();console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!a.Authorization,data:t});let s=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:a,body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",s.status,s.statusText,e),Error(e.message||"Failed to update fixture: ".concat(s.statusText))}let o=await s.json();return console.log("✅ Update fixture successful:",e),o.data||o},deleteFixture:async e=>{let t=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let s=JSON.parse(a),o=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(o)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",o.substring(0,20)+"..."),e.Authorization="Bearer ".concat(o),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Delete fixture - No token found!"),e})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/statistics"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture statistics: ".concat(t.statusText));return await t.json()},getFixtureEvents:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/events"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture events: ".concat(t.statusText));return await t.json()},getFixture:async e=>(await o.getFixtureById(e)).data}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,1953,9918,1715,1614,6877,1380,8942,2971,8069,1744],function(){return e(e.s=54240)}),_N_E=e.O()}]);