(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8815],{76399:function(e,t,r){Promise.resolve().then(r.bind(r,13333))},26490:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(57977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},62457:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(57977).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},40834:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(57977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},34059:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,t,r){"use strict";var a=r(15313);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},13333:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var a=r(57437),s=r(2265),n=r(31346),o=r(47907),i=r(15671),l=r(33277),c=r(575),u=r(34059),d=r(62457),f=r(40834),h=r(26490),g=r(2975);function m(){var e,t,r;let m=(0,o.useRouter)(),[x,p]=(0,s.useState)(!0),{data:y,isLoading:v,error:j,refetch:w}=(0,n.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>g.L.getUpcomingAndLive({limit:20}),refetchInterval:!!x&&3e4,staleTime:1e4}),N=e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800 animate-pulse";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}},b=(e,t)=>{switch(e){case"1H":case"2H":return"".concat(t,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}},k=e=>{let t=new Date(e);return{date:t.toLocaleDateString(),time:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},D=e=>{var t,r;let{fixture:s}=e,{date:n,time:o}=k(s.date),c=["1H","2H","HT"].includes(s.status);return(0,a.jsxs)(i.Zb,{className:"transition-all duration-200 cursor-pointer hover:shadow-md hover:ring-1 hover:ring-blue-300 hover:scale-[1.02] ".concat(c?"ring-2 ring-green-500":""),onClick:()=>{m.push("/dashboard/fixtures/".concat(s.externalId))},title:"Click to view fixture details",children:[(0,a.jsxs)(i.Ol,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.C,{className:N(s.status),children:b(s.status,s.elapsed)}),c&&(0,a.jsx)(l.C,{variant:"outline",className:"text-green-600 border-green-600",children:"LIVE"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[n," • ",o]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[s.leagueName," • ",s.round]})]}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.Z,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsx)("span",{className:"font-medium",children:s.homeTeamName})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.Z,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsx)("span",{className:"font-medium",children:s.awayTeamName})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:null!==(t=s.goalsHome)&&void 0!==t?t:"-"}),(0,a.jsx)("div",{className:"text-2xl font-bold",children:null!==(r=s.goalsAway)&&void 0!==r?r:"-"})]})]}),s.venue&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(d.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[s.venue.name,", ",s.venue.city]})]}),null!==s.scoreHalftimeHome&&null!==s.scoreHalftimeAway&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Half-time: ",s.scoreHalftimeHome," - ",s.scoreHalftimeAway]})]})})]})};return j?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,a.jsxs)(c.z,{onClick:()=>w(),children:[(0,a.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.z,{variant:x?"default":"outline",size:"sm",onClick:()=>p(!x),children:[(0,a.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),"Auto Refresh ",x?"ON":"OFF"]}),(0,a.jsxs)(c.z,{variant:"outline",size:"sm",onClick:()=>w(),disabled:v,children:[(0,a.jsx)(f.Z,{className:"mr-2 h-4 w-4 ".concat(v?"animate-spin":"")}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.filter(e=>"NS"===e.status).length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:(null==y?void 0:null===(r=y.data)||void 0===r?void 0:r.length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})})]}),v?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,t)=>(0,a.jsx)(i.Zb,{className:"animate-pulse",children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded"})]})})},t))}):(null==y?void 0:y.data)&&y.data.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:y.data.map(e=>(0,a.jsx)(D,{fixture:e},e.id))}):(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6 text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No live or upcoming fixtures found"})})}),(null==y?void 0:y.meta)&&y.meta.totalPages>1&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Page ",y.meta.currentPage," of ",y.meta.totalPages,"(",y.meta.totalItems," total fixtures)"]})})]})}},33277:function(e,t,r){"use strict";r.d(t,{C:function(){return i}});var a=r(57437);r(2265);var s=r(49769),n=r(22169);let o=(0,s.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:r}),t),...s})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var a=r(57437),s=r(2265),n=r(59143),o=r(49769),i=r(22169);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:c=!1,...u}=e,d=c?n.g7:"button";return(0,a.jsx)(d,{className:(0,i.cn)(l({variant:s,size:o,className:r})),ref:t,...u})});c.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return o},aY:function(){return u},ll:function(){return l}});var a=r(57437),s=r(2265),n=r(22169);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...s})});o.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},74921:function(e,t,r){"use strict";r.d(t,{x:function(){return o}});var a=r(73107),s=r(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=s.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:a}=await r.json(),s=e.user;if(s)return e.setAuth(s,a,t),this.setAuthToken(a),console.log("✅ Token refreshed successfully"),a}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:a,reject:s}=r;e?s(e):a(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let o=new n},2975:function(e,t,r){"use strict";r.d(t,{L:function(){return s}});var a=r(74921);let s={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch fixtures: ".concat(r.statusText));return await r.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch live fixtures: ".concat(r.statusText));return await r.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;void 0!==a&&r.append(t,a.toString())}),await a.x.get("/football/fixtures/schedules/".concat(e,"?").concat(r.toString()))},getFixtureStatistics:async e=>await a.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Season sync - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Season sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Season sync - No token found!"),e})();console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger season sync: ".concat(t.statusText))}let r=await t.json();return console.log("✅ Season sync successful"),r},triggerDailySync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Daily sync - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Daily sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Daily sync - No token found!"),e})();console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger daily sync: ".concat(t.statusText))}let r=await t.json();return console.log("✅ Daily sync successful"),r},getSyncStatus:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Sync status - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Sync status - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Sync status - No token found!"),e})();console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||"Failed to get sync status: ".concat(t.statusText))}let r=await t.json();return console.log("✅ Sync status successful"),r},createFixture:async e=>{var t;let r=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Create fixture - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Create fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Create fixture - No token found!"),e})();console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!r.Authorization,data:e});let a=await fetch("/api/fixtures",{method:"POST",headers:r,body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to create fixture: ".concat(a.statusText))}let s=await a.json();return console.log("✅ Create fixture successful:",null===(t=s.data)||void 0===t?void 0:t.id),s.data||s},updateFixture:async(e,t)=>{let r=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Update fixture - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Update fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Update fixture - No token found!"),e})();console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!r.Authorization,data:t});let a=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:r,body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to update fixture: ".concat(a.statusText))}let s=await a.json();return console.log("✅ Update fixture successful:",e),s.data||s},deleteFixture:async e=>{let t=(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",s.substring(0,20)+"..."),e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");if(r)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(r),e}return console.warn("❌ Delete fixture - No token found!"),e})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let r=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",r.status,r.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(r.statusText))}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/statistics"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture statistics: ".concat(t.statusText));return await t.json()},getFixtureEvents:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/events"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture events: ".concat(t.statusText));return await t.json()},getFixture:async e=>(await s.getFixtureById(e)).data}},48763:function(e,t,r){"use strict";r.d(t,{t:function(){return o}});var a=r(12574),s=r(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},o=(0,a.U)()((0,s.tJ)((e,t)=>({...n,setAuth:(t,r,a)=>{e({user:t,accessToken:r,refreshToken:a,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let a=t().user;a&&e({user:{...a,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let a=Array.isArray(e)?e:[e];return"admin"===r.role||(a.includes("editor")?["admin","editor"].includes(r.role):a.includes("moderator")?["admin","editor","moderator"].includes(r.role):a.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var a=r(75504),s=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}},61266:function(e,t,r){"use strict";r.d(t,{F:function(){return n},e:function(){return o}});var a=r(2265);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}function o(...e){return a.useCallback(n(...e),e)}},59143:function(e,t,r){"use strict";r.d(t,{Z8:function(){return o},g7:function(){return i},sA:function(){return c}});var a=r(2265),s=r(61266),n=r(57437);function o(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{let t=n(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(l.ref=t?(0,s.F)(t,i):i),a.cloneElement(r,l)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...o}=e,i=a.Children.toArray(s),l=i.find(u);if(l){let e=l.props.children,s=i.map(t=>t!==l?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...o,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var i=o("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function u(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},49769:function(e,t,r){"use strict";r.d(t,{j:function(){return o}});var a=r(75504);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.W,o=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let n=s(t)||s(a);return o[e][n]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,l,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[2150,9101,8939,1346,2971,8069,1744],function(){return e(e.s=76399)}),_N_E=e.O()}]);