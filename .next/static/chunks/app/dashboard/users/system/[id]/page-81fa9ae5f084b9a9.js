(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1579],{74479:function(e,s,a){Promise.resolve().then(a.bind(a,27278))},26490:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(57977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},27278:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return Z}});var t=a(57437),r=a(2265),i=a(47907),l=a(53879),c=a(29295),n=a(50489),d=a(77326),o=a(90684),m=a(97307);let u=(0,a(57977).Z)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var x=a(95032),h=a(26490),j=a(575),y=a(15671),p=a(33277),N=a(75808),v=a(99497),g=a(95453),f=a(91679),A=a(76862),w=a(38358),b=a(11546);function Z(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:a}=(0,A.p)(),{canManageUsers:Z}=(0,b.TE)(),L=e.id,{data:T,isLoading:S,error:C}=w.OL.useGetById(L),{mutate:k,isLoading:U}=w.OL.useDelete(),{data:D=[]}=w.OL.useActivityLogs(L),[q,E]=(0,r.useState)(!1);return S?(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})}):C||!T?(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,t.jsxs)(j.z,{onClick:()=>s.push("/dashboard/users/system"),children:[(0,t.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})}):(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(j.z,{variant:"ghost",size:"sm",onClick:()=>s.push("/dashboard/users/system"),children:[(0,t.jsx)(l.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:T.fullName||T.username}),(0,t.jsx)("p",{className:"text-gray-600",children:T.email})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[Z()&&(0,t.jsxs)(j.z,{variant:"outline",onClick:()=>s.push("/dashboard/users/system/".concat(L,"/edit")),children:[(0,t.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"Edit"]}),Z()&&(0,t.jsxs)(f.aR,{open:q,onOpenChange:E,children:[(0,t.jsx)(f.vW,{asChild:!0,children:(0,t.jsxs)(j.z,{variant:"destructive",children:[(0,t.jsx)(n.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})}),(0,t.jsxs)(f._T,{children:[(0,t.jsxs)(f.fY,{children:[(0,t.jsx)(f.f$,{children:"Are you sure?"}),(0,t.jsx)(f.yT,{children:"This action cannot be undone. This will permanently delete the user account and remove all associated data."})]}),(0,t.jsxs)(f.xo,{children:[(0,t.jsx)(f.le,{children:"Cancel"}),(0,t.jsx)(f.OL,{onClick:()=>{k(Number(L),{onSuccess:()=>{a({title:"User deleted",description:"System user has been successfully deleted."}),s.push("/dashboard/users/system")},onError:e=>{a({title:"Error",description:(null==e?void 0:e.message)||"Failed to delete user.",variant:"destructive"})}})},className:"bg-red-600 hover:bg-red-700",disabled:U,children:U?"Deleting...":"Delete User"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(v.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,t.jsxs)(v.dr,{children:[(0,t.jsx)(v.SP,{value:"overview",children:"Overview"}),(0,t.jsx)(v.SP,{value:"permissions",children:"Permissions"}),(0,t.jsx)(v.SP,{value:"activity",children:"Activity"})]}),(0,t.jsxs)(v.nU,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)(y.Zb,{children:[(0,t.jsx)(y.Ol,{children:(0,t.jsxs)(y.ll,{className:"flex items-center",children:[(0,t.jsx)(d.Z,{className:"w-5 h-5 mr-2"}),"Profile Information"]})}),(0,t.jsxs)(y.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(N.qE,{className:"w-16 h-16",children:(0,t.jsx)(N.Q5,{children:T.fullName?T.fullName.charAt(0):T.username.charAt(0)})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.C,{variant:(e=>{switch(e){case"admin":return"destructive";case"editor":return"default";case"moderator":return"secondary";default:return"outline"}})(T.role),children:T.role.charAt(0).toUpperCase()+T.role.slice(1)}),(0,t.jsx)(p.C,{variant:T.isActive?"default":"secondary",children:T.isActive?"Active":"Inactive"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",T.id]})]})]}),(0,t.jsx)(g.Z,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.Z,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Email"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:T.email})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.Z,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Username"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:T.username})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.Z,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Last Login"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:T.lastLoginAt?new Date(T.lastLoginAt).toLocaleString():"Never"})]})]})]})]}),(0,t.jsxs)(y.Zb,{children:[(0,t.jsx)(y.Ol,{children:(0,t.jsxs)(y.ll,{className:"flex items-center",children:[(0,t.jsx)(u,{className:"w-5 h-5 mr-2"}),"Account Details"]})}),(0,t.jsx)(y.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.Z,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Created At"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:new Date(T.createdAt).toLocaleString()})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.Z,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Updated At"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:new Date(T.updatedAt).toLocaleString()})]})]})})]})]}),(0,t.jsx)(v.nU,{value:"permissions",className:"space-y-6",children:(0,t.jsxs)(y.Zb,{children:[(0,t.jsxs)(y.Ol,{children:[(0,t.jsx)(y.ll,{children:"Role Permissions"}),(0,t.jsxs)(y.SZ,{children:["Permissions granted to this user based on their role: ",T.role]})]}),(0,t.jsx)(y.aY,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)(p.C,{variant:"outline",children:"admin"===T.role?"Full System Access":"editor"===T.role?"Content Management":"Content Moderation"})})})})]})}),(0,t.jsx)(v.nU,{value:"activity",className:"space-y-6",children:(0,t.jsxs)(y.Zb,{children:[(0,t.jsxs)(y.Ol,{children:[(0,t.jsxs)(y.ll,{className:"flex items-center",children:[(0,t.jsx)(x.Z,{className:"w-5 h-5 mr-2"}),"Recent Activity"]}),(0,t.jsx)(y.SZ,{children:"Latest actions performed by this user"})]}),(0,t.jsx)(y.aY,{children:(0,t.jsx)("div",{className:"space-y-4",children:D.length>0?D.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:e.action}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:e.details})]})]},e.id)):(0,t.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No activity logs available"})})})]})})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(y.Zb,{children:[(0,t.jsx)(y.Ol,{children:(0,t.jsxs)(y.ll,{className:"flex items-center",children:[(0,t.jsx)(h.Z,{className:"w-5 h-5 mr-2"}),"Quick Stats"]})}),(0,t.jsxs)(y.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Days Since Created"}),(0,t.jsx)("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(T.createdAt).getTime())/864e5)})]}),T.lastLoginAt&&(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Days Since Last Login"}),(0,t.jsx)("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(T.lastLoginAt).getTime())/864e5)})]})]})]}),Z()&&(0,t.jsxs)(y.Zb,{children:[(0,t.jsx)(y.Ol,{children:(0,t.jsx)(y.ll,{children:"Quick Actions"})}),(0,t.jsxs)(y.aY,{className:"space-y-2",children:[(0,t.jsxs)(j.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>s.push("/dashboard/users/system/".concat(L,"/edit")),children:[(0,t.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"Edit Profile"]}),(0,t.jsxs)(j.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{a({title:"Coming Soon",description:"Password reset functionality will be implemented."})},children:[(0,t.jsx)(u,{className:"w-4 h-4 mr-2"}),"Reset Password"]})]})]})]})]})]})}},38358:function(e,s,a){"use strict";a.d(s,{KX:function(){return n},OL:function(){return u},SZ:function(){return m},aw:function(){return o}});var t=a(31346),r=a(64095),i=a(8186),l=a(90773),c=a(56288);let n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,t.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let a=e.search.toLowerCase();s=s.filter(e=>{var s;return e.username.toLowerCase().includes(a)||e.email.toLowerCase().includes(a)||(null===(s=e.fullName)||void 0===s?void 0:s.toLowerCase().includes(a))})}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let a=e.page||1,t=e.limit||10,r=(a-1)*t;return{data:s.slice(r,r+t),meta:{total:s.length,page:a,limit:t,totalPages:Math.ceil(s.length/t)}}},staleTime:3e5})},d=e=>(0,t.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:"user".concat(e),email:"user".concat(e,"@fecms-sport.com"),role:"editor",fullName:"User ".concat(e),isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),o=()=>{let e=(0,r.NL)(),s=(0,i.D)({mutationFn:l.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),c.toast.success("System user created successfully")},onError:e=>{c.toast.error("Failed to create user: ".concat(e.message))}});return{createUser:s,updateUser:(0,i.D)({mutationFn:e=>{let{id:s,data:a}=e;return l.i.updateUser(s,a)},onSuccess:(s,a)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",a.id]}),c.toast.success("System user updated successfully")},onError:e=>{c.toast.error("Failed to update user: ".concat(e.message))}}),deleteUser:(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),c.toast.success("System user deleted successfully")},onError:e=>{c.toast.error("Failed to delete user: ".concat(e.message))}}),toggleUserStatus:(0,i.D)({mutationFn:async e=>{let{userId:s,isActive:a}=e;return l.i.updateUser(s,{isActive:a})},onSuccess:(s,a)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",a.userId]}),c.toast.success("User ".concat(a.isActive?"activated":"deactivated"," successfully"))},onError:e=>{c.toast.error("Failed to update user status: ".concat(e.message))}})}},m=()=>(0,t.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),u={useSystemUsers:n,useGetById:e=>d(e),useCreate:()=>{let{createUser:e}=o();return e},useUpdate:()=>{let{updateUser:e}=o();return e},useDelete:()=>{let{deleteUser:e}=o();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=o();return e},useStats:m,useActivityLogs:e=>(0,t.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,3411,6877,8057,2971,8069,1744],function(){return e(e.s=74479)}),_N_E=e.O()}]);