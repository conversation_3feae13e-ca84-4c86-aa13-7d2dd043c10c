(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[13],{16984:function(e,s,t){Promise.resolve().then(t.bind(t,56754))},53879:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},79580:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},70699:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},77326:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},11213:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},56754:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return A}});var r=t(57437),a=t(2265),i=t(47907),n=t(82670),l=t(21270),c=t(60124),o=t(53879),d=t(70699),u=t(11213),m=t(77326),f=t(575),h=t(15671),x=t(22782),p=t(12647),y=t(18641),g=t(86468),j=t(95453),v=t(76862),N=t(38358),b=t(11546);let w=c.z.object({username:c.z.string().min(3,"Username must be at least 3 characters"),email:c.z.string().email("Invalid email address"),fullName:c.z.string().min(1,"Full name is required"),role:c.z.enum(["admin","editor","moderator"],{required_error:"Role is required"}),isActive:c.z.boolean()});function A(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,v.p)(),{canManageUsers:c}=(0,b.TE)(),A=e.id,{data:k,isLoading:Z,error:S}=N.OL.useGetById(A),{mutate:C,isLoading:z}=N.OL.useUpdate(),[T,U]=(0,a.useState)(!1),L=(0,n.cI)({resolver:(0,l.F)(w),defaultValues:{username:"",email:"",fullName:"",role:"editor",isActive:!0}});(0,a.useEffect)(()=>{k&&L.reset({username:k.username||"",email:k.email||"",fullName:k.fullName||"",role:k.role||"editor",isActive:k.isActive})},[k,L]),(0,a.useEffect)(()=>{let e=L.watch(()=>{U(!0)});return()=>e.unsubscribe()},[L]);let R=e=>{C({id:Number(A),data:e},{onSuccess:()=>{t({title:"User updated",description:"System user has been successfully updated."}),U(!1),s.push("/dashboard/users/system/".concat(A))},onError:e=>{t({title:"Error",description:(null==e?void 0:e.message)||"Failed to update user.",variant:"destructive"})}})},F=()=>{T?confirm("You have unsaved changes. Are you sure you want to leave?")&&s.push("/dashboard/users/system/".concat(A)):s.push("/dashboard/users/system/".concat(A))};return c()?Z?(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]})]})}):S||!k?(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,r.jsxs)(f.z,{onClick:()=>s.push("/dashboard/users/system"),children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})}):(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(f.z,{variant:"ghost",size:"sm",onClick:F,children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit User"}),(0,r.jsx)("p",{className:"text-gray-600",children:k.email})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f.z,{variant:"outline",onClick:F,children:"Cancel"}),(0,r.jsxs)(f.z,{onClick:L.handleSubmit(R),disabled:z||!T,children:[(0,r.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),z?"Saving...":"Save Changes"]})]})]}),(0,r.jsx)("form",{onSubmit:L.handleSubmit(R),className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center",children:[(0,r.jsx)(u.Z,{className:"w-5 h-5 mr-2"}),"Basic Information"]}),(0,r.jsx)(h.SZ,{children:"Update the user's personal information"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p._,{htmlFor:"fullName",children:"Full Name *"}),(0,r.jsx)(x.I,{id:"fullName",...L.register("fullName")}),L.formState.errors.fullName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:L.formState.errors.fullName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p._,{htmlFor:"username",children:"Username *"}),(0,r.jsx)(x.I,{id:"username",...L.register("username")}),L.formState.errors.username&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:L.formState.errors.username.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p._,{htmlFor:"email",children:"Email Address *"}),(0,r.jsx)(x.I,{id:"email",type:"email",...L.register("email")}),L.formState.errors.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:L.formState.errors.email.message})]})]})]}),(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsxs)(h.ll,{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Role & Permissions"]}),(0,r.jsx)(h.SZ,{children:"Configure the user's role and access level"})]}),(0,r.jsxs)(h.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p._,{htmlFor:"role",children:"Role *"}),(0,r.jsxs)(y.Ph,{value:L.watch("role"),onValueChange:e=>L.setValue("role",e),children:[(0,r.jsx)(y.i4,{children:(0,r.jsx)(y.ki,{placeholder:"Select a role"})}),(0,r.jsxs)(y.Bw,{children:[(0,r.jsx)(y.Ql,{value:"admin",children:"Administrator"}),(0,r.jsx)(y.Ql,{value:"editor",children:"Editor"}),(0,r.jsx)(y.Ql,{value:"moderator",children:"Moderator"})]})]}),L.formState.errors.role&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:L.formState.errors.role.message})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(j.Z,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium",children:"Role Permissions"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["admin"===L.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Full system access"}),(0,r.jsx)("li",{children:"• User management"}),(0,r.jsx)("li",{children:"• System configuration"}),(0,r.jsx)("li",{children:"• All content operations"})]}),"editor"===L.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Content creation and editing"}),(0,r.jsx)("li",{children:"• Media management"}),(0,r.jsx)("li",{children:"• Limited user management"})]}),"moderator"===L.watch("role")&&(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• Content review and approval"}),(0,r.jsx)("li",{children:"• User content moderation"}),(0,r.jsx)("li",{children:"• Basic reporting access"})]})]})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(h.Zb,{children:[(0,r.jsxs)(h.Ol,{children:[(0,r.jsx)(h.ll,{children:"Account Status"}),(0,r.jsx)(h.SZ,{children:"Manage the user's account status"})]}),(0,r.jsx)(h.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(p._,{htmlFor:"isActive",className:"text-sm font-medium",children:"Account Active"}),(0,r.jsx)(g.r,{id:"isActive",checked:L.watch("isActive"),onCheckedChange:e=>L.setValue("isActive",e)})]})})]}),(0,r.jsxs)(h.Zb,{children:[(0,r.jsx)(h.Ol,{children:(0,r.jsx)(h.ll,{children:"Account Information"})}),(0,r.jsxs)(h.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"User ID"}),(0,r.jsx)("span",{className:"font-medium",children:k.id})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(k.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last Updated"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(k.updatedAt).toLocaleDateString()})]}),k.lastLoginAt&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last Login"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(k.lastLoginAt).toLocaleDateString()})]})]})]}),(0,r.jsxs)(h.Zb,{children:[(0,r.jsx)(h.Ol,{children:(0,r.jsx)(h.ll,{children:"Security Actions"})}),(0,r.jsxs)(h.aY,{className:"space-y-2",children:[(0,r.jsx)(f.z,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{t({title:"Coming Soon",description:"Password reset functionality will be implemented."})},children:"Reset Password"}),(0,r.jsx)(f.z,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{t({title:"Coming Soon",description:"Force logout functionality will be implemented."})},children:"Force Logout"})]})]})]})]})})]}):(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to edit users."}),(0,r.jsxs)(f.z,{onClick:()=>s.push("/dashboard/users/system"),children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})})}},575:function(e,s,t){"use strict";t.d(s,{d:function(){return c},z:function(){return o}});var r=t(57437),a=t(2265),i=t(59143),n=t(49769),l=t(22169);let c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:o=!1,...d}=e,u=o?i.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(c({variant:a,size:n,className:t})),ref:s,...d})});o.displayName="Button"},22782:function(e,s,t){"use strict";t.d(s,{I:function(){return n}});var r=t(57437),a=t(2265),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...n})});n.displayName="Input"},12647:function(e,s,t){"use strict";t.d(s,{_:function(){return o}});var r=t(57437),a=t(2265),i=t(24602),n=t(49769),l=t(22169);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.f,{ref:s,className:(0,l.cn)(c(),t),...a})});o.displayName=i.f.displayName},18641:function(e,s,t){"use strict";t.d(s,{Bw:function(){return x},Ph:function(){return d},Ql:function(){return p},i4:function(){return m},ki:function(){return u}});var r=t(57437),a=t(2265),i=t(18178),n=t(23441),l=t(85159),c=t(80037),o=t(22169);let d=i.fC;i.ZA;let u=i.B4,m=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsxs)(i.xz,{ref:s,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[a,(0,r.jsx)(i.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.xz.displayName;let f=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});f.displayName=i.u_.displayName;let h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName=i.$G.displayName;let x=a.forwardRef((e,s)=>{let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(i.h_,{children:(0,r.jsxs)(i.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(f,{}),(0,r.jsx)(i.l_,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(h,{})]})})});x.displayName=i.VY.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",t),...a})}).displayName=i.__.displayName;let p=a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,r.jsxs)(i.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.wU,{children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(i.eT,{children:a})]})});p.displayName=i.ck.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...a})}).displayName=i.Z0.displayName},95453:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(57437),a=t(2265),i=t(51014),n=t(22169);let l=a.forwardRef((e,s)=>{let{className:t,orientation:a="horizontal",decorative:l=!0,...c}=e;return(0,r.jsx)(i.f,{ref:s,decorative:l,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",t),...c})});l.displayName=i.f.displayName},86468:function(e,s,t){"use strict";t.d(s,{r:function(){return l}});var r=t(57437),a=t(2265),i=t(94845),n=t(22169);let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...a,ref:s,children:(0,r.jsx)(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=i.fC.displayName},76862:function(e,s,t){"use strict";t.d(s,{p:function(){return a}});var r=t(56288);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},38358:function(e,s,t){"use strict";t.d(s,{KX:function(){return c},OL:function(){return m},SZ:function(){return u},aw:function(){return d}});var r=t(31346),a=t(64095),i=t(8186),n=t(90773),l=t(56288);let c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:["system-users",e],queryFn:async()=>{let s=[{id:1,username:"admin",email:"<EMAIL>",role:"admin",fullName:"System Administrator",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"},{id:2,username:"editor1",email:"<EMAIL>",role:"editor",fullName:"Content Editor",isActive:!0,createdAt:"2024-01-02T00:00:00Z",updatedAt:"2024-01-14T15:20:00Z",lastLoginAt:"2024-01-14T14:45:00Z"},{id:3,username:"moderator1",email:"<EMAIL>",role:"moderator",fullName:"Content Moderator",isActive:!0,createdAt:"2024-01-03T00:00:00Z",updatedAt:"2024-01-13T09:15:00Z",lastLoginAt:"2024-01-13T16:20:00Z"},{id:4,username:"editor2",email:"<EMAIL>",role:"editor",fullName:"Senior Editor",isActive:!1,createdAt:"2024-01-05T00:00:00Z",updatedAt:"2024-01-10T11:00:00Z",lastLoginAt:"2024-01-08T13:30:00Z"}];if(e.search){let t=e.search.toLowerCase();s=s.filter(e=>{var s;return e.username.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||(null===(s=e.fullName)||void 0===s?void 0:s.toLowerCase().includes(t))})}e.role&&(s=s.filter(s=>s.role===e.role)),void 0!==e.isActive&&(s=s.filter(s=>s.isActive===e.isActive));let t=e.page||1,r=e.limit||10,a=(t-1)*r;return{data:s.slice(a,a+r),meta:{total:s.length,page:t,limit:r,totalPages:Math.ceil(s.length/r)}}},staleTime:3e5})},o=e=>(0,r.a)({queryKey:["system-user",e],queryFn:async()=>({id:Number(e),username:"user".concat(e),email:"user".concat(e,"@fecms-sport.com"),role:"editor",fullName:"User ".concat(e),isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-15T08:30:00Z"}),enabled:!!e}),d=()=>{let e=(0,a.NL)(),s=(0,i.D)({mutationFn:n.i.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),l.toast.success("System user created successfully")},onError:e=>{l.toast.error("Failed to create user: ".concat(e.message))}});return{createUser:s,updateUser:(0,i.D)({mutationFn:e=>{let{id:s,data:t}=e;return n.i.updateUser(s,t)},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.id]}),l.toast.success("System user updated successfully")},onError:e=>{l.toast.error("Failed to update user: ".concat(e.message))}}),deleteUser:(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["system-users"]}),l.toast.success("System user deleted successfully")},onError:e=>{l.toast.error("Failed to delete user: ".concat(e.message))}}),toggleUserStatus:(0,i.D)({mutationFn:async e=>{let{userId:s,isActive:t}=e;return n.i.updateUser(s,{isActive:t})},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["system-users"]}),e.invalidateQueries({queryKey:["system-user",t.userId]}),l.toast.success("User ".concat(t.isActive?"activated":"deactivated"," successfully"))},onError:e=>{l.toast.error("Failed to update user status: ".concat(e.message))}})}},u=()=>(0,r.a)({queryKey:["system-user-stats"],queryFn:async()=>({total:12,active:10,inactive:2,byRole:{admin:2,editor:6,moderator:4},recentLogins:8,newThisMonth:2}),staleTime:6e5}),m={useSystemUsers:c,useGetById:e=>o(e),useCreate:()=>{let{createUser:e}=d();return e},useUpdate:()=>{let{updateUser:e}=d();return e},useDelete:()=>{let{deleteUser:e}=d();return e},useToggleStatus:()=>{let{toggleUserStatus:e}=d();return e},useStats:u,useActivityLogs:e=>(0,r.a)({queryKey:["system-user-activity",e],queryFn:async()=>[{id:1,action:"User Login",timestamp:"2024-01-15T08:30:00Z",details:"Successful login from *************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,action:"Profile Update",timestamp:"2024-01-14T15:20:00Z",details:"Updated email address",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:3,action:"Permission Change",timestamp:"2024-01-13T10:45:00Z",details:"Added moderator permissions",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}],enabled:!!e})}},11546:function(e,s,t){"use strict";t.d(s,{TE:function(){return o},a1:function(){return c}});var r=t(57437),a=t(2265),i=t(47907),n=t(27786),l=t(96146);let c=e=>{let{children:s,requiredRole:t,fallbackUrl:c="/auth/login"}=e,o=(0,i.useRouter)(),{isAuthenticated:d,user:u,isLoading:m}=(0,n.a)();if((0,a.useEffect)(()=>{if(!m){if(!d||!u){o.push(c);return}if(t&&!(Array.isArray(t)?t:[t]).includes(u.role)){o.push("/dashboard?error=unauthorized");return}}},[d,u,m,t,o,c]),m)return(0,r.jsx)(l.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,r.jsx)(l.SX,{message:"Redirecting to login..."});if(t){let e=Array.isArray(t)?t:[t];if(!e.includes(u.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,r.jsx)(r.Fragment,{children:s})},o=()=>{let{user:e}=(0,n.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),t=()=>s("admin"),r=()=>s(["admin","editor"]),a=()=>s(["admin","editor","moderator"]),i=()=>t(),l=()=>r(),c=()=>a(),o=()=>t();return{user:e,hasRole:s,isAdmin:t,isEditor:r,isModerator:a,canManageUsers:i,canManageContent:l,canModerate:c,canSync:o,can:e=>{switch(e){case"manage-users":return i();case"manage-content":return l();case"moderate":return c();case"sync":return o();default:return!1}}}}},51014:function(e,s,t){"use strict";t.d(s,{f:function(){return o}});var r=t(2265),a=t(29586),i=t(57437),n="horizontal",l=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=n,...c}=e,o=l.includes(r)?r:n;return(0,i.jsx)(a.WV.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var o=c},94845:function(e,s,t){"use strict";t.d(s,{bU:function(){return w},fC:function(){return b}});var r=t(2265),a=t(44991),i=t(61266),n=t(84104),l=t(9310),c=t(65030),o=t(76769),d=t(29586),u=t(57437),m="Switch",[f,h]=(0,n.b)(m),[x,p]=f(m),y=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:c,defaultChecked:o,required:f,disabled:h,value:p="on",onCheckedChange:y,form:g,...j}=e,[b,w]=r.useState(null),A=(0,i.e)(s,e=>w(e)),k=r.useRef(!1),Z=!b||g||!!b.closest("form"),[S,C]=(0,l.T)({prop:c,defaultProp:null!=o&&o,onChange:y,caller:m});return(0,u.jsxs)(x,{scope:t,checked:S,disabled:h,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":f,"data-state":N(S),"data-disabled":h?"":void 0,disabled:h,value:p,...j,ref:A,onClick:(0,a.M)(e.onClick,e=>{C(e=>!e),Z&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),Z&&(0,u.jsx)(v,{control:b,bubbles:!k.current,name:n,value:p,checked:S,required:f,disabled:h,form:g,style:{transform:"translateX(-100%)"}})]})});y.displayName=m;var g="SwitchThumb",j=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,a=p(g,t);return(0,u.jsx)(d.WV.span,{"data-state":N(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:s})});j.displayName=g;var v=r.forwardRef((e,s)=>{let{__scopeSwitch:t,control:a,checked:n,bubbles:l=!0,...d}=e,m=r.useRef(null),f=(0,i.e)(m,s),h=(0,c.D)(n),x=(0,o.t)(a);return r.useEffect(()=>{let e=m.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==n&&s){let t=new Event("click",{bubbles:l});s.call(e,n),e.dispatchEvent(t)}},[h,n,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...d,tabIndex:-1,ref:f,style:{...d.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var b=y,w=j}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,2810,6877,2971,8069,1744],function(){return e(e.s=16984)}),_N_E=e.O()}]);