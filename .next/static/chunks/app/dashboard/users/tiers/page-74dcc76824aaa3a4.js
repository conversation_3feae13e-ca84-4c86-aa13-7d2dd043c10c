(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2873],{76752:function(e,s,t){Promise.resolve().then(t.bind(t,96111))},96111:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return y}});var r=t(57437),a=t(2265),i=t(8967),n=t(83051),l=t(27271),c=t(34059),d=t(48219),o=t(56227),u=t(29733),m=t(16015),x=t(5835),h=t(95032),f=t(15671),p=t(99497),j=t(18641),g=t(33277),v=t(14440),N=t(93425);function y(){var e,s,t,y,b,w,T;let[k,C]=(0,a.useState)("30d"),{data:R,isLoading:Z}=N.Xn.useTierStats(k),{data:S,isLoading:A}=N.Xn.useRevenueStats(k),{data:P,isLoading:U}=N.Xn.useTierMigration(k),z={free:"#6B7280",premium:"#3B82F6",enterprise:"#10B981"},I=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),L=e=>"".concat(e>0?"+":"").concat(e.toFixed(1),"%"),D=e=>e>0?(0,r.jsx)(i.Z,{className:"w-4 h-4 text-green-500"}):e<0?(0,r.jsx)(n.Z,{className:"w-4 h-4 text-red-500"}):(0,r.jsx)(l.Z,{className:"w-4 h-4 text-gray-500"}),Y=e=>e>0?"text-green-600":e<0?"text-red-600":"text-gray-600";return Z||A||U?(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded"},e))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})}):(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"User Tiers Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Monitor subscription tiers, revenue, and user migrations"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)(j.Ph,{value:k,onValueChange:C,children:[(0,r.jsx)(j.i4,{className:"w-32",children:(0,r.jsx)(j.ki,{})}),(0,r.jsxs)(j.Bw,{children:[(0,r.jsx)(j.Ql,{value:"7d",children:"Last 7 days"}),(0,r.jsx)(j.Ql,{value:"30d",children:"Last 30 days"}),(0,r.jsx)(j.Ql,{value:"90d",children:"Last 90 days"}),(0,r.jsx)(j.Ql,{value:"1y",children:"Last year"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(f.Zb,{children:(0,r.jsxs)(f.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(null==R?void 0:null===(e=R.totalUsers)||void 0===e?void 0:e.toLocaleString())||0})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[D((null==R?void 0:R.totalUsersChange)||0),(0,r.jsx)("span",{className:"text-sm ml-1 ".concat(Y((null==R?void 0:R.totalUsersChange)||0)),children:L((null==R?void 0:R.totalUsersChange)||0)}),(0,r.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),(0,r.jsx)(f.Zb,{children:(0,r.jsxs)(f.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Monthly Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:I((null==S?void 0:S.monthlyRevenue)||0)})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-green-600"})})]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[D((null==S?void 0:S.revenueChange)||0),(0,r.jsx)("span",{className:"text-sm ml-1 ".concat(Y((null==S?void 0:S.revenueChange)||0)),children:L((null==S?void 0:S.revenueChange)||0)}),(0,r.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),(0,r.jsx)(f.Zb,{children:(0,r.jsxs)(f.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Revenue per User"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:I((null==S?void 0:S.arpu)||0)})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"w-6 h-6 text-purple-600"})})]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[D((null==S?void 0:S.arpuChange)||0),(0,r.jsx)("span",{className:"text-sm ml-1 ".concat(Y((null==S?void 0:S.arpuChange)||0)),children:L((null==S?void 0:S.arpuChange)||0)}),(0,r.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})}),(0,r.jsx)(f.Zb,{children:(0,r.jsxs)(f.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Conversion Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[((null==R?void 0:R.conversionRate)||0).toFixed(1),"%"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(u.Z,{className:"w-6 h-6 text-yellow-600"})})]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[D((null==R?void 0:R.conversionRateChange)||0),(0,r.jsx)("span",{className:"text-sm ml-1 ".concat(Y((null==R?void 0:R.conversionRateChange)||0)),children:L((null==R?void 0:R.conversionRateChange)||0)}),(0,r.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]})})]}),(0,r.jsxs)(p.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,r.jsxs)(p.dr,{children:[(0,r.jsx)(p.SP,{value:"overview",children:"Overview"}),(0,r.jsx)(p.SP,{value:"distribution",children:"Distribution"}),(0,r.jsx)(p.SP,{value:"migration",children:"Migration"}),(0,r.jsx)(p.SP,{value:"revenue",children:"Revenue"})]}),(0,r.jsxs)(p.nU,{value:"overview",className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Tier Distribution"]}),(0,r.jsx)(f.SZ,{children:"Current user distribution across tiers"})]}),(0,r.jsx)(f.aY,{className:"space-y-4",children:(null==R?void 0:null===(s=R.tierDistribution)||void 0===s?void 0:s.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:z[e.tier]}}),(0,r.jsx)("span",{className:"text-sm font-medium capitalize",children:e.tier}),(0,r.jsxs)(g.C,{variant:"outline",children:[e.count.toLocaleString()," users"]})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.percentage.toFixed(1),"%"]})]}),(0,r.jsx)(v.E,{value:e.percentage,className:"h-2"})]},e.tier)))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No data available"})})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[(0,r.jsx)(x.Z,{className:"w-5 h-5 mr-2"}),"Growth Trends"]}),(0,r.jsx)(f.SZ,{children:"User growth by tier over time"})]}),(0,r.jsx)(f.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:(null==R?void 0:null===(t=R.growthTrends)||void 0===t?void 0:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:z[e.tier]}}),(0,r.jsx)("span",{className:"font-medium capitalize",children:e.tier})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[D(e.change),(0,r.jsx)("span",{className:"font-medium ".concat(Y(e.change)),children:L(e.change)})]})]},e.tier)))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No trend data available"})})})]})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[(0,r.jsx)(h.Z,{className:"w-5 h-5 mr-2"}),"API Usage by Tier"]}),(0,r.jsx)(f.SZ,{children:"Average API usage across different tiers"})]}),(0,r.jsx)(f.aY,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(null==R?void 0:null===(y=R.apiUsageByTier)||void 0===y?void 0:y.map(e=>(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full mx-auto mb-2",style:{backgroundColor:z[e.tier]}}),(0,r.jsx)("p",{className:"text-sm font-medium capitalize",children:e.tier}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.avgUsage.toLocaleString()}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"avg calls/month"}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)(v.E,{value:e.usagePercentage,className:"h-1"})})]},e.tier)))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4 col-span-3",children:"No usage data available"})})})]})]}),(0,r.jsx)(p.nU,{value:"distribution",className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsx)(f.ll,{children:"Tier Breakdown"}),(0,r.jsx)(f.SZ,{children:"Detailed breakdown of users in each tier"})]}),(0,r.jsx)(f.aY,{children:(0,r.jsx)("div",{className:"space-y-4",children:(null==R?void 0:null===(b=R.tierDistribution)||void 0===b?void 0:b.map(e=>(0,r.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold capitalize",children:[e.tier," Tier"]}),(0,r.jsxs)(g.C,{variant:"enterprise"===e.tier?"default":"premium"===e.tier?"secondary":"outline",children:[e.count.toLocaleString()," users"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Percentage of total"}),(0,r.jsxs)("span",{children:[e.percentage.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Monthly growth"}),(0,r.jsx)("span",{className:Y(e.monthlyGrowth||0),children:L(e.monthlyGrowth||0)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Avg API usage"}),(0,r.jsx)("span",{children:(e.avgApiUsage||0).toLocaleString()})]})]})]},e.tier)))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No distribution data available"})})})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsx)(f.ll,{children:"Tier Characteristics"}),(0,r.jsx)(f.SZ,{children:"Key metrics for each subscription tier"})]}),(0,r.jsx)(f.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 border border-green-200 bg-green-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"Enterprise"}),(0,r.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-green-700",children:[(0,r.jsx)("p",{children:"• 100,000 API calls/month"}),(0,r.jsx)("p",{children:"• $99/month subscription"}),(0,r.jsx)("p",{children:"• 24/7 priority support"}),(0,r.jsx)("p",{children:"• Custom integrations"})]})]}),(0,r.jsxs)("div",{className:"p-4 border border-blue-200 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800",children:"Premium"}),(0,r.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-blue-700",children:[(0,r.jsx)("p",{children:"• 10,000 API calls/month"}),(0,r.jsx)("p",{children:"• $29/month subscription"}),(0,r.jsx)("p",{children:"• Priority email support"}),(0,r.jsx)("p",{children:"• Advanced analytics"})]})]}),(0,r.jsxs)("div",{className:"p-4 border border-gray-200 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"Free"}),(0,r.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-gray-700",children:[(0,r.jsx)("p",{children:"• 1,000 API calls/month"}),(0,r.jsx)("p",{children:"• Free tier"}),(0,r.jsx)("p",{children:"• Community support"}),(0,r.jsx)("p",{children:"• Basic documentation"})]})]})]})})]})]})}),(0,r.jsx)(p.nU,{value:"migration",className:"space-y-6",children:(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[(0,r.jsx)(u.Z,{className:"w-5 h-5 mr-2"}),"Tier Migration Patterns"]}),(0,r.jsx)(f.SZ,{children:"How users move between subscription tiers"})]}),(0,r.jsx)(f.aY,{children:(0,r.jsx)("div",{className:"space-y-6",children:(null==P?void 0:null===(w=P.migrations)||void 0===w?void 0:w.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.C,{variant:"enterprise"===e.fromTier?"default":"premium"===e.fromTier?"secondary":"outline",children:e.fromTier}),(0,r.jsx)("span",{children:"→"}),(0,r.jsx)(g.C,{variant:"enterprise"===e.toTier?"default":"premium"===e.toTier?"secondary":"outline",children:e.toTier})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.count," users migrated"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.percentage.toFixed(1),"%"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"of total migrations"})]})]},"".concat(e.fromTier,"-").concat(e.toTier))))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No migration data available"})})})]})}),(0,r.jsx)(p.nU,{value:"revenue",className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsxs)(f.ll,{className:"flex items-center",children:[(0,r.jsx)(d.Z,{className:"w-5 h-5 mr-2"}),"Revenue by Tier"]}),(0,r.jsx)(f.SZ,{children:"Revenue contribution from each tier"})]}),(0,r.jsx)(f.aY,{className:"space-y-4",children:(null==S?void 0:null===(T=S.revenueByTier)||void 0===T?void 0:T.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:z[e.tier]}}),(0,r.jsx)("span",{className:"text-sm font-medium capitalize",children:e.tier}),(0,r.jsx)(g.C,{variant:"outline",children:I(e.amount)})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.percentage.toFixed(1),"%"]})]}),(0,r.jsx)(v.E,{value:e.percentage,className:"h-2"})]},e.tier)))||(0,r.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No revenue data available"})})]}),(0,r.jsxs)(f.Zb,{children:[(0,r.jsxs)(f.Ol,{children:[(0,r.jsx)(f.ll,{children:"Revenue Metrics"}),(0,r.jsx)(f.SZ,{children:"Key financial performance indicators"})]}),(0,r.jsx)(f.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-green-600 font-medium",children:"MRR"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-800",children:I((null==S?void 0:S.mrr)||0)})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:"ARR"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-800",children:I(12*((null==S?void 0:S.mrr)||0))})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-purple-600 font-medium",children:"ARPU"}),(0,r.jsx)("p",{className:"text-lg font-bold text-purple-800",children:I((null==S?void 0:S.arpu)||0)})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Churn Rate"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-yellow-800",children:[((null==S?void 0:S.churnRate)||0).toFixed(1),"%"]})]})]})})]})]})})]})]})}},33277:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(57437);t(2265);var a=t(49769),i=t(22169);let n=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...a})}},15671:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return n},aY:function(){return o},ll:function(){return c}});var r=t(57437),a=t(2265),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...a})});n.displayName="Card";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},18641:function(e,s,t){"use strict";t.d(s,{Bw:function(){return f},Ph:function(){return o},Ql:function(){return p},i4:function(){return m},ki:function(){return u}});var r=t(57437),a=t(2265),i=t(18178),n=t(23441),l=t(85159),c=t(80037),d=t(22169);let o=i.fC;i.ZA;let u=i.B4,m=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsxs)(i.xz,{ref:s,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[a,(0,r.jsx)(i.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.xz.displayName;let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.u_,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=i.u_.displayName;let h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.$G,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName=i.$G.displayName;let f=a.forwardRef((e,s)=>{let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(i.h_,{children:(0,r.jsxs)(i.VY,{ref:s,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(x,{}),(0,r.jsx)(i.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(h,{})]})})});f.displayName=i.VY.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.__,{ref:s,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t),...a})}).displayName=i.__.displayName;let p=a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,r.jsxs)(i.ck,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.wU,{children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(i.eT,{children:a})]})});p.displayName=i.ck.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...a})}).displayName=i.Z0.displayName},99497:function(e,s,t){"use strict";t.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return l},nU:function(){return o}});var r=t(57437),a=t(2265),i=t(64694),n=t(22169);let l=i.fC,c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.aV,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});c.displayName=i.aV.displayName;let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.xz,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});d.displayName=i.xz.displayName;let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.VY,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});o.displayName=i.VY.displayName},74921:function(e,s,t){"use strict";t.d(s,{x:function(){return n}});var r=t(73107),a=t(48763);class i{setupInterceptors(){this.client.interceptors.request.use(e=>{let s=this.getAuthToken();return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var s;let t=e.config;if((null===(s=e.response)||void 0===s?void 0:s.status)===401&&!t._retry){if(this.isRefreshing)return new Promise((e,s)=>{this.failedQueue.push({resolve:e,reject:s})}).then(e=>(t.headers.Authorization="Bearer ".concat(e),this.client(t))).catch(e=>Promise.reject(e));t._retry=!0,this.isRefreshing=!0;try{let s=await this.refreshToken();if(s)return this.processQueue(null,s),t.headers.Authorization="Bearer ".concat(s),this.client(t);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let s=localStorage.getItem("auth-storage");if(s){var e;let t=JSON.parse(s);return(null===(e=t.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),s=e.refreshToken;if(!s)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:s})});if(!t.ok)throw Error("Token refresh failed");let{accessToken:r}=await t.json(),a=e.user;if(a)return e.setAuth(a,r,s),this.setAuthToken(r),console.log("✅ Token refreshed successfully"),r}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(t=>{let{resolve:r,reject:a}=t;e?a(e):r(s)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,s){return(await this.client.get(e,s)).data}async post(e,s,t){return(await this.client.post(e,s,t)).data}async put(e,s,t){return(await this.client.put(e,s,t)).data}async patch(e,s,t){return(await this.client.patch(e,s,t)).data}async delete(e,s){return(await this.client.delete(e,s)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let n=new i},48763:function(e,s,t){"use strict";t.d(s,{t:function(){return n}});var r=t(12574),a=t(65249);let i={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},n=(0,r.U)()((0,a.tJ)((e,s)=>({...i,setAuth:(s,t,r)=>{e({user:s,accessToken:t,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(i)},setLoading:s=>{e({isLoading:s})},updateUser:t=>{let r=s().user;r&&e({user:{...r,...t}})},hasPermission:e=>{let t=s().user;if(!t)return!1;let r=Array.isArray(e)?e:[e];return"admin"===t.role||(r.includes("editor")?["admin","editor"].includes(t.role):r.includes("moderator")?["admin","editor","moderator"].includes(t.role):r.includes(t.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,s,t){"use strict";t.d(s,{cn:function(){return i}});var r=t(75504),a=t(51367);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.m6)((0,r.W)(s))}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,199,5370,2971,8069,1744],function(){return e(e.s=76752)}),_N_E=e.O()}]);