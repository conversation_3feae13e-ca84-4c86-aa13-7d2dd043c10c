(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2578],{37602:function(e,s,t){Promise.resolve().then(t.bind(t,37821))},53879:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},10527:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},79580:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},70699:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},69724:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},11213:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},37821:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return Z}});var r=t(57437),a=t(2265),i=t(47907),n=t(82670),l=t(21270),c=t(60124),d=t(53879),o=t(70699),u=t(11213),m=t(10527),h=t(69724),x=t(575),p=t(15671),f=t(22782),j=t(12647),g=t(18641),y=t(86468),v=t(3549),b=t(95453),N=t(33277),w=t(14440),k=t(76862),C=t(93425),S=t(11546);let z=c.z.object({name:c.z.string().min(1,"Name is required"),email:c.z.string().email("Invalid email address"),company:c.z.string().optional(),phone:c.z.string().optional(),website:c.z.string().url("Invalid website URL").optional().or(c.z.literal("")),tier:c.z.enum(["free","premium","enterprise"],{required_error:"Tier is required"}),status:c.z.enum(["active","inactive","suspended"]),emailVerified:c.z.boolean(),apiCallsLimit:c.z.number().min(0,"API calls limit must be non-negative"),notes:c.z.string().optional()});function Z(){var e;let s=(0,i.useParams)(),t=(0,i.useRouter)(),{toast:c}=(0,k.p)(),{can:Z}=(0,S.TE)(),A=s.id,{data:L,isLoading:_,error:I}=C.Xn.useGetById(A),{mutate:E,isLoading:R}=C.Xn.useUpdate(),{mutate:V,isLoading:P}=C.Xn.useUpgradeTier(),{mutate:U,isLoading:F}=C.Xn.useDowngradeTier(),[T,D]=(0,a.useState)(!1),[M,B]=(0,a.useState)(""),Y=(0,n.cI)({resolver:(0,l.F)(z),defaultValues:{name:"",email:"",company:"",phone:"",website:"",tier:"free",status:"active",emailVerified:!1,apiCallsLimit:1e3,notes:""}});(0,a.useEffect)(()=>{L&&(B(L.tier||"free"),Y.reset({name:L.name||"",email:L.email||"",company:L.company||"",phone:L.phone||"",website:L.website||"",tier:L.tier||"free",status:L.status||"active",emailVerified:L.emailVerified||!1,apiCallsLimit:L.apiCallsLimit||1e3,notes:L.notes||""}))},[L,Y]),(0,a.useEffect)(()=>{let e=Y.watch(()=>{D(!0)});return()=>e.unsubscribe()},[Y]);let O=e=>{if(M!==e.tier){let s=q(e.tier)>q(M);(s?V:U)({userId:A,newTier:e.tier},{onSuccess:()=>{E({id:A,...e},{onSuccess:()=>{c({title:"User updated",description:"User has been successfully updated and tier ".concat(s?"upgraded":"downgraded"," to ").concat(e.tier,".")}),D(!1),t.push("/dashboard/users/registered/".concat(A))},onError:e=>{c({title:"Partial Success",description:"Tier was changed but other updates failed: ".concat((null==e?void 0:e.message)||"Unknown error"),variant:"destructive"})}})},onError:e=>{c({title:"Error",description:(null==e?void 0:e.message)||"Failed to change user tier.",variant:"destructive"})}})}else E({id:A,...e},{onSuccess:()=>{c({title:"User updated",description:"User has been successfully updated."}),D(!1),t.push("/dashboard/users/registered/".concat(A))},onError:e=>{c({title:"Error",description:(null==e?void 0:e.message)||"Failed to update user.",variant:"destructive"})}})},q=e=>{switch(e){case"free":return 1;case"premium":return 2;case"enterprise":return 3;default:return 0}},Q=e=>{switch(e){case"enterprise":return"default";case"premium":return"secondary";default:return"outline"}},X=()=>{T?confirm("You have unsaved changes. Are you sure you want to leave?")&&t.push("/dashboard/users/registered/".concat(A)):t.push("/dashboard/users/registered/".concat(A))};if(!Z("users:update"))return(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to edit users."}),(0,r.jsxs)(x.z,{onClick:()=>t.push("/dashboard/users/registered"),children:[(0,r.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});if(_)return(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})});if(I||!L)return(0,r.jsx)("div",{className:"container mx-auto p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,r.jsxs)(x.z,{onClick:()=>t.push("/dashboard/users/registered"),children:[(0,r.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});let H=L.tier?Math.round(L.apiCallsUsed/L.apiCallsLimit*100):0,W=(e=>{switch(e){case"free":return{apiCalls:1e3,features:["Basic API access","Email support"]};case"premium":return{apiCalls:1e4,features:["Enhanced API access","Priority support","Analytics"]};case"enterprise":return{apiCalls:1e5,features:["Full API access","24/7 support","Custom integration","SLA"]};default:return{apiCalls:1e3,features:[]}}})(Y.watch("tier"));return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(x.z,{variant:"ghost",size:"sm",onClick:X,children:[(0,r.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit User"}),(0,r.jsx)("p",{className:"text-gray-600",children:L.email})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.z,{variant:"outline",onClick:X,children:"Cancel"}),(0,r.jsxs)(x.z,{onClick:Y.handleSubmit(O),disabled:R||P||F||!T,children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 mr-2"}),R||P||F?"Saving...":"Save Changes"]})]})]}),(0,r.jsx)("form",{onSubmit:Y.handleSubmit(O),className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(p.Zb,{children:[(0,r.jsxs)(p.Ol,{children:[(0,r.jsxs)(p.ll,{className:"flex items-center",children:[(0,r.jsx)(u.Z,{className:"w-5 h-5 mr-2"}),"Basic Information"]}),(0,r.jsx)(p.SZ,{children:"Update the user's personal and contact information"})]}),(0,r.jsxs)(p.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"name",children:"Full Name *"}),(0,r.jsx)(f.I,{id:"name",...Y.register("name")}),Y.formState.errors.name&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:Y.formState.errors.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"email",children:"Email Address *"}),(0,r.jsx)(f.I,{id:"email",type:"email",...Y.register("email")}),Y.formState.errors.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:Y.formState.errors.email.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"company",children:"Company"}),(0,r.jsx)(f.I,{id:"company",...Y.register("company"),placeholder:"Company name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(f.I,{id:"phone",type:"tel",...Y.register("phone"),placeholder:"+****************"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"website",children:"Website"}),(0,r.jsx)(f.I,{id:"website",type:"url",...Y.register("website"),placeholder:"https://example.com"}),Y.formState.errors.website&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:Y.formState.errors.website.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"notes",children:"Notes"}),(0,r.jsx)(v.g,{id:"notes",...Y.register("notes"),placeholder:"Additional notes about this user...",rows:3})]})]})]}),(0,r.jsxs)(p.Zb,{children:[(0,r.jsxs)(p.Ol,{children:[(0,r.jsxs)(p.ll,{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Subscription & Tier"]}),(0,r.jsx)(p.SZ,{children:"Manage the user's subscription tier and API limits"})]}),(0,r.jsxs)(p.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"tier",children:"Subscription Tier *"}),(0,r.jsxs)(g.Ph,{value:Y.watch("tier"),onValueChange:e=>Y.setValue("tier",e),children:[(0,r.jsx)(g.i4,{children:(0,r.jsx)(g.ki,{placeholder:"Select a tier"})}),(0,r.jsxs)(g.Bw,{children:[(0,r.jsx)(g.Ql,{value:"free",children:"Free"}),(0,r.jsx)(g.Ql,{value:"premium",children:"Premium"}),(0,r.jsx)(g.Ql,{value:"enterprise",children:"Enterprise"})]})]}),Y.formState.errors.tier&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:Y.formState.errors.tier.message})]}),M!==Y.watch("tier")&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsx)(h.Z,{className:"w-4 h-4 text-yellow-500"}),(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:["Tier will be changed from"," ",(0,r.jsx)(N.C,{variant:Q(M),children:M})," ","to"," ",(0,r.jsx)(N.C,{variant:Q(Y.watch("tier")),children:Y.watch("tier")})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"apiCallsLimit",children:"API Calls Limit"}),(0,r.jsx)(f.I,{id:"apiCallsLimit",type:"number",min:"0",...Y.register("apiCallsLimit",{valueAsNumber:!0})}),Y.formState.errors.apiCallsLimit&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:Y.formState.errors.apiCallsLimit.message}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Recommended limit for ",Y.watch("tier"),": ",W.apiCalls.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(b.Z,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium",children:"Tier Features"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:(0,r.jsx)("ul",{className:"space-y-1",children:W.features.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})})]})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(p.Zb,{children:[(0,r.jsxs)(p.Ol,{children:[(0,r.jsx)(p.ll,{children:"Account Status"}),(0,r.jsx)(p.SZ,{children:"Manage the user's account status"})]}),(0,r.jsxs)(p.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j._,{htmlFor:"status",children:"Status"}),(0,r.jsxs)(g.Ph,{value:Y.watch("status"),onValueChange:e=>Y.setValue("status",e),children:[(0,r.jsx)(g.i4,{children:(0,r.jsx)(g.ki,{})}),(0,r.jsxs)(g.Bw,{children:[(0,r.jsx)(g.Ql,{value:"active",children:"Active"}),(0,r.jsx)(g.Ql,{value:"inactive",children:"Inactive"}),(0,r.jsx)(g.Ql,{value:"suspended",children:"Suspended"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(j._,{htmlFor:"emailVerified",className:"text-sm font-medium",children:"Email Verified"}),(0,r.jsx)(y.r,{id:"emailVerified",checked:Y.watch("emailVerified"),onCheckedChange:e=>Y.setValue("emailVerified",e)})]})]})]}),(0,r.jsxs)(p.Zb,{children:[(0,r.jsx)(p.Ol,{children:(0,r.jsx)(p.ll,{children:"Current Usage"})}),(0,r.jsxs)(p.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"API Calls"}),(0,r.jsxs)("span",{children:[L.apiCallsUsed," / ",L.apiCallsLimit]})]}),(0,r.jsx)(w.E,{value:H,className:"h-2"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[H,"% used"]})]}),(0,r.jsx)(b.Z,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Monthly Spend"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",(null===(e=L.monthlySpend)||void 0===e?void 0:e.toFixed(2))||"0.00"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last API Call"}),(0,r.jsx)("span",{className:"font-medium",children:L.lastApiCall?new Date(L.lastApiCall).toLocaleDateString():"Never"})]})]})]})]}),(0,r.jsxs)(p.Zb,{children:[(0,r.jsx)(p.Ol,{children:(0,r.jsx)(p.ll,{children:"Account Information"})}),(0,r.jsxs)(p.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"User ID"}),(0,r.jsx)("span",{className:"font-medium",children:L.id})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(L.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last Updated"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(L.updatedAt).toLocaleDateString()})]}),L.lastLogin&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Last Login"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(L.lastLogin).toLocaleDateString()})]})]})]})]})]})})]})}},33277:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(57437);t(2265);var a=t(49769),i=t(22169);let n=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...a})}},575:function(e,s,t){"use strict";t.d(s,{d:function(){return c},z:function(){return d}});var r=t(57437),a=t(2265),i=t(59143),n=t(49769),l=t(22169);let c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,u=d?i.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(c({variant:a,size:n,className:t})),ref:s,...o})});d.displayName="Button"},22782:function(e,s,t){"use strict";t.d(s,{I:function(){return n}});var r=t(57437),a=t(2265),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...n})});n.displayName="Input"},12647:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var r=t(57437),a=t(2265),i=t(24602),n=t(49769),l=t(22169);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.f,{ref:s,className:(0,l.cn)(c(),t),...a})});d.displayName=i.f.displayName},18641:function(e,s,t){"use strict";t.d(s,{Bw:function(){return p},Ph:function(){return o},Ql:function(){return f},i4:function(){return m},ki:function(){return u}});var r=t(57437),a=t(2265),i=t(18178),n=t(23441),l=t(85159),c=t(80037),d=t(22169);let o=i.fC;i.ZA;let u=i.B4,m=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsxs)(i.xz,{ref:s,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[a,(0,r.jsx)(i.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.xz.displayName;let h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.u_,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});h.displayName=i.u_.displayName;let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.$G,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});x.displayName=i.$G.displayName;let p=a.forwardRef((e,s)=>{let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(i.h_,{children:(0,r.jsxs)(i.VY,{ref:s,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(h,{}),(0,r.jsx)(i.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(x,{})]})})});p.displayName=i.VY.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.__,{ref:s,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t),...a})}).displayName=i.__.displayName;let f=a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,r.jsxs)(i.ck,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.wU,{children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(i.eT,{children:a})]})});f.displayName=i.ck.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.Z0,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...a})}).displayName=i.Z0.displayName},95453:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});var r=t(57437),a=t(2265),i=t(51014),n=t(22169);let l=a.forwardRef((e,s)=>{let{className:t,orientation:a="horizontal",decorative:l=!0,...c}=e;return(0,r.jsx)(i.f,{ref:s,decorative:l,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",t),...c})});l.displayName=i.f.displayName},86468:function(e,s,t){"use strict";t.d(s,{r:function(){return l}});var r=t(57437),a=t(2265),i=t(94845),n=t(22169);let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...a,ref:s,children:(0,r.jsx)(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=i.fC.displayName},3549:function(e,s,t){"use strict";t.d(s,{g:function(){return n}});var r=t(57437),a=t(2265),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...a})});n.displayName="Textarea"},76862:function(e,s,t){"use strict";t.d(s,{p:function(){return a}});var r=t(56288);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},11546:function(e,s,t){"use strict";t.d(s,{TE:function(){return d},a1:function(){return c}});var r=t(57437),a=t(2265),i=t(47907),n=t(27786),l=t(96146);let c=e=>{let{children:s,requiredRole:t,fallbackUrl:c="/auth/login"}=e,d=(0,i.useRouter)(),{isAuthenticated:o,user:u,isLoading:m}=(0,n.a)();if((0,a.useEffect)(()=>{if(!m){if(!o||!u){d.push(c);return}if(t&&!(Array.isArray(t)?t:[t]).includes(u.role)){d.push("/dashboard?error=unauthorized");return}}},[o,u,m,t,d,c]),m)return(0,r.jsx)(l.SX,{message:"Verifying authentication..."});if(!o||!u)return(0,r.jsx)(l.SX,{message:"Redirecting to login..."});if(t){let e=Array.isArray(t)?t:[t];if(!e.includes(u.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,r.jsx)(r.Fragment,{children:s})},d=()=>{let{user:e}=(0,n.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),t=()=>s("admin"),r=()=>s(["admin","editor"]),a=()=>s(["admin","editor","moderator"]),i=()=>t(),l=()=>r(),c=()=>a(),d=()=>t();return{user:e,hasRole:s,isAdmin:t,isEditor:r,isModerator:a,canManageUsers:i,canManageContent:l,canModerate:c,canSync:d,can:e=>{switch(e){case"manage-users":return i();case"manage-content":return l();case"moderate":return c();case"sync":return d();default:return!1}}}}},51014:function(e,s,t){"use strict";t.d(s,{f:function(){return d}});var r=t(2265),a=t(29586),i=t(57437),n="horizontal",l=["horizontal","vertical"],c=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=n,...c}=e,d=l.includes(r)?r:n;return(0,i.jsx)(a.WV.div,{"data-orientation":d,...t?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});c.displayName="Separator";var d=c},94845:function(e,s,t){"use strict";t.d(s,{bU:function(){return w},fC:function(){return N}});var r=t(2265),a=t(44991),i=t(61266),n=t(84104),l=t(9310),c=t(65030),d=t(76769),o=t(29586),u=t(57437),m="Switch",[h,x]=(0,n.b)(m),[p,f]=h(m),j=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:c,defaultChecked:d,required:h,disabled:x,value:f="on",onCheckedChange:j,form:g,...y}=e,[N,w]=r.useState(null),k=(0,i.e)(s,e=>w(e)),C=r.useRef(!1),S=!N||g||!!N.closest("form"),[z,Z]=(0,l.T)({prop:c,defaultProp:null!=d&&d,onChange:j,caller:m});return(0,u.jsxs)(p,{scope:t,checked:z,disabled:x,children:[(0,u.jsx)(o.WV.button,{type:"button",role:"switch","aria-checked":z,"aria-required":h,"data-state":b(z),"data-disabled":x?"":void 0,disabled:x,value:f,...y,ref:k,onClick:(0,a.M)(e.onClick,e=>{Z(e=>!e),S&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),S&&(0,u.jsx)(v,{control:N,bubbles:!C.current,name:n,value:f,checked:z,required:h,disabled:x,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=m;var g="SwitchThumb",y=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,a=f(g,t);return(0,u.jsx)(o.WV.span,{"data-state":b(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:s})});y.displayName=g;var v=r.forwardRef((e,s)=>{let{__scopeSwitch:t,control:a,checked:n,bubbles:l=!0,...o}=e,m=r.useRef(null),h=(0,i.e)(m,s),x=(0,c.D)(n),p=(0,d.t)(a);return r.useEffect(()=>{let e=m.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==n&&s){let t=new Event("click",{bubbles:l});s.call(e,n),e.dispatchEvent(t)}},[x,n,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:h,style:{...o.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var N=j,w=y}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,2810,6877,5370,2971,8069,1744],function(){return e(e.s=37602)}),_N_E=e.O()}]);