(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2336],{6246:function(e,s,a){Promise.resolve().then(a.bind(a,62666))},10527:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48219:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},37451:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},29733:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},69724:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},34059:function(e,s,a){"use strict";a.d(s,{Z:function(){return l}});let l=(0,a(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},62666:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return D}});var l=a(57437),t=a(2265),i=a(47907),c=a(53879),r=a(69724),n=a(77326),d=a(29295),x=a(50489),m=a(34059),h=a(90684),o=a(37451);let j=(0,a(57977).Z)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var u=a(97307),p=a(95032),N=a(29733),g=a(10527),v=a(48219),y=a(575),f=a(15671),b=a(33277),w=a(75808),Z=a(99497),k=a(14440),C=a(95453),S=a(91679),L=a(76862),A=a(93425),U=a(11546);function D(){var e,s,a,D,P,T;let E=(0,i.useParams)(),M=(0,i.useRouter)(),{toast:I}=(0,L.p)(),{canManageUsers:O}=(0,U.TE)(),z=E.id,{data:Y,isLoading:R,error:_}=A.Xn.useGetById(z),{mutate:q,isLoading:F}=A.Xn.useSuspend(),{mutate:X,isLoading:Q}=A.Xn.useReactivate(),{mutate:V,isLoading:W}=A.Xn.useDelete(),{data:$}=A.Xn.useUsageStats(z),{data:B=[]}=A.Xn.useApiCalls(z),[H,G]=(0,t.useState)(!1),[J,K]=(0,t.useState)(!1),ee=e=>{switch(e){case"enterprise":return"default";case"premium":return"secondary";default:return"outline"}};if(R)return(0,l.jsx)("div",{className:"container mx-auto p-6",children:(0,l.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,l.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,l.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,l.jsx)("div",{className:"h-48 bg-gray-200 rounded"})]})]})]})});if(_||!Y)return(0,l.jsx)("div",{className:"container mx-auto p-6",children:(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"User Not Found"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested user could not be found."}),(0,l.jsxs)(y.z,{onClick:()=>M.push("/dashboard/users/registered"),children:[(0,l.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"Back to Users"]})]})});let es=Y.tier?Math.round(Y.apiCallsUsed/Y.apiCallsLimit*100):0;return(0,l.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)(y.z,{variant:"ghost",size:"sm",onClick:()=>M.push("/dashboard/users/registered"),children:[(0,l.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:Y.name}),(0,l.jsx)("p",{className:"text-gray-600",children:Y.email})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[O()&&(0,l.jsxs)(l.Fragment,{children:["active"===Y.status?(0,l.jsxs)(S.aR,{open:J,onOpenChange:K,children:[(0,l.jsx)(S.vW,{asChild:!0,children:(0,l.jsxs)(y.z,{variant:"outline",children:[(0,l.jsx)(r.Z,{className:"w-4 h-4 mr-2"}),"Suspend"]})}),(0,l.jsxs)(S._T,{children:[(0,l.jsxs)(S.fY,{children:[(0,l.jsx)(S.f$,{children:"Suspend User"}),(0,l.jsx)(S.yT,{children:"This will suspend the user's access to the API. They will not be able to make API calls until reactivated."})]}),(0,l.jsxs)(S.xo,{children:[(0,l.jsx)(S.le,{children:"Cancel"}),(0,l.jsx)(S.OL,{onClick:()=>{q(z,{onSuccess:()=>{I({title:"User suspended",description:"User has been successfully suspended."}),K(!1)},onError:e=>{I({title:"Error",description:(null==e?void 0:e.message)||"Failed to suspend user.",variant:"destructive"})}})},disabled:F,children:F?"Suspending...":"Suspend User"})]})]})]}):"suspended"===Y.status?(0,l.jsxs)(y.z,{variant:"outline",onClick:()=>{X(z,{onSuccess:()=>{I({title:"User reactivated",description:"User has been successfully reactivated."})},onError:e=>{I({title:"Error",description:(null==e?void 0:e.message)||"Failed to reactivate user.",variant:"destructive"})}})},disabled:Q,children:[(0,l.jsx)(n.Z,{className:"w-4 h-4 mr-2"}),Q?"Reactivating...":"Reactivate"]}):null,(0,l.jsxs)(y.z,{variant:"outline",onClick:()=>M.push("/dashboard/users/registered/".concat(z,"/edit")),children:[(0,l.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Edit"]})]}),O()&&(0,l.jsxs)(S.aR,{open:H,onOpenChange:G,children:[(0,l.jsx)(S.vW,{asChild:!0,children:(0,l.jsxs)(y.z,{variant:"destructive",children:[(0,l.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})}),(0,l.jsxs)(S._T,{children:[(0,l.jsxs)(S.fY,{children:[(0,l.jsx)(S.f$,{children:"Are you sure?"}),(0,l.jsx)(S.yT,{children:"This action cannot be undone. This will permanently delete the user account and remove all associated data including API keys and usage history."})]}),(0,l.jsxs)(S.xo,{children:[(0,l.jsx)(S.le,{children:"Cancel"}),(0,l.jsx)(S.OL,{onClick:()=>{V(z,{onSuccess:()=>{I({title:"User deleted",description:"User has been successfully deleted."}),M.push("/dashboard/users/registered")},onError:e=>{I({title:"Error",description:(null==e?void 0:e.message)||"Failed to delete user.",variant:"destructive"})}})},className:"bg-red-600 hover:bg-red-700",disabled:W,children:W?"Deleting...":"Delete User"})]})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsxs)(Z.mQ,{defaultValue:"overview",className:"space-y-6",children:[(0,l.jsxs)(Z.dr,{children:[(0,l.jsx)(Z.SP,{value:"overview",children:"Overview"}),(0,l.jsx)(Z.SP,{value:"usage",children:"API Usage"}),(0,l.jsx)(Z.SP,{value:"subscription",children:"Subscription"}),(0,l.jsx)(Z.SP,{value:"activity",children:"Activity"})]}),(0,l.jsxs)(Z.nU,{value:"overview",className:"space-y-6",children:[(0,l.jsxs)(f.Zb,{children:[(0,l.jsx)(f.Ol,{children:(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Profile Information"]})}),(0,l.jsxs)(f.aY,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)(w.qE,{className:"w-16 h-16",children:[(0,l.jsx)(w.F$,{src:Y.avatar,alt:Y.name}),(0,l.jsx)(w.Q5,{children:Y.name.split(" ").map(e=>e.charAt(0)).join("")})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(b.C,{variant:ee(Y.tier),children:(null===(e=Y.tier)||void 0===e?void 0:e.charAt(0).toUpperCase())+(null===(s=Y.tier)||void 0===s?void 0:s.slice(1))}),(0,l.jsx)(b.C,{variant:(e=>{switch(e){case"active":return"default";case"inactive":return"secondary";case"suspended":return"destructive";default:return"outline"}})(Y.status),children:Y.status.charAt(0).toUpperCase()+Y.status.slice(1)}),Y.emailVerified&&(0,l.jsx)(b.C,{variant:"secondary",children:"Verified"})]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",Y.id]})]})]}),(0,l.jsx)(C.Z,{}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(h.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Email"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:Y.email})]}),Y.company&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Company"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:Y.company})]}),Y.phone&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(j,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Phone"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:Y.phone})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(u.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Last Login"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:Y.lastLogin?new Date(Y.lastLogin).toLocaleString():"Never"})]})]})]})]}),(0,l.jsxs)(f.Zb,{children:[(0,l.jsx)(f.Ol,{children:(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(p.Z,{className:"w-5 h-5 mr-2"}),"API Usage Overview"]})}),(0,l.jsxs)(f.aY,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:null===(a=Y.apiCallsUsed)||void 0===a?void 0:a.toLocaleString()}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Calls Used"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-2xl font-bold text-green-600",children:null===(D=Y.apiCallsLimit)||void 0===D?void 0:D.toLocaleString()}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Calls Limit"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[es,"%"]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Usage"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"API Calls"}),(0,l.jsxs)("span",{children:[Y.apiCallsUsed," / ",Y.apiCallsLimit]})]}),(0,l.jsx)(k.E,{value:es,variant:es>=90?"error":es>=75?"warning":"success",className:"h-2"})]}),es>=90&&(0,l.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,l.jsx)(r.Z,{className:"w-4 h-4 text-red-500"}),(0,l.jsx)("p",{className:"text-sm text-red-700",children:"User is approaching their API limit. Consider upgrading their tier."})]})]})]})]}),(0,l.jsxs)(Z.nU,{value:"usage",className:"space-y-6",children:[(0,l.jsxs)(f.Zb,{children:[(0,l.jsxs)(f.Ol,{children:[(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(N.Z,{className:"w-5 h-5 mr-2"}),"API Usage Statistics"]}),(0,l.jsx)(f.SZ,{children:"Detailed breakdown of API usage"})]}),(0,l.jsx)(f.aY,{children:$?(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-blue-600",children:$.today}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Today"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-green-600",children:$.thisWeek}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-yellow-600",children:$.thisMonth}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"This Month"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-purple-600",children:$.total}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total"})]})]})}):(0,l.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No usage statistics available"})})]}),(0,l.jsxs)(f.Zb,{children:[(0,l.jsxs)(f.Ol,{children:[(0,l.jsx)(f.ll,{children:"Recent API Calls"}),(0,l.jsx)(f.SZ,{children:"Latest API requests made by this user"})]}),(0,l.jsx)(f.aY,{children:(0,l.jsx)("div",{className:"space-y-3",children:B.length>0?B.slice(0,10).map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:e.endpoint}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(b.C,{variant:200===e.status?"default":"destructive",children:e.status}),(0,l.jsxs)("span",{className:"text-xs text-gray-500",children:[e.responseTime,"ms"]})]})]},e.id)):(0,l.jsx)("p",{className:"text-gray-500 italic text-center py-4",children:"No API calls recorded"})})})]})]}),(0,l.jsx)(Z.nU,{value:"subscription",className:"space-y-6",children:(0,l.jsxs)(f.Zb,{children:[(0,l.jsx)(f.Ol,{children:(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(g.Z,{className:"w-5 h-5 mr-2"}),"Subscription Details"]})}),(0,l.jsx)(f.aY,{className:"space-y-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(v.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Current Tier"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:(0,l.jsx)(b.C,{variant:ee(Y.tier),children:(null===(P=Y.tier)||void 0===P?void 0:P.charAt(0).toUpperCase())+(null===(T=Y.tier)||void 0===T?void 0:T.slice(1))})})]}),Y.subscriptionStartDate&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(u.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Subscription Start"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:new Date(Y.subscriptionStartDate).toLocaleDateString()})]}),Y.subscriptionEndDate&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(u.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Subscription End"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 ml-6",children:new Date(Y.subscriptionEndDate).toLocaleDateString()})]}),Y.monthlySpend&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(v.Z,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"Monthly Spend"})]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 ml-6",children:["$",Y.monthlySpend.toFixed(2)]})]})]})})]})}),(0,l.jsx)(Z.nU,{value:"activity",className:"space-y-6",children:(0,l.jsxs)(f.Zb,{children:[(0,l.jsxs)(f.Ol,{children:[(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(p.Z,{className:"w-5 h-5 mr-2"}),"Account Activity"]}),(0,l.jsx)(f.SZ,{children:"Recent account-related activities"})]}),(0,l.jsx)(f.aY,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Account created"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:new Date(Y.createdAt).toLocaleString()})]})]}),Y.lastLogin&&(0,l.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Last login"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:new Date(Y.lastLogin).toLocaleString()})]})]}),Y.emailVerified&&(0,l.jsxs)("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Email verified"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"Email address confirmed"})]})]})]})})]})})]})}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(f.Zb,{children:[(0,l.jsx)(f.Ol,{children:(0,l.jsxs)(f.ll,{className:"flex items-center",children:[(0,l.jsx)(N.Z,{className:"w-5 h-5 mr-2"}),"Quick Stats"]})}),(0,l.jsxs)(f.aY,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"API Calls Today"}),(0,l.jsx)("span",{className:"font-medium",children:(null==$?void 0:$.today)||0})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Days as Member"}),(0,l.jsx)("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(Y.createdAt).getTime())/864e5)})]}),Y.lastLogin&&(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Days Since Last Login"}),(0,l.jsx)("span",{className:"font-medium",children:Math.floor((Date.now()-new Date(Y.lastLogin).getTime())/864e5)})]})]})]}),O()&&(0,l.jsxs)(f.Zb,{children:[(0,l.jsx)(f.Ol,{children:(0,l.jsx)(f.ll,{children:"Quick Actions"})}),(0,l.jsxs)(f.aY,{className:"space-y-2",children:[(0,l.jsxs)(y.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>M.push("/dashboard/users/registered/".concat(z,"/edit")),children:[(0,l.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Edit Profile"]}),(0,l.jsxs)(y.z,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>{I({title:"Coming Soon",description:"Tier upgrade functionality will be implemented."})},children:[(0,l.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),"Upgrade Tier"]})]})]})]})]})]})}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,3411,6877,5370,8057,2971,8069,1744],function(){return e(e.s=6246)}),_N_E=e.O()}]);