(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5305],{91689:function(e,s,t){Promise.resolve().then(t.bind(t,53971))},48219:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29733:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},69724:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},53971:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return R}});var r=t(57437),a=t(2265),l=t(11546),i=t(93425),n=t(15671),d=t(33277),c=t(575),o=t(22782),u=t(18641),m=t(34645),x=t(91679),h=t(22632),f=t(75808),p=t(14440),j=t(29295),g=t(9230),v=t(29733),N=t(58106),y=t(85497),w=t(34059),b=t(95032),A=t(48219),k=t(69724),C=t(28670),Z=t(8792),U=t(96120);function R(){var e,s,t,R;let{canManageUsers:E}=(0,l.TE)(),[T,S]=(0,a.useState)({search:"",tier:void 0,isActive:void 0,isEmailVerified:void 0,page:1,limit:10}),{data:z,isLoading:D}=(0,i.Ow)(T),{data:L}=(0,i.ie)(),{data:O}=(0,i.zL)(85),{upgradeTier:P,downgradeTier:Y,suspendUser:_,reactivateUser:I}=(0,i.Il)(),[V,F]=(0,a.useState)({isOpen:!1,type:null,user:null}),Q=e=>{S(s=>({...s,search:e,page:1}))},M=e=>{switch(e){case"free":default:return"bg-gray-100 text-gray-800 border-gray-200";case"premium":return"bg-blue-100 text-blue-800 border-blue-200";case"enterprise":return"bg-purple-100 text-purple-800 border-purple-200"}},X=e=>e>=90?"error":e>=75?"warning":"success",q=(e,s,t)=>{F({isOpen:!0,type:e,user:s,newTier:t})},$=async()=>{if(V.user&&V.type)try{switch(V.type){case"suspend":await _.mutateAsync(V.user.id);break;case"reactivate":await I.mutateAsync(V.user.id);break;case"upgrade":V.newTier&&await P.mutateAsync({userId:V.user.id,newTier:V.newTier,subscriptionMonths:12});break;case"downgrade":V.newTier&&await Y.mutateAsync({userId:V.user.id,newTier:V.newTier})}}catch(e){console.error("Action failed:",e)}finally{F({isOpen:!1,type:null,user:null})}},B=[{key:"username",title:"User",render:(e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(f.qE,{className:"h-8 w-8",children:(0,r.jsx)(f.Q5,{className:"text-xs",children:s.username.slice(0,2).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:s.username}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["ID: ",s.id]})]})]})},{key:"email",title:"Email & Status",render:(e,s)=>(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"font-mono text-sm",children:s.email}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[s.isEmailVerified?(0,r.jsx)(d.C,{variant:"outline",className:"text-xs text-green-600 border-green-200",children:"Verified"}):(0,r.jsx)(d.C,{variant:"outline",className:"text-xs text-red-600 border-red-200",children:"Unverified"}),(0,r.jsx)(d.C,{variant:"outline",className:"text-xs ".concat(s.isActive?"text-green-600 border-green-200":"text-red-600 border-red-200"),children:s.isActive?"Active":"Suspended"})]})]})},{key:"tier",title:"Tier",render:e=>(0,r.jsx)(d.C,{className:"".concat(M(e)," capitalize"),children:e})},{key:"apiUsage",title:"API Usage",render:(e,s)=>{let t=s.apiCallsUsed||0,a=s.apiCallsLimit;if(!a)return(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium",children:[t.toLocaleString()," calls"]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:"Unlimited"})]});let l=Math.round(t/a*100);return(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{children:t.toLocaleString()}),(0,r.jsx)("span",{children:a.toLocaleString()})]}),(0,r.jsx)(p.E,{value:l,className:"h-2",variant:X(l)}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[l,"% used"]})]})}},{key:"subscription",title:"Subscription",render:(e,s)=>s.hasActiveSubscription?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)(d.C,{variant:"outline",className:"text-green-600 border-green-200 mb-1",children:"Active"}),s.subscriptionEndDate&&(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Expires ",(0,U.Q)(new Date(s.subscriptionEndDate),{addSuffix:!0})]})]}):(0,r.jsx)(d.C,{variant:"outline",className:"text-gray-600 border-gray-200",children:"No subscription"})},{key:"lastLoginAt",title:"Last Login",render:e=>e?(0,r.jsx)("div",{className:"text-sm",children:(0,U.Q)(new Date(e),{addSuffix:!0})}):(0,r.jsx)("span",{className:"text-muted-foreground text-sm",children:"Never"})},{key:"actions",title:"Actions",render:(e,s)=>E?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(Z.default,{href:"/dashboard/users/registered/".concat(s.id),children:(0,r.jsx)(j.Z,{className:"h-3 w-3"})})}),(0,r.jsxs)(m.h_,{children:[(0,r.jsx)(m.$F,{asChild:!0,children:(0,r.jsx)(c.z,{variant:"outline",size:"sm",children:(0,r.jsx)(g.Z,{className:"h-3 w-3"})})}),(0,r.jsxs)(m.AW,{align:"end",children:[(0,r.jsx)(m.Xi,{asChild:!0,children:(0,r.jsxs)(Z.default,{href:"/dashboard/users/registered/".concat(s.id),children:[(0,r.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"View Details"]})}),"free"===s.tier&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(m.Xi,{onClick:()=>q("upgrade",s,"premium"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Premium"]}),(0,r.jsxs)(m.Xi,{onClick:()=>q("upgrade",s,"enterprise"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Enterprise"]})]}),"premium"===s.tier&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(m.Xi,{onClick:()=>q("upgrade",s,"enterprise"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Upgrade to Enterprise"]}),(0,r.jsxs)(m.Xi,{onClick:()=>q("downgrade",s,"free"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Free"]})]}),"enterprise"===s.tier&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(m.Xi,{onClick:()=>q("downgrade",s,"premium"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Premium"]}),(0,r.jsxs)(m.Xi,{onClick:()=>q("downgrade",s,"free"),children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Downgrade to Free"]})]}),s.isActive?(0,r.jsxs)(m.Xi,{onClick:()=>q("suspend",s),className:"text-red-600",children:[(0,r.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Suspend User"]}):(0,r.jsxs)(m.Xi,{onClick:()=>q("reactivate",s),className:"text-green-600",children:[(0,r.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"Reactivate User"]})]})]})]}):(0,r.jsx)(c.z,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(Z.default,{href:"/dashboard/users/registered/".concat(s.id),children:"View"})})}];return E?(0,r.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Registered Users"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage API consumers and their subscriptions"})]})}),L&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Total Users"}),(0,r.jsx)(w.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:L.totalUsers.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[L.newUsersThisMonth," new this month"]})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Active Users"}),(0,r.jsx)(b.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:L.activeUsers.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(L.activeUsers/L.totalUsers*100),"% of total"]})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"API Calls"}),(0,r.jsx)(v.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:L.apiUsageStats.totalCallsThisMonth.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Avg ",L.apiUsageStats.averageCallsPerUser,"/user"]})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ll,{className:"text-sm font-medium",children:"Revenue"}),(0,r.jsx)(A.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",L.revenueMetrics.monthlyRecurringRevenue.toLocaleString()]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Monthly recurring"})]})]})]}),O&&O.length>0&&(0,r.jsxs)(n.Zb,{className:"border-yellow-200 bg-yellow-50",children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"flex items-center text-yellow-800",children:[(0,r.jsx)(k.Z,{className:"mr-2 h-5 w-5"}),"Users Approaching API Limits"]}),(0,r.jsxs)(n.SZ,{className:"text-yellow-700",children:[O.length," users are approaching their API usage limits and may need attention."]})]}),(0,r.jsx)(n.aY,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[O.slice(0,10).map(e=>(0,r.jsx)(d.C,{variant:"outline",className:"text-yellow-800 border-yellow-300",children:e.username},e.id)),O.length>10&&(0,r.jsxs)(d.C,{variant:"outline",className:"text-yellow-800 border-yellow-300",children:["+",O.length-10," more"]})]})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsx)(n.ll,{children:"Filters"})}),(0,r.jsx)(n.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Search"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(C.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(o.I,{placeholder:"Search users...",value:T.search,onChange:e=>Q(e.target.value),className:"pl-10"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Tier"}),(0,r.jsxs)(u.Ph,{value:T.tier||"all",onValueChange:e=>{S(s=>({...s,tier:"all"===e?void 0:e,page:1}))},children:[(0,r.jsx)(u.i4,{children:(0,r.jsx)(u.ki,{placeholder:"All tiers"})}),(0,r.jsxs)(u.Bw,{children:[(0,r.jsx)(u.Ql,{value:"all",children:"All tiers"}),(0,r.jsx)(u.Ql,{value:"free",children:"Free"}),(0,r.jsx)(u.Ql,{value:"premium",children:"Premium"}),(0,r.jsx)(u.Ql,{value:"enterprise",children:"Enterprise"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Status"}),(0,r.jsxs)(u.Ph,{value:void 0===T.isActive?"all":T.isActive?"active":"suspended",onValueChange:e=>{S(s=>({...s,isActive:"all"===e?void 0:"active"===e,page:1}))},children:[(0,r.jsx)(u.i4,{children:(0,r.jsx)(u.ki,{placeholder:"All statuses"})}),(0,r.jsxs)(u.Bw,{children:[(0,r.jsx)(u.Ql,{value:"all",children:"All statuses"}),(0,r.jsx)(u.Ql,{value:"active",children:"Active"}),(0,r.jsx)(u.Ql,{value:"suspended",children:"Suspended"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Email Status"}),(0,r.jsxs)(u.Ph,{value:void 0===T.isEmailVerified?"all":T.isEmailVerified?"verified":"unverified",onValueChange:e=>{S(s=>({...s,isEmailVerified:"all"===e?void 0:"verified"===e,page:1}))},children:[(0,r.jsx)(u.i4,{children:(0,r.jsx)(u.ki,{placeholder:"All statuses"})}),(0,r.jsxs)(u.Bw,{children:[(0,r.jsx)(u.Ql,{value:"all",children:"All statuses"}),(0,r.jsx)(u.Ql,{value:"verified",children:"Verified"}),(0,r.jsx)(u.Ql,{value:"unverified",children:"Unverified"})]})]})]})]})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{children:["Users (",(null==z?void 0:z.meta.totalItems)||0,")"]}),(0,r.jsx)(n.SZ,{children:"Manage registered users and their subscriptions"})]}),(0,r.jsx)(n.aY,{children:(0,r.jsx)(h.w,{columns:B,data:(null==z?void 0:z.data)||[],loading:D,pagination:{page:T.page,limit:T.limit,total:(null==z?void 0:z.meta.totalItems)||0,onPageChange:e=>S(s=>({...s,page:e})),onLimitChange:e=>S(s=>({...s,limit:e,page:1}))}})})]}),(0,r.jsx)(x.aR,{open:V.isOpen,onOpenChange:e=>!e&&F({isOpen:!1,type:null,user:null}),children:(0,r.jsxs)(x._T,{children:[(0,r.jsxs)(x.fY,{children:[(0,r.jsxs)(x.f$,{children:["suspend"===V.type&&"Suspend User","reactivate"===V.type&&"Reactivate User","upgrade"===V.type&&"Upgrade User Tier","downgrade"===V.type&&"Downgrade User Tier"]}),(0,r.jsxs)(x.yT,{children:["suspend"===V.type&&"Are you sure you want to suspend ".concat(null===(e=V.user)||void 0===e?void 0:e.username,"? This will prevent them from accessing the API."),"reactivate"===V.type&&"Are you sure you want to reactivate ".concat(null===(s=V.user)||void 0===s?void 0:s.username,"? They will regain access to the API."),"upgrade"===V.type&&"Are you sure you want to upgrade ".concat(null===(t=V.user)||void 0===t?void 0:t.username," to ").concat(V.newTier," tier? This will increase their API limits."),"downgrade"===V.type&&"Are you sure you want to downgrade ".concat(null===(R=V.user)||void 0===R?void 0:R.username," to ").concat(V.newTier," tier? This will reduce their API limits.")]})]}),(0,r.jsxs)(x.xo,{children:[(0,r.jsx)(x.le,{children:"Cancel"}),(0,r.jsx)(x.OL,{onClick:$,children:"Confirm"})]})]})})]}):(0,r.jsx)("div",{className:"container mx-auto py-6",children:(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(w.Z,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"You don't have permission to manage registered users."})]})})})})}},91679:function(e,s,t){"use strict";t.d(s,{OL:function(){return h},_T:function(){return c},aR:function(){return d},f$:function(){return u},fY:function(){return o},le:function(){return f},vW:function(){return p},xo:function(){return x},yT:function(){return m}});var r=t(57437),a=t(2265),l=t(54887),i=t(22169),n=t(575);let d=e=>{let{open:s,onOpenChange:t,children:i}=e;return(a.useEffect(()=>{let e=e=>{"Escape"===e.key&&s&&(null==t||t(!1))};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,t]),s)?(0,l.createPortal)((0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50",onClick:()=>null==t?void 0:t(!1)}),(0,r.jsx)("div",{className:"relative z-10",children:i})]}),document.body):null},c=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6",t),...l,children:a})});c.displayName="AlertDialogContent";let o=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left mb-4",t),...l,children:a})});o.displayName="AlertDialogHeader";let u=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)("h2",{ref:s,className:(0,i.cn)("text-lg font-semibold text-gray-900",t),...l,children:a})});u.displayName="AlertDialogTitle";let m=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-gray-600",t),...l,children:a})});m.displayName="AlertDialogDescription";let x=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",t),...l,children:a})});x.displayName="AlertDialogFooter";let h=a.forwardRef((e,s)=>{let{className:t,variant:a="default",children:l,...d}=e;return(0,r.jsx)(n.z,{ref:s,className:(0,i.cn)("destructive"===a&&"bg-red-600 hover:bg-red-700 text-white",t),...d,children:l})});h.displayName="AlertDialogAction";let f=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsx)(n.z,{ref:s,variant:"outline",className:(0,i.cn)("mt-2 sm:mt-0",t),...l,children:a})});f.displayName="AlertDialogCancel";let p=a.forwardRef((e,s)=>{let{className:t,children:l,asChild:i=!1,...n}=e;return i?a.cloneElement(l,{ref:s,...n}):(0,r.jsx)("button",{ref:s,className:t,...n,children:l})});p.displayName="AlertDialogTrigger"},75808:function(e,s,t){"use strict";t.d(s,{F$:function(){return d},Q5:function(){return c},qE:function(){return n}});var r=t(57437),a=t(2265),l=t(27733),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(l.fC,{ref:s,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...a})});n.displayName=l.fC.displayName;let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(l.Ee,{ref:s,className:(0,i.cn)("aspect-square h-full w-full",t),...a})});d.displayName=l.Ee.displayName;let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(l.NY,{ref:s,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...a})});c.displayName=l.NY.displayName},34645:function(e,s,t){"use strict";t.d(s,{$F:function(){return u},AW:function(){return m},Ju:function(){return h},VD:function(){return f},Xi:function(){return x},h_:function(){return o}});var r=t(57437),a=t(2265),l=t(81100),i=t(37805),n=t(80037),d=t(37501),c=t(22169);let o=l.fC,u=l.xz;l.ZA,l.Uv,l.Tr,l.Ee,a.forwardRef((e,s)=>{let{className:t,inset:a,children:n,...d}=e;return(0,r.jsxs)(l.fF,{ref:s,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",t),...d,children:[n,(0,r.jsx)(i.Z,{className:"ml-auto"})]})}).displayName=l.fF.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(l.tu,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...a})}).displayName=l.tu.displayName;let m=a.forwardRef((e,s)=>{let{className:t,sideOffset:a=4,...i}=e;return(0,r.jsx)(l.Uv,{children:(0,r.jsx)(l.VY,{ref:s,sideOffset:a,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...i})})});m.displayName=l.VY.displayName;let x=a.forwardRef((e,s)=>{let{className:t,inset:a,...i}=e;return(0,r.jsx)(l.ck,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",a&&"pl-8",t),...i})});x.displayName=l.ck.displayName,a.forwardRef((e,s)=>{let{className:t,children:a,checked:i,...d}=e;return(0,r.jsxs)(l.oC,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:i,...d,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.wU,{children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})}),a]})}).displayName=l.oC.displayName,a.forwardRef((e,s)=>{let{className:t,children:a,...i}=e;return(0,r.jsxs)(l.Rk,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.wU,{children:(0,r.jsx)(d.Z,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=l.Rk.displayName;let h=a.forwardRef((e,s)=>{let{className:t,inset:a,...i}=e;return(0,r.jsx)(l.__,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",t),...i})});h.displayName=l.__.displayName;let f=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(l.Z0,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...a})});f.displayName=l.Z0.displayName},11546:function(e,s,t){"use strict";t.d(s,{TE:function(){return c},a1:function(){return d}});var r=t(57437),a=t(2265),l=t(47907),i=t(27786),n=t(96146);let d=e=>{let{children:s,requiredRole:t,fallbackUrl:d="/auth/login"}=e,c=(0,l.useRouter)(),{isAuthenticated:o,user:u,isLoading:m}=(0,i.a)();if((0,a.useEffect)(()=>{if(!m){if(!o||!u){c.push(d);return}if(t&&!(Array.isArray(t)?t:[t]).includes(u.role)){c.push("/dashboard?error=unauthorized");return}}},[o,u,m,t,c,d]),m)return(0,r.jsx)(n.SX,{message:"Verifying authentication..."});if(!o||!u)return(0,r.jsx)(n.SX,{message:"Redirecting to login..."});if(t){let e=Array.isArray(t)?t:[t];if(!e.includes(u.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,r.jsx)(r.Fragment,{children:s})},c=()=>{let{user:e}=(0,i.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),t=()=>s("admin"),r=()=>s(["admin","editor"]),a=()=>s(["admin","editor","moderator"]),l=()=>t(),n=()=>r(),d=()=>a(),c=()=>t();return{user:e,hasRole:s,isAdmin:t,isEditor:r,isModerator:a,canManageUsers:l,canManageContent:n,canModerate:d,canSync:c,can:e=>{switch(e){case"manage-users":return l();case"manage-content":return n();case"moderate":return d();case"sync":return c();default:return!1}}}}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,8792,3411,2397,9918,1255,6877,1380,5370,2971,8069,1744],function(){return e(e.s=91689)}),_N_E=e.O()}]);