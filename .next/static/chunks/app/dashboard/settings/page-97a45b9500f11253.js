(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3455],{79800:function(e,t,r){Promise.resolve().then(r.bind(r,41284))},57977:function(e,t,r){"use strict";r.d(t,{Z:function(){return d}});var n=r(2265);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:u,...d},f)=>(0,n.createElement)("svg",{ref:f,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:s("lucide",l),...!i&&!o(d)&&{"aria-hidden":"true"},...d},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(i)?i:[i]])),d=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...l},o)=>(0,n.createElement)(u,{ref:o,iconNode:t,className:s(`lucide-${a(i(e))}`,`lucide-${e}`,r),...l}));return r.displayName=i(e),r}},53879:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29910:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},66260:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},34059:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41284:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return p}});var n=r(57437),a=r(15671),l=r(575),i=r(66260),s=r(34059),o=r(29910);let c=(0,r(57977).Z)("construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]]);var u=r(53879),d=r(8792);let f=e=>{let{title:t,description:r="This page is under development and will be available soon.",iconName:f="construction",backUrl:p="/dashboard",backLabel:h="Back to Dashboard"}=e,m=(e=>{switch(e){case"trophy":return i.Z;case"users":return s.Z;case"settings":return o.Z;default:return c}})(f);return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,n.jsxs)(a.Zb,{className:"w-full max-w-md text-center",children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20",children:(0,n.jsx)(m,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"})}),(0,n.jsx)(a.ll,{className:"text-xl",children:t}),(0,n.jsx)(a.SZ,{className:"text-base",children:r})]}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,n.jsx)("p",{children:"Features coming soon:"}),(0,n.jsxs)("ul",{className:"mt-2 space-y-1 text-left",children:[(0,n.jsx)("li",{children:"• Data management interface"}),(0,n.jsx)("li",{children:"• CRUD operations"}),(0,n.jsx)("li",{children:"• Advanced filtering"}),(0,n.jsx)("li",{children:"• Export functionality"})]})]}),(0,n.jsx)(l.z,{asChild:!0,className:"w-full",children:(0,n.jsxs)(d.default,{href:p,children:[(0,n.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),h]})})]})})]})})};function p(){return(0,n.jsx)(f,{title:"System Settings",description:"Configure application settings and preferences.",iconName:"settings"})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return c}});var n=r(57437),a=r(2265),l=r(59143),i=r(49769),s=r(22169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...u}=e,d=c?l.g7:"button";return(0,n.jsx)(d,{className:(0,s.cn)(o({variant:a,size:i,className:r})),ref:t,...u})});c.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return s},SZ:function(){return c},Zb:function(){return i},aY:function(){return u},ll:function(){return o}});var n=r(57437),a=r(2265),l=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...a})});i.displayName="Card";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});s.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return l}});var n=r(75504),a=r(51367);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}},61266:function(e,t,r){"use strict";r.d(t,{F:function(){return l},e:function(){return i}});var n=r(2265);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},59143:function(e,t,r){"use strict";r.d(t,{Z8:function(){return i},g7:function(){return s},sA:function(){return c}});var n=r(2265),a=r(61266),l=r(57437);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e,i;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let n in t){let a=e[n],l=t[n];/^on[A-Z]/.test(n)?a&&l?r[n]=(...e)=>{let t=l(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...l}:"className"===n&&(r[n]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,a.F)(t,s):s),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...i}=e,s=n.Children.toArray(a),o=s.find(u);if(o){let e=o.props.children,a=s.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,l.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var s=i("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},49769:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var n=r(75504);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.W,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let l=a(t)||a(n);return i[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...c}[t]):({...s,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[2150,8792,2971,8069,1744],function(){return e(e.s=79800)}),_N_E=e.O()}]);