(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4171],{22982:function(e,t,a){Promise.resolve().then(a.bind(a,99233))},99233:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return f}});var r=a(57437),n=a(2265),o=a(15671),s=a(575),i=a(33277),l=a(74921),c=a(90773),u=a(2975),d=a(47011);function f(){let[e,t]=(0,n.useState)([]),[a,f]=(0,n.useState)(!1),h=(e,a)=>{t(t=>t.find(t=>t.endpoint===e)?t.map(t=>t.endpoint===e?{...t,...a}:t):[...t,{endpoint:e,status:"pending",...a}])},g=async(e,t)=>{let a=Date.now();h(e,{status:"pending"});try{let r=await t(),n=Date.now()-a;h(e,{status:"success",data:r,duration:n})}catch(r){let t=Date.now()-a;h(e,{status:"error",error:r.message||"Unknown error",duration:t})}},p=async()=>{f(!0),t([]),await g("API Documentation",async()=>{var e,t;let a=await l.x.get("/api-docs-json");return{title:(null===(e=a.info)||void 0===e?void 0:e.title)||"APISportsGame API",version:(null===(t=a.info)||void 0===t?void 0:t.version)||"1.0.0",endpoints:Object.keys(a.paths||{}).length}}),await g("Public Fixtures",async()=>{var e,t,a,r,n,o,s;let i=await u.L.getUpcomingAndLive({limit:3});return{totalFixtures:(null===(e=i.data)||void 0===e?void 0:e.length)||0,liveMatches:(null===(t=i.data)||void 0===t?void 0:t.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0,upcomingMatches:(null===(a=i.data)||void 0===a?void 0:a.filter(e=>"NS"===e.status).length)||0,sampleFixture:(null===(n=i.data)||void 0===n?void 0:null===(r=n[0])||void 0===r?void 0:r.homeTeamName)+" vs "+(null===(s=i.data)||void 0===s?void 0:null===(o=s[0])||void 0===o?void 0:o.awayTeamName)||"No fixtures"}}),await g("Public Leagues",async()=>{var e,t,a,r;let n=await d.A.getLeagues({limit:3});return{totalLeagues:(null===(e=n.meta)||void 0===e?void 0:e.totalItems)||0,currentPage:(null===(t=n.meta)||void 0===t?void 0:t.currentPage)||1,sampleLeague:(null===(r=n.data)||void 0===r?void 0:null===(a=r[0])||void 0===a?void 0:a.name)||"No leagues"}}),await g("System Auth Login",async()=>{let e=await c.i.login({username:"admin",password:"admin123456"});return{username:e.user.username,role:e.user.role,email:e.user.email,tokenLength:e.accessToken.length}}),f(!1)},m=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"success":return"✅";case"error":return"❌";case"pending":return"⏳";default:return"⚪"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"API Connection Test"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Test connection to APISportsGame API endpoints"})]}),(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[(0,r.jsx)(o.ll,{children:"API Configuration"}),(0,r.jsx)(o.SZ,{children:"Current API settings and connection details"})]}),(0,r.jsx)(o.aY,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Base URL:"}),(0,r.jsx)("span",{className:"text-gray-600",children:"http://localhost:3000"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Timeout:"}),(0,r.jsx)("span",{className:"text-gray-600",children:"30 seconds"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"font-medium",children:"Auth Token:"}),(0,r.jsx)("span",{className:"text-gray-600",children:localStorage.getItem("accessToken")?"✅ Present":"❌ Not found"})]})]})})]}),(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[(0,r.jsx)(o.ll,{children:"Test Results"}),(0,r.jsx)(o.SZ,{children:"API endpoint connectivity tests"})]}),(0,r.jsx)(o.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(s.z,{onClick:p,disabled:a,className:"w-full",children:a?"Running Tests...":"Run All Tests"}),e.length>0&&(0,r.jsx)("div",{className:"space-y-3",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-lg",children:y(e.status)}),(0,r.jsx)("span",{className:"font-medium",children:e.endpoint})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.C,{className:m(e.status),children:e.status}),e.duration&&(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration,"ms"]})]})]}),e.error&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded p-2 mt-2",children:[(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:"Error:"}),(0,r.jsx)("p",{className:"text-red-700 text-sm",children:e.error})]}),e.data&&"success"===e.status&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded p-2 mt-2",children:[(0,r.jsx)("p",{className:"text-green-800 text-sm font-medium",children:"Response:"}),(0,r.jsxs)("pre",{className:"text-green-700 text-xs mt-1 overflow-x-auto",children:[JSON.stringify(e.data,null,2).substring(0,200),JSON.stringify(e.data,null,2).length>200?"...":""]})]})]},t))})]})})]})]})}},33277:function(e,t,a){"use strict";a.d(t,{C:function(){return i}});var r=a(57437);a(2265);var n=a(49769),o=a(22169);let s=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...n}=e;return(0,r.jsx)("div",{className:(0,o.cn)(s({variant:a}),t),...n})}},575:function(e,t,a){"use strict";a.d(t,{d:function(){return l},z:function(){return c}});var r=a(57437),n=a(2265),o=a(59143),s=a(49769),i=a(22169);let l=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:a,variant:n,size:s,asChild:c=!1,...u}=e,d=c?o.g7:"button";return(0,r.jsx)(d,{className:(0,i.cn)(l({variant:n,size:s,className:a})),ref:t,...u})});c.displayName="Button"},15671:function(e,t,a){"use strict";a.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return s},aY:function(){return u},ll:function(){return l}});var r=a(57437),n=a(2265),o=a(22169);let s=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...n})});s.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",a),...n})});i.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",a),...n})});l.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...n})});c.displayName="CardDescription";let u=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",a),...n})});u.displayName="CardContent",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"},90773:function(e,t,a){"use strict";a.d(t,{i:function(){return o}});var r=a(74921),n=a(48763);let o={login:async e=>{console.log("\uD83D\uDD10 Attempting login via proxy...");try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Login failed")}let a=await t.json();console.log("✅ Login successful via proxy");let r=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.accessToken)}});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to fetch profile")}return{user:await r.json(),accessToken:a.accessToken,refreshToken:a.refreshToken}}catch(t){if(console.error("❌ Login failed via proxy:",t.message),(t.message.includes("fetch")||t.message.includes("network"))&&(console.warn("⚠️ Network error, using mock data"),"admin"===e.username&&"admin123456"===e.password)){let e={user:{id:1,username:"admin",email:"<EMAIL>",fullName:"System Administrator",role:"admin",isActive:!0,lastLoginAt:new Date().toISOString(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now()};return await new Promise(e=>setTimeout(e,500)),e}throw t}},logout:async e=>{let t=await fetch("/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Logout failed");return await t.json()},logoutFromAllDevices:async()=>await r.x.post("/system-auth/logout-all"),refreshToken:async e=>{let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Token refresh failed");return await t.json()},getProfile:async()=>{let e=n.t.getState(),t=e.accessToken,a=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}});if(!a.ok){if(401===a.status)throw console.warn("⚠️ Token expired, forcing logout..."),e.clearAuth(),window.location.href="/auth/login",Error("Token expired, please login again");throw Error((await a.json()).message||"Failed to fetch profile")}return await a.json()},updateProfile:async e=>await r.x.put("/system-auth/profile",e),changePassword:async e=>await r.x.post("/system-auth/change-password",e),createUser:async e=>await r.x.post("/system-auth/users",e),updateUser:async(e,t)=>await r.x.put("/system-auth/users/".concat(e),t)}},74921:function(e,t,a){"use strict";a.d(t,{x:function(){return s}});var r=a(73107),n=a(48763);class o{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!a._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(a.headers.Authorization="Bearer ".concat(e),this.client(a))).catch(e=>Promise.reject(e));a._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),a.headers.Authorization="Bearer ".concat(t),this.client(a);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=n.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let a=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!a.ok)throw Error("Token refresh failed");let{accessToken:r}=await a.json(),n=e.user;if(n)return e.setAuth(n,r,t),this.setAuthToken(r),console.log("✅ Token refreshed successfully"),r}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(a=>{let{resolve:r,reject:n}=a;e?n(e):r(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let s=new o},2975:function(e,t,a){"use strict";a.d(t,{L:function(){return n}});var r=a(74921);let n={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,r]=e;void 0!==r&&a.append(t,r.toString())}),await r.x.get("/football/fixtures/schedules/".concat(e,"?").concat(a.toString()))},getFixtureStatistics:async e=>await r.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Season sync - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Season sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Season sync - No token found!"),e})();console.log("\uD83D\uDD04 Season sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"season"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Season sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger season sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Daily sync - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Daily sync - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Daily sync - No token found!"),e})();console.log("\uD83D\uDD04 Daily sync request via proxy");let t=await fetch("/api/fixtures/sync",{method:"POST",headers:e,body:JSON.stringify({type:"daily"})});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",t.status,t.statusText,e),Error(e.message||"Failed to trigger daily sync: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let e=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Sync status - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Sync status - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Sync status - No token found!"),e})();console.log("\uD83D\uDD04 Sync status request via proxy");let t=await fetch("/api/fixtures/sync",{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw console.error("❌ Sync status failed:",t.status,t.statusText,e),Error(e.message||"Failed to get sync status: ".concat(t.statusText))}let a=await t.json();return console.log("✅ Sync status successful"),a},createFixture:async e=>{var t;let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Create fixture - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Create fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Create fixture - No token found!"),e})();console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!a.Authorization,data:e});let r=await fetch("/api/fixtures",{method:"POST",headers:a,body:JSON.stringify(e)});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",r.status,r.statusText,e),Error(e.message||"Failed to create fixture: ".concat(r.statusText))}let n=await r.json();return console.log("✅ Create fixture successful:",null===(t=n.data)||void 0===t?void 0:t.id),n.data||n},updateFixture:async(e,t)=>{let a=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Update fixture - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Update fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Update fixture - No token found!"),e})();console.log("\uD83D\uDD04 Update fixture request:",{externalId:e,hasAuth:!!a.Authorization,data:t});let r=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:a,body:JSON.stringify(t)});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",r.status,r.statusText,e),Error(e.message||"Failed to update fixture: ".concat(r.statusText))}let n=await r.json();return console.log("✅ Update fixture successful:",e),n.data||n},deleteFixture:async e=>{let t=(()=>{let e={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var t;let r=JSON.parse(a),n=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(n)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",n.substring(0,20)+"..."),e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),e.Authorization="Bearer ".concat(a),e}return console.warn("❌ Delete fixture - No token found!"),e})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",e)},getFixtureStatistics:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/statistics"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture statistics: ".concat(t.statusText));return await t.json()},getFixtureEvents:async e=>{let t=await fetch("/api/fixtures/".concat(e,"/events"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture events: ".concat(t.statusText));return await t.json()},getFixture:async e=>(await n.getFixtureById(e)).data}},47011:function(e,t,a){"use strict";a.d(t,{A:function(){return o}});var r=a(74921);let n=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},o={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?"".concat(e,"-").concat(t):e.toString(),r=await fetch("/api/leagues/".concat(a),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch league ".concat(e));return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let r=n(),s={"Content-Type":"application/json"};r&&(s.Authorization="Bearer ".concat(r));let i=await o.getLeagueById(e,a);if(!i||!i.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));let l=await fetch("/api/leagues/".concat(i.id),{method:"PATCH",headers:s,body:JSON.stringify(t)});if(!l.ok)throw Error((await l.json()).message||"Failed to update league ".concat(e));return await l.json()},deleteLeague:async(e,t)=>{let a=await o.getLeagueById(e,t);if(!a||!a.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await r.x.delete("/football/leagues/".concat(a.id))},getActiveLeagues:async()=>o.getLeagues({active:!0}),getLeaguesByCountry:async e=>o.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>o.updateLeague(e,{active:t},a)}},48763:function(e,t,a){"use strict";a.d(t,{t:function(){return s}});var r=a(12574),n=a(65249);let o={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},s=(0,r.U)()((0,n.tJ)((e,t)=>({...o,setAuth:(t,a,r)=>{e({user:t,accessToken:a,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(o)},setLoading:t=>{e({isLoading:t})},updateUser:a=>{let r=t().user;r&&e({user:{...r,...a}})},hasPermission:e=>{let a=t().user;if(!a)return!1;let r=Array.isArray(e)?e:[e];return"admin"===a.role||(r.includes("editor")?["admin","editor"].includes(a.role):r.includes("moderator")?["admin","editor","moderator"].includes(a.role):r.includes(a.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,a){"use strict";a.d(t,{cn:function(){return o}});var r=a(75504),n=a(51367);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,r.W)(t))}},61266:function(e,t,a){"use strict";a.d(t,{F:function(){return o},e:function(){return s}});var r=a(2265);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let a=!1,r=e.map(e=>{let r=n(e,t);return a||"function"!=typeof r||(a=!0),r});if(a)return()=>{for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof a?a():n(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},59143:function(e,t,a){"use strict";a.d(t,{Z8:function(){return s},g7:function(){return i},sA:function(){return c}});var r=a(2265),n=a(61266),o=a(57437);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...o}=e;if(r.isValidElement(a)){let e,s;let i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.ref:(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.props.ref:a.props.ref||a.ref,l=function(e,t){let a={...t};for(let r in t){let n=e[r],o=t[r];/^on[A-Z]/.test(r)?n&&o?a[r]=(...e)=>{let t=o(...e);return n(...e),t}:n&&(a[r]=n):"style"===r?a[r]={...n,...o}:"className"===r&&(a[r]=[n,o].filter(Boolean).join(" "))}return{...e,...a}}(o,a.props);return a.type!==r.Fragment&&(l.ref=t?(0,n.F)(t,i):i),r.cloneElement(a,l)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=r.forwardRef((e,a)=>{let{children:n,...s}=e,i=r.Children.toArray(n),l=i.find(u);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:a,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,o.jsx)(t,{...s,ref:a,children:n})});return a.displayName=`${e}.Slot`,a}var i=s("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},49769:function(e,t,a){"use strict";a.d(t,{j:function(){return s}});var r=a(75504);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,s=(e,t)=>a=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:s,defaultVariants:i}=t,l=Object.keys(s).map(e=>{let t=null==a?void 0:a[e],r=null==i?void 0:i[e];if(null===t)return null;let o=n(t)||n(r);return s[e][o]}),c=a&&Object.entries(a).reduce((e,t)=>{let[a,r]=t;return void 0===r||(e[a]=r),e},{});return o(e,l,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:a,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...i,...c}[t]):({...i,...c})[t]===a})?[...e,a,r]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}},function(e){e.O(0,[2150,8939,2971,8069,1744],function(){return e(e.s=22982)}),_N_E=e.O()}]);