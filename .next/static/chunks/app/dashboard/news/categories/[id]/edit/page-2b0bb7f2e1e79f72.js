(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7141],{41188:function(e,t,r){Promise.resolve().then(r.bind(r,64941))},64941:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w}});var s=r(57437),a=r(2265),i=r(47907),n=r(82670),o=r(21270),c=r(60124),l=r(53879),d=r(3665),u=r(37841),m=r(70699),h=r(575),g=r(15671),f=r(22782),p=r(12647),x=r(3549),y=r(86468),v=r(95453),b=r(76862),j=r(91552);let N=c.z.object({name:c.z.string().min(1,"Category name is required").max(100,"Name must be less than 100 characters"),slug:c.z.string().min(1,"Slug is required").max(100,"Slug must be less than 100 characters"),description:c.z.string().optional(),icon:c.z.string().optional(),color:c.z.string().optional(),sortOrder:c.z.number().min(0,"Sort order must be 0 or greater").optional(),isActive:c.z.boolean(),isPublic:c.z.boolean(),metaTitle:c.z.string().max(200,"Meta title must be less than 200 characters").optional(),metaDescription:c.z.string().max(500,"Meta description must be less than 500 characters").optional()});function w(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{toast:r}=(0,b.p)(),c=Number(e.id),{data:w,isLoading:S,error:C}=(0,j.b5)(c),{mutate:k,isLoading:T}=(0,j.Ny)(),[A,D]=(0,a.useState)(!1),z=(0,n.cI)({resolver:(0,o.F)(N),defaultValues:{name:"",slug:"",description:"",icon:"",color:"",sortOrder:0,isActive:!0,isPublic:!0,metaTitle:"",metaDescription:""}});(0,a.useEffect)(()=>{w&&z.reset({name:w.name||"",slug:w.slug||"",description:w.description||"",icon:w.icon||"",color:w.color||"",sortOrder:w.sortOrder||0,isActive:w.isActive,isPublic:w.isPublic,metaTitle:w.metaTitle||"",metaDescription:w.metaDescription||""})},[w,z]),(0,a.useEffect)(()=>{let e=z.watch(()=>{D(!0)});return()=>e.unsubscribe()},[z]),z.watch("name");let P=()=>{(!A||window.confirm("You have unsaved changes. Are you sure you want to leave?"))&&t.push("/dashboard/news/categories/".concat(c))};return S?(0,s.jsxs)("div",{className:"container mx-auto py-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)(h.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"h-8 w-48 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2"})]})]}),(0,s.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:(0,s.jsxs)(g.Zb,{children:[(0,s.jsxs)(g.Ol,{children:[(0,s.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-48 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)(g.aY,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-4 w-full bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-4 w-1/2 bg-gray-200 rounded animate-pulse"})]})]})})]}):C||!w?(0,s.jsxs)("div",{className:"container mx-auto py-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)(h.z,{variant:"ghost",size:"sm",onClick:P,className:"h-8 w-8 p-0",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Category Not Found"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"The requested category could not be found."})]})]}),(0,s.jsx)(g.Zb,{children:(0,s.jsx)(g.aY,{className:"py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(d.Z,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Category Not Found"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"The category you're looking for doesn't exist or may have been deleted."}),(0,s.jsx)(h.z,{onClick:()=>t.push("/dashboard/news/categories"),children:"Go Back to Categories"})]})})})]}):(0,s.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(h.z,{variant:"ghost",size:"sm",onClick:P,className:"h-8 w-8 p-0",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Edit Category"}),(0,s.jsxs)("p",{className:"text-muted-foreground",children:['Update details for "',w.name,'"']})]})]})}),(0,s.jsxs)("form",{onSubmit:z.handleSubmit(e=>{var s,a,i,n,o;let{slug:l,...d}=e;k({id:c,data:{...d,icon:(null===(s=d.icon)||void 0===s?void 0:s.trim())||void 0,color:(null===(a=d.color)||void 0===a?void 0:a.trim())||void 0,metaTitle:(null===(i=d.metaTitle)||void 0===i?void 0:i.trim())||void 0,metaDescription:(null===(n=d.metaDescription)||void 0===n?void 0:n.trim())||void 0,description:(null===(o=d.description)||void 0===o?void 0:o.trim())||void 0}},{onSuccess:()=>{r({title:"Category updated",description:"News category has been successfully updated."}),D(!1),t.push("/dashboard/news/categories/".concat(c))},onError:e=>{r({title:"Error",description:(null==e?void 0:e.message)||"Failed to update category.",variant:"destructive"})}})}),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsxs)(g.Zb,{children:[(0,s.jsxs)(g.Ol,{children:[(0,s.jsxs)(g.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(d.Z,{className:"h-5 w-5"}),"Basic Information"]}),(0,s.jsx)(g.SZ,{children:"Update the basic details for the news category"})]}),(0,s.jsxs)(g.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"name",children:"Category Name *"}),(0,s.jsx)(f.I,{id:"name",placeholder:"e.g., Sports News, Technology, Politics",...z.register("name"),className:z.formState.errors.name?"border-red-500":""}),z.formState.errors.name&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:z.formState.errors.name.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"slug",children:"URL Slug (Read-only)"}),(0,s.jsx)(f.I,{id:"slug",placeholder:"e.g., sports-news, technology, politics",...z.register("slug"),className:"bg-gray-50 text-gray-600",readOnly:!0,disabled:!0}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"URL slug cannot be changed after creation to maintain link integrity"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"description",children:"Description"}),(0,s.jsx)(x.g,{id:"description",placeholder:"Brief description of this category...",rows:4,...z.register("description")}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Optional description to help users understand this category"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"icon",children:"Icon"}),(0,s.jsx)(f.I,{id:"icon",placeholder:"e.g., sports, news, tech",...z.register("icon")}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Icon identifier for this category"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"color",children:"Color"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(f.I,{id:"color",type:"color",className:"w-16 h-10 p-1 border rounded",...z.register("color")}),(0,s.jsx)(f.I,{placeholder:"#FF0000",className:"flex-1",...z.register("color")})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Theme color for this category"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"sortOrder",children:"Sort Order"}),(0,s.jsx)(f.I,{id:"sortOrder",type:"number",min:"0",placeholder:"0",...z.register("sortOrder",{valueAsNumber:!0}),className:z.formState.errors.sortOrder?"border-red-500":""}),z.formState.errors.sortOrder&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:z.formState.errors.sortOrder.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Display order (lower numbers appear first)"})]})]})]}),(0,s.jsxs)(g.Zb,{children:[(0,s.jsxs)(g.Ol,{children:[(0,s.jsxs)(g.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(u.Z,{className:"h-5 w-5"}),"Category Settings"]}),(0,s.jsx)(g.SZ,{children:"Configure visibility and status settings"})]}),(0,s.jsxs)(g.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(p._,{htmlFor:"isActive",children:"Active Status"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable this category for use in news articles"})]}),(0,s.jsx)(y.r,{id:"isActive",checked:z.watch("isActive"),onCheckedChange:e=>z.setValue("isActive",e)})]}),(0,s.jsx)(v.Z,{}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(p._,{htmlFor:"isPublic",children:"Public Visibility"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Make this category visible to public users"})]}),(0,s.jsx)(y.r,{id:"isPublic",checked:z.watch("isPublic"),onCheckedChange:e=>z.setValue("isPublic",e)})]}),(0,s.jsx)(v.Z,{}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"metaTitle",children:"Meta Title (SEO)"}),(0,s.jsx)(f.I,{id:"metaTitle",placeholder:"SEO-optimized title for search engines",...z.register("metaTitle"),className:z.formState.errors.metaTitle?"border-red-500":""}),z.formState.errors.metaTitle&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:z.formState.errors.metaTitle.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Title tag for search engines (max 200 characters)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"metaDescription",children:"Meta Description (SEO)"}),(0,s.jsx)(x.g,{id:"metaDescription",placeholder:"Brief description for search engine results",rows:3,...z.register("metaDescription"),className:z.formState.errors.metaDescription?"border-red-500":""}),z.formState.errors.metaDescription&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:z.formState.errors.metaDescription.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Description for search engine results (max 500 characters)"})]}),(0,s.jsx)(v.Z,{}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium",children:"Preview"}),(0,s.jsxs)("div",{className:"rounded-lg border p-3 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 rounded border",style:{backgroundColor:z.watch("color")||"#6B7280"}}),(0,s.jsx)(d.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:z.watch("name")||"Category Name"}),z.watch("icon")&&(0,s.jsx)("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:z.watch("icon")}),!z.watch("isActive")&&(0,s.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:"Inactive"}),!z.watch("isPublic")&&(0,s.jsx)("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded",children:"Private"})]}),z.watch("slug")&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["URL: /news/category/",z.watch("slug")]}),z.watch("description")&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:z.watch("description")}),void 0!==z.watch("sortOrder")&&0!==z.watch("sortOrder")&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Sort Order: ",z.watch("sortOrder")]}),z.watch("metaTitle")&&(0,s.jsxs)("div",{className:"text-xs",children:[(0,s.jsx)("span",{className:"font-medium",children:"SEO Title:"})," ",z.watch("metaTitle")]})]})]}),A&&(0,s.jsx)("div",{className:"text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200",children:"⚠️ You have unsaved changes"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[(0,s.jsx)(h.z,{type:"button",variant:"outline",onClick:P,disabled:T,children:"Cancel"}),(0,s.jsxs)(h.z,{type:"submit",disabled:T||!z.formState.isValid,className:"flex items-center gap-2",children:[(0,s.jsx)(m.Z,{className:"h-4 w-4"}),T?"Saving...":"Save Changes"]})]})]})]})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return c},z:function(){return l}});var s=r(57437),a=r(2265),i=r(59143),n=r(49769),o=r(22169);let c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:l=!1,...d}=e,u=l?i.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(c({variant:a,size:n,className:r})),ref:t,...d})});l.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return l},Zb:function(){return n},aY:function(){return d},ll:function(){return c}});var s=r(57437),a=r(2265),i=r(22169);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...a})});n.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});l.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},22782:function(e,t,r){"use strict";r.d(t,{I:function(){return n}});var s=r(57437),a=r(2265),i=r(22169);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"},12647:function(e,t,r){"use strict";r.d(t,{_:function(){return l}});var s=r(57437),a=r(2265),i=r(24602),n=r(49769),o=r(22169);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.f,{ref:t,className:(0,o.cn)(c(),r),...a})});l.displayName=i.f.displayName},95453:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var s=r(57437),a=r(2265),i=r(51014),n=r(22169);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...c}=e;return(0,s.jsx)(i.f,{ref:t,decorative:o,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...c})});o.displayName=i.f.displayName},86468:function(e,t,r){"use strict";r.d(t,{r:function(){return o}});var s=r(57437),a=r(2265),i=r(94845),n=r(22169);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,s.jsx)(i.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});o.displayName=i.fC.displayName},3549:function(e,t,r){"use strict";r.d(t,{g:function(){return n}});var s=r(57437),a=r(2265),i=r(22169);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...a})});n.displayName="Textarea"},76862:function(e,t,r){"use strict";r.d(t,{p:function(){return a}});var s=r(56288);let a=()=>({toast:e=>{"destructive"===e.variant?s.toast.error(e.title||e.description||"Error occurred"):s.toast.success(e.title||e.description||"Success")}})},74921:function(e,t,r){"use strict";r.d(t,{x:function(){return n}});var s=r(73107),a=r(48763);class i{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:s}=await r.json(),a=e.user;if(a)return e.setAuth(a,s,t),this.setAuthToken(s),console.log("✅ Token refreshed successfully"),s}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:s,reject:a}=r;e?a(e):s(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let n=new i},91552:function(e,t,r){"use strict";r.d(t,{L_:function(){return u},b5:function(){return m},HL:function(){return g},P9:function(){return p},dk:function(){return h},fu:function(){return x},Ny:function(){return f}});var s=r(31346),a=r(64095),i=r(8186),n=r(74921);class o{async getCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let r=t.toString(),s=r?"".concat(this.baseUrl,"?").concat(r):this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",s);let a=await n.x.get(s);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,r=e.limit||20,s=a.length,i=(t-1)*r;return{data:a.slice(i,i+r),meta:{currentPage:t,totalPages:Math.ceil(s/r),totalItems:s,limit:r}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await n.x.get("".concat(this.baseUrl,"/").concat(e));return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return n.x.post(this.baseUrl,e)}async updateCategory(e,t){return n.x.patch("".concat(this.baseUrl,"/").concat(e),t)}async deleteCategory(e){return n.x.delete("".concat(this.baseUrl,"/").concat(e))}async toggleCategoryStatus(e,t){return n.x.patch("".concat(this.baseUrl,"/").concat(e),{isActive:t})}async reorderCategories(e){return n.x.post("".concat(this.baseUrl,"/reorder"),{categoryIds:e})}async getCategoryStats(){return n.x.get("".concat(this.baseUrl,"/stats"))}constructor(){this.baseUrl="/api/news/categories"}}let c=new o;var l=r(56288);let d={all:["categories"],lists:()=>[...d.all,"list"],list:e=>[...d.lists(),e],details:()=>[...d.all,"detail"],detail:e=>[...d.details(),e],public:()=>[...d.all,"public"]};function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,s.a)({queryKey:d.list(e),queryFn:()=>c.getCategories(e),staleTime:3e5})}function m(e){return(0,s.a)({queryKey:d.detail(e),queryFn:()=>c.getCategoryById(e),enabled:!!e,staleTime:6e5})}function h(){return(0,s.a)({queryKey:d.public(),queryFn:()=>c.getPublicCategories(),staleTime:9e5})}function g(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),l.toast.success('Category "'.concat(t.name,'" created successfully'))},onError:e=>{l.toast.error("Failed to create category: "+e.message)}})}function f(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>{let{id:t,data:r}=e;return c.updateCategory(t,r)},onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success('Category "'.concat(t.name,'" updated successfully'))},onError:e=>{l.toast.error("Failed to update category: "+e.message)}})}function p(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.deleteCategory(e),onSuccess:(t,r)=>{e.removeQueries({queryKey:d.detail(r)}),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()}),l.toast.success("Category deleted successfully")},onError:e=>{l.toast.error("Failed to delete category: "+e.message)}})}function x(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>{let{id:t,isActive:r}=e;return c.toggleCategoryStatus(t,r)},onSuccess:t=>{e.setQueryData(d.detail(t.id),t),e.invalidateQueries({queryKey:d.lists()}),e.invalidateQueries({queryKey:d.public()});let r=t.isActive?"activated":"deactivated";l.toast.success('Category "'.concat(t.name,'" ').concat(r," successfully"))},onError:e=>{l.toast.error("Failed to toggle category status: "+e.message)}})}},48763:function(e,t,r){"use strict";r.d(t,{t:function(){return n}});var s=r(12574),a=r(65249);let i={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},n=(0,s.U)()((0,a.tJ)((e,t)=>({...i,setAuth:(t,r,s)=>{e({user:t,accessToken:r,refreshToken:s,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(i)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let s=t().user;s&&e({user:{...s,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let s=Array.isArray(e)?e:[e];return"admin"===r.role||(s.includes("editor")?["admin","editor"].includes(r.role):s.includes("moderator")?["admin","editor","moderator"].includes(r.role):s.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return i}});var s=r(75504),a=r(51367);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}}},function(e){e.O(0,[2150,9101,8939,1346,2341,2810,6607,2971,8069,1744],function(){return e(e.s=41188)}),_N_E=e.O()}]);