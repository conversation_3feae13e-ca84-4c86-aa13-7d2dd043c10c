(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[567],{81831:function(e,t,s){Promise.resolve().then(s.bind(s,39334))},53879:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},97307:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51930:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},37841:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},69475:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},3665:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},47907:function(e,t,s){"use strict";var r=s(15313);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},39334:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return N}});var r=s(57437),a=s(47907),n=s(53879),i=s(3665),o=s(37841),l=s(51930),c=s(29295),u=s(50489),d=s(97307),h=s(69475),m=s(575),x=s(15671),f=s(33277),g=s(95453),p=s(76862),y=s(91552),v=s(4133),j=s(2265);let b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});function N(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),{toast:s}=(0,p.p)(),N=e.id,{data:C,isLoading:k,error:S}=(0,y.b5)(Number(N)),{mutate:A,isLoading:R}=(0,y.P9)(),[Z,T]=(0,j.useState)(!1),z=()=>{t.push("/dashboard/news/categories")};return k?(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)(m.z,{variant:"ghost",size:"sm",onClick:z,className:"h-8 w-8 p-0",children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 w-48 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2"})]})]}),(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:(0,r.jsxs)(x.Zb,{children:[(0,r.jsxs)(x.Ol,{children:[(0,r.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 w-48 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)(x.aY,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-4 w-full bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 w-1/2 bg-gray-200 rounded animate-pulse"})]})]})})]}):S||!C?(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)(m.z,{variant:"ghost",size:"sm",onClick:z,className:"h-8 w-8 p-0",children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Category Not Found"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"The requested category could not be found."})]})]}),(0,r.jsx)(x.Zb,{children:(0,r.jsx)(x.aY,{className:"py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.Z,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Category Not Found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"The category you're looking for doesn't exist or may have been deleted."}),(0,r.jsx)(m.z,{onClick:z,children:"Go Back to Categories"})]})})})]}):(0,r.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(m.z,{variant:"ghost",size:"sm",onClick:z,className:"h-8 w-8 p-0",children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:C.name}),(0,r.jsx)(f.C,{variant:C.isActive?"default":"secondary",children:C.isActive?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.Z,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.Z,{className:"h-3 w-3 mr-1"}),"Inactive"]})})]}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Category details and management"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(m.z,{variant:"outline",onClick:()=>{t.push("/dashboard/news/categories/".concat(N,"/edit"))},children:[(0,r.jsx)(c.Z,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,r.jsxs)(m.z,{variant:"destructive",onClick:()=>T(!0),disabled:R,children:[(0,r.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(x.Zb,{children:[(0,r.jsxs)(x.Ol,{children:[(0,r.jsxs)(x.ll,{className:"flex items-center gap-2",children:[(0,r.jsx)(i.Z,{className:"h-5 w-5"}),"Basic Information"]}),(0,r.jsx)(x.SZ,{children:"Category details and settings"})]}),(0,r.jsx)(x.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Name"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:C.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Slug"}),(0,r.jsx)("p",{className:"text-sm font-mono bg-gray-50 px-2 py-1 rounded",children:C.slug})]}),C.description&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Description"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:C.description})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Status"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:C.isActive?(0,r.jsxs)(f.C,{variant:"default",className:"text-xs",children:[(0,r.jsx)(o.Z,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,r.jsxs)(f.C,{variant:"secondary",className:"text-xs",children:[(0,r.jsx)(l.Z,{className:"h-3 w-3 mr-1"}),"Inactive"]})})]})]})})]}),(0,r.jsxs)(x.Zb,{children:[(0,r.jsxs)(x.Ol,{children:[(0,r.jsxs)(x.ll,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.Z,{className:"h-5 w-5"}),"Meta Information"]}),(0,r.jsx)(x.SZ,{children:"Creation and modification details"})]}),(0,r.jsx)(x.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"ID"}),(0,r.jsxs)("p",{className:"text-sm font-mono",children:["#",C.id]})]}),C.createdAt&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Created"}),(0,r.jsx)("p",{className:"text-sm",children:b(C.createdAt)})]}),C.updatedAt&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"Last Modified"}),(0,r.jsx)("p",{className:"text-sm",children:b(C.updatedAt)})]}),(0,r.jsx)(g.Z,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium text-gray-600",children:"URL Preview"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground font-mono bg-gray-50 px-2 py-1 rounded",children:["/news/category/",C.slug]})]})]})})]}),(0,r.jsxs)(x.Zb,{className:"md:col-span-2",children:[(0,r.jsxs)(x.Ol,{children:[(0,r.jsxs)(x.ll,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.Z,{className:"h-5 w-5"}),"Usage Statistics"]}),(0,r.jsx)(x.SZ,{children:"Articles and usage information for this category"})]}),(0,r.jsx)(x.aY,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:C.articleCount||0}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Articles"})]}),(0,r.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:C.isActive?"Yes":"No"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Available for New Articles"})]}),(0,r.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:C.slug.length}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Slug Characters"})]})]})})]})]}),(0,r.jsx)(v.sm,{isOpen:Z,onClose:()=>T(!1),onConfirm:()=>{A(Number(N),{onSuccess:()=>{s({title:"Category deleted",description:"News category has been successfully deleted."}),t.push("/dashboard/news/categories")},onError:e=>{s({title:"Error",description:(null==e?void 0:e.message)||"Failed to delete category.",variant:"destructive"})}})},title:"Delete Category",message:'Are you sure you want to delete "'.concat(C.name,'"? This action cannot be undone.'),confirmText:"Delete",variant:"destructive",loading:R})]})}function w(e){let{children:t,className:s}=e;return(0,r.jsx)("label",{className:s,children:t})}},33277:function(e,t,s){"use strict";s.d(t,{C:function(){return o}});var r=s(57437);s(2265);var a=s(49769),n=s(22169);let i=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},575:function(e,t,s){"use strict";s.d(t,{d:function(){return l},z:function(){return c}});var r=s(57437),a=s(2265),n=s(59143),i=s(49769),o=s(22169);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:c=!1,...u}=e,d=c?n.g7:"button";return(0,r.jsx)(d,{className:(0,o.cn)(l({variant:a,size:i,className:s})),ref:t,...u})});c.displayName="Button"},15671:function(e,t,s){"use strict";s.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return u},ll:function(){return l}});var r=s(57437),a=s(2265),n=s(22169);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",s),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},4133:function(e,t,s){"use strict";s.d(t,{sm:function(){return h},uB:function(){return m},u_:function(){return d}});var r=s(57437),a=s(2265),n=s(15669),i=s(691),o=s(52235),l=s(575),c=s(22169);let u={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},d=e=>{let{isOpen:t,onClose:s,title:d,description:h,children:m,size:x="md",showCloseButton:f=!0,closeOnOverlayClick:g=!0,className:p}=e;return(0,r.jsx)(n.u,{appear:!0,show:t,as:a.Fragment,children:(0,r.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:g?s:()=>{},children:[(0,r.jsx)(n.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(n.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(i.Vq.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",u[x],p),children:[(d||f)&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[d&&(0,r.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),h&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:h})]}),f&&(0,r.jsx)(l.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"mt-2",children:m})]})})})})]})})},h=e=>{let{isOpen:t,onClose:s,onConfirm:a,title:n="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:o="Confirm",cancelText:c="Cancel",variant:u="default",loading:h=!1}=e;return(0,r.jsx)(d,{isOpen:t,onClose:s,title:n,size:"sm",closeOnOverlayClick:!h,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,r.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,r.jsx)(l.z,{variant:"outline",onClick:s,disabled:h,children:c}),(0,r.jsx)(l.z,{variant:"destructive"===u?"destructive":"default",onClick:a,disabled:h,children:h?"Processing...":o})]})]})})},m=e=>{let{isOpen:t,onClose:s,title:a,description:n,children:i,onSubmit:o,submitText:c="Save",cancelText:u="Cancel",loading:h=!1,size:m="md"}=e;return(0,r.jsx)(d,{isOpen:t,onClose:s,title:a,description:n,size:m,closeOnOverlayClick:!h,children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==o||o()},className:"space-y-4",children:[i,(0,r.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,r.jsx)(l.z,{type:"button",variant:"outline",onClick:s,disabled:h,children:u}),o&&(0,r.jsx)(l.z,{type:"submit",disabled:h,children:h?"Saving...":c})]})]})})}},95453:function(e,t,s){"use strict";s.d(t,{Z:function(){return o}});var r=s(57437),a=s(2265),n=s(51014),i=s(22169);let o=a.forwardRef((e,t)=>{let{className:s,orientation:a="horizontal",decorative:o=!0,...l}=e;return(0,r.jsx)(n.f,{ref:t,decorative:o,orientation:a,className:(0,i.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",s),...l})});o.displayName=n.f.displayName},76862:function(e,t,s){"use strict";s.d(t,{p:function(){return a}});var r=s(56288);let a=()=>({toast:e=>{"destructive"===e.variant?r.toast.error(e.title||e.description||"Error occurred"):r.toast.success(e.title||e.description||"Success")}})},74921:function(e,t,s){"use strict";s.d(t,{x:function(){return i}});var r=s(73107),a=s(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!s._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(s.headers.Authorization="Bearer ".concat(e),this.client(s))).catch(e=>Promise.reject(e));s._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),s.headers.Authorization="Bearer ".concat(t),this.client(s);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let s=JSON.parse(t);return(null===(e=s.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let s=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!s.ok)throw Error("Token refresh failed");let{accessToken:r}=await s.json(),a=e.user;if(a)return e.setAuth(a,r,t),this.setAuthToken(r),console.log("✅ Token refreshed successfully"),r}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(s=>{let{resolve:r,reject:a}=s;e?a(e):r(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let i=new n},91552:function(e,t,s){"use strict";s.d(t,{L_:function(){return d},b5:function(){return h},HL:function(){return x},P9:function(){return g},dk:function(){return m},fu:function(){return p},Ny:function(){return f}});var r=s(31346),a=s(64095),n=s(8186),i=s(74921);class o{async getCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=t.toString(),r=s?"".concat(this.baseUrl,"?").concat(s):this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let a=await i.x.get(r);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,s=e.limit||20,r=a.length,n=(t-1)*s;return{data:a.slice(n,n+s),meta:{currentPage:t,totalPages:Math.ceil(r/s),totalItems:r,limit:s}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await i.x.get("".concat(this.baseUrl,"/").concat(e));return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return i.x.post(this.baseUrl,e)}async updateCategory(e,t){return i.x.patch("".concat(this.baseUrl,"/").concat(e),t)}async deleteCategory(e){return i.x.delete("".concat(this.baseUrl,"/").concat(e))}async toggleCategoryStatus(e,t){return i.x.patch("".concat(this.baseUrl,"/").concat(e),{isActive:t})}async reorderCategories(e){return i.x.post("".concat(this.baseUrl,"/reorder"),{categoryIds:e})}async getCategoryStats(){return i.x.get("".concat(this.baseUrl,"/stats"))}constructor(){this.baseUrl="/api/news/categories"}}let l=new o;var c=s(56288);let u={all:["categories"],lists:()=>[...u.all,"list"],list:e=>[...u.lists(),e],details:()=>[...u.all,"detail"],detail:e=>[...u.details(),e],public:()=>[...u.all,"public"]};function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:u.list(e),queryFn:()=>l.getCategories(e),staleTime:3e5})}function h(e){return(0,r.a)({queryKey:u.detail(e),queryFn:()=>l.getCategoryById(e),enabled:!!e,staleTime:6e5})}function m(){return(0,r.a)({queryKey:u.public(),queryFn:()=>l.getPublicCategories(),staleTime:9e5})}function x(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>l.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:u.all}),c.toast.success('Category "'.concat(t.name,'" created successfully'))},onError:e=>{c.toast.error("Failed to create category: "+e.message)}})}function f(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,data:s}=e;return l.updateCategory(t,s)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),c.toast.success('Category "'.concat(t.name,'" updated successfully'))},onError:e=>{c.toast.error("Failed to update category: "+e.message)}})}function g(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>l.deleteCategory(e),onSuccess:(t,s)=>{e.removeQueries({queryKey:u.detail(s)}),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),c.toast.success("Category deleted successfully")},onError:e=>{c.toast.error("Failed to delete category: "+e.message)}})}function p(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,isActive:s}=e;return l.toggleCategoryStatus(t,s)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()});let s=t.isActive?"activated":"deactivated";c.toast.success('Category "'.concat(t.name,'" ').concat(s," successfully"))},onError:e=>{c.toast.error("Failed to toggle category status: "+e.message)}})}},48763:function(e,t,s){"use strict";s.d(t,{t:function(){return i}});var r=s(12574),a=s(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},i=(0,r.U)()((0,a.tJ)((e,t)=>({...n,setAuth:(t,s,r)=>{e({user:t,accessToken:s,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:s=>{let r=t().user;r&&e({user:{...r,...s}})},hasPermission:e=>{let s=t().user;if(!s)return!1;let r=Array.isArray(e)?e:[e];return"admin"===s.role||(r.includes("editor")?["admin","editor"].includes(s.role):r.includes("moderator")?["admin","editor","moderator"].includes(s.role):r.includes(s.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(75504),a=s(51367);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},29586:function(e,t,s){"use strict";s.d(t,{WV:function(){return o},jH:function(){return l}});var r=s(2265),a=s(54887),n=s(59143),i=s(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.Z8)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e,o=a?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},51014:function(e,t,s){"use strict";s.d(t,{f:function(){return c}});var r=s(2265),a=s(29586),n=s(57437),i="horizontal",o=["horizontal","vertical"],l=r.forwardRef((e,t)=>{let{decorative:s,orientation:r=i,...l}=e,c=o.includes(r)?r:i;return(0,n.jsx)(a.WV.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var c=l},8186:function(e,t,s){"use strict";s.d(t,{D:function(){return h}});var r=s(2265),a=s(31678),n=s(34654),i=s(79522),o=s(6761);class l extends o.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let s=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,n.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){i.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,s,r,a,n,i,o,l;e.onSuccess?(null==(t=(s=this.mutateOptions).onSuccess)||t.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(a=this.mutateOptions).onSettled)||r.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(n=(i=this.mutateOptions).onError)||n.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(l=this.mutateOptions).onSettled)||o.call(l,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var c=s(97536),u=s(64095),d=s(3439);function h(e,t,s){let n=(0,a.lV)(e,t,s),o=(0,u.NL)({context:n.context}),[h]=r.useState(()=>new l(o,n));r.useEffect(()=>{h.setOptions(n)},[h,n]);let x=(0,c.$)(r.useCallback(e=>h.subscribe(i.V.batchCalls(e)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),f=r.useCallback((e,t)=>{h.mutate(e,t).catch(m)},[h]);if(x.error&&(0,d.L)(h.options.useErrorBoundary,[x.error]))throw x.error;return{...x,mutate:f,mutateAsync:x.mutate}}function m(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,1953,2971,8069,1744],function(){return e(e.s=81831)}),_N_E=e.O()}]);