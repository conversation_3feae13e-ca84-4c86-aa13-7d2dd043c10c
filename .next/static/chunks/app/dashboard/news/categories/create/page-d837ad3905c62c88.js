(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9726],{35738:function(e,t,r){Promise.resolve().then(r.bind(r,70082))},70082:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return j}});var s=r(57437),a=r(2265),n=r(47907),i=r(82670),o=r(21270),c=r(60124),l=r(53879),u=r(3665),d=r(37841),h=r(70699),g=r(575),f=r(15671),m=r(22782),p=r(12647),y=r(3549),x=r(86468),v=r(95453),b=r(76862),w=r(91552);let N=c.z.object({name:c.z.string().min(1,"Category name is required").max(100,"Name must be less than 100 characters"),slug:c.z.string().min(1,"Slug is required").max(100,"Slug must be less than 100 characters"),description:c.z.string().optional(),isActive:c.z.boolean()});function j(){let e=(0,n.useRouter)(),{toast:t}=(0,b.p)(),{mutate:r,isLoading:c}=(0,w.HL)(),j=(0,i.cI)({resolver:(0,o.F)(N),defaultValues:{name:"",slug:"",description:"",isActive:!0}}),C=j.watch("name"),k=e=>e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"");(0,a.useState)(()=>{if(C){let e=j.getValues("slug"),t=k(C);e&&e!==k(j.getValues("name"))||j.setValue("slug",t)}});let S=()=>{e.push("/dashboard/news/categories")};return(0,s.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(g.z,{variant:"ghost",size:"sm",onClick:S,className:"h-8 w-8 p-0",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Create News Category"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Add a new category for organizing news articles"})]})]})}),(0,s.jsxs)("form",{onSubmit:j.handleSubmit(s=>{var a;r({...s,description:(null===(a=s.description)||void 0===a?void 0:a.trim())||void 0},{onSuccess:()=>{t({title:"Category created",description:"News category has been successfully created."}),e.push("/dashboard/news/categories")},onError:e=>{t({title:"Error",description:(null==e?void 0:e.message)||"Failed to create category.",variant:"destructive"})}})}),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsxs)(f.Zb,{children:[(0,s.jsxs)(f.Ol,{children:[(0,s.jsxs)(f.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(u.Z,{className:"h-5 w-5"}),"Basic Information"]}),(0,s.jsx)(f.SZ,{children:"Enter the basic details for the news category"})]}),(0,s.jsxs)(f.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"name",children:"Category Name *"}),(0,s.jsx)(m.I,{id:"name",placeholder:"e.g., Sports News, Technology, Politics",...j.register("name"),className:j.formState.errors.name?"border-red-500":""}),j.formState.errors.name&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.formState.errors.name.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"slug",children:"URL Slug *"}),(0,s.jsx)(m.I,{id:"slug",placeholder:"e.g., sports-news, technology, politics",...j.register("slug"),className:j.formState.errors.slug?"border-red-500":""}),j.formState.errors.slug&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.formState.errors.slug.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"URL-friendly version of the category name. Will be auto-generated from name."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p._,{htmlFor:"description",children:"Description"}),(0,s.jsx)(y.g,{id:"description",placeholder:"Brief description of this category...",rows:4,...j.register("description")}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Optional description to help users understand this category"})]})]})]}),(0,s.jsxs)(f.Zb,{children:[(0,s.jsxs)(f.Ol,{children:[(0,s.jsxs)(f.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(d.Z,{className:"h-5 w-5"}),"Category Settings"]}),(0,s.jsx)(f.SZ,{children:"Configure visibility and status settings"})]}),(0,s.jsxs)(f.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(p._,{htmlFor:"isActive",children:"Active Status"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable this category for use in news articles"})]}),(0,s.jsx)(x.r,{id:"isActive",checked:j.watch("isActive"),onCheckedChange:e=>j.setValue("isActive",e)})]}),(0,s.jsx)(v.Z,{}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium",children:"Preview"}),(0,s.jsxs)("div",{className:"rounded-lg border p-3 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:j.watch("name")||"Category Name"}),!j.watch("isActive")&&(0,s.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:"Inactive"})]}),j.watch("slug")&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["URL: /news/category/",j.watch("slug")]}),j.watch("description")&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:j.watch("description")})]})]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[(0,s.jsx)(g.z,{type:"button",variant:"outline",onClick:S,disabled:c,children:"Cancel"}),(0,s.jsxs)(g.z,{type:"submit",disabled:c||!j.formState.isValid,className:"flex items-center gap-2",children:[(0,s.jsx)(h.Z,{className:"h-4 w-4"}),c?"Creating...":"Create Category"]})]})]})]})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return c},z:function(){return l}});var s=r(57437),a=r(2265),n=r(59143),i=r(49769),o=r(22169);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:l=!1,...u}=e,d=l?n.g7:"button";return(0,s.jsx)(d,{className:(0,o.cn)(c({variant:a,size:i,className:r})),ref:t,...u})});l.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return l},Zb:function(){return i},aY:function(){return u},ll:function(){return c}});var s=r(57437),a=r(2265),n=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});l.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},22782:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...i})});i.displayName="Input"},12647:function(e,t,r){"use strict";r.d(t,{_:function(){return l}});var s=r(57437),a=r(2265),n=r(24602),i=r(49769),o=r(22169);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,o.cn)(c(),r),...a})});l.displayName=n.f.displayName},95453:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var s=r(57437),a=r(2265),n=r(51014),i=r(22169);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...c}=e;return(0,s.jsx)(n.f,{ref:t,decorative:o,orientation:a,className:(0,i.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...c})});o.displayName=n.f.displayName},86468:function(e,t,r){"use strict";r.d(t,{r:function(){return o}});var s=r(57437),a=r(2265),n=r(94845),i=r(22169);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,s.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});o.displayName=n.fC.displayName},3549:function(e,t,r){"use strict";r.d(t,{g:function(){return i}});var s=r(57437),a=r(2265),n=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...a})});i.displayName="Textarea"},76862:function(e,t,r){"use strict";r.d(t,{p:function(){return a}});var s=r(56288);let a=()=>({toast:e=>{"destructive"===e.variant?s.toast.error(e.title||e.description||"Error occurred"):s.toast.success(e.title||e.description||"Success")}})},74921:function(e,t,r){"use strict";r.d(t,{x:function(){return i}});var s=r(73107),a=r(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:s}=await r.json(),a=e.user;if(a)return e.setAuth(a,s,t),this.setAuthToken(s),console.log("✅ Token refreshed successfully"),s}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:s,reject:a}=r;e?a(e):s(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let i=new n},91552:function(e,t,r){"use strict";r.d(t,{L_:function(){return d},b5:function(){return h},HL:function(){return f},P9:function(){return p},dk:function(){return g},fu:function(){return y},Ny:function(){return m}});var s=r(31346),a=r(64095),n=r(8186),i=r(74921);class o{async getCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let r=t.toString(),s=r?"".concat(this.baseUrl,"?").concat(r):this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",s);let a=await i.x.get(s);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,r=e.limit||20,s=a.length,n=(t-1)*r;return{data:a.slice(n,n+r),meta:{currentPage:t,totalPages:Math.ceil(s/r),totalItems:s,limit:r}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await i.x.get("".concat(this.baseUrl,"/").concat(e));return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return i.x.post(this.baseUrl,e)}async updateCategory(e,t){return i.x.patch("".concat(this.baseUrl,"/").concat(e),t)}async deleteCategory(e){return i.x.delete("".concat(this.baseUrl,"/").concat(e))}async toggleCategoryStatus(e,t){return i.x.patch("".concat(this.baseUrl,"/").concat(e),{isActive:t})}async reorderCategories(e){return i.x.post("".concat(this.baseUrl,"/reorder"),{categoryIds:e})}async getCategoryStats(){return i.x.get("".concat(this.baseUrl,"/stats"))}constructor(){this.baseUrl="/api/news/categories"}}let c=new o;var l=r(56288);let u={all:["categories"],lists:()=>[...u.all,"list"],list:e=>[...u.lists(),e],details:()=>[...u.all,"detail"],detail:e=>[...u.details(),e],public:()=>[...u.all,"public"]};function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,s.a)({queryKey:u.list(e),queryFn:()=>c.getCategories(e),staleTime:3e5})}function h(e){return(0,s.a)({queryKey:u.detail(e),queryFn:()=>c.getCategoryById(e),enabled:!!e,staleTime:6e5})}function g(){return(0,s.a)({queryKey:u.public(),queryFn:()=>c.getPublicCategories(),staleTime:9e5})}function f(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>c.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:u.all}),l.toast.success('Category "'.concat(t.name,'" created successfully'))},onError:e=>{l.toast.error("Failed to create category: "+e.message)}})}function m(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,data:r}=e;return c.updateCategory(t,r)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),l.toast.success('Category "'.concat(t.name,'" updated successfully'))},onError:e=>{l.toast.error("Failed to update category: "+e.message)}})}function p(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>c.deleteCategory(e),onSuccess:(t,r)=>{e.removeQueries({queryKey:u.detail(r)}),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),l.toast.success("Category deleted successfully")},onError:e=>{l.toast.error("Failed to delete category: "+e.message)}})}function y(){let e=(0,a.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,isActive:r}=e;return c.toggleCategoryStatus(t,r)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()});let r=t.isActive?"activated":"deactivated";l.toast.success('Category "'.concat(t.name,'" ').concat(r," successfully"))},onError:e=>{l.toast.error("Failed to toggle category status: "+e.message)}})}},48763:function(e,t,r){"use strict";r.d(t,{t:function(){return i}});var s=r(12574),a=r(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},i=(0,s.U)()((0,a.tJ)((e,t)=>({...n,setAuth:(t,r,s)=>{e({user:t,accessToken:r,refreshToken:s,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let s=t().user;s&&e({user:{...s,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let s=Array.isArray(e)?e:[e];return"admin"===r.role||(s.includes("editor")?["admin","editor"].includes(r.role):s.includes("moderator")?["admin","editor","moderator"].includes(r.role):s.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(75504),a=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}}},function(e){e.O(0,[2150,9101,8939,1346,2341,2810,5723,2971,8069,1744],function(){return e(e.s=35738)}),_N_E=e.O()}]);