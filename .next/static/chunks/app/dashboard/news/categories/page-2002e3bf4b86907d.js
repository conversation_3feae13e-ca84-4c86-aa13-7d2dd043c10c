(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5075],{32726:function(e,t,s){Promise.resolve().then(s.bind(s,41259))},49108:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},37805:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14960:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},98306:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},37841:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},69475:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},61172:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},65404:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},79580:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},70094:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},28670:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},3665:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},69724:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47907:function(e,t,s){"use strict";var r=s(15313);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},41259:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return R}});var r=s(57437),a=s(2265),i=s(47907),n=s(64095),l=s(15671),c=s(575),o=s(22782),u=s(33277),d=s(22632),h=s(4133),x=s(18641),m=s(11546),g=s(91552),y=s(37841),f=s(29295),p=s(57977);let v=(0,p.Z)("toggle-right",[["circle",{cx:"15",cy:"12",r:"3",key:"1afu0r"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]),j=(0,p.Z)("toggle-left",[["circle",{cx:"9",cy:"12",r:"3",key:"u3jwor"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]);var b=s(50489),N=s(69724),C=s(61172),w=s(70094),k=s(3665),Z=s(69475),A=s(28670),S=s(65404),M=s(8792);function R(){var e,t,s,p,R;(0,i.useRouter)(),(0,n.NL)();let{isEditor:P,isAdmin:z}=(0,m.TE)(),[O,q]=(0,a.useState)({page:1,limit:20}),[F,D]=(0,a.useState)(""),[E,L]=(0,a.useState)(!1),[Q,T]=(0,a.useState)(null),{data:U,isLoading:V,error:K,refetch:B}=(0,g.L_)(O),H=(0,g.P9)(),I=(0,g.fu)(),Y=()=>{q(e=>({...e,search:F,page:1}))},_=e=>{T(e),L(!0)},X=async()=>{if(Q)try{await H.mutateAsync(Q.id),L(!1),T(null)}catch(e){}},$=async e=>{try{await I.mutateAsync({id:e.id,isActive:!e.isActive})}catch(e){}},G=e=>e?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200",J=[{key:"name",title:"Category",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full flex-shrink-0",style:{backgroundColor:t.color||"#6B7280"}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:t.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["/",t.slug]})]})]})},{key:"description",title:"Description",render:e=>(0,r.jsx)("div",{className:"max-w-xs truncate text-gray-600",children:e||"No description"})},{key:"articleCount",title:"Articles",render:(e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-medium",children:t.publishedArticleCount||0}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[t.articleCount||0," total"]})]})},{key:"isActive",title:"Status",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.C,{variant:"outline",className:G(e),children:e?"Active":"Inactive"}),t.isPublic&&(0,r.jsx)(u.C,{variant:"outline",className:"bg-blue-100 text-blue-800 border-blue-200",children:"Public"})]})},{key:"sortOrder",title:"Order",render:e=>(0,r.jsx)("div",{className:"text-center font-mono text-sm",children:e})},{key:"actions",title:"Actions",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(M.default,{href:"/dashboard/news/categories/".concat(t.id),children:(0,r.jsx)(c.z,{variant:"ghost",size:"sm",children:(0,r.jsx)(y.Z,{className:"h-4 w-4"})})}),(P()||z())&&(0,r.jsx)(M.default,{href:"/dashboard/news/categories/".concat(t.id,"/edit"),children:(0,r.jsx)(c.z,{variant:"ghost",size:"sm",children:(0,r.jsx)(f.Z,{className:"h-4 w-4"})})}),(P()||z())&&(0,r.jsx)(c.z,{variant:"ghost",size:"sm",onClick:()=>$(t),disabled:I.isLoading,children:t.isActive?(0,r.jsx)(v,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(j,{className:"h-4 w-4 text-gray-400"})}),z()&&(0,r.jsx)(c.z,{variant:"ghost",size:"sm",onClick:()=>_(t),disabled:H.isLoading,className:"text-red-600 hover:text-red-700",children:(0,r.jsx)(b.Z,{className:"h-4 w-4"})})]})}];return K?(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)(l.Zb,{children:(0,r.jsxs)(l.aY,{className:"flex flex-col items-center justify-center py-12",children:[(0,r.jsx)(N.Z,{className:"h-12 w-12 text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Failed to load categories"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:K instanceof Error?K.message:"An unexpected error occurred"}),(0,r.jsx)(c.z,{onClick:()=>B(),children:"Try Again"})]})})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[(0,r.jsx)(C.Z,{className:"h-8 w-8"}),"News Categories"]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage news categories and their settings"})]}),(P()||z())&&(0,r.jsx)(M.default,{href:"/dashboard/news/categories/create",children:(0,r.jsxs)(c.z,{children:[(0,r.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),"Add Category"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(k.Z,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Categories"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(null==U?void 0:null===(e=U.meta)||void 0===e?void 0:e.totalItems)||0})]})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(v,{className:"h-5 w-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Active"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(null==U?void 0:null===(t=U.data)||void 0===t?void 0:t.filter(e=>e.isActive).length)||0})]})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)(y.Z,{className:"h-5 w-5 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Public"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(null==U?void 0:null===(s=U.data)||void 0===s?void 0:s.filter(e=>e.isPublic).length)||0})]})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,r.jsx)(Z.Z,{className:"h-5 w-5 text-orange-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Articles"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(null==U?void 0:null===(p=U.data)||void 0===p?void 0:p.reduce((e,t)=>e+(t.articleCount||0),0))||0})]})]})})})]}),(0,r.jsx)(l.Zb,{className:"mb-6",children:(0,r.jsx)(l.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(A.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(o.I,{placeholder:"Search categories...",value:F,onChange:e=>D(e.target.value),onKeyPress:e=>{"Enter"===e.key&&Y()},className:"pl-10"})]})}),(0,r.jsxs)(x.Ph,{onValueChange:e=>{let t="all"===e?void 0:"active"===e;q(e=>({...e,isActive:t,page:1}))},children:[(0,r.jsx)(x.i4,{className:"w-full md:w-48",children:(0,r.jsx)(x.ki,{placeholder:"Filter by status"})}),(0,r.jsxs)(x.Bw,{children:[(0,r.jsx)(x.Ql,{value:"all",children:"All Status"}),(0,r.jsx)(x.Ql,{value:"active",children:"Active"}),(0,r.jsx)(x.Ql,{value:"inactive",children:"Inactive"})]})]}),(0,r.jsxs)(x.Ph,{onValueChange:e=>{let t="all"===e?void 0:"public"===e;q(e=>({...e,isPublic:t,page:1}))},children:[(0,r.jsx)(x.i4,{className:"w-full md:w-48",children:(0,r.jsx)(x.ki,{placeholder:"Filter by visibility"})}),(0,r.jsxs)(x.Bw,{children:[(0,r.jsx)(x.Ql,{value:"all",children:"All Visibility"}),(0,r.jsx)(x.Ql,{value:"public",children:"Public"}),(0,r.jsx)(x.Ql,{value:"private",children:"Private"})]})]}),(0,r.jsxs)(c.z,{onClick:Y,className:"w-full md:w-auto",children:[(0,r.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Search"]})]})})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"p-0",children:(0,r.jsx)(d.w,{data:(null==U?void 0:U.data)||[],columns:J,loading:V,pagination:{page:O.page||1,limit:O.limit||20,total:(null==U?void 0:null===(R=U.meta)||void 0===R?void 0:R.totalItems)||0,onPageChange:e=>q(t=>({...t,page:e})),onLimitChange:e=>q(t=>({...t,limit:e,page:1}))}})})}),(0,r.jsx)(h.sm,{isOpen:E,onClose:()=>{L(!1),T(null)},onConfirm:X,title:"Delete Category",message:Q?'Are you sure you want to delete "'.concat(Q.name,'"? This action cannot be undone and may affect associated articles.'):"",confirmText:"Delete",cancelText:"Cancel",loading:H.isLoading,variant:"destructive"})]})}},4133:function(e,t,s){"use strict";s.d(t,{sm:function(){return h},uB:function(){return x},u_:function(){return d}});var r=s(57437),a=s(2265),i=s(15669),n=s(691),l=s(52235),c=s(575),o=s(22169);let u={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},d=e=>{let{isOpen:t,onClose:s,title:d,description:h,children:x,size:m="md",showCloseButton:g=!0,closeOnOverlayClick:y=!0,className:f}=e;return(0,r.jsx)(i.u,{appear:!0,show:t,as:a.Fragment,children:(0,r.jsxs)(n.Vq,{as:"div",className:"relative z-50",onClose:y?s:()=>{},children:[(0,r.jsx)(i.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(i.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(n.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",u[m],f),children:[(d||g)&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[d&&(0,r.jsx)(n.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),h&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:h})]}),g&&(0,r.jsx)(c.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"mt-2",children:x})]})})})})]})})},h=e=>{let{isOpen:t,onClose:s,onConfirm:a,title:i="Confirm Action",message:n="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:o="Cancel",variant:u="default",loading:h=!1}=e;return(0,r.jsx)(d,{isOpen:t,onClose:s,title:i,size:"sm",closeOnOverlayClick:!h,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:n}),(0,r.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,r.jsx)(c.z,{variant:"outline",onClick:s,disabled:h,children:o}),(0,r.jsx)(c.z,{variant:"destructive"===u?"destructive":"default",onClick:a,disabled:h,children:h?"Processing...":l})]})]})})},x=e=>{let{isOpen:t,onClose:s,title:a,description:i,children:n,onSubmit:l,submitText:o="Save",cancelText:u="Cancel",loading:h=!1,size:x="md"}=e;return(0,r.jsx)(d,{isOpen:t,onClose:s,title:a,description:i,size:x,closeOnOverlayClick:!h,children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==l||l()},className:"space-y-4",children:[n,(0,r.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,r.jsx)(c.z,{type:"button",variant:"outline",onClick:s,disabled:h,children:u}),l&&(0,r.jsx)(c.z,{type:"submit",disabled:h,children:h?"Saving...":o})]})]})})}},91552:function(e,t,s){"use strict";s.d(t,{L_:function(){return d},b5:function(){return h},HL:function(){return m},P9:function(){return y},dk:function(){return x},fu:function(){return f},Ny:function(){return g}});var r=s(31346),a=s(64095),i=s(8186),n=s(74921);class l{async getCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=t.toString(),r=s?"".concat(this.baseUrl,"?").concat(s):this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let a=await n.x.get(r);if(console.log("✅ Categories fetched successfully:",a),a.data&&Array.isArray(a.data))return a;if(Array.isArray(a)){let t=e.page||1,s=e.limit||20,r=a.length,i=(t-1)*s;return{data:a.slice(i,i+s),meta:{currentPage:t,totalPages:Math.ceil(r/s),totalItems:r,limit:s}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await n.x.get("".concat(this.baseUrl,"/").concat(e));return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return n.x.post(this.baseUrl,e)}async updateCategory(e,t){return n.x.patch("".concat(this.baseUrl,"/").concat(e),t)}async deleteCategory(e){return n.x.delete("".concat(this.baseUrl,"/").concat(e))}async toggleCategoryStatus(e,t){return n.x.patch("".concat(this.baseUrl,"/").concat(e),{isActive:t})}async reorderCategories(e){return n.x.post("".concat(this.baseUrl,"/reorder"),{categoryIds:e})}async getCategoryStats(){return n.x.get("".concat(this.baseUrl,"/stats"))}constructor(){this.baseUrl="/api/news/categories"}}let c=new l;var o=s(56288);let u={all:["categories"],lists:()=>[...u.all,"list"],list:e=>[...u.lists(),e],details:()=>[...u.all,"detail"],detail:e=>[...u.details(),e],public:()=>[...u.all,"public"]};function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:u.list(e),queryFn:()=>c.getCategories(e),staleTime:3e5})}function h(e){return(0,r.a)({queryKey:u.detail(e),queryFn:()=>c.getCategoryById(e),enabled:!!e,staleTime:6e5})}function x(){return(0,r.a)({queryKey:u.public(),queryFn:()=>c.getPublicCategories(),staleTime:9e5})}function m(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:u.all}),o.toast.success('Category "'.concat(t.name,'" created successfully'))},onError:e=>{o.toast.error("Failed to create category: "+e.message)}})}function g(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>{let{id:t,data:s}=e;return c.updateCategory(t,s)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),o.toast.success('Category "'.concat(t.name,'" updated successfully'))},onError:e=>{o.toast.error("Failed to update category: "+e.message)}})}function y(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>c.deleteCategory(e),onSuccess:(t,s)=>{e.removeQueries({queryKey:u.detail(s)}),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),o.toast.success("Category deleted successfully")},onError:e=>{o.toast.error("Failed to delete category: "+e.message)}})}function f(){let e=(0,a.NL)();return(0,i.D)({mutationFn:e=>{let{id:t,isActive:s}=e;return c.toggleCategoryStatus(t,s)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()});let s=t.isActive?"activated":"deactivated";o.toast.success('Category "'.concat(t.name,'" ').concat(s," successfully"))},onError:e=>{o.toast.error("Failed to toggle category status: "+e.message)}})}},11546:function(e,t,s){"use strict";s.d(t,{TE:function(){return o},a1:function(){return c}});var r=s(57437),a=s(2265),i=s(47907),n=s(27786),l=s(96146);let c=e=>{let{children:t,requiredRole:s,fallbackUrl:c="/auth/login"}=e,o=(0,i.useRouter)(),{isAuthenticated:u,user:d,isLoading:h}=(0,n.a)();if((0,a.useEffect)(()=>{if(!h){if(!u||!d){o.push(c);return}if(s&&!(Array.isArray(s)?s:[s]).includes(d.role)){o.push("/dashboard?error=unauthorized");return}}},[u,d,h,s,o,c]),h)return(0,r.jsx)(l.SX,{message:"Verifying authentication..."});if(!u||!d)return(0,r.jsx)(l.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(d.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",d.role]})]})})}return(0,r.jsx)(r.Fragment,{children:t})},o=()=>{let{user:e}=(0,n.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),s=()=>t("admin"),r=()=>t(["admin","editor"]),a=()=>t(["admin","editor","moderator"]),i=()=>s(),l=()=>r(),c=()=>a(),o=()=>s();return{user:e,hasRole:t,isAdmin:s,isEditor:r,isModerator:a,canManageUsers:i,canManageContent:l,canModerate:c,canSync:o,can:e=>{switch(e){case"manage-users":return i();case"manage-content":return l();case"moderate":return c();case"sync":return o();default:return!1}}}}},8186:function(e,t,s){"use strict";s.d(t,{D:function(){return h}});var r=s(2265),a=s(31678),i=s(34654),n=s(79522),l=s(6761);class c extends l.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let s=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,i.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){n.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,s,r,a,i,n,l,c;e.onSuccess?(null==(t=(s=this.mutateOptions).onSuccess)||t.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(a=this.mutateOptions).onSettled)||r.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(i=(n=this.mutateOptions).onError)||i.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(l=(c=this.mutateOptions).onSettled)||l.call(c,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var o=s(97536),u=s(64095),d=s(3439);function h(e,t,s){let i=(0,a.lV)(e,t,s),l=(0,u.NL)({context:i.context}),[h]=r.useState(()=>new c(l,i));r.useEffect(()=>{h.setOptions(i)},[h,i]);let m=(0,o.$)(r.useCallback(e=>h.subscribe(n.V.batchCalls(e)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),g=r.useCallback((e,t)=>{h.mutate(e,t).catch(x)},[h]);if(m.error&&(0,d.L)(h.options.useErrorBoundary,[m.error]))throw m.error;return{...m,mutate:g,mutateAsync:m.mutate}}function x(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,1953,8792,6877,1380,2971,8069,1744],function(){return e(e.s=32726)}),_N_E=e.O()}]);