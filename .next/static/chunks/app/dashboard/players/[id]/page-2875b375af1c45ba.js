(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5491],{75788:function(e,s,t){Promise.resolve().then(t.bind(t,12274))},95032:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},53879:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},97307:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},37805:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},62985:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},58943:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},5423:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},62457:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},40834:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},56227:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29733:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},66260:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},11213:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},34059:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,s,t){"use strict";var r=t(15313);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},12274:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return F}});var r=t(57437),a=t(47907),l=t(31346),n=t(15671),c=t(575),i=t(33277),d=t(16996),o=t(5423),x=t(37805),m=t(34059),u=t(11213),h=t(53879),p=t(62985),g=t(40834),f=t(58943),y=t(97307),j=t(95032),b=t(57977);let N=(0,b.Z)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]]),v=(0,b.Z)("weight",[["circle",{cx:"12",cy:"5",r:"3",key:"rqqgnr"}],["path",{d:"M6.5 8a2 2 0 0 0-1.905 1.46L2.1 18.5A2 2 0 0 0 4 21h16a2 2 0 0 0 1.925-2.54L19.4 9.5A2 2 0 0 0 17.48 8Z",key:"56o5sh"}]]);var w=t(62457),k=t(29733),Z=t(66260),C=t(56227);let P=(0,b.Z)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var M=t(8792);let S=async e=>{let s=await fetch("/api/players/".concat(e));if(!s.ok)throw Error("Failed to fetch player details");return s.json()},R=e=>{let{playerId:s,playerName:t}=e;return(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6 p-4 bg-gray-50 rounded-lg border",children:[(0,r.jsxs)(M.default,{href:"/dashboard",className:"flex items-center hover:text-blue-600 transition-colors",children:[(0,r.jsx)(o.Z,{className:"w-4 h-4 mr-1"}),"Dashboard"]}),(0,r.jsx)(x.Z,{className:"w-4 h-4 text-gray-400"}),(0,r.jsxs)(M.default,{href:"/dashboard/players",className:"flex items-center hover:text-blue-600 transition-colors",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4 mr-1"}),"Players"]}),(0,r.jsx)(x.Z,{className:"w-4 h-4 text-gray-400"}),(0,r.jsxs)("span",{className:"text-gray-900 font-medium flex items-center",children:[(0,r.jsx)(u.Z,{className:"w-4 h-4 mr-1"}),t||"Player ".concat(s)]})]})},D=(e,s)=>e&&s&&0!==s?"".concat((e/s*100).toFixed(1),"%"):"-",A=e=>{let s=Math.floor(e/60);return s>0?"".concat(s,"h ").concat(e%60,"m"):"".concat(e,"m")};function F(){let e=(0,a.useParams)(),s=(0,a.useRouter)(),t=e.id,{data:o,isLoading:x,error:b}=(0,l.a)({queryKey:["player",t],queryFn:()=>S(t),enabled:!!t});return x?(0,r.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 animate-pulse rounded-lg"}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-gray-300 to-gray-400 rounded-xl shadow-lg p-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)("div",{className:"w-20 h-12 bg-gray-400 animate-pulse rounded"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"h-10 w-64 bg-gray-400 animate-pulse rounded"}),(0,r.jsx)("div",{className:"h-6 w-48 bg-gray-400 animate-pulse rounded"})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6",children:[(0,r.jsx)("div",{className:"h-96 bg-gray-200 animate-pulse rounded-lg"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 animate-pulse rounded-lg"})]})]}):b||!o?(0,r.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(c.z,{variant:"outline",onClick:()=>s.back(),children:[(0,r.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]})}),(0,r.jsx)(n.Zb,{className:"border-red-200",children:(0,r.jsxs)(n.aY,{className:"flex flex-col items-center justify-center py-16",children:[(0,r.jsx)("div",{className:"p-4 bg-red-100 rounded-full mb-6",children:(0,r.jsx)(p.Z,{className:"w-16 h-16 text-red-600"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Unable to Load Player"}),(0,r.jsx)("p",{className:"text-gray-600 text-center max-w-md mb-6",children:(null==b?void 0:b.message)||"We encountered an error while loading the player details. This might be due to network issues or the player data being unavailable."}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)(c.z,{onClick:()=>window.location.reload(),variant:"default",children:[(0,r.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,r.jsxs)(c.z,{onClick:()=>s.push("/dashboard/players"),variant:"outline",children:[(0,r.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]})]})]})})]}):(0,r.jsxs)("div",{className:"space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsx)(R,{playerId:t,playerName:o.player.name}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg text-white p-8",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)(c.z,{variant:"secondary",onClick:()=>s.back(),className:"bg-white/20 hover:bg-white/30 text-white border-white/30",children:[(0,r.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),"Back to Players"]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold tracking-tight flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-white/20 rounded-lg",children:(0,r.jsx)(u.Z,{className:"w-8 h-8"})}),(0,r.jsx)("span",{children:o.player.name}),o.player.injured&&(0,r.jsxs)(i.C,{variant:"destructive",className:"bg-red-500 hover:bg-red-600",children:[(0,r.jsx)(p.Z,{className:"w-3 h-3 mr-1"}),"Injured"]})]}),(0,r.jsx)("p",{className:"text-blue-100 text-lg mt-2",children:"Player Details & Career Statistics"})]})]})})}),(0,r.jsxs)(n.Zb,{className:"overflow-hidden",children:[(0,r.jsx)(n.Ol,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,r.jsxs)(n.ll,{className:"flex items-center space-x-2 text-xl",children:[(0,r.jsx)(m.Z,{className:"w-6 h-6 text-blue-600"}),(0,r.jsx)("span",{children:"Player Information"})]})}),(0,r.jsx)(n.aY,{className:"p-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 flex flex-col items-center space-y-6",children:[(0,r.jsxs)("div",{className:"relative",children:[o.player.photo&&(0,d.Sc)(o.player.photo)?(0,r.jsx)("img",{src:(0,d.Sc)(o.player.photo),alt:"".concat(o.player.name," photo"),className:"w-48 h-48 rounded-full object-cover bg-gray-100 border-4 border-white shadow-xl ring-4 ring-blue-100",onError:e=>{var s;e.currentTarget.style.display="none",null===(s=e.currentTarget.nextElementSibling)||void 0===s||s.classList.remove("hidden")}}):null,(0,r.jsx)("div",{className:"w-48 h-48 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center border-4 border-white shadow-xl ring-4 ring-blue-100 ".concat(o.player.photo&&(0,d.Sc)(o.player.photo)?"hidden":""),children:(0,r.jsx)(u.Z,{className:"w-24 h-24 text-gray-400"})}),o.player.injured&&(0,r.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,r.jsxs)(i.C,{variant:"destructive",className:"shadow-lg",children:[(0,r.jsx)(p.Z,{className:"w-3 h-3 mr-1"}),"Injured"]})})]}),(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:o.player.name}),(0,r.jsxs)("p",{className:"text-gray-600 text-lg font-medium",children:[o.player.firstname," ",o.player.lastname]}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-4",children:[(0,r.jsx)(f.Z,{className:"w-4 h-4 text-gray-500"}),(0,r.jsx)("span",{className:"text-gray-600",children:o.player.nationality})]})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 h-full",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold mb-6 text-blue-900 flex items-center",children:[(0,r.jsx)(u.Z,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(y.Z,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-blue-600 uppercase tracking-wide",children:"Age"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[o.player.age||(e=>{let s=new Date,t=new Date(e),r=s.getFullYear()-t.getFullYear(),a=s.getMonth()-t.getMonth();return(a<0||0===a&&s.getDate()<t.getDate())&&r--,r})(o.player.birth.date)," years old"]})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(f.Z,{className:"w-5 h-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-green-600 uppercase tracking-wide",children:"Nationality"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:o.player.nationality})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)(y.Z,{className:"w-5 h-5 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-red-600 uppercase tracking-wide",children:"Date of Birth"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:new Date(o.player.birth.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]})]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-orange-50 rounded-lg p-6 h-full",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold mb-6 text-orange-900 flex items-center",children:[(0,r.jsx)(j.Z,{className:"w-5 h-5 mr-2"}),"Physical Statistics"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,r.jsx)(N,{className:"w-5 h-5 text-orange-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-orange-600 uppercase tracking-wide",children:"Height"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:o.player.height||"Not available"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,r.jsx)(v,{className:"w-5 h-5 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-purple-600 uppercase tracking-wide",children:"Weight"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:o.player.weight||"Not available"})]})]})]})]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-6 h-full",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold mb-6 text-indigo-900 flex items-center",children:[(0,r.jsx)(w.Z,{className:"w-5 h-5 mr-2"}),"Origin & Background"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[o.player.birth.place&&(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-indigo-100 rounded-lg",children:(0,r.jsx)(w.Z,{className:"w-5 h-5 text-indigo-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-indigo-600 uppercase tracking-wide",children:"Place of Birth"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:o.player.birth.place}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:o.player.birth.country})]})]}),!o.player.birth.place&&(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-2 bg-gray-100 rounded-lg",children:(0,r.jsx)(f.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 uppercase tracking-wide",children:"Country"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:o.player.birth.country})]})]})]})]})})]})})]}),o.statistics&&o.statistics.length>0?(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-8 border border-green-200",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold flex items-center space-x-3 text-green-900 mb-2",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(k.Z,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("span",{children:"Career Statistics"})]}),(0,r.jsx)("p",{className:"text-green-700 text-lg",children:"Professional football career performance across all competitions"})]}),o.statistics.map((e,s)=>(0,r.jsxs)(n.Zb,{className:"overflow-hidden border-0 shadow-lg",children:[(0,r.jsx)(n.Ol,{className:"bg-gradient-to-r from-gray-50 to-slate-50 border-b",children:(0,r.jsxs)(n.ll,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e.team.logo&&(0,d.Sc)(e.team.logo)?(0,r.jsx)("img",{src:(0,d.Sc)(e.team.logo),alt:"".concat(e.team.name," logo"),className:"w-12 h-12 object-contain bg-white rounded-lg p-1 border",onError:e=>{var s;e.currentTarget.style.display="none",null===(s=e.currentTarget.nextElementSibling)||void 0===s||s.classList.remove("hidden")}}):null,(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center border ".concat(e.team.logo&&(0,d.Sc)(e.team.logo)?"hidden":""),children:(0,r.jsx)(Z.Z,{className:"w-6 h-6 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:e.team.name}),(0,r.jsxs)("p",{className:"text-gray-600 flex items-center space-x-2",children:[(0,r.jsx)("span",{children:e.league.name}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.league.season," Season"]})]})]})]}),(0,r.jsx)(i.C,{variant:"outline",className:"text-sm font-medium px-3 py-1",children:e.games.position})]})}),(0,r.jsxs)(n.aY,{className:"p-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-blue-50 rounded-xl p-6 text-center border border-blue-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:e.games.appearences}),(0,r.jsx)("p",{className:"text-sm font-medium text-blue-700 mt-1",children:"Appearances"})]})}),(0,r.jsx)("div",{className:"bg-green-50 rounded-xl p-6 text-center border border-green-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-green-600",children:e.games.lineups}),(0,r.jsx)("p",{className:"text-sm font-medium text-green-700 mt-1",children:"Lineups"})]})}),(0,r.jsx)("div",{className:"bg-purple-50 rounded-xl p-6 text-center border border-purple-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-purple-600",children:A(e.games.minutes)}),(0,r.jsx)("p",{className:"text-sm font-medium text-purple-700 mt-1",children:"Minutes Played"})]})}),(0,r.jsx)("div",{className:"bg-red-50 rounded-xl p-6 text-center border border-red-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-red-600",children:e.goals.total||0}),(0,r.jsx)("p",{className:"text-sm font-medium text-red-700 mt-1",children:"Goals"}),e.games.appearences>0&&(0,r.jsxs)("p",{className:"text-xs text-red-500 mt-2 bg-red-100 px-2 py-1 rounded-full",children:[((e.goals.total||0)/e.games.appearences).toFixed(2)," per game"]})]})}),null!==e.goals.assists&&(0,r.jsx)("div",{className:"bg-orange-50 rounded-xl p-6 text-center border border-orange-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:e.goals.assists}),(0,r.jsx)("p",{className:"text-sm font-medium text-orange-700 mt-1",children:"Assists"}),e.games.appearences>0&&(0,r.jsxs)("p",{className:"text-xs text-orange-500 mt-2 bg-orange-100 px-2 py-1 rounded-full",children:[(e.goals.assists/e.games.appearences).toFixed(2)," per game"]})]})}),e.games.rating&&(0,r.jsx)("div",{className:"bg-indigo-50 rounded-xl p-6 text-center border border-indigo-100 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-indigo-600",children:parseFloat(e.games.rating).toFixed(1)}),(0,r.jsx)("p",{className:"text-sm font-medium text-indigo-700 mt-1",children:"Average Rating"})]})})]}),(e.cards.yellow>0||e.cards.red>0)&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(p.Z,{className:"w-5 h-5 mr-2 text-yellow-600"}),"Disciplinary Record"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-yellow-50 rounded-xl p-6 text-center border border-yellow-100",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-yellow-600",children:e.cards.yellow}),(0,r.jsx)("p",{className:"text-sm font-medium text-yellow-700 mt-1",children:"Yellow Cards"})]}),e.cards.red>0&&(0,r.jsxs)("div",{className:"bg-red-50 rounded-xl p-6 text-center border border-red-100",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-red-600",children:e.cards.red}),(0,r.jsx)("p",{className:"text-sm font-medium text-red-700 mt-1",children:"Red Cards"})]}),e.fouls.committed&&(0,r.jsxs)("div",{className:"bg-orange-50 rounded-xl p-6 text-center border border-orange-100",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:e.fouls.committed}),(0,r.jsx)("p",{className:"text-sm font-medium text-orange-700 mt-1",children:"Fouls Committed"})]})]})]}),(e.shots.total||e.passes.total||e.tackles.total||e.duels.total)&&(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-8",children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(j.Z,{className:"w-5 h-5 mr-2 text-blue-600"}),"Detailed Performance Statistics"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.shots.total&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-6 border border-red-100",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg mr-3",children:(0,r.jsx)(C.Z,{className:"w-5 h-5 text-red-600"})}),(0,r.jsx)("h5",{className:"font-semibold text-red-900",children:"Shooting"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Total Shots"}),(0,r.jsx)("span",{className:"font-bold text-red-700",children:e.shots.total})]}),e.shots.on&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"On Target"}),(0,r.jsx)("span",{className:"font-bold text-red-700",children:e.shots.on})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Accuracy"}),(0,r.jsx)("span",{className:"font-bold text-red-700 bg-red-100 px-2 py-1 rounded-full text-xs",children:D(e.shots.on,e.shots.total)})]})]})]})]}),e.passes.total&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-100",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg mr-3",children:(0,r.jsx)(j.Z,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsx)("h5",{className:"font-semibold text-blue-900",children:"Passing"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Total Passes"}),(0,r.jsx)("span",{className:"font-bold text-blue-700",children:e.passes.total.toLocaleString()})]}),e.passes.key&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Key Passes"}),(0,r.jsx)("span",{className:"font-bold text-blue-700",children:e.passes.key})]}),e.passes.accuracy&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Accuracy"}),(0,r.jsxs)("span",{className:"font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded-full text-xs",children:[e.passes.accuracy,"%"]})]})]})]}),e.tackles.total&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg mr-3",children:(0,r.jsx)(P,{className:"w-5 h-5 text-green-600"})}),(0,r.jsx)("h5",{className:"font-semibold text-green-900",children:"Defending"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Tackles"}),(0,r.jsx)("span",{className:"font-bold text-green-700",children:e.tackles.total})]}),e.tackles.interceptions&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Interceptions"}),(0,r.jsx)("span",{className:"font-bold text-green-700",children:e.tackles.interceptions})]}),e.tackles.blocks&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Blocks"}),(0,r.jsx)("span",{className:"font-bold text-green-700",children:e.tackles.blocks})]})]})]}),e.duels.total&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg mr-3",children:(0,r.jsx)(Z.Z,{className:"w-5 h-5 text-purple-600"})}),(0,r.jsx)("h5",{className:"font-semibold text-purple-900",children:"Duels & Dribbles"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Duels Won"}),(0,r.jsxs)("span",{className:"font-bold text-purple-700",children:[e.duels.won,"/",e.duels.total]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Success Rate"}),(0,r.jsx)("span",{className:"font-bold text-purple-700 bg-purple-100 px-2 py-1 rounded-full text-xs",children:D(e.duels.won,e.duels.total)})]}),e.dribbles.success&&e.dribbles.attempts&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:"Dribbles"}),(0,r.jsxs)("span",{className:"font-bold text-purple-700",children:[e.dribbles.success,"/",e.dribbles.attempts]})]})]})]})]})]})]})]},s))]}):(0,r.jsx)(n.Zb,{children:(0,r.jsxs)(n.aY,{className:"flex flex-col items-center justify-center py-12",children:[(0,r.jsx)(Z.Z,{className:"w-16 h-16 text-gray-300 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Statistics Available"}),(0,r.jsx)("p",{className:"text-gray-500 text-center mb-4",children:"No career statistics found for this player. This could be because:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-500 text-left space-y-1",children:[(0,r.jsx)("li",{children:"• Player hasn't played in any matches yet"}),(0,r.jsx)("li",{children:"• Statistics are still being updated"}),(0,r.jsx)("li",{children:"• Player is not active in current leagues"})]})]})})]})}},33277:function(e,s,t){"use strict";t.d(s,{C:function(){return c}});var r=t(57437);t(2265);var a=t(49769),l=t(22169);let n=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...a})}},575:function(e,s,t){"use strict";t.d(s,{d:function(){return i},z:function(){return d}});var r=t(57437),a=t(2265),l=t(59143),n=t(49769),c=t(22169);let i=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,x=d?l.g7:"button";return(0,r.jsx)(x,{className:(0,c.cn)(i({variant:a,size:n,className:t})),ref:s,...o})});d.displayName="Button"},15671:function(e,s,t){"use strict";t.d(s,{Ol:function(){return c},SZ:function(){return d},Zb:function(){return n},aY:function(){return o},ll:function(){return i}});var r=t(57437),a=t(2265),l=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...a})});n.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},22169:function(e,s,t){"use strict";t.d(s,{cn:function(){return l}});var r=t(75504),a=t(51367);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.m6)((0,r.W)(s))}},16996:function(e,s,t){"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let s=e.startsWith("/")?e.slice(1):e;return"".concat("http://*************","/").concat(s)}function a(e){return r(e)}function l(e){return r(e)}function n(e){return r(e)}t.d(s,{Bf:function(){return a},Fc:function(){return n},Sc:function(){return r},ou:function(){return l}})},61266:function(e,s,t){"use strict";t.d(s,{F:function(){return l},e:function(){return n}});var r=t(2265);function a(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}function l(...e){return s=>{let t=!1,r=e.map(e=>{let r=a(e,s);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let s=0;s<r.length;s++){let t=r[s];"function"==typeof t?t():a(e[s],null)}}}}function n(...e){return r.useCallback(l(...e),e)}},59143:function(e,s,t){"use strict";t.d(s,{Z8:function(){return n},g7:function(){return c},sA:function(){return d}});var r=t(2265),a=t(61266),l=t(57437);function n(e){let s=function(e){let s=r.forwardRef((e,s)=>{let{children:t,...l}=e;if(r.isValidElement(t)){let e,n;let c=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,i=function(e,s){let t={...s};for(let r in s){let a=e[r],l=s[r];/^on[A-Z]/.test(r)?a&&l?t[r]=(...e)=>{let s=l(...e);return a(...e),s}:a&&(t[r]=a):"style"===r?t[r]={...a,...l}:"className"===r&&(t[r]=[a,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==r.Fragment&&(i.ref=s?(0,a.F)(s,c):c),r.cloneElement(t,i)}return r.Children.count(t)>1?r.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),t=r.forwardRef((e,t)=>{let{children:a,...n}=e,c=r.Children.toArray(a),i=c.find(o);if(i){let e=i.props.children,a=c.map(s=>s!==i?s:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(s,{...n,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,l.jsx)(s,{...n,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var c=n("Slot"),i=Symbol("radix.slottable");function d(e){let s=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return s.displayName=`${e}.Slottable`,s.__radixId=i,s}function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},49769:function(e,s,t){"use strict";t.d(s,{j:function(){return n}});var r=t(75504);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.W,n=(e,s)=>t=>{var r;if((null==s?void 0:s.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:c}=s,i=Object.keys(n).map(e=>{let s=null==t?void 0:t[e],r=null==c?void 0:c[e];if(null===s)return null;let l=a(s)||a(r);return n[e][l]}),d=t&&Object.entries(t).reduce((e,s)=>{let[t,r]=s;return void 0===r||(e[t]=r),e},{});return l(e,i,null==s?void 0:null===(r=s.compoundVariants)||void 0===r?void 0:r.reduce((e,s)=>{let{class:t,className:r,...a}=s;return Object.entries(a).every(e=>{let[s,t]=e;return Array.isArray(t)?t.includes({...c,...d}[s]):({...c,...d})[s]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},function(e){e.O(0,[2150,9101,1346,8792,2971,8069,1744],function(){return e(e.s=75788)}),_N_E=e.O()}]);