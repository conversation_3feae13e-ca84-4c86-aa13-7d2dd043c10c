(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5857],{29792:function(e,a,s){Promise.resolve().then(s.bind(s,11736))},11736:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return M}});var t=s(57437),l=s(2265),n=s(47907),r=s(31346),i=s(15671),o=s(575),c=s(22782),d=s(33277),u=s(22632),m=s(11546),x=s(64095),h=s(8186);let g={getPlayers:async function(){var e;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(a).forEach(e=>{let[a,t]=e;void 0!==t&&s.append(a,t.toString())}),console.log("\uD83D\uDD04 Fetching players via proxy:","/api/players?".concat(s.toString()));let t=await fetch("/api/players?".concat(s.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Failed to fetch players"}))).message||"Failed to fetch players");let l=await t.json();return console.log("✅ Players fetched successfully:",l.meta||"".concat((null===(e=l.data)||void 0===e?void 0:e.length)||0," players")),l},getPlayer:async e=>{var a;console.log("\uD83D\uDD04 Fetching player by ID:",e);let s=await fetch("/api/players/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json().catch(()=>({message:"Failed to fetch player"}))).message||"Failed to fetch player");let t=await s.json();return console.log("✅ Player fetched successfully:",null===(a=t.player)||void 0===a?void 0:a.name),t},searchPlayers:async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;return g.getPlayers({search:e,limit:a})},getPlayersByTeam:async(e,a)=>g.getPlayers({team:e,season:a,limit:100}),getPlayersByLeague:async(e,a)=>g.getPlayers({league:e,season:a,limit:100}),getPlayersByPosition:async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return g.getPlayers({position:e,limit:a})},getTopScorers:async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20,t=new URLSearchParams;e&&t.append("league",e.toString()),a&&t.append("season",a.toString()),s&&t.append("limit",s.toString()),console.log("\uD83D\uDD04 Fetching top scorers via proxy:","/api/players/topscorers?".concat(t.toString()));let l=await fetch("/api/players/topscorers?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!l.ok)throw Error((await l.json().catch(()=>({message:"Failed to fetch top scorers"}))).message||"Failed to fetch top scorers");let n=await l.json();return console.log("✅ Top scorers fetched successfully:",n.meta),n}};var p=s(56288);let f=function(){var e,a;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,r.a)({queryKey:["players",s],queryFn:()=>g.getPlayers(s),staleTime:3e5});return{players:(null===(e=t.data)||void 0===e?void 0:e.data)||[],playersMeta:null===(a=t.data)||void 0===a?void 0:a.meta,isPlayersLoading:t.isLoading,playersError:t.error,refetchPlayers:t.refetch}},v=function(e,a){var s,t;let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20,n=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=(0,r.a)({queryKey:["players","topscorers",e,a,l],queryFn:()=>g.getTopScorers(e,a,l),enabled:n&&!!e,staleTime:3e5});return{topScorers:(null===(s=i.data)||void 0===s?void 0:s.data)||[],topScorersMeta:null===(t=i.data)||void 0===t?void 0:t.meta,isTopScorersLoading:i.isLoading,topScorersError:i.error,refetchTopScorers:i.refetch}},j=()=>{let e=(0,x.NL)(),a=(0,h.D)({mutationFn:async e=>{let{leagueId:a,season:s}=e,t=await fetch("/api/players/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({leagueId:a,season:s})});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Sync failed"}))).message||"Failed to sync players");return await t.json()},onSuccess:a=>{e.invalidateQueries({queryKey:["players"]}),p.toast.success("Successfully synced ".concat(a.count||0," players"))},onError:e=>{p.toast.error(e.message||"Failed to sync players")}});return{syncPlayers:a.mutate,isSyncing:a.isLoading,syncError:a.error}};var y=s(47011),N=s(16996),w=s(34059),b=s(39346),S=s(66260),C=s(56227),P=s(37841),k=s(53348),F=s(97307),Z=s(65404),T=s(28670),L=s(40834),D=s(75879),E=s(9208),I=s(12647),A=s(14440),Y=s(29069),z=s(79580),R=s(62985),O=s(81708),B=s(34187);let q=e=>{var a;let{open:s,onClose:n}=e,[i,u]=(0,l.useState)(""),[m,x]=(0,l.useState)(new Date().getFullYear()),[h,g]=(0,l.useState)("select"),[p,f]=(0,l.useState)(null),{syncPlayers:v,isSyncing:b}=j(),{data:P,isLoading:Z}=(0,r.a)({queryKey:["leagues","all"],queryFn:()=>y.A.getLeagues({limit:100,active:!0})}),T=null==P?void 0:null===(a=P.data)||void 0===a?void 0:a.find(e=>e.externalId.toString()===i),L=()=>{g("select"),u(""),x(new Date().getFullYear()),f(null),n()};return(0,t.jsx)(E.Vq,{open:s,onOpenChange:L,children:(0,t.jsxs)(E.cZ,{className:"sm:max-w-md",children:[(0,t.jsxs)(E.fK,{children:[(0,t.jsxs)(E.$N,{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Sync Players"})]}),(0,t.jsxs)(E.Be,{children:["select"===h&&"Select league and season to sync top scorers","confirm"===h&&"Confirm the sync operation","syncing"===h&&"Syncing players data...","complete"===h&&"Sync operation completed"]})]}),(()=>{switch(h){case"select":var e;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I._,{htmlFor:"league",children:"Select League"}),Z?(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-md",children:[(0,t.jsx)(z.Z,{className:"w-4 h-4 animate-spin"}),(0,t.jsx)("span",{children:"Loading leagues..."})]}):(0,t.jsxs)("select",{id:"league",value:i,onChange:e=>u(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[(0,t.jsx)("option",{value:"",children:"Choose a league"}),null==P?void 0:null===(e=P.data)||void 0===e?void 0:e.map(e=>(0,t.jsxs)("option",{value:e.externalId.toString(),children:[e.name," (",e.country,")"]},e.externalId))]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I._,{htmlFor:"season",children:"Season"}),(0,t.jsx)(c.I,{id:"season",type:"number",value:m,onChange:e=>x(parseInt(e.target.value)||new Date().getFullYear()),min:"2000",max:new Date().getFullYear()+1,placeholder:"Enter season year"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current year: ",new Date().getFullYear()]})]}),T&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-muted/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[T.logo&&(0,t.jsx)("img",{src:(0,N.Fc)(T.logo)||"/images/default-league.png",alt:T.name,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:T.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:T.country})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,t.jsxs)(d.C,{variant:"outline",children:[(0,t.jsx)(S.Z,{className:"w-3 h-3 mr-1"}),"League ID: ",T.externalId]}),(0,t.jsxs)(d.C,{variant:"outline",children:[(0,t.jsx)(F.Z,{className:"w-3 h-3 mr-1"}),"Season: ",m]})]})]}),(0,t.jsxs)(Y.bZ,{children:[(0,t.jsx)(R.Z,{className:"h-4 w-4"}),(0,t.jsx)(Y.X,{children:"This will sync top scorers from the external API for the selected league and season. The process may take a few moments depending on the amount of data."})]})]});case"confirm":return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(C.Z,{className:"w-8 h-8 text-blue-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Confirm Player Sync"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"You are about to sync top scorers data for:"})]}),(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-muted/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(null==T?void 0:T.logo)&&(0,t.jsx)("img",{src:(0,N.Fc)(T.logo)||"/images/default-league.png",alt:T.name,className:"w-10 h-10 object-contain"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:null==T?void 0:T.name}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[null==T?void 0:T.country," • Season ",m]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,t.jsx)(O.Z,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["API Endpoint: /football/players?league=",i,"&season=",m]})]})]}),(0,t.jsxs)(Y.bZ,{children:[(0,t.jsx)(R.Z,{className:"h-4 w-4"}),(0,t.jsxs)(Y.X,{children:[(0,t.jsx)("strong",{children:"Note:"})," This will fetch and update player statistics for top scorers. Existing data may be updated with the latest information from the external API."]})]})]});case"syncing":return(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(z.Z,{className:"w-8 h-8 text-blue-600 animate-spin"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Syncing Players..."}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:"Fetching top scorers data from external API"}),(0,t.jsx)(A.E,{value:void 0,className:"w-full"})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,t.jsxs)("p",{children:["League: ",null==T?void 0:T.name]}),(0,t.jsxs)("p",{children:["Season: ",m]}),(0,t.jsx)("p",{children:"Please wait while we sync the data..."})]})]});case"complete":return(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(B.Z,{className:"w-8 h-8 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Sync Complete!"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Successfully synced player data"})]}),p&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-green-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"font-medium",children:"Sync Results"}),(0,t.jsxs)(d.C,{variant:"default",className:"bg-green-100 text-green-800",children:[(0,t.jsx)(w.Z,{className:"w-3 h-3 mr-1"}),p.count||0," players"]})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:["League: ",null==T?void 0:T.name]}),(0,t.jsxs)("p",{children:["Season: ",m]}),(0,t.jsxs)("p",{children:["Players synced: ",p.count||0]}),p.updated&&(0,t.jsxs)("p",{children:["Players updated: ",p.updated]}),p.created&&(0,t.jsxs)("p",{children:["New players: ",p.created]})]})]}),(0,t.jsxs)(Y.bZ,{children:[(0,t.jsx)(B.Z,{className:"h-4 w-4"}),(0,t.jsx)(Y.X,{children:"The players data has been successfully updated. You can now view the latest top scorers in the players management page."})]})]});default:return null}})(),(0,t.jsx)(E.cN,{children:(0,t.jsxs)("div",{className:"flex space-x-2 w-full",children:["select"===h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.z,{variant:"outline",onClick:L,className:"flex-1",children:"Cancel"}),(0,t.jsx)(o.z,{onClick:()=>{"select"===h&&i&&m&&g("confirm")},disabled:!i||!m,className:"flex-1",children:"Next"})]}),"confirm"===h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.z,{variant:"outline",onClick:()=>g("select"),className:"flex-1",children:"Back"}),(0,t.jsxs)(o.z,{onClick:()=>{i&&m&&(g("syncing"),v({leagueId:parseInt(i),season:m},{onSuccess:e=>{f(e),g("complete")},onError:()=>{g("select")}}))},disabled:b,className:"flex-1",children:[(0,t.jsx)(k.Z,{className:"w-4 h-4 mr-2"}),"Start Sync"]})]}),"syncing"===h&&(0,t.jsxs)(o.z,{variant:"outline",disabled:!0,className:"w-full",children:[(0,t.jsx)(z.Z,{className:"w-4 h-4 mr-2 animate-spin"}),"Syncing..."]}),"complete"===h&&(0,t.jsxs)(o.z,{onClick:L,className:"w-full",children:[(0,t.jsx)(B.Z,{className:"w-4 h-4 mr-2"}),"Done"]})]})})]})})};function M(){var e,a,s,x,h,g,j,E;let I=(0,n.useRouter)(),{isEditor:A,isAdmin:Y}=(0,m.TE)(),[z,R]=(0,l.useState)({page:1,limit:20}),[O,B]=(0,l.useState)(""),[M,V]=(0,l.useState)(""),[_,G]=(0,l.useState)(new Date().getFullYear()),[K,U]=(0,l.useState)(""),[X,J]=(0,l.useState)(!1),[W,H]=(0,l.useState)(!1),{players:$,playersMeta:Q,isPlayersLoading:ee,playersError:ea}=f(z),{topScorers:es,topScorersMeta:et,isTopScorersLoading:el,topScorersError:en,refetchTopScorers:er}=v(M?parseInt(M):void 0,_,20,X),{data:ei}=(0,r.a)({queryKey:["leagues","active"],queryFn:()=>y.A.getLeagues({limit:100,active:!0})}),eo=X&&!M;console.log("\uD83D\uDD0D Players Page Debug:",{playersCount:$.length,topScorersCount:es.length,showTopScorers:X,selectedLeague:M,selectedSeason:_,filters:z,playersMeta:Q,playersError:ea,topScorersError:en,showTopScorersEmptyState:eo});let ec=e=>{B(e),R(a=>({...a,search:e||void 0,page:1}))},ed=e=>{V(e),R(a=>({...a,league:e?parseInt(e):void 0,page:1}))},eu=e=>{G(e),R(a=>({...a,season:e,page:1}))},em=e=>{U(e),R(a=>({...a,position:e||void 0,page:1}))},ex=X?es:$,eh=X?et:Q,eg=X?el:ee,ep=X?en:ea,ef=[{title:"Player",key:"name",render:(e,a)=>{var s,l,n,r,i;return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(null===(s=a.player)||void 0===s?void 0:s.photo)&&(0,N.Sc)(a.player.photo)?(0,t.jsx)("img",{src:(0,N.Sc)(a.player.photo),alt:"".concat((null===(l=a.player)||void 0===l?void 0:l.name)||"Player"," photo"),className:"w-10 h-10 rounded-full object-cover bg-gray-100",onError:e=>{var a;e.currentTarget.style.display="none",null===(a=e.currentTarget.nextElementSibling)||void 0===a||a.classList.remove("hidden")}}):null,(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center ".concat((null===(n=a.player)||void 0===n?void 0:n.photo)&&(0,N.Sc)(a.player.photo)?"hidden":""),children:(0,t.jsx)(w.Z,{className:"w-5 h-5 text-gray-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium flex items-center space-x-2",children:[(null===(r=a.player)||void 0===r?void 0:r.name)||"Unknown Player",X&&(0,t.jsx)(b.Z,{className:"w-4 h-4 text-yellow-500"})]}),(null===(i=a.player)||void 0===i?void 0:i.nationality)&&(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:a.player.nationality})]})]})}},{title:"Position",key:"position",render:(e,a)=>{var s,l,n;return(0,t.jsx)(d.C,{variant:"outline",className:"text-xs",children:(null===(n=a.statistics)||void 0===n?void 0:null===(l=n[0])||void 0===l?void 0:null===(s=l.games)||void 0===s?void 0:s.position)||"N/A"})}},{title:"Team",key:"team",render:(e,a)=>{var s,l,n,r,i,o,c,d,u;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(null===(n=a.statistics)||void 0===n?void 0:null===(l=n[0])||void 0===l?void 0:null===(s=l.team)||void 0===s?void 0:s.logo)&&(0,N.Sc)(a.statistics[0].team.logo)?(0,t.jsx)("img",{src:(0,N.Sc)(a.statistics[0].team.logo),alt:"".concat(a.statistics[0].team.name," logo"),className:"w-6 h-6 object-contain bg-gray-50 rounded",onError:e=>{var a;e.currentTarget.style.display="none",null===(a=e.currentTarget.nextElementSibling)||void 0===a||a.classList.remove("hidden")}}):null,(0,t.jsx)("div",{className:"w-6 h-6 rounded bg-gray-100 flex items-center justify-center ".concat((null===(o=a.statistics)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:null===(r=i.team)||void 0===r?void 0:r.logo)&&(0,N.Sc)(a.statistics[0].team.logo)?"hidden":""),children:(0,t.jsx)(S.Z,{className:"w-3 h-3 text-gray-400"})}),(0,t.jsx)("span",{className:"text-sm",children:(null===(u=a.statistics)||void 0===u?void 0:null===(d=u[0])||void 0===d?void 0:null===(c=d.team)||void 0===c?void 0:c.name)||"Free Agent"})]})}},{title:"Age",key:"age",render:(e,a)=>{var s;return(0,t.jsx)("div",{className:"text-center",children:(null===(s=a.player)||void 0===s?void 0:s.age)?(0,t.jsx)(d.C,{variant:"secondary",className:"font-mono",children:a.player.age}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"-"})})}},{title:X?"Goals":"Stats",key:"statistics",render:(e,a)=>{var s,l,n,r,i,o;return(0,t.jsx)("div",{className:"text-center",children:X&&(null===(n=a.statistics)||void 0===n?void 0:null===(l=n[0])||void 0===l?void 0:null===(s=l.goals)||void 0===s?void 0:s.total)?(0,t.jsxs)(d.C,{variant:"default",className:"bg-green-100 text-green-800",children:[(0,t.jsx)(C.Z,{className:"w-3 h-3 mr-1"}),a.statistics[0].goals.total]}):(null===(o=a.statistics)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:null===(r=i.games)||void 0===r?void 0:r.appearences)?(0,t.jsxs)(d.C,{variant:"outline",children:[a.statistics[0].games.appearences," games"]}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"-"})})}},{title:"Actions",key:"actions",render:(e,a)=>{var s,l;return(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)(o.z,{variant:"ghost",size:"sm",onClick:()=>{var e;let s=null===(e=a.player)||void 0===e?void 0:e.id;s?I.push("/dashboard/players/".concat(s)):p.toast.error("Player ID not available")},disabled:!(null===(s=a.player)||void 0===s?void 0:s.id),className:"hover:bg-blue-50 hover:text-blue-600 transition-colors",title:(null===(l=a.player)||void 0===l?void 0:l.id)?"View ".concat(a.player.name," details"):"Player ID not available",children:[(0,t.jsx)(P.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"sr-only",children:"View player details"})]})})}}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold tracking-tight flex items-center space-x-3",children:[(0,t.jsx)(w.Z,{className:"w-8 h-8"}),(0,t.jsx)("span",{children:"Players Management"}),X&&(0,t.jsxs)(d.C,{variant:"default",className:"bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(S.Z,{className:"w-4 h-4 mr-1"}),"Top Scorers"]})]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:X?"Top scorers for ".concat(M?null==ei?void 0:null===(a=ei.data)||void 0===a?void 0:null===(e=a.find(e=>e.externalId.toString()===M))||void 0===e?void 0:e.name:"selected league"," (").concat(_,")"):"Manage and view all players in the database"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.z,{variant:"outline",onClick:()=>{J(!X),!X&&M&&er()},children:X?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(w.Z,{className:"w-4 h-4 mr-2"}),"All Players"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.Z,{className:"w-4 h-4 mr-2"}),"Top Scorers"]})}),(A()||Y())&&(0,t.jsxs)(o.z,{onClick:()=>H(!0),variant:"default",children:[(0,t.jsx)(k.Z,{className:"w-4 h-4 mr-2"}),"Sync Players"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Players"}),(0,t.jsx)(w.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==eh?void 0:null===(s=eh.totalItems)||void 0===s?void 0:s.toLocaleString())||"0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:X?"Top scorers found":"Players in database"})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Current Page"}),(0,t.jsx)(F.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[(null==eh?void 0:eh.currentPage)||1," / ",(null==eh?void 0:eh.totalPages)||1]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Showing ",ex.length," of ",(null==eh?void 0:eh.totalItems)||0]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Selected League"}),(0,t.jsx)(S.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsx)("div",{className:"text-lg font-bold truncate",children:M?(null==ei?void 0:null===(h=ei.data)||void 0===h?void 0:null===(x=h.find(e=>e.externalId.toString()===M))||void 0===x?void 0:x.name)||"Unknown":"All Leagues"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Season ",_]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(i.ll,{className:"text-sm font-medium",children:"Filters Active"}),(0,t.jsx)(Z.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(i.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:[O,M,K].filter(Boolean).length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:X?"Top scorers mode":"Search & filter options"})]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{children:(0,t.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Search & Filters"})]})}),(0,t.jsxs)(i.aY,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)(c.I,{placeholder:"Search players...",value:O,onChange:e=>ec(e.target.value),className:"w-full"})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{value:M,onChange:e=>ed(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Leagues"}),null==ei?void 0:null===(g=ei.data)||void 0===g?void 0:g.map(e=>(0,t.jsx)("option",{value:e.externalId.toString(),children:e.name},e.externalId))]})}),(0,t.jsx)("div",{children:(0,t.jsx)(c.I,{type:"number",placeholder:"Season",value:_,onChange:e=>eu(parseInt(e.target.value)||new Date().getFullYear()),min:"2000",max:new Date().getFullYear()+1,className:"w-full"})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{value:K,onChange:e=>em(e.target.value),className:"w-full h-10 px-3 rounded-md border border-input bg-background text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Positions"}),(0,t.jsx)("option",{value:"Goalkeeper",children:"Goalkeeper"}),(0,t.jsx)("option",{value:"Defender",children:"Defender"}),(0,t.jsx)("option",{value:"Midfielder",children:"Midfielder"}),(0,t.jsx)("option",{value:"Forward",children:"Forward"})]})}),(0,t.jsx)("div",{children:(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>window.location.reload(),className:"w-full",children:[(0,t.jsx)(L.Z,{className:"w-4 h-4 mr-2"}),"Refresh"]})}),(0,t.jsx)("div",{children:(0,t.jsx)(o.z,{variant:"outline",onClick:()=>{B(""),V(""),G(new Date().getFullYear()),U(""),J(!1),R({page:1,limit:20})},className:"w-full",children:"Clear All"})})]}),(O||M||K||X)&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mt-4",children:[O&&(0,t.jsxs)(d.C,{variant:"secondary",children:["Search: ",O]}),M&&(0,t.jsxs)(d.C,{variant:"secondary",children:["League: ",null==ei?void 0:null===(E=ei.data)||void 0===E?void 0:null===(j=E.find(e=>e.externalId.toString()===M))||void 0===j?void 0:j.name]}),K&&(0,t.jsxs)(d.C,{variant:"secondary",children:["Position: ",K]}),X&&(0,t.jsxs)(d.C,{variant:"default",className:"bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(D.Z,{className:"w-3 h-3 mr-1"}),"Top Scorers Mode"]})]})]})]}),(0,t.jsxs)(i.Zb,{children:[(0,t.jsx)(i.Ol,{children:(0,t.jsx)(i.ll,{className:"flex items-center space-x-2",children:X?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Top Scorers"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(w.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Players List"})]})})}),(0,t.jsx)(i.aY,{children:eo?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(S.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select League for Top Scorers"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Please select a league and season to view top scorers data."}),(0,t.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,t.jsx)(d.C,{variant:"outline",children:"1. Choose League"}),(0,t.jsx)(d.C,{variant:"outline",children:"2. Set Season"}),(0,t.jsx)(d.C,{variant:"outline",children:"3. View Results"})]})]}):ep?(0,t.jsxs)("div",{className:"text-center py-8 text-red-500",children:["Error loading players: ",(null==ep?void 0:ep.message)||"Unknown error"]}):(0,t.jsx)(u.w,{columns:ef,data:ex,loading:eg,pagination:{page:(null==eh?void 0:eh.currentPage)||1,limit:(null==eh?void 0:eh.limit)||20,total:(null==eh?void 0:eh.totalItems)||0,onPageChange:e=>{R(a=>({...a,page:e}))},onLimitChange:e=>R(a=>({...a,limit:e,page:1}))},emptyMessage:X?"No top scorers found. Try selecting a league and season.":"No players found."})})]}),(0,t.jsx)(q,{open:W,onClose:()=>H(!1)})]})}},29069:function(e,a,s){"use strict";s.d(a,{X:function(){return c},bZ:function(){return o}});var t=s(57437),l=s(2265),n=s(49769),r=s(22169);let i=(0,n.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=l.forwardRef((e,a)=>{let{className:s,variant:l,...n}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(i({variant:l}),s),...n})});o.displayName="Alert",l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium leading-none tracking-tight",s),...l})}).displayName="AlertTitle";let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",s),...l})});c.displayName="AlertDescription"},9208:function(e,a,s){"use strict";s.d(a,{$N:function(){return h},Be:function(){return g},Vq:function(){return o},cN:function(){return x},cZ:function(){return u},fK:function(){return m}});var t=s(57437),l=s(2265),n=s(72936),r=s(52235),i=s(22169);let o=n.fC;n.xz;let c=n.h_;n.x8;let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.aV,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...l})});d.displayName=n.aV.displayName;let u=l.forwardRef((e,a)=>{let{className:s,children:l,...o}=e;return(0,t.jsxs)(c,{children:[(0,t.jsx)(d,{}),(0,t.jsxs)(n.VY,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...o,children:[l,(0,t.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(r.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=n.VY.displayName;let m=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...s})};m.displayName="DialogHeader";let x=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};x.displayName="DialogFooter";let h=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.Dx,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...l})});h.displayName=n.Dx.displayName;let g=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.dk,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...l})});g.displayName=n.dk.displayName},12647:function(e,a,s){"use strict";s.d(a,{_:function(){return c}});var t=s(57437),l=s(2265),n=s(24602),r=s(49769),i=s(22169);let o=(0,r.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.f,{ref:a,className:(0,i.cn)(o(),s),...l})});c.displayName=n.f.displayName},14440:function(e,a,s){"use strict";s.d(a,{E:function(){return r}});var t=s(57437),l=s(2265),n=s(22169);let r=l.forwardRef((e,a)=>{let{className:s,value:l=0,max:r=100,showValue:i=!1,size:o="md",variant:c="default",...d}=e,u=Math.min(Math.max(l/r*100,0),100);return(0,t.jsxs)("div",{ref:a,className:(0,n.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[o],s),...d,children:[(0,t.jsx)("div",{className:(0,n.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[c]),style:{width:"".concat(u,"%")}}),i&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(u),"%"]})})]})});r.displayName="Progress"},47011:function(e,a,s){"use strict";s.d(a,{A:function(){return n}});var t=s(74921);let l=()=>{try{let a=localStorage.getItem("auth-storage");if(a){var e;let s=JSON.parse(a);return(null===(e=s.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,t]=e;void 0!==t&&a.append(s,t.toString())});let s=await fetch("/api/leagues?".concat(a.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,a)=>{let s=a?"".concat(e,"-").concat(a):e.toString(),t=await fetch("/api/leagues/".concat(s),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch league ".concat(e));return await t.json()},createLeague:async e=>await t.x.post("/football/leagues",e),updateLeague:async(e,a,s)=>{let t=l(),r={"Content-Type":"application/json"};t&&(r.Authorization="Bearer ".concat(t));let i=await n.getLeagueById(e,s);if(!i||!i.id)throw Error("League not found: ".concat(e).concat(s?"-".concat(s):""));let o=await fetch("/api/leagues/".concat(i.id),{method:"PATCH",headers:r,body:JSON.stringify(a)});if(!o.ok)throw Error((await o.json()).message||"Failed to update league ".concat(e));return await o.json()},deleteLeague:async(e,a)=>{let s=await n.getLeagueById(e,a);if(!s||!s.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));await t.x.delete("/football/leagues/".concat(s.id))},getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,a,s)=>n.updateLeague(e,{active:a},s)}},11546:function(e,a,s){"use strict";s.d(a,{TE:function(){return c},a1:function(){return o}});var t=s(57437),l=s(2265),n=s(47907),r=s(27786),i=s(96146);let o=e=>{let{children:a,requiredRole:s,fallbackUrl:o="/auth/login"}=e,c=(0,n.useRouter)(),{isAuthenticated:d,user:u,isLoading:m}=(0,r.a)();if((0,l.useEffect)(()=>{if(!m){if(!d||!u){c.push(o);return}if(s&&!(Array.isArray(s)?s:[s]).includes(u.role)){c.push("/dashboard?error=unauthorized");return}}},[d,u,m,s,c,o]),m)return(0,t.jsx)(i.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,t.jsx)(i.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(u.role))return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,t.jsx)(t.Fragment,{children:a})},c=()=>{let{user:e}=(0,r.a)(),a=a=>!!e&&(Array.isArray(a)?a:[a]).includes(e.role),s=()=>a("admin"),t=()=>a(["admin","editor"]),l=()=>a(["admin","editor","moderator"]),n=()=>s(),i=()=>t(),o=()=>l(),c=()=>s();return{user:e,hasRole:a,isAdmin:s,isEditor:t,isModerator:l,canManageUsers:n,canManageContent:i,canModerate:o,canSync:c,can:e=>{switch(e){case"manage-users":return n();case"manage-content":return i();case"moderate":return o();case"sync":return c();default:return!1}}}}},16996:function(e,a,s){"use strict";function t(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let a=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(a)}function l(e){return t(e)}function n(e){return t(e)}function r(e){return t(e)}s.d(a,{Bf:function(){return l},Fc:function(){return r},Sc:function(){return t},ou:function(){return n}})}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,952,6877,1380,2971,8069,1744],function(){return e(e.s=29792)}),_N_E=e.O()}]);