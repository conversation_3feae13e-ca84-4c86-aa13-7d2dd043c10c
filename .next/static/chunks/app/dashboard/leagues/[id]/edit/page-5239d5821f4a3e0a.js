(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5026],{2431:function(e,t,a){Promise.resolve().then(a.bind(a,56109))},56109:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return b}});var r=a(57437),s=a(2265),n=a(47907),i=a(64095),l=a(15671),o=a(575),c=a(77625),d=a(29069),u=a(85110),f=a(17818),m=a(64915),h=a(16996),g=a(53879),p=a(66260),x=a(69724),y=a(70699),v=a(56288),j=a(48763);function b(){let e=(0,n.useParams)(),t=(0,n.useRouter)(),a=(0,i.NL)(),{user:b,hasPermission:N}=(0,j.t)(),w=e.id,[k,L]=w.includes("-")?w.split("-"):[w,new Date().getFullYear().toString()],C=parseInt(k),T=parseInt(L),[S,R]=(0,s.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:"",season:T||new Date().getFullYear()}),[A,E]=(0,s.useState)(!1),[I,z]=(0,s.useState)({}),[Z,F]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!T||isNaN(T)){console.warn("Invalid season provided, redirecting to league detail page"),t.push("/dashboard/leagues/".concat(C));return}},[T,C,t]);let{league:P,isLoading:q}=(0,m.HK)(C,T),{updateLeague:_,isUpdateLoading:D}=(0,m.My)();(0,s.useEffect)(()=>{if(P&&!A){var e,t,a;R({name:P.name||"",country:P.country||"",type:(null===(e=P.type)||void 0===e?void 0:e.toLowerCase())||"",active:null===(t=P.active)||void 0===t||t,isHot:null!==(a=P.isHot)&&void 0!==a&&a,logo:P.logo||"",season:P.season||T||new Date().getFullYear()}),E(!0)}},[P,A,T]);let O=(e,t)=>{R(a=>({...a,[e]:t})),e in I&&I[e]&&z(t=>({...t,[e]:void 0}))},U=()=>{let e={};return S.name.trim()||(e.name="League name is required"),S.country.trim()||(e.country="Country is required"),S.type.trim()||(e.type="League type is required"),z(e),0===Object.keys(e).length};return q?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(c.Od,{className:"h-10 w-20"}),(0,r.jsx)(c.Od,{className:"h-8 w-48"})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(c.Od,{className:"h-6 w-32"}),(0,r.jsx)(c.Od,{className:"h-4 w-64"})]}),(0,r.jsx)(l.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(c.Od,{className:"h-20"}),(0,r.jsx)(c.Od,{className:"h-20"}),(0,r.jsx)(c.Od,{className:"h-20"}),(0,r.jsx)(c.Od,{className:"h-20"})]})})]})]}):P?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold tracking-tight",children:["Edit League ",T&&"(Season ".concat(T,")")]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Update league information and settings",T&&" for season ".concat(T)]})]})]}),(!b||!N("editor"))&&(0,r.jsxs)(d.bZ,{variant:"destructive",children:[(0,r.jsx)(x.Z,{className:"h-4 w-4"}),(0,r.jsx)(d.X,{children:b?"You need Editor permissions to edit leagues. Contact an administrator for access.":"You must be logged in to edit leagues."})]}),Z&&(0,r.jsxs)(d.bZ,{variant:"destructive",children:[(0,r.jsx)(x.Z,{className:"h-4 w-4"}),(0,r.jsx)(d.X,{children:Z})]}),(0,r.jsx)("form",{onSubmit:e=>{if(e.preventDefault(),!b||!N("editor")){F("You need Editor permissions to update leagues"),v.toast.error("Authentication required: Editor permissions needed");return}if(!P||!P.id){v.toast.error("League data not loaded. Please try again.");return}if(!U()){v.toast.error("Please fix the form errors");return}_({externalId:C,season:S.season,data:{name:S.name.trim(),country:S.country.trim(),type:S.type.trim(),active:S.active,isHot:S.isHot,...S.logo&&{logo:S.logo.trim()}}},{onSuccess:()=>{a.invalidateQueries({queryKey:["leagues",C,S.season]}),a.invalidateQueries({queryKey:["leagues"]}),v.toast.success("League updated successfully"),t.push("/dashboard/leagues/".concat(C,"-").concat(S.season))},onError:e=>{var t,a,r,s;console.error("League update error:",e),(null===(t=e.message)||void 0===t?void 0:t.includes("401"))||(null===(a=e.message)||void 0===a?void 0:a.includes("unauthorized"))?(F("Authentication failed. Please login again."),v.toast.error("Authentication failed. Please login again.")):(null===(r=e.message)||void 0===r?void 0:r.includes("403"))||(null===(s=e.message)||void 0===s?void 0:s.includes("forbidden"))?(F("You do not have permission to update this league"),v.toast.error("Permission denied: Editor access required")):v.toast.error(e.message||"Failed to update league")}})},children:(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"League Information"})]}),(0,r.jsx)(l.SZ,{children:"Edit the league details and configuration settings."})]}),(0,r.jsxs)(l.aY,{className:"space-y-6",children:[(0,r.jsx)(f.hj,{title:"Basic Information",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(f.UP,{label:"League Name",placeholder:"Enter league name",required:!0,value:S.name,onChange:e=>O("name",e.target.value),error:I.name}),(0,r.jsx)(f.UP,{label:"Country",placeholder:"Enter country",required:!0,value:S.country,onChange:e=>O("country",e.target.value),error:I.country}),(0,r.jsx)(f.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:S.type,onValueChange:e=>O("type",e),options:[{value:"league",label:"League"},{value:"cup",label:"Cup"},{value:"playoffs",label:"Playoffs"},{value:"friendly",label:"Friendly"},{value:"qualification",label:"Qualification"}],error:I.type},"league-type-".concat(A?"loaded":"loading")),(0,r.jsx)(f.UP,{label:"Season",placeholder:"Season cannot be edited",required:!0,type:"number",min:"2000",max:"2050",value:S.season.toString(),onChange:e=>O("season",parseInt(e.target.value)||new Date().getFullYear()),disabled:!0,description:"Season cannot be modified during edit. Create a new league for a different season."}),(0,r.jsx)(f.UP,{label:"Logo URL",placeholder:"Enter logo URL (optional)",value:S.logo,onChange:e=>O("logo",e.target.value)})]})}),(0,r.jsx)(f.hj,{title:"Settings",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(u.Z,{checked:S.active,onCheckedChange:e=>O("active",e),label:"Active Status",description:"Enable or disable this league",variant:"success",size:"md"})}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(u.Z,{checked:S.isHot,onCheckedChange:e=>O("isHot",e),label:"Hot League",description:"Mark as featured/popular league",variant:"danger",size:"md"})})]})})}),S.logo&&(0,r.jsx)(f.hj,{title:"Logo Preview",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("img",{src:(0,h.Fc)(S.logo)||S.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let t=e.target;t.src="",t.style.display="none"}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Logo Preview"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:S.logo})]})]})}),(0,r.jsxs)(f.iN,{children:[(0,r.jsx)(o.z,{type:"button",variant:"outline",onClick:()=>t.back(),children:"Cancel"}),(0,r.jsxs)(o.z,{type:"submit",disabled:D||!b||!N("editor"),children:[(0,r.jsx)(y.Z,{className:"w-4 h-4 mr-2"}),D?"Updating...":"Update League"]})]})]})]})})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(p.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),(0,r.jsx)("p",{className:"text-gray-500",children:"The league you're trying to edit doesn't exist or you don't have permission to edit it."})]})})})]})}},29069:function(e,t,a){"use strict";a.d(t,{X:function(){return c},bZ:function(){return o}});var r=a(57437),s=a(2265),n=a(49769),i=a(22169);let l=(0,n.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,t)=>{let{className:a,variant:s,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(l({variant:s}),a),...n})});o.displayName="Alert",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",a),...s})});c.displayName="AlertDescription"},575:function(e,t,a){"use strict";a.d(t,{d:function(){return o},z:function(){return c}});var r=a(57437),s=a(2265),n=a(59143),i=a(49769),l=a(22169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:a,variant:s,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(o({variant:s,size:i,className:a})),ref:t,...d})});c.displayName="Button"},15671:function(e,t,a){"use strict";a.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var r=a(57437),s=a(2265),n=a(22169);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...s})});i.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});l.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",a),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"},17818:function(e,t,a){"use strict";a.d(t,{ji:function(){return y},iN:function(){return j},hj:function(){return v},UP:function(){return g},mg:function(){return x},XL:function(){return p}});var r=a(57437),s=a(2265),n=a(12647),i=a(22782),l=a(3549),o=a(18641),c=a(86969),d=a(80037),u=a(22169);let f=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.fC,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...s,children:(0,r.jsx)(c.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})})});f.displayName=c.fC.displayName;var m=a(31657);let h=(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:i,required:l,className:o,children:c}=e;return(0,r.jsxs)("div",{ref:t,className:(0,u.cn)("space-y-2",o),children:[a&&(0,r.jsxs)(n._,{className:(0,u.cn)("text-sm font-medium",i&&"text-red-600"),children:[a,l&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),c,s&&!i&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:s}),i&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:i})]})});h.displayName="FormField";let g=(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:n,required:l,className:o,...c}=e;return(0,r.jsx)(h,{label:a,description:s,error:n,required:l,children:(0,r.jsx)(i.I,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",o),...c})})});g.displayName="InputField";let p=(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:n,required:i,className:o,...c}=e;return(0,r.jsx)(h,{label:a,description:s,error:n,required:i,children:(0,r.jsx)(l.g,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",o),...c})})});p.displayName="TextareaField";let x=(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:n,required:i,placeholder:l,value:c,onValueChange:d,options:f,className:m,disabled:g}=e,p=f.find(e=>e.value===c),x="http://*************";return(0,r.jsx)(h,{label:a,description:s,error:n,required:i,children:(0,r.jsxs)(o.Ph,{value:c,onValueChange:d,disabled:g,children:[(0,r.jsx)(o.i4,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",m),children:(0,r.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,r.jsx)("div",{className:"flex items-center space-x-2 flex-1",children:p?p?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[p.logo&&(0,r.jsx)("img",{src:"".concat(x,"/").concat(p.logo),alt:p.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{children:p.label})]}):l:(0,r.jsx)("span",{className:"text-muted-foreground",children:l})})})}),(0,r.jsx)(o.Bw,{children:f.map(e=>(0,r.jsx)(o.Ql,{value:e.value,disabled:e.disabled,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&(0,r.jsx)("img",{src:"".concat(x,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{children:e.label})]})},e.value))})]})})});x.displayName="SelectField";let y=(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:i,checked:l,onCheckedChange:o,className:c}=e;return(0,r.jsx)(h,{description:s,error:i,className:c,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f,{ref:t,checked:l,onCheckedChange:o,className:(0,u.cn)(i&&"border-red-500")}),a&&(0,r.jsx)(n._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",i&&"text-red-600"),children:a})]})})});y.displayName="CheckboxField",(0,s.forwardRef)((e,t)=>{let{label:a,description:s,error:i,required:l,value:o,onValueChange:c,options:d,orientation:f="vertical",className:g}=e;return(0,r.jsx)(h,{label:a,description:s,error:i,required:l,className:g,children:(0,r.jsx)(m.E,{ref:t,value:o,onValueChange:c,className:(0,u.cn)("horizontal"===f?"flex flex-row space-x-4":"space-y-2"),children:d.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(i&&"border-red-500")}),(0,r.jsx)(n._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})}).displayName="RadioField";let v=e=>{let{title:t,description:a,children:s,className:n}=e;return(0,r.jsxs)("div",{className:(0,u.cn)("space-y-4",n),children:[(t||a)&&(0,r.jsxs)("div",{className:"space-y-1",children:[t&&(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t}),a&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:a})]}),(0,r.jsx)("div",{className:"space-y-4",children:s})]})},j=e=>{let{children:t,className:a,align:s="right"}=e;return(0,r.jsx)("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===s&&"justify-start","center"===s&&"justify-center","right"===s&&"justify-end",a),children:t})}},22782:function(e,t,a){"use strict";a.d(t,{I:function(){return i}});var r=a(57437),s=a(2265),n=a(22169);let i=s.forwardRef((e,t)=>{let{className:a,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...i})});i.displayName="Input"},12647:function(e,t,a){"use strict";a.d(t,{_:function(){return c}});var r=a(57437),s=a(2265),n=a(24602),i=a(49769),l=a(22169);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.f,{ref:t,className:(0,l.cn)(o(),a),...s})});c.displayName=n.f.displayName},31657:function(e,t,a){"use strict";a.d(t,{E:function(){return o},m:function(){return c}});var r=a(57437),s=a(2265),n=a(68928),i=a(37501),l=a(22169);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.fC,{className:(0,l.cn)("grid gap-2",a),...s,ref:t})});o.displayName=n.fC.displayName;let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.ck,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,r.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,r.jsx)(i.Z,{className:"h-3.5 w-3.5 fill-primary"})})})});c.displayName=n.ck.displayName},18641:function(e,t,a){"use strict";a.d(t,{Bw:function(){return g},Ph:function(){return d},Ql:function(){return p},i4:function(){return f},ki:function(){return u}});var r=a(57437),s=a(2265),n=a(18178),i=a(23441),l=a(85159),o=a(80037),c=a(22169);let d=n.fC;n.ZA;let u=n.B4,f=s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,r.jsxs)(n.xz,{ref:t,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[s,(0,r.jsx)(n.JO,{asChild:!0,children:(0,r.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=n.xz.displayName;let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});m.displayName=n.u_.displayName;let h=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});h.displayName=n.$G.displayName;let g=s.forwardRef((e,t)=>{let{className:a,children:s,position:i="popper",...l}=e;return(0,r.jsx)(n.h_,{children:(0,r.jsxs)(n.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,r.jsx)(m,{}),(0,r.jsx)(n.l_,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(h,{})]})})});g.displayName=n.VY.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.__,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",a),...s})}).displayName=n.__.displayName;let p=s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsxs)(n.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(n.eT,{children:s})]})});p.displayName=n.ck.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=n.Z0.displayName},77625:function(e,t,a){"use strict";a.d(t,{Od:function(){return n},hM:function(){return l},q4:function(){return i}});var r=a(57437),s=a(22169);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...a})}let i=e=>{let{className:t}=e;return(0,r.jsxs)("div",{className:(0,s.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-4 w-3/4"}),(0,r.jsx)(n,{className:"h-4 w-1/2"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-2/3"})]})]})},l=e=>{let{rows:t=5,columns:a=4,className:i}=e;return(0,r.jsx)("div",{className:(0,s.cn)("space-y-4",i),children:(0,r.jsxs)("div",{className:"border rounded-lg",children:[(0,r.jsx)("div",{className:"border-b p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,r.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-full"},t))})},t))]})})}},86468:function(e,t,a){"use strict";a.d(t,{r:function(){return l}});var r=a(57437),s=a(2265),n=a(94845),i=a(22169);let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...s,ref:t,children:(0,r.jsx)(n.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=n.fC.displayName},3549:function(e,t,a){"use strict";a.d(t,{g:function(){return i}});var r=a(57437),s=a(2265),n=a(22169);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...s})});i.displayName="Textarea"},85110:function(e,t,a){"use strict";a.d(t,{Z:function(){return i}});var r=a(57437);a(2265);var s=a(86468),n=a(12647);function i(e){let{checked:t,onCheckedChange:a,label:i,description:l,disabled:o=!1,size:c="md",variant:d="default"}=e;return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(s.r,{id:i,checked:t,onCheckedChange:a,disabled:o}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(n._,{htmlFor:i,className:"font-medium cursor-pointer ".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[c]," ").concat({default:t?"text-blue-700":"text-gray-700",success:t?"text-green-700":"text-gray-700",warning:t?"text-yellow-700":"text-gray-700",danger:t?"text-red-700":"text-gray-700"}[d]," ").concat(o?"opacity-50":""),children:i}),l&&(0,r.jsx)("span",{className:"text-xs text-gray-500 ".concat(o?"opacity-50":""),children:l})]})]})}},74921:function(e,t,a){"use strict";a.d(t,{x:function(){return i}});var r=a(73107),s=a(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!a._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(a.headers.Authorization="Bearer ".concat(e),this.client(a))).catch(e=>Promise.reject(e));a._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),a.headers.Authorization="Bearer ".concat(t),this.client(a);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=s.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let a=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!a.ok)throw Error("Token refresh failed");let{accessToken:r}=await a.json(),s=e.user;if(s)return e.setAuth(s,r,t),this.setAuthToken(r),console.log("✅ Token refreshed successfully"),r}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(a=>{let{resolve:r,reject:s}=a;e?s(e):r(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let i=new n},47011:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var r=a(74921);let s=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?"".concat(e,"-").concat(t):e.toString(),r=await fetch("/api/leagues/".concat(a),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch league ".concat(e));return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let r=s(),i={"Content-Type":"application/json"};r&&(i.Authorization="Bearer ".concat(r));let l=await n.getLeagueById(e,a);if(!l||!l.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));let o=await fetch("/api/leagues/".concat(l.id),{method:"PATCH",headers:i,body:JSON.stringify(t)});if(!o.ok)throw Error((await o.json()).message||"Failed to update league ".concat(e));return await o.json()},deleteLeague:async(e,t)=>{let a=await n.getLeagueById(e,t);if(!a||!a.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await r.x.delete("/football/leagues/".concat(a.id))},getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>n.updateLeague(e,{active:t},a)}},64915:function(e,t,a){"use strict";a.d(t,{HK:function(){return o},My:function(){return c},sF:function(){return l}});var r=a(31346),s=a(64095),n=a(8186),i=a(47011);let l=function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=(0,r.a)({queryKey:["leagues",a],queryFn:()=>i.A.getLeagues(a),staleTime:6e5});return{leagues:(null===(e=s.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=s.data)||void 0===t?void 0:t.meta,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},o=(e,t)=>{let a=(0,r.a)({queryKey:["leagues",e,t],queryFn:()=>i.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},c=()=>{let e=(0,s.NL)(),t=(0,n.D)({mutationFn:e=>i.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,n.D)({mutationFn:e=>{let{externalId:t,data:a,season:r}=e;return i.A.updateLeague(t,a,r)},onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId,a.season],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,n.D)({mutationFn:e=>{let{id:t,active:a}=e;return i.A.toggleLeagueStatus(t,a)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isLoading,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isLoading,updateError:a.error,updateData:a.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},48763:function(e,t,a){"use strict";a.d(t,{t:function(){return i}});var r=a(12574),s=a(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},i=(0,r.U)()((0,s.tJ)((e,t)=>({...n,setAuth:(t,a,r)=>{e({user:t,accessToken:a,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:a=>{let r=t().user;r&&e({user:{...r,...a}})},hasPermission:e=>{let a=t().user;if(!a)return!1;let r=Array.isArray(e)?e:[e];return"admin"===a.role||(r.includes("editor")?["admin","editor"].includes(a.role):r.includes("moderator")?["admin","editor","moderator"].includes(a.role):r.includes(a.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,a){"use strict";a.d(t,{cn:function(){return n}});var r=a(75504),s=a(51367);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.m6)((0,r.W)(t))}},16996:function(e,t,a){"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://*************","/").concat(t)}function s(e){return r(e)}function n(e){return r(e)}function i(e){return r(e)}a.d(t,{Bf:function(){return s},Fc:function(){return i},Sc:function(){return r},ou:function(){return n}})}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,8041,7226,2971,8069,1744],function(){return e(e.s=2431)}),_N_E=e.O()}]);