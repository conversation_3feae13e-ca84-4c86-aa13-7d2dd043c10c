(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2785],{1859:function(e,s,t){Promise.resolve().then(t.bind(t,35655))},35655:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return eF}});var a=t(57437),n=t(47907),r=t(2265),l=t(64095),i=t(8186),c=t(15671),o=t(575),d=t(33277),m=t(77625),x=t(4133),u=t(85110),h=t(11546),g=t(64915),f=t(47011),p=t(16996),j=t(56288),N=t(53879),v=t(66260),y=t(37451),w=t(29295),b=t(50489),C=t(97404),S=t(97307),Z=t(26490),M=t(69724),F=t(40834),k=t(31346),L=t(5835),D=t(95032),T=t(34059),z=t(35420),A=t(56227),E=t(29733),I=function(e){var s,t;let{league:n,className:r=""}=e,{data:l,isLoading:i,error:o}=(t=n.externalId,(0,k.a)({queryKey:["league-statistics",t],queryFn:async()=>({totalTeams:Math.floor(20*Math.random())+16,totalFixtures:Math.floor(300*Math.random())+200,completedFixtures:Math.floor(150*Math.random())+100,upcomingFixtures:Math.floor(80*Math.random())+40,liveFixtures:Math.floor(5*Math.random()),seasonProgress:Math.floor(100*Math.random()),currentRound:"Round ".concat(Math.floor(38*Math.random())+1),coverageScore:Math.floor(5*Math.random())+6,insights:["Season is 67% complete with high activity","Above average scoring rate this season","Strong home team advantage observed"],lastUpdated:new Date().toISOString()}),staleTime:9e5,enabled:!!t}));if(i)return(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Statistics"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Array.from({length:8}).map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.Od,{className:"h-4 w-20"}),(0,a.jsx)(m.Od,{className:"h-8 w-16"})]},s))})})]});if(o||!l)return(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Statistics"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(D.Z,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-sm",children:"Statistics not available"})]})})]});let x=[{label:"Total Teams",value:l.totalTeams||0,icon:T.Z,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Total Fixtures",value:l.totalFixtures||0,icon:S.Z,color:"text-green-600",bgColor:"bg-green-50"},{label:"Completed",value:l.completedFixtures||0,icon:v.Z,color:"text-yellow-600",bgColor:"bg-yellow-50"},{label:"Upcoming",value:l.upcomingFixtures||0,icon:z.Z,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Live Matches",value:l.liveFixtures||0,icon:D.Z,color:"text-red-600",bgColor:"bg-red-50"},{label:"Season Progress",value:"".concat(l.seasonProgress||0,"%"),icon:A.Z,color:"text-indigo-600",bgColor:"bg-indigo-50"},{label:"Current Round",value:l.currentRound||"N/A",icon:E.Z,color:"text-orange-600",bgColor:"bg-orange-50"},{label:"Coverage Score",value:"".concat(l.coverageScore||0,"/10"),icon:L.Z,color:"text-teal-600",bgColor:"bg-teal-50"}];return(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Statistics"})]}),(0,a.jsxs)(d.C,{variant:"outline",className:"text-xs",children:["Season ",n.season]})]})}),(0,a.jsxs)(c.aY,{children:[(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:x.map((e,s)=>{let t=e.icon;return(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor),children:(0,a.jsx)(t,{className:"w-4 h-4 ".concat(e.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 font-medium",children:e.label}),(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.value})]})]},s)})}),l.insights&&l.insights.length>0&&(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(E.Z,{className:"w-4 h-4 mr-2"}),"Key Insights"]}),(0,a.jsx)("div",{className:"space-y-2",children:l.insights.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e})]},s))})]}),(null===(s=n.season_detail)||void 0===s?void 0:s.coverage)&&(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Coverage Breakdown"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:Object.entries(n.season_detail.coverage).map(e=>{let[s,t]=e;return"fixtures"===s?null:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:s.replace("_"," ")}),(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t?"bg-green-500":"bg-red-500")})]},s)})})]})]})]})},O=t(22782),R=t(99925),P=t(93832),U=t(28670),Y=t(65404),_=t(62457),V=t(37841),B=t(18025);let H=async(e,s)=>{try{let t=s||new Date().getFullYear();console.log("\uD83D\uDD04 Fetching teams for league:",e,"season:",t);let a=await fetch("/api/teams?league=".concat(e,"&season=").concat(t));if(!a.ok)throw Error("Failed to fetch teams: ".concat(a.status));let n=await a.json();if(console.log("✅ Teams API response:",n),!n.data||!Array.isArray(n.data))return console.warn("⚠️ No teams data in API response, using fallback"),G(e);return n.data.map(e=>{var s,t,a,n,r;return{id:(null===(s=e.externalId)||void 0===s?void 0:s.toString())||(null===(t=e.id)||void 0===t?void 0:t.toString())||Math.random().toString(),name:e.name||"Unknown Team",logo:e.logo||"",foundedYear:e.founded||new Date().getFullYear(),country:e.country||"Unknown",city:(null===(a=e.venue)||void 0===a?void 0:a.city)||"Unknown",stadium:(null===(n=e.venue)||void 0===n?void 0:n.name)||"Unknown Stadium",capacity:(null===(r=e.venue)||void 0===r?void 0:r.capacity)||0,website:"",description:"",stats:{matchesPlayed:0,wins:0,draws:0,losses:0,goalsFor:0,goalsAgainst:0,points:0,position:void 0},recentForm:[],manager:"",playersCount:0,externalId:e.externalId,code:e.code,venue:e.venue?{id:e.venue.id,name:e.venue.name,address:e.venue.address,city:e.venue.city,capacity:e.venue.capacity,surface:e.venue.surface,image:e.venue.image}:void 0,founded:e.founded}})}catch(s){return console.error("❌ Error fetching teams:",s),G(e)}},G=e=>{let s=["England","Scotland","Wales","Ireland"],t=["London","Manchester","Liverpool","Birmingham","Newcastle","Brighton","Sheffield","Burnley","Luton","Bournemouth"],a=["Emirates Stadium","Old Trafford","Anfield","Stamford Bridge","Etihad Stadium","Tottenham Hotspur Stadium","St. James' Park","American Express Community Stadium","Villa Park","London Stadium"],n=["Mikel Arteta","Erik ten Hag","J\xfcrgen Klopp","Mauricio Pochettino","Pep Guardiola","Ange Postecoglou","Eddie Howe","Roberto De Zerbi","Unai Emery","David Moyes","Roy Hodgson","Marco Silva"];return["Arsenal FC","Manchester United","Liverpool FC","Chelsea FC","Manchester City","Tottenham Hotspur","Newcastle United","Brighton & Hove","Aston Villa","West Ham United","Crystal Palace","Fulham FC","Brentford FC","Wolverhampton","Everton FC","Nottingham Forest","Bournemouth AFC","Sheffield United","Burnley FC","Luton Town"].map((r,l)=>{let i=Math.floor(38*Math.random())+10,c=Math.floor(Math.random()*i*.6),o=Math.floor(Math.random()*(i-c)*.7),d=i-c-o,m=[];for(let e=0;e<5;e++){let e=Math.random();e<.4?m.push("W"):e<.7?m.push("D"):m.push("L")}return{id:"team-".concat(e,"-").concat(l+1),name:r,logo:"/images/teams/".concat(r.toLowerCase().replace(/\s+/g,"-"),".png"),foundedYear:Math.floor(120*Math.random())+1880,country:s[Math.floor(Math.random()*s.length)],city:t[Math.floor(Math.random()*t.length)],stadium:a[Math.floor(Math.random()*a.length)],capacity:Math.floor(6e4*Math.random())+2e4,website:"https://".concat(r.toLowerCase().replace(/\s+/g,""),".com"),description:"".concat(r," is one of the most prestigious football clubs with a rich history and passionate fanbase."),stats:{matchesPlayed:i,wins:c,draws:d,losses:o,goalsFor:Math.floor(80*Math.random())+20,goalsAgainst:Math.floor(60*Math.random())+15,points:3*c+d,position:l+1},recentForm:m,manager:n[Math.floor(Math.random()*n.length)],playersCount:Math.floor(10*Math.random())+25}}).sort((e,s)=>s.stats.points-e.stats.points)},W=e=>{let{leagueId:s,season:t}=e,[a,n]=(0,r.useState)([]),[l,i]=(0,r.useState)(!0),[c,o]=(0,r.useState)(null),[d,m]=(0,r.useState)(""),[x,u]=(0,r.useState)("position"),[h,g]=(0,r.useState)("asc"),[f,p]=(0,r.useState)(""),j=async()=>{try{i(!0),o(null);let e=await H(s,t);n(e)}catch(e){o(e instanceof Error?e.message:"Failed to fetch teams")}finally{i(!1)}};(0,r.useEffect)(()=>{s&&j()},[s,t]);let N=(0,r.useMemo)(()=>{let e=a;return d&&(e=e.filter(e=>e.name.toLowerCase().includes(d.toLowerCase())||e.country.toLowerCase().includes(d.toLowerCase())||e.city.toLowerCase().includes(d.toLowerCase()))),f&&(e=e.filter(e=>e.country===f)),[...e].sort((e,s)=>{let t,a;switch(x){case"name":t=e.name.toLowerCase(),a=s.name.toLowerCase();break;case"foundedYear":t=e.foundedYear,a=s.foundedYear;break;case"country":t=e.country.toLowerCase(),a=s.country.toLowerCase();break;case"points":t=e.stats.points,a=s.stats.points;break;case"position":t=e.stats.position||999,a=s.stats.position||999;break;default:return 0}return t<a?"asc"===h?-1:1:t>a?"asc"===h?1:-1:0})},[a,d,f,x,h]),v=(0,r.useMemo)(()=>Array.from(new Set(a.map(e=>e.country))).sort(),[a]),y=(0,r.useMemo)(()=>{let e=a.length,s=v.length,t=e>0?Math.round(a.reduce((e,s)=>e+s.foundedYear,0)/e):0;return{totalTeams:e,totalCountries:s,averageFoundedYear:t,totalMatches:a.reduce((e,s)=>e+s.stats.matchesPlayed,0)}},[a,v]);return{teams:a,loading:l,error:c,searchTerm:d,setSearchTerm:m,sortBy:x,setSortBy:u,sortOrder:h,setSortOrder:g,countryFilter:f,setCountryFilter:p,filteredAndSortedTeams:N,countries:v,stats:y,refreshTeams:async()=>{await j()}}};var q=t(9208),K=t(95453),Q=t(75808),J=t(52235),$=t(81708),X=e=>{let{team:s,isOpen:t,onClose:n,onViewDetails:r}=e;if(!s)return null;let l=s.stats.matchesPlayed>0?Math.round(s.stats.wins/s.stats.matchesPlayed*100):0,i=s.stats.goalsFor-s.stats.goalsAgainst,c=e=>{switch(e){case"W":return"bg-green-500";case"L":return"bg-red-500";case"D":return"bg-yellow-500";default:return"bg-gray-500"}};return(0,a.jsx)(q.Vq,{open:t,onOpenChange:n,children:(0,a.jsxs)(q.cZ,{className:"max-w-2xl",children:[(0,a.jsxs)(q.fK,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(q.$N,{className:"text-xl font-semibold",children:"Team Quick View"}),(0,a.jsx)(o.z,{variant:"ghost",size:"icon",onClick:n,className:"h-8 w-8",children:(0,a.jsx)(J.Z,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(Q.qE,{className:"h-16 w-16",children:[(0,a.jsx)(Q.F$,{src:s.logo,alt:"".concat(s.name," logo")}),(0,a.jsx)(Q.Q5,{className:"bg-blue-500 text-white text-lg font-bold",children:s.name.substring(0,2).toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:s.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Founded ",s.foundedYear]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(_.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[s.city,", ",s.country]})]})]})]}),s.stats.position&&(0,a.jsxs)(d.C,{variant:"secondary",className:"text-lg px-3 py-1",children:["#",s.stats.position]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[s.description&&(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:s.description})}),(0,a.jsx)(K.Z,{}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.stats.points}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Points"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[l,"%"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Win Rate"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(i>=0?"text-green-600":"text-red-600"),children:[i>=0?"+":"",i]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Goal Diff"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.stats.matchesPlayed}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Matches"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-semibold mb-3 flex items-center space-x-2",children:[(0,a.jsx)(L.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Recent Form (Last 5 matches)"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[s.recentForm.map((e,s)=>(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ".concat(c(e)),children:e},s)),0===s.recentForm.length&&(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"No recent matches"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Season Statistics"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Wins:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:s.stats.wins})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Draws:"}),(0,a.jsx)("span",{className:"font-medium text-yellow-600",children:s.stats.draws})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Losses:"}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:s.stats.losses})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Goals For:"}),(0,a.jsx)("span",{className:"font-medium",children:s.stats.goalsFor})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Goals Against:"}),(0,a.jsx)("span",{className:"font-medium",children:s.stats.goalsAgainst})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Points:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:s.stats.points})]})]})]}),(0,a.jsx)(K.Z,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[s.stadium&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Stadium:"}),(0,a.jsx)("span",{className:"font-medium",children:s.stadium}),s.capacity&&(0,a.jsxs)("span",{className:"text-muted-foreground",children:["(",s.capacity.toLocaleString(),")"]})]}),s.manager&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Manager:"}),(0,a.jsx)("span",{className:"font-medium",children:s.manager})]}),s.playersCount&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Squad Size:"}),(0,a.jsxs)("span",{className:"font-medium",children:[s.playersCount," players"]})]}),s.website&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline font-medium",children:"Official Website"})]})]})]}),(0,a.jsxs)(q.cN,{className:"space-x-2",children:[(0,a.jsx)(o.z,{variant:"outline",onClick:n,children:"Close"}),r&&(0,a.jsx)(o.z,{onClick:()=>r(s.id),children:"View Full Details"})]})]})})},ee=e=>{let{leagueId:s,season:t,className:n=""}=e,[l,i]=(0,r.useState)(""),[x,u]=(0,r.useState)("grid"),[h,g]=(0,r.useState)(null),[f,j]=(0,r.useState)(!1),{teams:N,loading:y,error:w,stats:b,refreshTeams:C}=W({leagueId:s,season:t}),Z=N.filter(e=>{var s,t;return e.name.toLowerCase().includes(l.toLowerCase())||(null===(s=e.stadium)||void 0===s?void 0:s.toLowerCase().includes(l.toLowerCase()))||(null===(t=e.city)||void 0===t?void 0:t.toLowerCase().includes(l.toLowerCase()))}),M=e=>{g(e),j(!0)};return y?(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Teams"})]}),(0,a.jsx)(m.Od,{className:"h-8 w-24"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,s)=>(0,a.jsx)(m.Od,{className:"h-32"},s))})})]}):w?(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Teams"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(T.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Failed to load teams data"}),(0,a.jsx)(o.z,{variant:"outline",className:"mt-4",onClick:C,children:"Try Again"})]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Teams"}),(0,a.jsxs)(d.C,{variant:"secondary",className:"ml-2",children:[N.length," teams"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.z,{variant:"outline",size:"sm",onClick:()=>u("grid"===x?"list":"grid"),children:"grid"===x?(0,a.jsx)(R.Z,{className:"w-4 h-4"}):(0,a.jsx)(P.Z,{className:"w-4 h-4"})}),(0,a.jsx)(o.z,{variant:"outline",size:"sm",onClick:C,children:(0,a.jsx)(E.Z,{className:"w-4 h-4"})})]})]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(U.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),"                                    ",(0,a.jsx)(O.I,{placeholder:"Search teams by name, stadium, or city...",value:l,onChange:e=>i(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(Y.Z,{className:"w-4 h-4 mr-2"}),"Filter"]})]}),0===Z.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(T.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:l?"No teams found matching your search":"No teams available"})]}):(0,a.jsx)("div",{className:"grid"===x?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4":"space-y-3",children:Z.map(e=>(0,a.jsxs)("div",{className:"\n                                                            border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer\n                                                            ".concat("list"===x?"flex items-center space-x-4":"","\n                                                      "),onClick:()=>M(e),children:[(0,a.jsx)("div",{className:"\n                                                            ".concat("list"===x?"flex-shrink-0":"text-center mb-3","\n                                                      "),children:e.logo&&(0,p.Bf)(e.logo)?(0,a.jsx)("img",{src:(0,p.Bf)(e.logo)||"",alt:e.name,className:"\n                                                                              object-contain\n                                                                              ".concat("list"===x?"w-12 h-12":"w-16 h-16 mx-auto","\n                                                                        "),onError:e=>{e.target.style.display="none"}}):(0,a.jsx)("div",{className:"\n                                                                        bg-gray-100 rounded-lg flex items-center justify-center\n                                                                        ".concat("list"===x?"w-12 h-12":"w-16 h-16 mx-auto","\n                                                                  "),children:(0,a.jsx)(v.Z,{className:"w-6 h-6 text-gray-400"})})}),(0,a.jsxs)("div",{className:"\n                                                            ".concat("list"===x?"flex-1":"","\n                                                      "),children:[(0,a.jsx)("h4",{className:"\n                                                                  font-medium text-gray-900\n                                                                  ".concat("list"===x?"text-base":"text-sm mb-1","\n                                                            "),children:e.name}),e.stadium&&(0,a.jsxs)("div",{className:"\n                                                                        flex items-center text-gray-500 text-xs\n                                                                        ".concat("list"===x?"mt-1":"justify-center mt-2","\n                                                                  "),children:[(0,a.jsx)(_.Z,{className:"w-3 h-3 mr-1"}),(0,a.jsx)("span",{children:e.stadium})]}),e.foundedYear&&(0,a.jsxs)("div",{className:"\n                                                                        flex items-center text-gray-500 text-xs mt-1\n                                                                        ".concat("list"===x?"":"justify-center","\n                                                                  "),children:[(0,a.jsx)(S.Z,{className:"w-3 h-3 mr-1"}),(0,a.jsxs)("span",{children:["Founded ",e.foundedYear]})]}),"list"===x&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:e.country&&(0,a.jsx)(d.C,{variant:"secondary",className:"text-xs",children:e.country})}),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(V.Z,{className:"w-4 h-4"})})]})]}),"grid"===x&&(0,a.jsx)("div",{className:"flex items-center justify-center mt-3",children:(0,a.jsxs)(o.z,{variant:"ghost",size:"sm",children:[(0,a.jsx)(V.Z,{className:"w-4 h-4 mr-1"}),"View Details"]})})]},e.id))}),Z.length>0&&(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)(o.z,{variant:"outline",children:["View All Teams",(0,a.jsx)(B.Z,{className:"w-4 h-4 ml-2"})]})})]})]}),(0,a.jsx)(X,{team:h,isOpen:f,onClose:()=>{j(!1),g(null)}})]})},es=t(99497),et=t(75462),ea=t(15570),en=t(9230);let er=async(e,s,t)=>{try{let a=s||new Date().toISOString().split("T")[0];console.log("\uD83D\uDD04 Fetching fixtures for league:",e,"date:",a,"season:",t);let n="/api/fixtures?league=".concat(e,"&date=").concat(a);t&&(n+="&season=".concat(t));let r=await fetch(n);if(!r.ok)throw Error("Failed to fetch fixtures: ".concat(r.status));let l=await r.json();if(console.log("✅ Fixtures API response:",l),!l.data||!Array.isArray(l.data))return console.warn("⚠️ No fixtures data in API response, using fallback"),el(e);return l.data.map(e=>{var s,t,a,n,r,l,i,c;let o="scheduled";if(e.status){let s=e.status.toLowerCase();["ft","aet","pen"].includes(s)?o="finished":["1h","2h","ht","et","p"].includes(s)?o="live":["pst","canc","abd","wo"].includes(s)&&(o="pst"===s?"postponed":"cancelled")}return{id:(null===(s=e.externalId)||void 0===s?void 0:s.toString())||(null===(t=e.id)||void 0===t?void 0:t.toString())||Math.random().toString(),date:e.date||new Date().toISOString(),status:o,minute:e.elapsed||void 0,homeTeam:{id:(null===(a=e.homeTeamId)||void 0===a?void 0:a.toString())||"",name:e.homeTeamName||"Unknown Home Team",logo:e.homeTeamLogo||""},awayTeam:{id:(null===(n=e.awayTeamId)||void 0===n?void 0:n.toString())||"",name:e.awayTeamName||"Unknown Away Team",logo:e.awayTeamLogo||""},homeScore:e.goalsHome||void 0,awayScore:e.goalsAway||void 0,venue:(null===(r=e.venue)||void 0===r?void 0:r.name)||"",referee:e.referee||"",attendance:void 0,round:e.round||"",season:(null===(l=e.season)||void 0===l?void 0:l.toString())||"",externalId:e.externalId,timestamp:e.timestamp,timezone:null===(i=e.fixture)||void 0===i?void 0:i.timezone,periods:null===(c=e.fixture)||void 0===c?void 0:c.periods,goals:e.goals}})}catch(s){return console.error("❌ Error fetching fixtures:",s),el(e)}},el=e=>{let s=["Arsenal FC","Manchester United","Liverpool FC","Chelsea FC","Manchester City","Tottenham Hotspur","Newcastle United","Brighton & Hove","Aston Villa","West Ham United","Crystal Palace","Fulham FC","Brentford FC","Wolverhampton","Everton FC","Nottingham Forest","Bournemouth AFC","Sheffield United","Burnley FC","Luton Town"],t=["Emirates Stadium","Old Trafford","Anfield","Stamford Bridge","Etihad Stadium","Tottenham Hotspur Stadium","St. James' Park","American Express Community Stadium","Villa Park","London Stadium","Selhurst Park","Craven Cottage","Goodison Park","City Ground"],a=["Michael Oliver","Anthony Taylor","Craig Pawson","Stuart Attwell","Paul Tierney","Simon Hooper","Chris Kavanagh","Andy Madley","Jarred Gillett","Tim Robinson","Tony Harrington","David Coote"],n=[],r=new Date;for(let l=-14;l<=28;l++){let i=new Date(r);i.setDate(r.getDate()+l);let c=Math.floor(4*Math.random());for(let o=0;o<c;o++){let c,d,m,x;let u=Math.floor(Math.random()*s.length),h=Math.floor(Math.random()*s.length);for(;h===u;)h=Math.floor(Math.random()*s.length);let g=new Date(i);if(g.setHours(Math.floor(6*Math.random())+12),g.setMinutes(.5>Math.random()?0:30),g<r){let e=Math.random();e<.02?c="postponed":e<.03?c="cancelled":(c="finished",d=Math.floor(5*Math.random()),m=Math.floor(5*Math.random()))}else{let e=(g.getTime()-r.getTime())/36e5;e<2&&e>-2&&.1>Math.random()?(c="live",d=Math.floor(4*Math.random()),m=Math.floor(4*Math.random()),x=Math.floor(90*Math.random())+1):c="scheduled"}let f={id:"fixture-".concat(e,"-").concat(l,"-").concat(o),date:g.toISOString(),status:c,minute:x,homeTeam:{id:"team-".concat(u),name:s[u],logo:"/images/teams/".concat(s[u].toLowerCase().replace(/\s+/g,"-"),".png")},awayTeam:{id:"team-".concat(h),name:s[h],logo:"/images/teams/".concat(s[h].toLowerCase().replace(/\s+/g,"-"),".png")},homeScore:d,awayScore:m,venue:t[Math.floor(Math.random()*t.length)],referee:a[Math.floor(Math.random()*a.length)],attendance:"finished"===c?Math.floor(6e4*Math.random())+2e4:void 0,round:"Matchday ".concat(Math.floor(38*Math.random())+1),season:"2024-25"};n.push(f)}}return n.sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime())},ei=e=>{let{leagueId:s,season:t,selectedDate:a}=e,[n,l]=(0,r.useState)([]),[i,c]=(0,r.useState)(!0),[o,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(a||new Date().toISOString().split("T")[0]),u=async e=>{try{c(!0),d(null);let a=await er(s,e||m,t);l(a)}catch(e){d(e instanceof Error?e.message:"Failed to fetch fixtures")}finally{c(!1)}};(0,r.useEffect)(()=>{s&&u()},[s,t,m]);let{recentFixtures:h,upcomingFixtures:g,liveFixtures:f}=(0,r.useMemo)(()=>{let e=new Date,s=new Date(e.getTime()-864e5),t=new Date(e.getTime()+6048e5);return{recentFixtures:n.filter(t=>{let a=new Date(t.date);return"finished"===t.status&&a>=s&&a<=e}).sort((e,s)=>new Date(s.date).getTime()-new Date(e.date).getTime()).slice(0,10),upcomingFixtures:n.filter(s=>{let a=new Date(s.date);return"scheduled"===s.status&&a>e&&a<=t}).sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()).slice(0,10),liveFixtures:n.filter(e=>"live"===e.status).sort((e,s)=>(e.minute||0)-(s.minute||0))}},[n]),p=(0,r.useMemo)(()=>{let e=n.length,s=n.filter(e=>"finished"===e.status).length,t=n.filter(e=>"scheduled"===e.status).length,a=n.filter(e=>"live"===e.status).length,r=n.filter(e=>"finished"===e.status&&void 0!==e.homeScore&&void 0!==e.awayScore),l=r.reduce((e,s)=>e+(s.homeScore||0)+(s.awayScore||0),0);return{totalFixtures:e,completedFixtures:s,scheduledFixtures:t,liveFixtures:a,averageGoalsPerMatch:r.length>0?Math.round(l/r.length*10)/10:0}},[n]);return{fixtures:n,loading:i,error:o,recentFixtures:h,upcomingFixtures:g,liveFixtures:f,selectedDate:m,setSelectedDate:x,stats:p,refreshFixtures:async()=>{await u()}}};var ec=e=>{let{leagueId:s,season:t,className:n=""}=e,[l,i]=(0,r.useState)("upcoming"),{fixtures:x,loading:u,error:h,recentFixtures:g,upcomingFixtures:f,liveFixtures:j,selectedDate:N,setSelectedDate:v,stats:y,refreshFixtures:w}=ei({leagueId:s,season:t}),b=e=>{let{team:s,size:t=24}=e,[n,l]=(0,r.useState)(!1),i=(0,p.Bf)(s.logo);return(0,a.jsx)("div",{className:"w-6 h-6 flex items-center justify-center rounded",children:i&&!n?(0,a.jsx)("img",{src:i,alt:"".concat(s.name," logo"),className:"w-6 h-6 object-contain",onError:()=>l(!0)}):(0,a.jsx)("div",{className:"w-6 h-6 bg-muted rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold",children:s.name.substring(0,2).toUpperCase()})})})},C=e=>{switch(e.toLowerCase()){case"live":case"in_progress":return"bg-red-500 text-white";case"finished":case"completed":return"bg-green-500 text-white";case"scheduled":case"upcoming":return"bg-blue-500 text-white";case"postponed":return"bg-yellow-500 text-white";case"cancelled":return"bg-gray-500 text-white";default:return"bg-gray-400 text-white"}},M=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=function(e){let s=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,a.jsx)(c.Zb,{className:"group hover:shadow-sm transition-all",children:(0,a.jsxs)(c.aY,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[s&&(0,a.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,a.jsx)(S.Z,{className:"h-3 w-3 mr-1"}),M(e.date)]}),(0,a.jsxs)(d.C,{className:"text-xs ".concat(C(e.status)),children:["live"===e.status&&(0,a.jsx)(et.Z,{className:"h-2 w-2 mr-1 animate-pulse"}),e.status.toUpperCase()]})]}),e.venue&&(0,a.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,a.jsx)(_.Z,{className:"h-3 w-3 mr-1"}),(0,a.jsx)("span",{className:"truncate max-w-20",children:e.venue})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(b,{team:e.homeTeam}),(0,a.jsx)("span",{className:"font-medium text-sm truncate",children:e.homeTeam.name})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b,{team:e.awayTeam}),(0,a.jsx)("span",{className:"font-medium text-sm truncate",children:e.awayTeam.name})]})]}),(0,a.jsx)("div",{className:"text-center ml-4",children:"finished"===e.status?(0,a.jsxs)("div",{className:"text-lg font-bold",children:[(0,a.jsxs)("div",{children:[e.homeScore," - ",e.awayScore]}),e.minute&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"FT"})]}):"live"===e.status?(0,a.jsxs)("div",{className:"text-lg font-bold text-red-600",children:[(0,a.jsxs)("div",{children:[e.homeScore||0," - ",e.awayScore||0]}),(0,a.jsxs)("div",{className:"text-xs text-red-600 animate-pulse",children:[e.minute,"'"]})]}):(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(Z.Z,{className:"h-3 w-3 mr-1"}),new Date(e.date).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})]})})}),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity ml-2 h-8 w-8 p-0",onClick:()=>{console.log("View fixture details:",e.id)},children:(0,a.jsx)(V.Z,{className:"h-4 w-4"})})]}),(e.referee||e.attendance)&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-muted text-xs text-muted-foreground",children:(0,a.jsxs)("div",{className:"flex justify-between",children:[e.referee&&(0,a.jsxs)("span",{children:["Ref: ",e.referee]}),e.attendance&&(0,a.jsxs)("span",{children:["Att: ",e.attendance.toLocaleString()]})]})})]})},e.id)};return u?(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Recent & Upcoming Fixtures"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(m.Od,{className:"h-8 w-full"}),Array.from({length:5}).map((e,s)=>(0,a.jsx)(m.Od,{className:"h-20"},s))]})})]}):h?(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Recent & Upcoming Fixtures"})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-destructive",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("p",{className:"text-sm",children:["Failed to load fixtures: ",h]}),(0,a.jsxs)(o.z,{variant:"outline",onClick:w,className:"mt-4",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})})]}):(0,a.jsxs)(c.Zb,{className:n,children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Recent & Upcoming Fixtures"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[j.length>0&&(0,a.jsxs)(d.C,{variant:"destructive",className:"animate-pulse",children:[(0,a.jsx)(et.Z,{className:"h-2 w-2 mr-1"}),j.length," Live"]}),(0,a.jsx)(o.z,{variant:"outline",size:"sm",onClick:w,children:(0,a.jsx)(F.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ea.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filter by Date:"})]}),(0,a.jsx)(O.I,{type:"date",value:N,onChange:e=>v(e.target.value),className:"w-auto"}),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",onClick:()=>v(new Date().toISOString().split("T")[0]),className:"text-xs",children:"Today"})]})]}),(0,a.jsxs)(c.aY,{className:"space-y-6",children:[j.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(et.Z,{className:"h-4 w-4 text-red-500 animate-pulse"}),(0,a.jsx)("span",{className:"font-semibold text-red-600",children:"Live Now"})]}),(0,a.jsxs)(d.C,{variant:"outline",className:"text-red-600 border-red-200",children:[j.length," match",1!==j.length?"es":""]})]}),(0,a.jsx)("div",{className:"space-y-2 mb-6",children:j.map(e=>k(e,!1))})]}),(0,a.jsxs)(es.mQ,{value:l,onValueChange:e=>i(e),children:[(0,a.jsxs)(es.dr,{className:"grid w-full grid-cols-2",children:[(0,a.jsxs)(es.SP,{value:"upcoming",className:"flex items-center space-x-2",children:[(0,a.jsx)(E.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Upcoming (",f.length,")"]})]}),(0,a.jsxs)(es.SP,{value:"recent",className:"flex items-center space-x-2",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Recent (",g.length,")"]})]})]}),(0,a.jsxs)(es.nU,{value:"upcoming",className:"space-y-3 mt-4",children:[0===f.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-sm",children:"No upcoming fixtures scheduled"})]}):f.slice(0,6).map(e=>k(e)),f.length>6&&(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(en.Z,{className:"h-4 w-4 mr-2"}),"View All Upcoming Fixtures"]})})]}),(0,a.jsxs)(es.nU,{value:"recent",className:"space-y-3 mt-4",children:[0===g.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(S.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-sm",children:"No recent fixtures available"})]}):g.slice(0,6).map(e=>k(e)),g.length>6&&(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(en.Z,{className:"h-4 w-4 mr-2"}),"View All Recent Fixtures"]})})]})]}),(0,a.jsx)("div",{className:"border-t pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:y.totalFixtures}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Fixtures"})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:y.completedFixtures}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Completed"})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:y.scheduledFixtures}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Scheduled"})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:j.length}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Live Now"})]})]})}),(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsxs)(o.z,{variant:"outline",className:"w-full",children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"View Full Fixtures Calendar",(0,a.jsx)(B.Z,{className:"h-4 w-4 ml-2"})]})})]})]})},eo=t(63861),ed=t(10775),em=t(27271);let ex=async(e,s,t)=>{try{let a=s||new Date().getFullYear();console.log("\uD83D\uDD04 Fetching standings for league:",e,"season:",a);let n=await fetch("/api/standings?league=".concat(e,"&season=").concat(a).concat(t?"&format=".concat(t):""));if(!n.ok)throw Error("Failed to fetch standings: ".concat(n.status));let r=await n.json();if(console.log("✅ Standings API response:",r),!r.data||!Array.isArray(r.data))return console.warn("⚠️ No standings data in API response, using fallback"),eu(e);return r.data.map((e,s)=>{var t,a,n,r,l,i,c,o,d,m;let x=["W","L","D"],u=Array.from({length:5},()=>x[Math.floor(Math.random()*x.length)]);return{position:e.position||e.rank||s+1,team:{id:(null===(t=e.teamId)||void 0===t?void 0:t.toString())||(null===(n=e.team)||void 0===n?void 0:null===(a=n.externalId)||void 0===a?void 0:a.toString())||(s+1).toString(),name:e.teamName||(null===(r=e.team)||void 0===r?void 0:r.name)||"Team ".concat(s+1),logo:e.teamLogo||(null===(l=e.team)||void 0===l?void 0:l.logo)||""},points:e.points||0,playedGames:e.playedGames||e.played||0,wins:e.wins||e.win||0,draws:e.draws||e.draw||0,losses:e.losses||e.lose||0,goalsFor:e.goalsFor||(null===(i=e.goals)||void 0===i?void 0:i.for)||0,goalsAgainst:e.goalsAgainst||(null===(c=e.goals)||void 0===c?void 0:c.against)||0,goalDifference:void 0!==e.goalDifference?e.goalDifference:(e.goalsFor||0)-(e.goalsAgainst||0),form:u,externalId:e.externalId||e.id,teamId:e.teamId||(null===(o=e.team)||void 0===o?void 0:o.externalId),teamName:e.teamName||(null===(d=e.team)||void 0===d?void 0:d.name),teamLogo:e.teamLogo||(null===(m=e.team)||void 0===m?void 0:m.logo)}}).sort((e,s)=>e.position-s.position)}catch(e){throw console.error("❌ Error fetching standings:",e),e}},eu=e=>[{id:"33",name:"Manchester United",logo:"https://media.api-sports.io/football/teams/33.png"},{id:"50",name:"Manchester City",logo:"https://media.api-sports.io/football/teams/50.png"},{id:"42",name:"Arsenal",logo:"https://media.api-sports.io/football/teams/42.png"},{id:"40",name:"Liverpool",logo:"https://media.api-sports.io/football/teams/40.png"},{id:"49",name:"Chelsea",logo:"https://media.api-sports.io/football/teams/49.png"},{id:"47",name:"Tottenham",logo:"https://media.api-sports.io/football/teams/47.png"},{id:"34",name:"Newcastle",logo:"https://media.api-sports.io/football/teams/34.png"},{id:"66",name:"Aston Villa",logo:"https://media.api-sports.io/football/teams/66.png"},{id:"51",name:"Brighton",logo:"https://media.api-sports.io/football/teams/51.png"},{id:"39",name:"Wolves",logo:"https://media.api-sports.io/football/teams/39.png"}].map((e,s)=>{let t=20+Math.floor(10*Math.random()),a=Math.floor(Math.random()*t*.7),n=Math.floor(Math.random()*(t-a)*.6),r=t-a-n,l=2*a+r+Math.floor(10*Math.random()),i=2*n+Math.floor(Math.random()*l*.8),c=["W","L","D"];return{position:s+1,team:e,points:3*a+r,playedGames:t,wins:a,draws:r,losses:n,goalsFor:l,goalsAgainst:i,goalDifference:l-i,form:Array.from({length:5},()=>c[Math.floor(Math.random()*c.length)]),externalId:parseInt(e.id),teamId:parseInt(e.id),teamName:e.name,teamLogo:e.logo}}).sort((e,s)=>s.points-e.points||s.goalDifference-e.goalDifference),eh=e=>{let{leagueId:s,season:t,format:a="external"}=e,[n,l]=(0,r.useState)([]),[i,c]=(0,r.useState)(!0),[o,d]=(0,r.useState)(null),m=async()=>{try{c(!0),d(null);let e=await ex(s,t,a);l(e)}catch(e){console.error("Error fetching standings:",e),d(e instanceof Error?e.message:"Failed to fetch standings"),l(eu(s))}finally{c(!1)}};(0,r.useEffect)(()=>{s&&m()},[s,t,a]);let x=(0,r.useMemo)(()=>{let e=n.length,s=n.length>0?n[0]:null,t=n.reduce((e,s)=>e+s.playedGames,0),a=n.reduce((e,s)=>e+s.goalsFor,0);return{totalTeams:e,topTeam:s,avgGoalsPerGame:t>0?Math.round(a/t*100)/100:0}},[n]);return{standings:n,loading:i,error:o,refreshStandings:m,stats:x}};var eg=e=>{var s;let{leagueId:t,season:n,className:r=""}=e,{standings:l,loading:i,error:x,refreshStandings:u,stats:h}=eh({leagueId:t,season:n}),g=e=>{let{team:s}=e,t=(0,p.Bf)(s.logo);return t?(0,a.jsx)("img",{src:t,alt:"".concat(s.name," logo"),className:"w-8 h-8 object-contain",onError:e=>{let s=e.target;s.style.display="none";let t=s.nextElementSibling;t&&(t.style.display="flex")}}):(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold",children:s.name.slice(0,2).toUpperCase()})},f=e=>{let{form:s}=e;return(0,a.jsx)("div",{className:"flex space-x-1",children:s.map((e,s)=>(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("W"===e?"bg-green-500":"L"===e?"bg-red-500":"bg-yellow-500"),title:"W"===e?"Win":"L"===e?"Loss":"Draw"},s))})},j=e=>{let{position:s}=e;return 1===s?(0,a.jsx)(eo.Z,{className:"h-4 w-4 text-yellow-500"}):s<=4?(0,a.jsx)(E.Z,{className:"h-4 w-4 text-green-500"}):s>=l.length-2?(0,a.jsx)(ed.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)(em.Z,{className:"h-4 w-4 text-gray-400"})};return i?(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"League Table"})]})}),(0,a.jsx)(c.aY,{className:"space-y-3",children:[...Array(10)].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.Od,{className:"w-6 h-6"}),(0,a.jsx)(m.Od,{className:"w-8 h-8 rounded-full"}),(0,a.jsx)(m.Od,{className:"flex-1 h-4"}),(0,a.jsx)(m.Od,{className:"w-8 h-4"}),(0,a.jsx)(m.Od,{className:"w-12 h-4"})]},s))})]}):x?(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"League Table"})]}),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",onClick:u,children:(0,a.jsx)(F.Z,{className:"h-4 w-4"})})]})}),(0,a.jsx)(c.aY,{children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(L.Z,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Unable to load standings"}),(0,a.jsx)(o.z,{variant:"outline",size:"sm",onClick:u,className:"mt-2",children:"Try Again"})]})})]}):(0,a.jsxs)(c.Zb,{className:r,children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"League Table"}),(0,a.jsx)(d.C,{variant:"secondary",className:"ml-2",children:n||new Date().getFullYear()})]}),(0,a.jsxs)(o.z,{variant:"ghost",size:"sm",onClick:u,className:"text-xs",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 mr-1"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[(0,a.jsx)(T.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Teams"})]}),(0,a.jsx)("div",{className:"font-semibold",children:h.totalTeams})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[(0,a.jsx)(eo.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Leader"})]}),(0,a.jsx)("div",{className:"font-semibold text-xs",children:(null===(s=h.topTeam)||void 0===s?void 0:s.team.name.substring(0,10))||"N/A"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-muted-foreground",children:[(0,a.jsx)(A.Z,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Avg Goals"})]}),(0,a.jsx)("div",{className:"font-semibold",children:h.avgGoalsPerGame})]})]})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground mb-3 px-2",children:[(0,a.jsx)("div",{className:"col-span-1 text-center",children:"#"}),(0,a.jsx)("div",{className:"col-span-4",children:"Team"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"P"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"W"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"D"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"L"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"GD"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"Pts"}),(0,a.jsx)("div",{className:"col-span-1 text-center",children:"Form"})]}),(0,a.jsx)("div",{className:"space-y-2",children:l.map((e,s)=>(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-2 items-center p-2 rounded-lg hover:bg-muted/50 transition-colors ".concat(s<4?"bg-green-50 border-l-4 border-green-500":s>=l.length-3?"bg-red-50 border-l-4 border-red-500":"bg-background"),children:[(0,a.jsx)("div",{className:"col-span-1 text-center font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm",children:e.position}),(0,a.jsx)(j,{position:e.position})]})}),(0,a.jsxs)("div",{className:"col-span-4 flex items-center space-x-2",children:[(0,a.jsx)(g,{team:e.team}),(0,a.jsx)("div",{className:"min-w-0 flex-1",children:(0,a.jsx)("p",{className:"font-medium text-sm truncate",children:e.team.name})})]}),(0,a.jsx)("div",{className:"col-span-1 text-center text-sm",children:e.playedGames}),(0,a.jsx)("div",{className:"col-span-1 text-center text-sm font-medium text-green-600",children:e.wins}),(0,a.jsx)("div",{className:"col-span-1 text-center text-sm font-medium text-yellow-600",children:e.draws}),(0,a.jsx)("div",{className:"col-span-1 text-center text-sm font-medium text-red-600",children:e.losses}),(0,a.jsxs)("div",{className:"col-span-1 text-center text-sm font-medium ".concat(e.goalDifference>0?"text-green-600":e.goalDifference<0?"text-red-600":"text-muted-foreground"),children:[e.goalDifference>0?"+":"",e.goalDifference]}),(0,a.jsx)("div",{className:"col-span-1 text-center text-sm font-bold",children:e.points}),(0,a.jsx)("div",{className:"col-span-1 flex justify-center",children:(0,a.jsx)(f,{form:e.form})})]},e.team.id))}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-1 bg-green-500 rounded"}),(0,a.jsx)("span",{children:"Champions League"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-1 bg-red-500 rounded"}),(0,a.jsx)("span",{children:"Relegation"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"W"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsx)("span",{children:"D"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("span",{children:"L"})]})]})]})})]})]})},ef=t(34645),ep=t(14440),ej=t(34187),eN=t(62985),ev=t(44715),ey=t(69475),ew=t(29910),eb=t(32805),eC=t(19039),eS=t(93276);let eZ=e=>{let{leagueId:s}=e,[t,a]=(0,r.useState)({status:"idle",progress:0}),[n,l]=(0,r.useState)({status:"idle",progress:0}),[i,c]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=localStorage.getItem("league-".concat(s,"-last-sync"));e&&c(e)},[s]);let o="syncing"!==t.status&&(!i||Date.now()-new Date(i).getTime()>3e5),d="exporting"!==n.status,m=async()=>{if(o){a({status:"syncing",progress:0,currentStep:"Initializing sync..."});try{let e=[{step:"Connecting to API...",duration:1e3},{step:"Fetching teams data...",duration:1500},{step:"Updating team information...",duration:1200},{step:"Fetching fixtures data...",duration:2e3},{step:"Processing fixture results...",duration:1800},{step:"Updating league statistics...",duration:1e3},{step:"Finalizing sync...",duration:500}];for(let s=0;s<e.length;s++){let{step:t,duration:n}=e[s],r=Math.round((s+1)/e.length*100);a({status:"syncing",progress:r,currentStep:t}),await new Promise(e=>setTimeout(e,n))}let t=new Date().toISOString();c(t),localStorage.setItem("league-".concat(s,"-last-sync"),t),a({status:"success",progress:100,message:"Data synchronized successfully"}),setTimeout(()=>{a({status:"idle",progress:0})},3e3)}catch(e){a({status:"error",progress:0,message:e instanceof Error?e.message:"Sync failed"}),setTimeout(()=>{a({status:"idle",progress:0})},5e3)}}},x=async e=>{if(d){l({status:"exporting",progress:0,currentStep:"Preparing export..."});try{let t=[{step:"Collecting league data...",duration:800},{step:"Gathering team information...",duration:1200},{step:"Processing fixtures data...",duration:1500},{step:"Compiling statistics...",duration:1e3},{step:"Generating ".concat(e.toUpperCase()," file..."),duration:2e3},{step:"Preparing download...",duration:500}];for(let e=0;e<t.length;e++){let{step:s,duration:a}=t[e],n=Math.round((e+1)/t.length*100);l({status:"exporting",progress:n,currentStep:s}),await new Promise(e=>setTimeout(e,a))}let a=new Blob(["Mock export data"],{type:"text/plain"}),n=URL.createObjectURL(a),r=document.createElement("a");r.href=n,r.download="league-".concat(s,"-export.").concat(e),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),l({status:"success",progress:100,message:"".concat(e.toUpperCase()," export completed successfully"),downloadUrl:n}),setTimeout(()=>{l({status:"idle",progress:0})},3e3)}catch(e){l({status:"error",progress:0,message:e instanceof Error?e.message:"Export failed"}),setTimeout(()=>{l({status:"idle",progress:0})},5e3)}}};return{syncStatus:t,exportStatus:n,lastSyncTime:i,canSync:o,canExport:d,syncData:m,exportData:x,shareLeague:async()=>{try{let e={title:"League ".concat(s),text:"Check out this league on our sports management platform",url:"".concat(window.location.origin,"/dashboard/leagues/").concat(s)};if(navigator.share&&navigator.canShare(e))await navigator.share(e);else{await navigator.clipboard.writeText(e.url);let s=document.createElement("div");s.textContent="League URL copied to clipboard!",s.style.cssText="\n          position: fixed;\n          top: 20px;\n          right: 20px;\n          background: #4ade80;\n          color: white;\n          padding: 12px 16px;\n          border-radius: 8px;\n          font-size: 14px;\n          z-index: 10000;\n        ",document.body.appendChild(s),setTimeout(()=>{document.body.removeChild(s)},3e3)}}catch(e){console.error("Failed to share:",e)}},refreshAll:async()=>{try{["league-".concat(s,"-teams"),"league-".concat(s,"-fixtures"),"league-".concat(s,"-statistics")].forEach(e=>{localStorage.removeItem(e),sessionStorage.removeItem(e)}),window.dispatchEvent(new CustomEvent("league-refresh",{detail:{leagueId:s}}));let e=document.createElement("div");e.textContent="All data refreshed successfully!",e.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        background: #3b82f6;\n        color: white;\n        padding: 12px 16px;\n        border-radius: 8px;\n        font-size: 14px;\n        z-index: 10000;\n      ",document.body.appendChild(e),setTimeout(()=>{document.body.removeChild(e)},3e3)}catch(e){console.error("Failed to refresh:",e)}}}};var eM=e=>{let{leagueId:s,className:t=""}=e,[n,l]=(0,r.useState)(!1),[i,m]=(0,r.useState)(!1),{syncStatus:x,exportStatus:u,lastSyncTime:h,canSync:g,canExport:f,syncData:p,exportData:j,shareLeague:N,refreshAll:v}=eZ({leagueId:s}),y=()=>{switch(x.status){case"syncing":return(0,a.jsx)(F.Z,{className:"h-4 w-4 animate-spin text-blue-500"});case"success":return(0,a.jsx)(ej.Z,{className:"h-4 w-4 text-green-500"});case"error":return(0,a.jsx)(eN.Z,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(Z.Z,{className:"h-4 w-4 text-gray-500"})}},w=async()=>{try{await p(),l(!1)}catch(e){console.error("Sync failed:",e)}},b=async e=>{try{await j(e),m(!1)}catch(e){console.error("Export failed:",e)}},M=async()=>{try{await N()}catch(e){console.error("Share failed:",e)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.Zb,{className:t,children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(ew.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"League Actions"})]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>l(!0),disabled:!g||"syncing"===x.status,className:"flex items-center space-x-2",children:[y(),(0,a.jsx)("span",{children:"Sync Data"})]}),(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>m(!0),disabled:!f||"exporting"===u.status,className:"flex items-center space-x-2",children:[(()=>{switch(u.status){case"exporting":return(0,a.jsx)(ev.Z,{className:"h-4 w-4 animate-pulse text-blue-500"});case"success":return(0,a.jsx)(ej.Z,{className:"h-4 w-4 text-green-500"});case"error":return(0,a.jsx)(eN.Z,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(ey.Z,{className:"h-4 w-4 text-gray-500"})}})(),(0,a.jsx)("span",{children:"Export"})]}),(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:M,className:"flex items-center space-x-2",children:[(0,a.jsx)(eb.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Share"})]}),(0,a.jsxs)(ef.h_,{children:[(0,a.jsx)(ef.$F,{asChild:!0,children:(0,a.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(eC.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"ml-2",children:"More"})]})}),(0,a.jsxs)(ef.AW,{align:"end",className:"w-56",children:[(0,a.jsx)(ef.Ju,{children:"Data Management"}),(0,a.jsxs)(ef.Xi,{onClick:v,children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 mr-2"}),"Refresh All Data"]}),(0,a.jsxs)(ef.Xi,{children:[(0,a.jsx)(eS.Z,{className:"h-4 w-4 mr-2"}),"Clear Cache"]}),(0,a.jsx)(ef.VD,{}),(0,a.jsx)(ef.Ju,{children:"Reports"}),(0,a.jsxs)(ef.Xi,{children:[(0,a.jsx)(L.Z,{className:"h-4 w-4 mr-2"}),"Generate Report"]}),(0,a.jsxs)(ef.Xi,{children:[(0,a.jsx)(S.Z,{className:"h-4 w-4 mr-2"}),"Schedule Export"]}),(0,a.jsx)(ef.VD,{}),(0,a.jsxs)(ef.Xi,{children:[(0,a.jsx)(C.Z,{className:"h-4 w-4 mr-2"}),"API Settings"]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[y(),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Data Sync"})]}),(0,a.jsx)(d.C,{variant:"success"===x.status?"default":"error"===x.status?"destructive":"secondary",children:"syncing"===x.status?"Syncing...":"success"===x.status?"Up to date":"error"===x.status?"Failed":"Pending"})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last: ",(e=>{if(!e)return"Never";let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?"".concat(t,"m ago"):t<1440?"".concat(Math.floor(t/60),"h ago"):s.toLocaleDateString()})(h)]})]}),"exporting"===u.status&&(0,a.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ev.Z,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Exporting Data"})]}),(0,a.jsxs)("span",{className:"text-xs text-blue-700",children:[u.progress,"%"]})]}),(0,a.jsx)(ep.E,{value:u.progress,className:"h-2"}),(0,a.jsxs)("p",{className:"text-xs text-blue-700 mt-1",children:["Processing ",u.currentStep,"..."]})]}),"syncing"===x.status&&(0,a.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 text-blue-600 animate-spin"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Syncing Data"})]}),(0,a.jsxs)("span",{className:"text-xs text-blue-700",children:[x.progress,"%"]})]}),(0,a.jsx)(ep.E,{value:x.progress,className:"h-2"}),(0,a.jsx)("p",{className:"text-xs text-blue-700 mt-1",children:x.currentStep})]}),("error"===x.status||"error"===u.status)&&(0,a.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eN.Z,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-900",children:"error"===x.status?"Sync Failed":"Export Failed"})]}),(0,a.jsx)("p",{className:"text-xs text-red-700 mt-1",children:"error"===x.status?x.message:u.message})]})]})]})]}),(0,a.jsx)(q.Vq,{open:n,onOpenChange:l,children:(0,a.jsxs)(q.cZ,{children:[(0,a.jsxs)(q.fK,{children:[(0,a.jsx)(q.$N,{children:"Sync League Data"}),(0,a.jsx)(q.Be,{children:"This will update all league data including teams, fixtures, and statistics from the external API. The process may take a few minutes to complete."})]}),(0,a.jsx)("div",{className:"py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Teams data:"}),(0,a.jsx)("span",{className:"text-green-600",children:"✓ Ready to sync"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Fixtures data:"}),(0,a.jsx)("span",{className:"text-green-600",children:"✓ Ready to sync"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Statistics:"}),(0,a.jsx)("span",{className:"text-green-600",children:"✓ Ready to sync"})]})]})}),(0,a.jsxs)(q.cN,{children:[(0,a.jsx)(o.z,{variant:"outline",onClick:()=>l(!1),children:"Cancel"}),(0,a.jsxs)(o.z,{onClick:w,disabled:"syncing"===x.status,children:[(0,a.jsx)(F.Z,{className:"h-4 w-4 mr-2"}),"Start Sync"]})]})]})}),(0,a.jsx)(q.Vq,{open:i,onOpenChange:m,children:(0,a.jsxs)(q.cZ,{children:[(0,a.jsxs)(q.fK,{children:[(0,a.jsx)(q.$N,{children:"Export League Data"}),(0,a.jsx)(q.Be,{children:"Choose the format for exporting league data. The export will include teams, fixtures, and statistics."})]}),(0,a.jsx)("div",{className:"py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>b("csv"),className:"justify-start",disabled:"exporting"===u.status,children:[(0,a.jsx)(ey.Z,{className:"h-4 w-4 mr-2"}),"Export as CSV",(0,a.jsx)(d.C,{variant:"secondary",className:"ml-auto",children:"Recommended"})]}),(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>b("json"),className:"justify-start",disabled:"exporting"===u.status,children:[(0,a.jsx)(eS.Z,{className:"h-4 w-4 mr-2"}),"Export as JSON"]}),(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>b("pdf"),className:"justify-start",disabled:"exporting"===u.status,children:[(0,a.jsx)(ey.Z,{className:"h-4 w-4 mr-2"}),"Export as PDF Report"]})]})}),(0,a.jsx)(q.cN,{children:(0,a.jsx)(o.z,{variant:"outline",onClick:()=>m(!1),children:"Cancel"})})]})})]})};function eF(){var e;let s=(0,n.useParams)(),t=(0,n.useRouter)(),{isEditor:k,isAdmin:L}=(0,h.TE)(),[D,T]=(0,r.useState)(!1),z=(0,l.NL)(),A=s.id,[E,O]=A.includes("-")?A.split("-"):[A,void 0],R=parseInt(E),P=O?parseInt(O):void 0,{league:U,isLoading:Y,error:_}=(0,g.HK)(R,P),V=(0,i.D)({mutationFn:()=>f.A.deleteLeague(R,P),onSuccess:()=>{z.invalidateQueries({queryKey:["leagues"]}),j.toast.success("League deleted successfully"),T(!1),t.push("/dashboard/leagues")},onError:e=>{j.toast.error(e.message||"Failed to delete league"),T(!1)}}),B=(0,i.D)({mutationFn:e=>f.A.updateLeague(R,{active:e},P),onSuccess:()=>{z.invalidateQueries({queryKey:["leagues",R,P]}),z.invalidateQueries({queryKey:["leagues"]}),j.toast.success("League status updated successfully")},onError:e=>{j.toast.error(e.message||"Failed to update league status")}}),H=(0,i.D)({mutationFn:e=>f.A.updateLeague(R,{isHot:e},P),onSuccess:()=>{z.invalidateQueries({queryKey:["leagues",R,P]}),z.invalidateQueries({queryKey:["leagues"]}),j.toast.success("League hot status updated successfully")},onError:e=>{j.toast.error(e.message||"Failed to update league hot status")}});return Y?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(m.Od,{className:"h-10 w-20"}),(0,a.jsx)(m.Od,{className:"h-8 w-48"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,a.jsx)(m.Od,{className:"h-96"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(m.Od,{className:"h-64"}),(0,a.jsx)(m.Od,{className:"h-48"})]})]})]}):_||!U?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,a.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,a.jsx)(c.Zb,{children:(0,a.jsx)(c.aY,{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(v.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"The league you're looking for doesn't exist or you don't have permission to view it."})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,a.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,p.Fc)(U.logo)?(0,a.jsx)("img",{src:(0,p.Fc)(U.logo)||"",alt:U.name,className:"w-12 h-12 object-contain",onError:e=>{e.target.style.display="none"}}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(v.Z,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:U.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,a.jsx)(y.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:U.country}),U.type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"capitalize",children:U.type})]})]})]})]})]}),(k()||L())&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsx)(eM,{leagueId:R.toString()})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[k()&&(0,a.jsxs)(o.z,{onClick:()=>{t.push("/dashboard/leagues/".concat(A,"/edit"))},variant:"outline",children:[(0,a.jsx)(w.Z,{className:"w-4 h-4 mr-2"}),"Edit League"]}),L()&&(0,a.jsxs)(o.z,{variant:"destructive",onClick:()=>{T(!0)},children:[(0,a.jsx)(b.Z,{className:"w-4 h-4 mr-2"}),"Delete League"]})]})]})]}),k()||L()?(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)(u.Z,{checked:U.active,onCheckedChange:e=>B.mutate(e),label:"Active Status",description:"Enable or disable this league",disabled:B.isLoading,variant:"success"}),(0,a.jsx)(u.Z,{checked:U.isHot||!1,onCheckedChange:e=>H.mutate(e),label:"Hot League",description:"Mark as featured/popular league",disabled:H.isLoading,variant:"danger"})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.C,{variant:U.active?"default":"secondary",children:U.active?"Active":"Inactive"}),U.isHot&&(0,a.jsxs)(d.C,{variant:"destructive",children:[(0,a.jsx)(C.Z,{className:"w-3 h-3 mr-1"}),"Hot League"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Information"})]})}),(0,a.jsx)(c.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"League Name"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:U.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Country"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.ou)(U.countryFlag)&&(0,a.jsx)("img",{src:(0,p.ou)(U.countryFlag)||"",alt:U.country,className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-lg font-medium",children:U.country})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"League Type"}),(0,a.jsx)("p",{className:"text-lg font-medium capitalize",children:U.type||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"External ID"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:U.externalId})]})]})})]}),U.season_detail&&(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Season Details"})]})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Season Year"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:U.season_detail.year})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Start Date"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:U.season_detail.start})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"End Date"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:U.season_detail.end})]})]}),U.season_detail.current&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(d.C,{variant:"secondary",children:[(0,a.jsx)(Z.Z,{className:"w-3 h-3 mr-1"}),"Current Season"]})}),U.season_detail.coverage&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Coverage Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat((null===(e=U.season_detail.coverage.fixtures)||void 0===e?void 0:e.events)?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Fixtures"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(U.season_detail.coverage.standings?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Standings"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(U.season_detail.coverage.players?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Players"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(U.season_detail.coverage.top_scorers?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Top Scorers"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{className:"text-lg",children:"Quick Stats"})}),(0,a.jsxs)(c.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)(d.C,{variant:U.active?"default":"secondary",children:U.active?"Active":"Inactive"})]}),U.isHot&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Popularity"}),(0,a.jsx)(d.C,{variant:"destructive",children:"Hot"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"League ID"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",U.externalId]})]}),U.season_detail&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Current Season"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:U.season_detail.year})]})]})]}),(0,p.Fc)(U.logo)&&(0,a.jsxs)(c.Zb,{children:[(0,a.jsx)(c.Ol,{children:(0,a.jsx)(c.ll,{className:"text-lg",children:"League Logo"})}),(0,a.jsx)(c.aY,{children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("img",{src:(0,p.Fc)(U.logo)||"",alt:U.name,className:"w-32 h-32 object-contain"})})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-8 mt-8",children:[(0,a.jsx)(I,{league:U}),(0,a.jsx)(eg,{leagueId:R.toString(),season:P}),(0,a.jsx)(ee,{leagueId:R.toString(),season:P}),(0,a.jsx)(ec,{leagueId:R.toString(),season:P})]}),(0,a.jsx)(x.u_,{isOpen:D,onClose:()=>T(!1),title:"Delete League",description:"Are you sure you want to delete this league? This action cannot be undone.",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[(0,a.jsx)(M.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the league:"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:(0,a.jsx)("strong",{children:U.name})}),(0,a.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:[U.country," • ",U.type]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)(o.z,{variant:"outline",onClick:()=>T(!1),disabled:V.isLoading,children:"Cancel"}),(0,a.jsx)(o.z,{variant:"destructive",onClick:()=>{V.mutate()},disabled:V.isLoading,children:V.isLoading?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),"Delete League"]})})]})]})})]})}},75808:function(e,s,t){"use strict";t.d(s,{F$:function(){return c},Q5:function(){return o},qE:function(){return i}});var a=t(57437),n=t(2265),r=t(27733),l=t(22169);let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.fC,{ref:s,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...n})});i.displayName=r.fC.displayName;let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.Ee,{ref:s,className:(0,l.cn)("aspect-square h-full w-full",t),...n})});c.displayName=r.Ee.displayName;let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.NY,{ref:s,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...n})});o.displayName=r.NY.displayName},33277:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var a=t(57437);t(2265);var n=t(49769),r=t(22169);let l=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(l({variant:t}),s),...n})}},575:function(e,s,t){"use strict";t.d(s,{d:function(){return c},z:function(){return o}});var a=t(57437),n=t(2265),r=t(59143),l=t(49769),i=t(22169);let c=(0,l.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,s)=>{let{className:t,variant:n,size:l,asChild:o=!1,...d}=e,m=o?r.g7:"button";return(0,a.jsx)(m,{className:(0,i.cn)(c({variant:n,size:l,className:t})),ref:s,...d})});o.displayName="Button"},9208:function(e,s,t){"use strict";t.d(s,{$N:function(){return h},Be:function(){return g},Vq:function(){return c},cN:function(){return u},cZ:function(){return m},fK:function(){return x}});var a=t(57437),n=t(2265),r=t(72936),l=t(52235),i=t(22169);let c=r.fC;r.xz;let o=r.h_;r.x8;let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.aV,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...n})});d.displayName=r.aV.displayName;let m=n.forwardRef((e,s)=>{let{className:t,children:n,...c}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(d,{}),(0,a.jsxs)(r.VY,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[n,(0,a.jsxs)(r.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.VY.displayName;let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};x.displayName="DialogHeader";let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};u.displayName="DialogFooter";let h=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.Dx,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...n})});h.displayName=r.Dx.displayName;let g=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.dk,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...n})});g.displayName=r.dk.displayName},34645:function(e,s,t){"use strict";t.d(s,{$F:function(){return m},AW:function(){return x},Ju:function(){return h},VD:function(){return g},Xi:function(){return u},h_:function(){return d}});var a=t(57437),n=t(2265),r=t(81100),l=t(37805),i=t(80037),c=t(37501),o=t(22169);let d=r.fC,m=r.xz;r.ZA,r.Uv,r.Tr,r.Ee,n.forwardRef((e,s)=>{let{className:t,inset:n,children:i,...c}=e;return(0,a.jsxs)(r.fF,{ref:s,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",n&&"pl-8",t),...c,children:[i,(0,a.jsx)(l.Z,{className:"ml-auto"})]})}).displayName=r.fF.displayName,n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.tu,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...n})}).displayName=r.tu.displayName;let x=n.forwardRef((e,s)=>{let{className:t,sideOffset:n=4,...l}=e;return(0,a.jsx)(r.Uv,{children:(0,a.jsx)(r.VY,{ref:s,sideOffset:n,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...l})})});x.displayName=r.VY.displayName;let u=n.forwardRef((e,s)=>{let{className:t,inset:n,...l}=e;return(0,a.jsx)(r.ck,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",n&&"pl-8",t),...l})});u.displayName=r.ck.displayName,n.forwardRef((e,s)=>{let{className:t,children:n,checked:l,...c}=e;return(0,a.jsxs)(r.oC,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:l,...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.wU,{children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})}),n]})}).displayName=r.oC.displayName,n.forwardRef((e,s)=>{let{className:t,children:n,...l}=e;return(0,a.jsxs)(r.Rk,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.wU,{children:(0,a.jsx)(c.Z,{className:"h-2 w-2 fill-current"})})}),n]})}).displayName=r.Rk.displayName;let h=n.forwardRef((e,s)=>{let{className:t,inset:n,...l}=e;return(0,a.jsx)(r.__,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",t),...l})});h.displayName=r.__.displayName;let g=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...n})});g.displayName=r.Z0.displayName},22782:function(e,s,t){"use strict";t.d(s,{I:function(){return l}});var a=t(57437),n=t(2265),r=t(22169);let l=n.forwardRef((e,s)=>{let{className:t,type:n,...l}=e;return(0,a.jsx)("input",{type:n,className:(0,r.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...l})});l.displayName="Input"},14440:function(e,s,t){"use strict";t.d(s,{E:function(){return l}});var a=t(57437),n=t(2265),r=t(22169);let l=n.forwardRef((e,s)=>{let{className:t,value:n=0,max:l=100,showValue:i=!1,size:c="md",variant:o="default",...d}=e,m=Math.min(Math.max(n/l*100,0),100);return(0,a.jsxs)("div",{ref:s,className:(0,r.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[c],t),...d,children:[(0,a.jsx)("div",{className:(0,r.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[o]),style:{width:"".concat(m,"%")}}),i&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(m),"%"]})})]})});l.displayName="Progress"},95453:function(e,s,t){"use strict";t.d(s,{Z:function(){return i}});var a=t(57437),n=t(2265),r=t(51014),l=t(22169);let i=n.forwardRef((e,s)=>{let{className:t,orientation:n="horizontal",decorative:i=!0,...c}=e;return(0,a.jsx)(r.f,{ref:s,decorative:i,orientation:n,className:(0,l.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",t),...c})});i.displayName=r.f.displayName},99497:function(e,s,t){"use strict";t.d(s,{SP:function(){return o},dr:function(){return c},mQ:function(){return i},nU:function(){return d}});var a=t(57437),n=t(2265),r=t(64694),l=t(22169);let i=r.fC,c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.aV,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...n})});c.displayName=r.aV.displayName;let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.xz,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...n})});o.displayName=r.xz.displayName;let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.VY,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...n})});d.displayName=r.VY.displayName}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,1953,3411,2397,81,6877,5543,2971,8069,1744],function(){return e(e.s=1859)}),_N_E=e.O()}]);