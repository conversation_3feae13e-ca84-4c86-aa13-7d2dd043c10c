(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2175],{55567:function(e,s,a){Promise.resolve().then(a.bind(a,43283))},43283:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return E}});var t=a(57437),l=a(2265),r=a(47907),n=a(64095),i=a(8186),c=a(15671),d=a(575),o=a(22782),x=a(33277),u=a(22632),m=a(4133),h=a(85110),g=a(11546),j=a(64915),v=a(47011),p=a(16996),N=a(56288),y=a(66260),f=a(37841),b=a(29295),w=a(50489),C=a(70094),S=a(15570),k=a(37451),L=a(65404),F=a(28670),Z=a(69724),A=a(12647),I=a(44715),z=a(40834),_=a(62985);function O(){var e;let s=new Date().getFullYear(),[a,r]=(0,l.useState)(s.toString()),[n,u]=(0,l.useState)(null),m=(0,i.D)({mutationFn:async e=>{let s=await fetch("/api/leagues/sync?season=".concat(e,"&newdb=true"),{method:"GET"});if(!s.ok)throw Error((await s.json()).error||"Failed to sync leagues");return s.json()},onSuccess:e=>{var s;u(e);let t=(null===(s=e.response)||void 0===s?void 0:s.length)||0;N.toast.success("Successfully synced ".concat(t," leagues for season ").concat(a))},onError:e=>{console.error("Sync error:",e),u({error:e.message}),N.toast.error("Sync failed: ".concat(e.message))}}),h=e=>{(""===e||/^\d{1,4}$/.test(e))&&r(e)};return(0,t.jsxs)(c.Zb,{className:"border-blue-200 bg-blue-50/50",children:[(0,t.jsxs)(c.Ol,{className:"pb-3",children:[(0,t.jsxs)(c.ll,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(I.Z,{className:"w-5 h-5 text-blue-600"}),"Quick Sync Leagues"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Sync leagues data from external API for a specific season"})]}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A._,{htmlFor:"season",children:"Season Year"}),(0,t.jsx)(o.I,{id:"season",type:"text",value:a,onChange:e=>h(e.target.value),placeholder:"2025",className:"w-32",maxLength:4}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Enter the year for the season you want to sync"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(d.z,{onClick:()=>{if(!a||isNaN(Number(a))){N.toast.error("Please enter a valid year");return}m.mutate(a)},disabled:m.isLoading||!a,className:"bg-blue-600 hover:bg-blue-700",children:m.isLoading?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(z.Z,{className:"w-4 h-4 mr-2 animate-spin"}),"Syncing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(I.Z,{className:"w-4 h-4 mr-2"}),"Sync Leagues"]})}),m.isLoading&&(0,t.jsx)(x.C,{variant:"secondary",className:"animate-pulse",children:"Please wait..."})]}),n&&(0,t.jsx)("div",{className:"pt-2 border-t",children:n.error?(0,t.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)(_.Z,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-red-800",children:"Sync Failed"}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:n.error}),n.details&&(0,t.jsx)("div",{className:"text-xs text-red-500",children:n.details})]})]}):(0,t.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-green-500 rounded-full mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-green-800",children:"Sync Completed Successfully"}),(0,t.jsxs)("div",{className:"text-xs text-green-600",children:[(null===(e=n.response)||void 0===e?void 0:e.length)||0," leagues synced for season ",a]})]})]})}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 bg-gray-50 p-2 rounded border",children:[(0,t.jsx)("strong",{children:"Note:"})," This will sync leagues data with newdb=true parameter, which may create new database entries. Use with caution."]})]})]})}function E(){var e,s;let a=(0,r.useRouter)(),A=(0,n.NL)(),{isEditor:I,isAdmin:z}=(0,g.TE)(),[_,E]=(0,l.useState)({page:1,limit:20}),[Y,D]=(0,l.useState)(""),[H,P]=(0,l.useState)(""),[T,q]=(0,l.useState)(!1),[Q,K]=(0,l.useState)(!1),[M,U]=(0,l.useState)(null),{leagues:G,leaguesMeta:R,isLoading:$,error:B}=(0,j.sF)(_),J=(0,i.D)({mutationFn:e=>{let{externalId:s,season:a}=e;return v.A.deleteLeague(s,a)},onSuccess:()=>{A.invalidateQueries({queryKey:["leagues"]}),N.toast.success("League deleted successfully"),K(!1),U(null)},onError:e=>{var s,a;console.error("Delete error:",e),N.toast.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to delete league")}}),V=(0,i.D)({mutationFn:e=>{let{externalId:s,season:a,active:t}=e;return v.A.updateLeague(s,{active:t},a)},onSuccess:()=>{A.invalidateQueries({queryKey:["leagues"]}),N.toast.success("League status updated successfully")},onError:e=>{var s,a;console.error("Toggle status error:",e),N.toast.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to update league status")}}),W=(0,i.D)({mutationFn:e=>{let{externalId:s,season:a,isHot:t}=e;return v.A.updateLeague(s,{isHot:t},a)},onSuccess:()=>{A.invalidateQueries({queryKey:["leagues"]}),N.toast.success("League hot status updated successfully")},onError:e=>{var s,a;console.error("Toggle hot error:",e),N.toast.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to update league hot status")}}),X=e=>{U(e),K(!0)},ee=e=>{D(e),E(s=>({...s,search:e||void 0,page:1}))},es=e=>{E(s=>({...s,country:e||void 0,page:1}))},ea=e=>{E(s=>({...s,active:e,page:1}))},et=()=>new Date().getFullYear(),el=()=>G?Array.from(new Set(G.map(e=>e.season))).sort((e,s)=>s-e):[],er=()=>{let e=et();return[e,e-1,e-2,e-3]},en=e=>{if("custom"===e){q(!0);return}E(s=>({...s,season:e?parseInt(e):void 0,page:1}))};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Leagues Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage football leagues, seasons, and configurations"})]}),I()&&(0,t.jsxs)(d.z,{onClick:()=>a.push("/dashboard/leagues/create"),children:[(0,t.jsx)(C.Z,{className:"w-4 h-4 mr-2"}),"Add League"]})]}),z()&&(0,t.jsx)(O,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Total Leagues"}),(0,t.jsx)(y.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==R?void 0:null===(e=R.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading..."}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all countries"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Active Leagues"}),(0,t.jsx)(S.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==G?void 0:G.filter(e=>e.active).length)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently running seasons"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Countries"}),(0,t.jsx)(k.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:new Set(null==G?void 0:G.map(e=>e.country)).size||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unique countries represented"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Hot Leagues"}),(0,t.jsx)(y.Z,{className:"h-4 w-4 text-red-500"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:(null==G?void 0:G.filter(e=>e.isHot).length)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Popular leagues"})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(c.ll,{className:"text-sm font-medium",children:"Active Seasons"}),(0,t.jsx)(S.Z,{className:"h-4 w-4 text-blue-500"})]}),(0,t.jsxs)(c.aY,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:el().length||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Different seasons"})]})]})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsx)(c.Ol,{children:(0,t.jsxs)(c.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(L.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Filters & Search"})]})}),(0,t.jsx)(c.aY,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(F.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(o.I,{placeholder:"Search leagues...",value:Y,onChange:e=>ee(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>es(e.target.value),value:_.country||"",children:[(0,t.jsx)("option",{value:"",children:"All Countries"}),Array.from(new Set(null==G?void 0:G.map(e=>e.country))).sort().map(e=>(0,t.jsx)("option",{value:e,children:e},e))]}),(0,t.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>en(e.target.value),value:(null===(s=_.season)||void 0===s?void 0:s.toString())||"",children:[(0,t.jsx)("option",{value:"",children:"All Seasons"}),er().map(e=>(0,t.jsx)("option",{value:e.toString(),children:e===et()?"".concat(e," (Current)"):e},e)),el().filter(e=>!er().includes(e)).map(e=>(0,t.jsx)("option",{value:e.toString(),children:e},e)),(0,t.jsx)("option",{value:"custom",children:"✏️ Custom..."})]}),(0,t.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>ea(""===e.target.value?void 0:"true"===e.target.value),value:void 0===_.active?"":_.active.toString(),children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"true",children:"Active Only"}),(0,t.jsx)("option",{value:"false",children:"Inactive Only"})]}),(0,t.jsx)(d.z,{variant:"outline",onClick:()=>{D(""),E({page:1,limit:20}),P(""),q(!1)},children:"Clear Filters"})]})})]}),(0,t.jsxs)(c.Zb,{children:[(0,t.jsx)(c.Ol,{children:(0,t.jsx)(c.ll,{children:"Leagues List"})}),(0,t.jsx)(c.aY,{children:(0,t.jsx)(u.w,{columns:[{key:"logo",title:"",render:(e,s)=>(0,t.jsx)("div",{className:"w-16 h-16 flex items-center justify-center bg-white rounded shadow border border-gray-200",children:s.logo?(0,t.jsx)("img",{src:(0,p.Fc)(s.logo)||"",alt:s.name,className:"w-14 h-14 object-contain transition-transform duration-200 hover:scale-105",onError:e=>{e.target.style.display="none"}}):(0,t.jsx)(y.Z,{className:"w-10 h-10 text-gray-400"})})},{key:"name",title:"League Name",render:(e,s)=>(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:s.name}),s.type&&(0,t.jsx)("div",{className:"text-sm text-gray-500 capitalize",children:s.type})]})},{key:"country",title:"Country",render:(e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[s.countryFlag&&(0,t.jsx)("img",{src:(0,p.ou)(s.countryFlag)||"",alt:s.country,className:"w-4 h-4"}),(0,t.jsx)("span",{children:s.country})]})},{key:"season",title:"Year",render:(e,s)=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("span",{className:"font-medium ".concat(s.season===et()?"text-blue-600":""),children:s.season}),s.season===et()&&(0,t.jsx)("div",{className:"text-xs text-blue-500",children:"Current"})]})},{key:"season_detail",title:"Season",render:(e,s)=>{let a=s.season_detail;return a?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:a.year}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[a.start," - ",a.end]}),a.current&&(0,t.jsx)(x.C,{variant:"secondary",className:"text-xs",children:"Current"})]}):(0,t.jsx)("span",{className:"text-gray-400",children:"-"})}},{key:"isHot",title:"Status",render:(e,s)=>(0,t.jsx)("div",{className:"space-y-2",children:I()||z()?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.Z,{checked:s.active,onCheckedChange:e=>V.mutate({externalId:s.externalId,season:s.season,active:e}),label:"Active",disabled:V.isLoading,variant:"success",size:"sm"}),(0,t.jsx)(h.Z,{checked:s.isHot||!1,onCheckedChange:e=>W.mutate({externalId:s.externalId,season:s.season,isHot:e}),label:"Hot",disabled:W.isLoading,variant:"danger",size:"sm"})]}):(0,t.jsxs)("div",{className:"space-y-1",children:[s.isHot&&(0,t.jsx)(x.C,{variant:"destructive",className:"text-xs",children:"Hot"}),(0,t.jsx)(x.C,{variant:s.active?"default":"secondary",className:"text-xs",children:s.active?"Active":"Inactive"})]})})},{key:"actions",title:"Actions",render:(e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.z,{variant:"ghost",size:"sm",onClick:()=>a.push("/dashboard/leagues/".concat(s.externalId,"-").concat(s.season)),children:(0,t.jsx)(f.Z,{className:"w-4 h-4"})}),I()&&(0,t.jsx)(d.z,{variant:"ghost",size:"sm",onClick:()=>a.push("/dashboard/leagues/".concat(s.externalId,"-").concat(s.season,"/edit")),children:(0,t.jsx)(b.Z,{className:"w-4 h-4"})}),z()&&(0,t.jsx)(d.z,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>X(s),children:(0,t.jsx)(w.Z,{className:"w-4 h-4"})})]})}],data:G||[],loading:$,pagination:{page:(null==R?void 0:R.currentPage)||1,limit:(null==R?void 0:R.limit)||20,total:(null==R?void 0:R.totalItems)||0,onPageChange:e=>{E(s=>({...s,page:e}))},onLimitChange:e=>{E(s=>({...s,limit:e,page:1}))}}})})]}),(0,t.jsx)(m.u_,{isOpen:Q,onClose:()=>{K(!1),U(null)},title:"Delete League",children:M&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[(0,t.jsx)(Z.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-red-800",children:"Are you sure you want to delete this league?"}),(0,t.jsx)("p",{className:"text-sm text-red-600 mt-1",children:"This action cannot be undone. All related data will be permanently removed."})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[M.logo?(0,t.jsx)("img",{src:(0,p.Fc)(M.logo)||"",alt:M.name,className:"w-8 h-8 object-contain"}):(0,t.jsx)(y.Z,{className:"w-8 h-8 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:M.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[M.countryFlag&&(0,t.jsx)("img",{src:(0,p.ou)(M.countryFlag)||"",alt:M.country,className:"w-4 h-4"}),(0,t.jsx)("span",{children:M.country}),M.type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{children:"•"}),(0,t.jsx)("span",{className:"capitalize",children:M.type})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Status:"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(x.C,{variant:M.active?"default":"secondary",className:"text-xs",children:M.active?"Active":"Inactive"}),M.isHot&&(0,t.jsx)(x.C,{variant:"destructive",className:"text-xs",children:"Hot"})]})]}),M.season_detail&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Season:"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsx)("div",{className:"font-medium",children:M.season_detail.year}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[M.season_detail.start," - ",M.season_detail.end]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(d.z,{variant:"outline",onClick:()=>{K(!1),U(null)},disabled:J.isLoading,children:"Cancel"}),(0,t.jsx)(d.z,{variant:"destructive",onClick:()=>{M&&J.mutate({externalId:M.externalId,season:M.season})},disabled:J.isLoading,children:J.isLoading?"Deleting...":"Delete League"})]})]})}),(0,t.jsx)(m.u_,{isOpen:T,onClose:()=>{q(!1),P("")},title:"Custom Season Filter",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter Season Year"}),(0,t.jsx)(o.I,{type:"number",placeholder:"e.g., 2023",value:H,onChange:e=>P(e.target.value),min:"2000",max:"2030",className:"w-full"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Please enter a year between 2000 and 2030"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(d.z,{variant:"outline",onClick:()=>{q(!1),P("")},children:"Cancel"}),(0,t.jsx)(d.z,{onClick:()=>{let e=parseInt(H);e&&e>=2e3&&e<=2030?(E(s=>({...s,season:e,page:1})),q(!1),P(""),N.toast.success("Filtering leagues for season ".concat(e))):N.toast.error("Please enter a valid year (2000-2030)")},disabled:!H,children:"Apply Filter"})]})]})})]})}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,1953,4199,6877,1380,5543,2971,8069,1744],function(){return e(e.s=55567)}),_N_E=e.O()}]);