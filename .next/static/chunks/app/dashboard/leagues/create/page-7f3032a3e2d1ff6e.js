(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5871],{31923:function(e,a,t){Promise.resolve().then(t.bind(t,83662))},83662:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return k}});var r=t(57437),s=t(2265),l=t(47907),n=t(15671),i=t(575),o=t(33277),c=t(95453),d=t(17818),u=t(85713),m=t(64915),x=t(16996),g=t(22169),h=t(53879),p=t(34187),f=t(28814),y=t(69724),j=t(66260),b=t(75879),v=t(37451),N=t(70699),w=t(56288);let L=[{value:"league",label:"League",description:"Regular league with home/away format"},{value:"cup",label:"Cup",description:"Knockout tournament format"},{value:"playoffs",label:"Playoffs",description:"End-of-season playoff tournament"},{value:"friendly",label:"Friendly",description:"Non-competitive matches"},{value:"qualification",label:"Qualification",description:"Qualifying rounds for main competition"}],I=["England","Spain","Germany","Italy","France","Brazil","Argentina","Netherlands","Portugal","International"];function k(){var e,a;let t=(0,l.useRouter)(),[k,C]=(0,s.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:"",externalId:void 0,logoFile:void 0}),[S,F]=(0,s.useState)({}),[E,Z]=(0,s.useState)(!1),{createLeague:R,isCreateLoading:P}=(0,m.My)(),A=(e,a)=>{C(t=>({...t,[e]:a})),("name"===e||"country"===e||"type"===e||"logo"===e||"externalId"===e)&&F(a=>({...a,[e]:void 0}))},T=()=>{let e={};if(k.name.trim()?k.name.trim().length<2?e.name="League name must be at least 2 characters":k.name.trim().length>100&&(e.name="League name must be less than 100 characters"):e.name="League name is required",k.country.trim()?k.country.trim().length<2&&(e.country="Country must be at least 2 characters"):e.country="Country is required",k.type.trim()||(e.type="League type is required"),k.externalId&&(k.externalId<1||!Number.isInteger(k.externalId))&&(e.externalId="External ID must be a positive integer"),k.logo&&k.logo.trim()&&!k.logo.startsWith("uploaded-file://"))try{new URL(k.logo)}catch(a){e.logo="Please enter a valid URL"}return F(e),0===Object.keys(e).length},q=()=>{let e=0;return k.name.trim()&&e++,k.country.trim()&&e++,k.type.trim()&&e++,Math.round(e/3*100)},z=k.name.trim()&&k.country.trim()&&k.type.trim(),_=e=>{if(e.preventDefault(),Z(!0),!T()){w.toast.error("Please fix the form errors before submitting"),Z(!1);return}let a=w.toast.loading("Creating league...");R({name:k.name.trim(),country:k.country.trim(),type:k.type.trim(),active:k.active,isHot:k.isHot,flag:"",season:new Date().getFullYear(),...k.logo&&{logo:k.logo.trim()},...k.externalId&&"number"==typeof k.externalId&&{externalId:k.externalId}},{onSuccess:e=>{w.toast.dismiss(a),localStorage.removeItem("league-create-draft"),w.toast.success('League "'.concat(k.name,'" created successfully!'),{description:"Redirecting to league details...",duration:3e3}),setTimeout(()=>{t.push("/dashboard/leagues/".concat(e.externalId))},500)},onError:e=>{w.toast.dismiss(a),w.toast.error(e.message||"Failed to create league. Please try again."),Z(!1)}})};return(0,s.useEffect)(()=>{let e=e=>{(e.ctrlKey||e.metaKey)&&"s"===e.key&&(e.preventDefault(),!z||P||E||_(e)),"Escape"===e.key&&t.back()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[z,P,E,t,_]),(0,s.useEffect)(()=>{let e=setTimeout(()=>{(k.name||k.country||k.type)&&localStorage.setItem("league-create-draft",JSON.stringify(k))},1e3);return()=>clearTimeout(e)},[k]),(0,s.useEffect)(()=>{let e=localStorage.getItem("league-create-draft");if(e)try{let a=JSON.parse(e);C(e=>({...e,...a})),w.toast.info("Draft restored from previous session")}catch(e){console.warn("Failed to load draft:",e)}},[]),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Create League"}),z&&(0,r.jsxs)(o.C,{variant:"secondary",className:"bg-green-100 text-green-800",children:[(0,r.jsx)(p.Z,{className:"w-3 h-3 mr-1"}),"Ready to submit"]})]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Add a new football league to the system"}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[q(),"% complete"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:(0,r.jsx)("div",{className:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:{width:"".concat(q(),"%")}})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsx)(n.Zb,{className:"border-blue-200 bg-blue-50",children:(0,r.jsxs)(n.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(f.Z,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Quick Setup"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700",children:"Fill in the basic information to get started. You can edit all details later."}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-blue-600",children:[(0,r.jsx)("kbd",{className:"px-1.5 py-0.5 bg-white rounded border",children:"Ctrl+S"})," to save quickly"]})]})}),(0,r.jsx)(n.Zb,{className:"border-green-200 bg-green-50",children:(0,r.jsxs)(n.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(p.Z,{className:"w-4 h-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Auto-Save"})]}),(0,r.jsx)("p",{className:"text-xs text-green-700",children:"Your progress is automatically saved as you type. No data loss!"}),localStorage.getItem("league-create-draft")&&(0,r.jsx)(i.z,{variant:"ghost",size:"sm",className:"mt-1 h-6 text-xs text-green-700 hover:text-green-800",onClick:()=>{localStorage.removeItem("league-create-draft"),w.toast.success("Draft cleared")},children:"Clear draft"})]})}),(0,r.jsx)(n.Zb,{className:"border-orange-200 bg-orange-50",children:(0,r.jsxs)(n.aY,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(y.Z,{className:"w-4 h-4 text-orange-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-orange-800",children:"Logo Upload"})]}),(0,r.jsx)("p",{className:"text-xs text-orange-700",children:"File upload feature requires backend API. URL method works immediately."})]})})]}),(0,r.jsx)("form",{onSubmit:_,children:(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"League Information"})]}),(0,r.jsx)(n.SZ,{children:"Enter the league details and configuration settings."})]}),(0,r.jsxs)(n.aY,{className:"space-y-6",children:[(0,r.jsx)(d.hj,{title:"Basic Information",description:"Enter the core details that identify this league",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(d.UP,{label:"League Name",placeholder:"e.g., Premier League, Champions League",required:!0,value:k.name,onChange:e=>A("name",e.target.value),error:S.name,description:"The official name of the football league"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.UP,{label:"Country",placeholder:"e.g., England, Spain, International",required:!0,value:k.country,onChange:e=>A("country",e.target.value),error:S.country,description:"The country or region where this league operates"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 mr-2",children:"Popular:"}),I.map(e=>(0,r.jsx)(o.C,{variant:"outline",className:"text-xs cursor-pointer hover:bg-blue-50 hover:border-blue-300",onClick:()=>A("country",e),children:e},e))]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:k.type,onValueChange:e=>A("type",e),options:L.map(e=>({value:e.value,label:e.label})),error:S.type,description:"Choose the format that best describes this competition"}),k.type&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.Z,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800",children:null===(e=L.find(e=>e.value===k.type))||void 0===e?void 0:e.label})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700 mt-1",children:null===(a=L.find(e=>e.value===k.type))||void 0===a?void 0:a.description})]})]}),(0,r.jsx)(d.UP,{label:"External ID (Optional)",placeholder:"Enter external API ID",type:"number",value:k.externalId||"",onChange:e=>A("externalId",e.target.value?parseInt(e.target.value):void 0),error:S.externalId,description:"Used for syncing with external APIs (e.g., Football API, ESPN)"})]})}),(0,r.jsx)(c.Z,{}),(0,r.jsx)(d.hj,{title:"Visual Identity",description:"Add a logo to make the league easily recognizable",children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6",children:(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"League Logo (Optional)"}),(0,r.jsx)(u.U,{value:k.logo,onChange:e=>A("logo",e),onFileSelect:e=>{C(a=>({...a,logoFile:e})),w.toast.info("Logo file selected. Note: File upload will be implemented when backend API is ready.")},error:S.logo,accept:"image/*",maxSize:5,placeholder:"Add league logo via URL or upload file",description:"Upload an image file or paste an image URL. Recommended size: 200x200px or larger."})]})})}),(0,r.jsx)(c.Z,{}),(0,r.jsx)(d.hj,{title:"Configuration",description:"Set the initial status and visibility settings",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.mg,{label:"Initial Status",value:k.active.toString(),onValueChange:e=>A("active","true"===e),options:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}],description:"Active leagues are visible to users and can have fixtures"}),(0,r.jsx)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:k.active?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.Z,{className:"w-3 h-3 text-green-500"}),(0,r.jsx)("span",{children:"League will be immediately available"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.Z,{className:"w-3 h-3 text-orange-500"}),(0,r.jsx)("span",{children:"League will be hidden until activated"})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.mg,{label:"Hot League",description:"Mark as hot/popular league for prominence",value:k.isHot.toString(),onValueChange:e=>A("isHot","true"===e),options:[{value:"false",label:"Normal League"},{value:"true",label:"Hot League"}]}),(0,r.jsx)("div",{className:"flex items-center space-x-2 text-xs text-gray-600",children:k.isHot?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.Z,{className:"w-3 h-3 text-yellow-500"}),(0,r.jsx)("span",{children:"Will appear in featured/popular leagues"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.Z,{className:"w-3 h-3 text-gray-400"}),(0,r.jsx)("span",{children:"Standard visibility in league listings"})]})})]})]})}),k.logo&&(0,r.jsx)(d.hj,{title:"Logo Preview",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("img",{src:(0,x.Fc)(k.logo)||k.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let a=e.target;a.src="",a.style.display="none",w.toast.error("Failed to load logo image")}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Logo Preview"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 break-all",children:k.logo})]})]})}),(0,r.jsxs)(d.iN,{children:[(0,r.jsx)(i.z,{type:"button",variant:"outline",onClick:()=>t.back(),disabled:P||E,children:"Cancel"}),(0,r.jsx)(i.z,{type:"submit",disabled:P||E||!z,className:(0,g.cn)("transition-all duration-200",z?"bg-blue-600 hover:bg-blue-700":""),children:P||E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),"Create League"]})})]})]})]})})]})}},17818:function(e,a,t){"use strict";t.d(a,{ji:function(){return y},iN:function(){return b},hj:function(){return j},UP:function(){return h},mg:function(){return f},XL:function(){return p}});var r=t(57437),s=t(2265),l=t(12647),n=t(22782),i=t(3549),o=t(18641),c=t(86969),d=t(80037),u=t(22169);let m=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(c.fC,{ref:a,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,r.jsx)(c.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(d.Z,{className:"h-4 w-4"})})})});m.displayName=c.fC.displayName;var x=t(31657);let g=(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:n,required:i,className:o,children:c}=e;return(0,r.jsxs)("div",{ref:a,className:(0,u.cn)("space-y-2",o),children:[t&&(0,r.jsxs)(l._,{className:(0,u.cn)("text-sm font-medium",n&&"text-red-600"),children:[t,i&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),c,s&&!n&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:s}),n&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:n})]})});g.displayName="FormField";let h=(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:l,required:i,className:o,...c}=e;return(0,r.jsx)(g,{label:t,description:s,error:l,required:i,children:(0,r.jsx)(n.I,{ref:a,className:(0,u.cn)(l&&"border-red-500 focus:border-red-500",o),...c})})});h.displayName="InputField";let p=(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:l,required:n,className:o,...c}=e;return(0,r.jsx)(g,{label:t,description:s,error:l,required:n,children:(0,r.jsx)(i.g,{ref:a,className:(0,u.cn)(l&&"border-red-500 focus:border-red-500",o),...c})})});p.displayName="TextareaField";let f=(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:l,required:n,placeholder:i,value:c,onValueChange:d,options:m,className:x,disabled:h}=e,p=m.find(e=>e.value===c),f="http://*************";return(0,r.jsx)(g,{label:t,description:s,error:l,required:n,children:(0,r.jsxs)(o.Ph,{value:c,onValueChange:d,disabled:h,children:[(0,r.jsx)(o.i4,{ref:a,className:(0,u.cn)(l&&"border-red-500 focus:border-red-500",x),children:(0,r.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,r.jsx)("div",{className:"flex items-center space-x-2 flex-1",children:p?p?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[p.logo&&(0,r.jsx)("img",{src:"".concat(f,"/").concat(p.logo),alt:p.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{children:p.label})]}):i:(0,r.jsx)("span",{className:"text-muted-foreground",children:i})})})}),(0,r.jsx)(o.Bw,{children:m.map(e=>(0,r.jsx)(o.Ql,{value:e.value,disabled:e.disabled,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&(0,r.jsx)("img",{src:"".concat(f,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{children:e.label})]})},e.value))})]})})});f.displayName="SelectField";let y=(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:n,checked:i,onCheckedChange:o,className:c}=e;return(0,r.jsx)(g,{description:s,error:n,className:c,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m,{ref:a,checked:i,onCheckedChange:o,className:(0,u.cn)(n&&"border-red-500")}),t&&(0,r.jsx)(l._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",n&&"text-red-600"),children:t})]})})});y.displayName="CheckboxField",(0,s.forwardRef)((e,a)=>{let{label:t,description:s,error:n,required:i,value:o,onValueChange:c,options:d,orientation:m="vertical",className:h}=e;return(0,r.jsx)(g,{label:t,description:s,error:n,required:i,className:h,children:(0,r.jsx)(x.E,{ref:a,value:o,onValueChange:c,className:(0,u.cn)("horizontal"===m?"flex flex-row space-x-4":"space-y-2"),children:d.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(n&&"border-red-500")}),(0,r.jsx)(l._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})}).displayName="RadioField";let j=e=>{let{title:a,description:t,children:s,className:l}=e;return(0,r.jsxs)("div",{className:(0,u.cn)("space-y-4",l),children:[(a||t)&&(0,r.jsxs)("div",{className:"space-y-1",children:[a&&(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a}),t&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t})]}),(0,r.jsx)("div",{className:"space-y-4",children:s})]})},b=e=>{let{children:a,className:t,align:s="right"}=e;return(0,r.jsx)("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===s&&"justify-start","center"===s&&"justify-center","right"===s&&"justify-end",t),children:a})}},18641:function(e,a,t){"use strict";t.d(a,{Bw:function(){return h},Ph:function(){return d},Ql:function(){return p},i4:function(){return m},ki:function(){return u}});var r=t(57437),s=t(2265),l=t(18178),n=t(23441),i=t(85159),o=t(80037),c=t(22169);let d=l.fC;l.ZA;let u=l.B4,m=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(l.xz,{ref:a,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[s,(0,r.jsx)(l.JO,{asChild:!0,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.xz.displayName;let x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.u_,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=l.u_.displayName;let g=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.$G,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})});g.displayName=l.$G.displayName;let h=s.forwardRef((e,a)=>{let{className:t,children:s,position:n="popper",...i}=e;return(0,r.jsx)(l.h_,{children:(0,r.jsxs)(l.VY,{ref:a,className:(0,c.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,r.jsx)(x,{}),(0,r.jsx)(l.l_,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(g,{})]})})});h.displayName=l.VY.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.__,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",t),...s})}).displayName=l.__.displayName;let p=s.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,r.jsxs)(l.ck,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.wU,{children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(l.eT,{children:s})]})});p.displayName=l.ck.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=l.Z0.displayName},95453:function(e,a,t){"use strict";t.d(a,{Z:function(){return i}});var r=t(57437),s=t(2265),l=t(51014),n=t(22169);let i=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(l.f,{ref:a,decorative:i,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...o})});i.displayName=l.f.displayName},47011:function(e,a,t){"use strict";t.d(a,{A:function(){return l}});var r=t(74921);let s=()=>{try{let a=localStorage.getItem("auth-storage");if(a){var e;let t=JSON.parse(a);return(null===(e=t.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},l={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&a.append(t,r.toString())});let t=await fetch("/api/leagues?".concat(a.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch leagues");return await t.json()},getLeagueById:async(e,a)=>{let t=a?"".concat(e,"-").concat(a):e.toString(),r=await fetch("/api/leagues/".concat(t),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch league ".concat(e));return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,a,t)=>{let r=s(),n={"Content-Type":"application/json"};r&&(n.Authorization="Bearer ".concat(r));let i=await l.getLeagueById(e,t);if(!i||!i.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));let o=await fetch("/api/leagues/".concat(i.id),{method:"PATCH",headers:n,body:JSON.stringify(a)});if(!o.ok)throw Error((await o.json()).message||"Failed to update league ".concat(e));return await o.json()},deleteLeague:async(e,a)=>{let t=await l.getLeagueById(e,a);if(!t||!t.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));await r.x.delete("/football/leagues/".concat(t.id))},getActiveLeagues:async()=>l.getLeagues({active:!0}),getLeaguesByCountry:async e=>l.getLeagues({country:e}),toggleLeagueStatus:async(e,a,t)=>l.updateLeague(e,{active:a},t)}},64915:function(e,a,t){"use strict";t.d(a,{HK:function(){return o},My:function(){return c},sF:function(){return i}});var r=t(31346),s=t(64095),l=t(8186),n=t(47011);let i=function(){var e,a;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=(0,r.a)({queryKey:["leagues",t],queryFn:()=>n.A.getLeagues(t),staleTime:6e5});return{leagues:(null===(e=s.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(a=s.data)||void 0===a?void 0:a.meta,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},o=(e,a)=>{let t=(0,r.a)({queryKey:["leagues",e,a],queryFn:()=>n.A.getLeagueById(e,a),enabled:!!e,staleTime:6e5});return{league:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},c=()=>{let e=(0,s.NL)(),a=(0,l.D)({mutationFn:e=>n.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),t=(0,l.D)({mutationFn:e=>{let{externalId:a,data:t,season:r}=e;return n.A.updateLeague(a,t,r)},onSuccess:(a,t)=>{e.setQueryData(["leagues",a.externalId,t.season],a),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,l.D)({mutationFn:e=>{let{id:a,active:t}=e;return n.A.toggleLeagueStatus(a,t)},onSuccess:a=>{e.setQueryData(["leagues",a.externalId],a),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:a.mutate,isCreateLoading:a.isLoading,createError:a.error,createData:a.data,updateLeague:t.mutate,isUpdateLoading:t.isLoading,updateError:t.error,updateData:t.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},16996:function(e,a,t){"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let a=e.startsWith("/")?e.slice(1):e;return"".concat("http://*************","/").concat(a)}function s(e){return r(e)}function l(e){return r(e)}function n(e){return r(e)}t.d(a,{Bf:function(){return s},Fc:function(){return n},Sc:function(){return r},ou:function(){return l}})}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,8041,6831,1905,2971,8069,1744],function(){return e(e.s=31923)}),_N_E=e.O()}]);