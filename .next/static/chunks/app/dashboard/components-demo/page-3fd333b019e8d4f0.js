(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{82020:function(e,s,a){Promise.resolve().then(a.bind(a,19312))},19312:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return j}});var l=a(57437),r=a(2265),n=a(15671),i=a(575),t=a(33277),c=a(22632),d=a(17818),o=a(4133),m=a(77625),u=a(37841),x=a(29295),h=a(50489);let f=[{id:1,name:"<PERSON>",email:"<EMAIL>",role:"Admin",status:"active",lastLogin:"2025-05-24"},{id:2,name:"<PERSON>",email:"<EMAIL>",role:"Editor",status:"active",lastLogin:"2025-05-23"},{id:3,name:"<PERSON>",email:"<EMAIL>",role:"Moderator",status:"inactive",lastLogin:"2025-05-20"},{id:4,name:"<PERSON>",email:"<EMAIL>",role:"Editor",status:"active",lastLogin:"2025-05-24"},{id:5,name:"Charlie <PERSON>",email:"<EMAIL>",role:"Moderator",status:"active",lastLogin:"2025-05-22"}];function j(){let[e,s]=(0,r.useState)(!1),[a,j]=(0,r.useState)(!1),[p,v]=(0,r.useState)(!1),[b,N]=(0,r.useState)(!1),[y,g]=(0,r.useState)({name:"",email:"",role:"",bio:"",notifications:!1});return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reusable Components Demo"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"Showcase of all reusable components built for the CMS"})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"DataTable Component"}),(0,l.jsx)(n.SZ,{children:"Advanced data table with sorting, filtering, and pagination"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsx)(c.w,{data:f,columns:[{key:"name",title:"Name",sortable:!0,render:e=>(0,l.jsx)("span",{className:"font-medium",children:e})},{key:"email",title:"Email",sortable:!0},{key:"role",title:"Role",sortable:!0,filterable:!0,render:e=>(0,l.jsx)(t.C,{variant:"Admin"===e?"default":"secondary",children:e})},{key:"status",title:"Status",sortable:!0,filterable:!0,render:e=>(0,l.jsx)(t.C,{variant:"active"===e?"default":"secondary",children:e})},{key:"lastLogin",title:"Last Login",sortable:!0},{key:"actions",title:"Actions",render:(e,s)=>(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(i.z,{size:"sm",variant:"outline",children:(0,l.jsx)(u.Z,{className:"h-4 w-4"})}),(0,l.jsx)(i.z,{size:"sm",variant:"outline",children:(0,l.jsx)(x.Z,{className:"h-4 w-4"})}),(0,l.jsx)(i.z,{size:"sm",variant:"outline",children:(0,l.jsx)(h.Z,{className:"h-4 w-4"})})]})}],searchable:!0,searchPlaceholder:"Search users...",emptyMessage:"No users found"})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Form Components"}),(0,l.jsx)(n.SZ,{children:"Various form fields with validation and styling"})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsxs)(d.hj,{title:"User Information",description:"Basic user details",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(d.UP,{label:"Full Name",placeholder:"Enter full name",required:!0,value:y.name,onChange:e=>g(s=>({...s,name:e.target.value}))}),(0,l.jsx)(d.UP,{label:"Email Address",type:"email",placeholder:"Enter email",required:!0,value:y.email,onChange:e=>g(s=>({...s,email:e.target.value}))})]}),(0,l.jsx)(d.mg,{label:"Role",placeholder:"Select a role",required:!0,value:y.role,onValueChange:e=>g(s=>({...s,role:e})),options:[{value:"admin",label:"Administrator"},{value:"editor",label:"Editor"},{value:"moderator",label:"Moderator"}]}),(0,l.jsx)(d.XL,{label:"Bio",placeholder:"Tell us about yourself...",description:"Optional bio information",value:y.bio,onChange:e=>g(s=>({...s,bio:e.target.value}))}),(0,l.jsx)(d.ji,{label:"Enable email notifications",checked:y.notifications,onCheckedChange:e=>g(s=>({...s,notifications:e}))})]}),(0,l.jsxs)(d.iN,{children:[(0,l.jsx)(i.z,{variant:"outline",children:"Cancel"}),(0,l.jsx)(i.z,{children:"Save Changes"})]})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Modal Components"}),(0,l.jsx)(n.SZ,{children:"Different types of modal dialogs"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(i.z,{onClick:()=>s(!0),children:"Basic Modal"}),(0,l.jsx)(i.z,{onClick:()=>j(!0),variant:"outline",children:"Confirm Modal"}),(0,l.jsx)(i.z,{onClick:()=>v(!0),variant:"outline",children:"Form Modal"})]})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{children:"Loading Skeletons"}),(0,l.jsx)(n.SZ,{children:"Loading states for different components"})]}),(0,l.jsx)(n.aY,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)(i.z,{onClick:()=>N(!b),variant:"outline",children:[b?"Hide":"Show"," Skeletons"]}),b&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Card Skeleton"}),(0,l.jsx)(m.q4,{})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Table Skeleton"}),(0,l.jsx)(m.hM,{rows:3,columns:4})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Basic Skeletons"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.Od,{className:"h-4 w-3/4"}),(0,l.jsx)(m.Od,{className:"h-4 w-1/2"}),(0,l.jsx)(m.Od,{className:"h-8 w-1/4"})]})]})]})]})})]}),(0,l.jsx)(o.u_,{isOpen:e,onClose:()=>s(!1),title:"Basic Modal",description:"This is a basic modal dialog example",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{children:"This is the content of the modal. You can put any content here."}),(0,l.jsx)("div",{className:"flex justify-end space-x-2",children:(0,l.jsx)(i.z,{variant:"outline",onClick:()=>s(!1),children:"Close"})})]})}),(0,l.jsx)(o.sm,{isOpen:a,onClose:()=>j(!1),onConfirm:()=>{console.log("Confirmed!"),j(!1)},title:"Confirm Action",message:"Are you sure you want to proceed with this action?",confirmText:"Yes, proceed",variant:"destructive"}),(0,l.jsx)(o.uB,{isOpen:p,onClose:()=>v(!1),onSubmit:()=>{console.log("Form submitted:",y),v(!1)},title:"Create New User",description:"Fill in the details to create a new user",submitText:"Create User",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(d.UP,{label:"Username",placeholder:"Enter username",required:!0}),(0,l.jsx)(d.UP,{label:"Email",type:"email",placeholder:"Enter email",required:!0}),(0,l.jsx)(d.mg,{label:"Role",placeholder:"Select role",required:!0,options:[{value:"admin",label:"Administrator"},{value:"editor",label:"Editor"},{value:"moderator",label:"Moderator"}]})]})})]})}},15671:function(e,s,a){"use strict";a.d(s,{Ol:function(){return t},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var l=a(57437),r=a(2265),n=a(22169);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...r})});i.displayName="Card";let t=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});t.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"},17818:function(e,s,a){"use strict";a.d(s,{ji:function(){return v},iN:function(){return N},hj:function(){return b},UP:function(){return f},mg:function(){return p},XL:function(){return j}});var l=a(57437),r=a(2265),n=a(12647),i=a(22782),t=a(3549),c=a(18641),d=a(86969),o=a(80037),m=a(22169);let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(d.fC,{ref:s,className:(0,m.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...r,children:(0,l.jsx)(d.z$,{className:(0,m.cn)("flex items-center justify-center text-current"),children:(0,l.jsx)(o.Z,{className:"h-4 w-4"})})})});u.displayName=d.fC.displayName;var x=a(31657);let h=(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:i,required:t,className:c,children:d}=e;return(0,l.jsxs)("div",{ref:s,className:(0,m.cn)("space-y-2",c),children:[a&&(0,l.jsxs)(n._,{className:(0,m.cn)("text-sm font-medium",i&&"text-red-600"),children:[a,t&&(0,l.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),d,r&&!i&&(0,l.jsx)("p",{className:"text-sm text-gray-500",children:r}),i&&(0,l.jsx)("p",{className:"text-sm text-red-600",children:i})]})});h.displayName="FormField";let f=(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:n,required:t,className:c,...d}=e;return(0,l.jsx)(h,{label:a,description:r,error:n,required:t,children:(0,l.jsx)(i.I,{ref:s,className:(0,m.cn)(n&&"border-red-500 focus:border-red-500",c),...d})})});f.displayName="InputField";let j=(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:n,required:i,className:c,...d}=e;return(0,l.jsx)(h,{label:a,description:r,error:n,required:i,children:(0,l.jsx)(t.g,{ref:s,className:(0,m.cn)(n&&"border-red-500 focus:border-red-500",c),...d})})});j.displayName="TextareaField";let p=(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:n,required:i,placeholder:t,value:d,onValueChange:o,options:u,className:x,disabled:f}=e,j=u.find(e=>e.value===d),p="http://*************";return(0,l.jsx)(h,{label:a,description:r,error:n,required:i,children:(0,l.jsxs)(c.Ph,{value:d,onValueChange:o,disabled:f,children:[(0,l.jsx)(c.i4,{ref:s,className:(0,m.cn)(n&&"border-red-500 focus:border-red-500",x),children:(0,l.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,l.jsx)("div",{className:"flex items-center space-x-2 flex-1",children:j?j?(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[j.logo&&(0,l.jsx)("img",{src:"".concat(p,"/").concat(j.logo),alt:j.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,l.jsx)("span",{children:j.label})]}):t:(0,l.jsx)("span",{className:"text-muted-foreground",children:t})})})}),(0,l.jsx)(c.Bw,{children:u.map(e=>(0,l.jsx)(c.Ql,{value:e.value,disabled:e.disabled,children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&(0,l.jsx)("img",{src:"".concat(p,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,l.jsx)("span",{children:e.label})]})},e.value))})]})})});p.displayName="SelectField";let v=(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:i,checked:t,onCheckedChange:c,className:d}=e;return(0,l.jsx)(h,{description:r,error:i,className:d,children:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(u,{ref:s,checked:t,onCheckedChange:c,className:(0,m.cn)(i&&"border-red-500")}),a&&(0,l.jsx)(n._,{className:(0,m.cn)("text-sm font-normal cursor-pointer",i&&"text-red-600"),children:a})]})})});v.displayName="CheckboxField",(0,r.forwardRef)((e,s)=>{let{label:a,description:r,error:i,required:t,value:c,onValueChange:d,options:o,orientation:u="vertical",className:f}=e;return(0,l.jsx)(h,{label:a,description:r,error:i,required:t,className:f,children:(0,l.jsx)(x.E,{ref:s,value:c,onValueChange:d,className:(0,m.cn)("horizontal"===u?"flex flex-row space-x-4":"space-y-2"),children:o.map(e=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(x.m,{value:e.value,disabled:e.disabled,className:(0,m.cn)(i&&"border-red-500")}),(0,l.jsx)(n._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})}).displayName="RadioField";let b=e=>{let{title:s,description:a,children:r,className:n}=e;return(0,l.jsxs)("div",{className:(0,m.cn)("space-y-4",n),children:[(s||a)&&(0,l.jsxs)("div",{className:"space-y-1",children:[s&&(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:s}),a&&(0,l.jsx)("p",{className:"text-sm text-gray-600",children:a})]}),(0,l.jsx)("div",{className:"space-y-4",children:r})]})},N=e=>{let{children:s,className:a,align:r="right"}=e;return(0,l.jsx)("div",{className:(0,m.cn)("flex space-x-2 pt-4 border-t","left"===r&&"justify-start","center"===r&&"justify-center","right"===r&&"justify-end",a),children:s})}},12647:function(e,s,a){"use strict";a.d(s,{_:function(){return d}});var l=a(57437),r=a(2265),n=a(24602),i=a(49769),t=a(22169);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(n.f,{ref:s,className:(0,t.cn)(c(),a),...r})});d.displayName=n.f.displayName},4133:function(e,s,a){"use strict";a.d(s,{sm:function(){return u},uB:function(){return x},u_:function(){return m}});var l=a(57437),r=a(2265),n=a(15669),i=a(691),t=a(52235),c=a(575),d=a(22169);let o={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},m=e=>{let{isOpen:s,onClose:a,title:m,description:u,children:x,size:h="md",showCloseButton:f=!0,closeOnOverlayClick:j=!0,className:p}=e;return(0,l.jsx)(n.u,{appear:!0,show:s,as:r.Fragment,children:(0,l.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:j?a:()=>{},children:[(0,l.jsx)(n.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,l.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,l.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,l.jsx)(n.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,l.jsxs)(i.Vq.Panel,{className:(0,d.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",o[h],p),children:[(m||f)&&(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{children:[m&&(0,l.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:m}),u&&(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:u})]}),f&&(0,l.jsx)(c.z,{variant:"ghost",size:"sm",onClick:a,className:"h-8 w-8 p-0",children:(0,l.jsx)(t.Z,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"mt-2",children:x})]})})})})]})})},u=e=>{let{isOpen:s,onClose:a,onConfirm:r,title:n="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:t="Confirm",cancelText:d="Cancel",variant:o="default",loading:u=!1}=e;return(0,l.jsx)(m,{isOpen:s,onClose:a,title:n,size:"sm",closeOnOverlayClick:!u,children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,l.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,l.jsx)(c.z,{variant:"outline",onClick:a,disabled:u,children:d}),(0,l.jsx)(c.z,{variant:"destructive"===o?"destructive":"default",onClick:r,disabled:u,children:u?"Processing...":t})]})]})})},x=e=>{let{isOpen:s,onClose:a,title:r,description:n,children:i,onSubmit:t,submitText:d="Save",cancelText:o="Cancel",loading:u=!1,size:x="md"}=e;return(0,l.jsx)(m,{isOpen:s,onClose:a,title:r,description:n,size:x,closeOnOverlayClick:!u,children:(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==t||t()},className:"space-y-4",children:[i,(0,l.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,l.jsx)(c.z,{type:"button",variant:"outline",onClick:a,disabled:u,children:o}),t&&(0,l.jsx)(c.z,{type:"submit",disabled:u,children:u?"Saving...":d})]})]})})}},31657:function(e,s,a){"use strict";a.d(s,{E:function(){return c},m:function(){return d}});var l=a(57437),r=a(2265),n=a(68928),i=a(37501),t=a(22169);let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(n.fC,{className:(0,t.cn)("grid gap-2",a),...r,ref:s})});c.displayName=n.fC.displayName;let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(n.ck,{ref:s,className:(0,t.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),...r,children:(0,l.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,l.jsx)(i.Z,{className:"h-3.5 w-3.5 fill-primary"})})})});d.displayName=n.ck.displayName},77625:function(e,s,a){"use strict";a.d(s,{Od:function(){return n},hM:function(){return t},q4:function(){return i}});var l=a(57437),r=a(22169);function n(e){let{className:s,...a}=e;return(0,l.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",s),...a})}let i=e=>{let{className:s}=e;return(0,l.jsxs)("div",{className:(0,r.cn)("border rounded-lg p-6 space-y-4",s),children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n,{className:"h-4 w-3/4"}),(0,l.jsx)(n,{className:"h-4 w-1/2"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n,{className:"h-3 w-full"}),(0,l.jsx)(n,{className:"h-3 w-full"}),(0,l.jsx)(n,{className:"h-3 w-2/3"})]})]})},t=e=>{let{rows:s=5,columns:a=4,className:i}=e;return(0,l.jsx)("div",{className:(0,r.cn)("space-y-4",i),children:(0,l.jsxs)("div",{className:"border rounded-lg",children:[(0,l.jsx)("div",{className:"border-b p-4",children:(0,l.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,l.jsx)(n,{className:"h-4 w-20"},s))})}),Array.from({length:s}).map((e,s)=>(0,l.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,l.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,l.jsx)(n,{className:"h-4 w-full"},s))})},s))]})})}},3549:function(e,s,a){"use strict";a.d(s,{g:function(){return i}});var l=a(57437),r=a(2265),n=a(22169);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...r})});i.displayName="Textarea"},22169:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var l=a(75504),r=a(51367);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.m6)((0,l.W)(s))}}},function(e){e.O(0,[2150,5481,4216,8116,3462,1953,8041,2932,1380,2971,8069,1744],function(){return e(e.s=82020)}),_N_E=e.O()}]);