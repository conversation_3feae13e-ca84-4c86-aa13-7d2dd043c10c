(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6574],{30512:function(e,t,r){Promise.resolve().then(r.bind(r,68926))},53879:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},62985:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},70699:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},34059:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,t,r){"use strict";var s=r(15313);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},68926:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return b}});var s=r(57437),a=r(2265),n=r(47907),i=r(64095),o=r(8186),l=r(15671),c=r(575),u=r(22782),d=r(12647),h=r(77625),m=r(62405),f=r(16996),g=r(56288),p=r(34059),v=r(53879),x=r(62985),y=r(70699);function b(){let e=(0,n.useParams)(),t=(0,n.useRouter)(),r=(0,i.NL)(),b=parseInt(e.id),[j,w]=(0,a.useState)({name:"",code:"",country:"",founded:"",logo:""}),{team:N,isLoading:k,error:T}=(0,m.t7)(b),R=(0,o.D)({mutationFn:async e=>(console.log("Update team data:",e),await new Promise(e=>setTimeout(e,1e3)),e),onSuccess:()=>{g.toast.success("Team updated successfully"),r.invalidateQueries({queryKey:["team",b]}),r.invalidateQueries({queryKey:["teams"]}),t.push("/dashboard/teams/".concat(b))},onError:e=>{g.toast.error("Failed to update team"),console.error("Update team error:",e)}});(0,a.useEffect)(()=>{if(N){var e;w({name:N.name||"",code:N.code||"",country:N.country||"",founded:(null===(e=N.founded)||void 0===e?void 0:e.toString())||"",logo:N.logo||""})}},[N]);let C=(e,t)=>{w(r=>({...r,[e]:t}))},S=()=>{t.push("/dashboard/teams/".concat(b))};return k?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(h.Od,{className:"h-8 w-8"}),(0,s.jsx)(h.Od,{className:"h-8 w-48"})]}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsx)(l.Ol,{children:(0,s.jsx)(h.Od,{className:"h-6 w-32"})}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsx)(h.Od,{className:"h-10 w-full"}),(0,s.jsx)(h.Od,{className:"h-10 w-full"}),(0,s.jsx)(h.Od,{className:"h-10 w-full"}),(0,s.jsx)(h.Od,{className:"h-10 w-full"})]})]})]}):T||!N?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,s.jsx)(p.Z,{className:"h-16 w-16 text-muted-foreground"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold",children:"Team Not Found"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"The team you're looking for doesn't exist or has been removed."})]}),(0,s.jsx)(c.z,{onClick:()=>t.push("/dashboard/teams"),children:"Back to Teams"})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(c.z,{variant:"ghost",size:"sm",onClick:S,className:"flex items-center gap-2",children:[(0,s.jsx)(v.Z,{className:"h-4 w-4"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Edit Team"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Update team information and details"})]})]}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsx)(l.Ol,{children:(0,s.jsxs)(l.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(p.Z,{className:"h-5 w-5"}),"Team Information"]})}),(0,s.jsx)(l.aY,{children:(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!j.name.trim()||!j.code.trim()||!j.country.trim()){g.toast.error("Please fill in all required fields");return}R.mutate(j)},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 border border-border rounded-lg overflow-hidden bg-muted flex items-center justify-center",children:j.logo?(0,s.jsx)("img",{src:(0,f.Bf)(j.logo)||"",alt:j.name,className:"w-full h-full object-contain",onError:e=>{e.currentTarget.style.display="none"}}):(0,s.jsx)(p.Z,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:j.name||"Team Name"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:j.code||"Team Code"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"name",children:"Team Name *"}),(0,s.jsx)(u.I,{id:"name",value:j.name,onChange:e=>C("name",e.target.value),placeholder:"Enter team name",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"code",children:"Team Code *"}),(0,s.jsx)(u.I,{id:"code",value:j.code,onChange:e=>C("code",e.target.value.toUpperCase()),placeholder:"e.g., MAN, CHE, LIV",maxLength:5,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"country",children:"Country *"}),(0,s.jsx)(u.I,{id:"country",value:j.country,onChange:e=>C("country",e.target.value),placeholder:"Enter country",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d._,{htmlFor:"founded",children:"Founded Year"}),(0,s.jsx)(u.I,{id:"founded",type:"number",value:j.founded,onChange:e=>C("founded",e.target.value),placeholder:"e.g., 1902",min:"1800",max:new Date().getFullYear()})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,s.jsx)(d._,{htmlFor:"logo",children:"Logo URL"}),(0,s.jsx)(u.I,{id:"logo",value:j.logo,onChange:e=>C("logo",e.target.value),placeholder:"Enter logo URL"})]})]}),(0,s.jsx)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(x.Z,{className:"h-5 w-5 text-amber-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-amber-800",children:"Development Notice"}),(0,s.jsx)("p",{className:"text-sm text-amber-700 mt-1",children:"Team editing functionality is currently under development. The API endpoint for updating teams has not been implemented yet. This form is ready for integration once the backend API is available."})]})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-3 pt-4 border-t",children:[(0,s.jsxs)(c.z,{type:"submit",disabled:R.isLoading,className:"flex items-center gap-2",children:[(0,s.jsx)(y.Z,{className:"h-4 w-4"}),R.isLoading?"Saving...":"Save Changes"]}),(0,s.jsx)(c.z,{type:"button",variant:"outline",onClick:S,disabled:R.isLoading,children:"Cancel"})]})]})})]})]})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var s=r(57437),a=r(2265),n=r(59143),i=r(49769),o=r(22169);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...u}=e,d=c?n.g7:"button";return(0,s.jsx)(d,{className:(0,o.cn)(l({variant:a,size:i,className:r})),ref:t,...u})});c.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return u},ll:function(){return l}});var s=r(57437),a=r(2265),n=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...a})});i.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},22782:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(22169);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...i})});i.displayName="Input"},12647:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var s=r(57437),a=r(2265),n=r(24602),i=r(49769),o=r(22169);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,o.cn)(l(),r),...a})});c.displayName=n.f.displayName},77625:function(e,t,r){"use strict";r.d(t,{Od:function(){return n},hM:function(){return o},q4:function(){return i}});var s=r(57437),a=r(22169);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...r})}let i=e=>{let{className:t}=e;return(0,s.jsxs)("div",{className:(0,a.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n,{className:"h-4 w-3/4"}),(0,s.jsx)(n,{className:"h-4 w-1/2"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n,{className:"h-3 w-full"}),(0,s.jsx)(n,{className:"h-3 w-full"}),(0,s.jsx)(n,{className:"h-3 w-2/3"})]})]})},o=e=>{let{rows:t=5,columns:r=4,className:i}=e;return(0,s.jsx)("div",{className:(0,a.cn)("space-y-4",i),children:(0,s.jsxs)("div",{className:"border rounded-lg",children:[(0,s.jsx)("div",{className:"border-b p-4",children:(0,s.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,s.jsx)(n,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,s.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,s.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,s.jsx)(n,{className:"h-4 w-full"},t))})},t))]})})}},74921:function(e,t,r){"use strict";r.d(t,{x:function(){return i}});var s=r(73107),a=r(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:s}=await r.json(),a=e.user;if(a)return e.setAuth(a,s,t),this.setAuthToken(s),console.log("✅ Token refreshed successfully"),s}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:s,reject:a}=r;e?a(e):s(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let i=new n},33016:function(e,t,r){"use strict";r.d(t,{k:function(){return a}});var s=r(74921);let a={getTeams:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=await fetch("/api/teams?".concat(t.toString()),{method:"GET",headers:(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let s=JSON.parse(r),a=null===(t=s.state)||void 0===t?void 0:t.accessToken;if(a)return e.Authorization="Bearer ".concat(a),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");r&&(e.Authorization="Bearer ".concat(r))}return e})()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch teams");return await r.json()},getTeamById:async e=>await s.x.get("/football/teams/".concat(e)),getTeamStatistics:async(e,t,r)=>{let a=new URLSearchParams({league:e.toString(),season:t.toString(),team:r.toString()});return await s.x.get("/football/teams/statistics?".concat(a.toString()))},getTeamsByLeague:async(e,t)=>{let r={league:e};return t&&(r.season=t),a.getTeams(r)},getTeamsByCountry:async e=>a.getTeams({country:e}),searchTeams:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=await a.getTeams(t),s=r.data.filter(t=>{var r;return t.name.toLowerCase().includes(e.toLowerCase())||(null===(r=t.code)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase()))});return{data:s,meta:{...r.meta,totalItems:s.length,totalPages:Math.ceil(s.length/(t.limit||10))}}},deleteTeam:async e=>{await s.x.delete("/football/teams/".concat(e))}}},62405:function(e,t,r){"use strict";r.d(t,{t7:function(){return i},vt:function(){return o},y2:function(){return n}});var s=r(31346),a=r(33016);let n=function(){var e,t;let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,s.a)({queryKey:["teams",r],queryFn:()=>a.k.getTeams(r),staleTime:6e5});return{teams:(null===(e=n.data)||void 0===e?void 0:e.data)||[],teamsMeta:null===(t=n.data)||void 0===t?void 0:t.meta,isLoading:n.isLoading,error:n.error,refetch:n.refetch}},i=e=>{let t=(0,s.a)({queryKey:["teams",e],queryFn:()=>a.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},o=e=>{let t=(0,s.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},48763:function(e,t,r){"use strict";r.d(t,{t:function(){return i}});var s=r(12574),a=r(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},i=(0,s.U)()((0,a.tJ)((e,t)=>({...n,setAuth:(t,r,s)=>{e({user:t,accessToken:r,refreshToken:s,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let s=t().user;s&&e({user:{...s,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let s=Array.isArray(e)?e:[e];return"admin"===r.role||(s.includes("editor")?["admin","editor"].includes(r.role):s.includes("moderator")?["admin","editor","moderator"].includes(r.role):s.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(75504),a=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}},16996:function(e,t,r){"use strict";function s(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(t)}function a(e){return s(e)}function n(e){return s(e)}function i(e){return s(e)}r.d(t,{Bf:function(){return a},Fc:function(){return i},Sc:function(){return s},ou:function(){return n}})},24602:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var s=r(2265),a=r(29586),n=r(57437),i=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},29586:function(e,t,r){"use strict";r.d(t,{WV:function(){return o},jH:function(){return l}});var s=r(2265),a=r(54887),n=r(59143),i=r(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e,o=a?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},8186:function(e,t,r){"use strict";r.d(t,{D:function(){return h}});var s=r(2265),a=r(31678),n=r(34654),i=r(79522),o=r(6761);class l extends o.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let r=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,n.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){i.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,r,s,a,n,i,o,l;e.onSuccess?(null==(t=(r=this.mutateOptions).onSuccess)||t.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(s=(a=this.mutateOptions).onSettled)||s.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(n=(i=this.mutateOptions).onError)||n.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(l=this.mutateOptions).onSettled)||o.call(l,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var c=r(97536),u=r(64095),d=r(3439);function h(e,t,r){let n=(0,a.lV)(e,t,r),o=(0,u.NL)({context:n.context}),[h]=s.useState(()=>new l(o,n));s.useEffect(()=>{h.setOptions(n)},[h,n]);let f=(0,c.$)(s.useCallback(e=>h.subscribe(i.V.batchCalls(e)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),g=s.useCallback((e,t)=>{h.mutate(e,t).catch(m)},[h]);if(f.error&&(0,d.L)(h.options.useErrorBoundary,[f.error]))throw f.error;return{...f,mutate:g,mutateAsync:f.mutate}}function m(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,2971,8069,1744],function(){return e(e.s=30512)}),_N_E=e.O()}]);