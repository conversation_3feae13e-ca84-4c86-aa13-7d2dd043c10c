(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1784],{28772:function(e,t,s){Promise.resolve().then(s.bind(s,53683))},53879:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},97307:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},5835:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},79580:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},69724:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},34059:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,t,s){"use strict";var r=s(15313);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},53683:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return C}});var r=s(57437),a=s(47907),n=s(2265),i=s(64095),c=s(8186),l=s(15671),o=s(575),d=s(33277),u=s(77625),m=s(4133),h=s(11546),x=s(62405),f=s(33016),g=s(16996),y=s(56288),p=s(53879),v=s(34059),j=s(5835),b=s(29295),N=s(50489),w=s(97307),k=s(69724);function C(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),{isEditor:s,isAdmin:C}=(0,h.TE)(),[T,M]=(0,n.useState)(!1),R=(0,i.NL)(),O=parseInt(e.id),{team:z,isLoading:S,error:Z}=(0,x.t7)(O),L=(0,c.D)({mutationFn:()=>f.k.deleteTeam(O),onSuccess:()=>{R.invalidateQueries({queryKey:["teams"]}),y.toast.success("Team deleted successfully"),M(!1),t.push("/dashboard/teams")},onError:e=>{y.toast.error(e.message||"Failed to delete team"),M(!1)}});return S?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(u.Od,{className:"h-10 w-20"}),(0,r.jsx)(u.Od,{className:"h-8 w-48"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,r.jsx)(u.Od,{className:"h-96"})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(u.Od,{className:"h-64"}),(0,r.jsx)(u.Od,{className:"h-48"})]})]})]}):Z||!z?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(p.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,r.jsx)(l.Zb,{children:(0,r.jsx)(l.aY,{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(v.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Team not found"}),(0,r.jsx)("p",{className:"text-gray-500",children:"The team you're looking for doesn't exist or you don't have permission to view it."})]})})})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(p.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,g.Bf)(z.logo)?(0,r.jsx)("img",{src:(0,g.Bf)(z.logo)||"",alt:z.name,className:"w-12 h-12 object-contain rounded-full",onError:e=>{e.target.style.display="none"}}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(v.Z,{className:"w-6 h-6 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:z.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[z.country&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:(0,g.ou)(z.country)||"",alt:"".concat(z.country," flag"),className:"w-4 h-3 object-cover",onError:e=>{e.target.style.display="none"}}),(0,r.jsx)("span",{children:z.country})]}),z.code&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:"font-mono text-sm",children:z.code})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{t.push("/dashboard/teams/".concat(O,"/statistics"))},children:[(0,r.jsx)(j.Z,{className:"w-4 h-4 mr-2"}),"Statistics"]}),s()&&(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{t.push("/dashboard/teams/".concat(O,"/edit"))},children:[(0,r.jsx)(b.Z,{className:"w-4 h-4 mr-2"}),"Edit"]}),C()&&(0,r.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>{M(!0)},className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,r.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),"Delete"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsxs)(l.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(v.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Team Information"})]})}),(0,r.jsx)(l.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team Name"}),(0,r.jsx)("p",{className:"text-lg font-medium",children:z.name})]}),z.code&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team Code"}),(0,r.jsx)("p",{className:"text-lg font-medium font-mono",children:z.code})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Country"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:z.country&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:(0,g.ou)(z.country)||"",alt:"".concat(z.country," flag"),className:"w-6 h-4 object-cover",onError:e=>{e.target.style.display="none"}}),(0,r.jsx)("span",{className:"text-lg font-medium",children:z.country})]})})]}),z.founded&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Founded"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(w.Z,{className:"w-4 h-4 text-gray-400"}),(0,r.jsx)("p",{className:"text-lg font-medium",children:z.founded})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team ID"}),(0,r.jsxs)("p",{className:"text-lg font-medium",children:["#",z.externalId]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Internal ID"}),(0,r.jsxs)("p",{className:"text-lg font-medium",children:["#",z.id]})]})]})})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,g.Bf)(z.logo)&&(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsx)(l.ll,{className:"text-lg",children:"Team Logo"})}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("img",{src:(0,g.Bf)(z.logo)||"",alt:z.name,className:"w-32 h-32 object-contain"})})})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsx)(l.ll,{className:"text-lg",children:"Quick Info"})}),(0,r.jsxs)(l.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Country"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:z.country&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:(0,g.ou)(z.country)||"",alt:"".concat(z.country," flag"),className:"w-4 h-3 object-cover",onError:e=>{e.target.style.display="none"}}),(0,r.jsx)("span",{className:"text-sm font-medium",children:z.country})]})})]}),z.founded&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Founded"}),(0,r.jsx)(d.C,{variant:"outline",className:"font-mono",children:z.founded})]}),z.code&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Code"}),(0,r.jsx)(d.C,{variant:"secondary",className:"font-mono",children:z.code})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Team ID"}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:["#",z.externalId]})]})]})]})]})]}),(0,r.jsx)(m.u_,{isOpen:T,onClose:()=>M(!1),title:"Delete Team",description:"Are you sure you want to delete this team? This action cannot be undone.",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)(k.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the team:"}),(0,r.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:[(0,r.jsx)("strong",{children:z.name})," (",z.country,")"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 pt-4",children:[(0,r.jsx)(o.z,{variant:"outline",onClick:()=>M(!1),disabled:L.isLoading,children:"Cancel"}),(0,r.jsx)(o.z,{variant:"destructive",onClick:()=>{L.mutate()},disabled:L.isLoading,children:L.isLoading?"Deleting...":"Delete Team"})]})]})})]})}},33277:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var r=s(57437);s(2265);var a=s(49769),n=s(22169);let i=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},575:function(e,t,s){"use strict";s.d(t,{d:function(){return l},z:function(){return o}});var r=s(57437),a=s(2265),n=s(59143),i=s(49769),c=s(22169);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,r.jsx)(u,{className:(0,c.cn)(l({variant:a,size:i,className:s})),ref:t,...d})});o.displayName="Button"},4133:function(e,t,s){"use strict";s.d(t,{sm:function(){return m},uB:function(){return h},u_:function(){return u}});var r=s(57437),a=s(2265),n=s(15669),i=s(691),c=s(52235),l=s(575),o=s(22169);let d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=e=>{let{isOpen:t,onClose:s,title:u,description:m,children:h,size:x="md",showCloseButton:f=!0,closeOnOverlayClick:g=!0,className:y}=e;return(0,r.jsx)(n.u,{appear:!0,show:t,as:a.Fragment,children:(0,r.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:g?s:()=>{},children:[(0,r.jsx)(n.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(n.u.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(i.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",d[x],y),children:[(u||f)&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[u&&(0,r.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:u}),m&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:m})]}),f&&(0,r.jsx)(l.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"mt-2",children:h})]})})})})]})})},m=e=>{let{isOpen:t,onClose:s,onConfirm:a,title:n="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:c="Confirm",cancelText:o="Cancel",variant:d="default",loading:m=!1}=e;return(0,r.jsx)(u,{isOpen:t,onClose:s,title:n,size:"sm",closeOnOverlayClick:!m,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,r.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,r.jsx)(l.z,{variant:"outline",onClick:s,disabled:m,children:o}),(0,r.jsx)(l.z,{variant:"destructive"===d?"destructive":"default",onClick:a,disabled:m,children:m?"Processing...":c})]})]})})},h=e=>{let{isOpen:t,onClose:s,title:a,description:n,children:i,onSubmit:c,submitText:o="Save",cancelText:d="Cancel",loading:m=!1,size:h="md"}=e;return(0,r.jsx)(u,{isOpen:t,onClose:s,title:a,description:n,size:h,closeOnOverlayClick:!m,children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==c||c()},className:"space-y-4",children:[i,(0,r.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,r.jsx)(l.z,{type:"button",variant:"outline",onClick:s,disabled:m,children:d}),c&&(0,r.jsx)(l.z,{type:"submit",disabled:m,children:m?"Saving...":o})]})]})})}},33016:function(e,t,s){"use strict";s.d(t,{k:function(){return a}});var r=s(74921);let a={getTeams:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=await fetch("/api/teams?".concat(t.toString()),{method:"GET",headers:(()=>{let e={"Content-Type":"application/json"};{try{let s=localStorage.getItem("auth-storage");if(s){var t;let r=JSON.parse(s),a=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(a)return e.Authorization="Bearer ".concat(a),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let s=localStorage.getItem("accessToken");s&&(e.Authorization="Bearer ".concat(s))}return e})()});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch teams");return await s.json()},getTeamById:async e=>await r.x.get("/football/teams/".concat(e)),getTeamStatistics:async(e,t,s)=>{let a=new URLSearchParams({league:e.toString(),season:t.toString(),team:s.toString()});return await r.x.get("/football/teams/statistics?".concat(a.toString()))},getTeamsByLeague:async(e,t)=>{let s={league:e};return t&&(s.season=t),a.getTeams(s)},getTeamsByCountry:async e=>a.getTeams({country:e}),searchTeams:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=await a.getTeams(t),r=s.data.filter(t=>{var s;return t.name.toLowerCase().includes(e.toLowerCase())||(null===(s=t.code)||void 0===s?void 0:s.toLowerCase().includes(e.toLowerCase()))});return{data:r,meta:{...s.meta,totalItems:r.length,totalPages:Math.ceil(r.length/(t.limit||10))}}},deleteTeam:async e=>{await r.x.delete("/football/teams/".concat(e))}}},62405:function(e,t,s){"use strict";s.d(t,{t7:function(){return i},vt:function(){return c},y2:function(){return n}});var r=s(31346),a=s(33016);let n=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,r.a)({queryKey:["teams",s],queryFn:()=>a.k.getTeams(s),staleTime:6e5});return{teams:(null===(e=n.data)||void 0===e?void 0:e.data)||[],teamsMeta:null===(t=n.data)||void 0===t?void 0:t.meta,isLoading:n.isLoading,error:n.error,refetch:n.refetch}},i=e=>{let t=(0,r.a)({queryKey:["teams",e],queryFn:()=>a.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},c=e=>{let t=(0,r.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},11546:function(e,t,s){"use strict";s.d(t,{TE:function(){return o},a1:function(){return l}});var r=s(57437),a=s(2265),n=s(47907),i=s(27786),c=s(96146);let l=e=>{let{children:t,requiredRole:s,fallbackUrl:l="/auth/login"}=e,o=(0,n.useRouter)(),{isAuthenticated:d,user:u,isLoading:m}=(0,i.a)();if((0,a.useEffect)(()=>{if(!m){if(!d||!u){o.push(l);return}if(s&&!(Array.isArray(s)?s:[s]).includes(u.role)){o.push("/dashboard?error=unauthorized");return}}},[d,u,m,s,o,l]),m)return(0,r.jsx)(c.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,r.jsx)(c.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(u.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,r.jsx)(r.Fragment,{children:t})},o=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),s=()=>t("admin"),r=()=>t(["admin","editor"]),a=()=>t(["admin","editor","moderator"]),n=()=>s(),c=()=>r(),l=()=>a(),o=()=>s();return{user:e,hasRole:t,isAdmin:s,isEditor:r,isModerator:a,canManageUsers:n,canManageContent:c,canModerate:l,canSync:o,can:e=>{switch(e){case"manage-users":return n();case"manage-content":return c();case"moderate":return l();case"sync":return o();default:return!1}}}}},16996:function(e,t,s){"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(t)}function a(e){return r(e)}function n(e){return r(e)}function i(e){return r(e)}s.d(t,{Bf:function(){return a},Fc:function(){return i},Sc:function(){return r},ou:function(){return n}})},8186:function(e,t,s){"use strict";s.d(t,{D:function(){return m}});var r=s(2265),a=s(31678),n=s(34654),i=s(79522),c=s(6761);class l extends c.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let s=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,n.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){i.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,s,r,a,n,i,c,l;e.onSuccess?(null==(t=(s=this.mutateOptions).onSuccess)||t.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(a=this.mutateOptions).onSettled)||r.call(a,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(n=(i=this.mutateOptions).onError)||n.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(c=(l=this.mutateOptions).onSettled)||c.call(l,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var o=s(97536),d=s(64095),u=s(3439);function m(e,t,s){let n=(0,a.lV)(e,t,s),c=(0,d.NL)({context:n.context}),[m]=r.useState(()=>new l(c,n));r.useEffect(()=>{m.setOptions(n)},[m,n]);let x=(0,o.$)(r.useCallback(e=>m.subscribe(i.V.batchCalls(e)),[m]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),f=r.useCallback((e,t)=>{m.mutate(e,t).catch(h)},[m]);if(x.error&&(0,u.L)(m.options.useErrorBoundary,[x.error]))throw x.error;return{...x,mutate:f,mutateAsync:x.mutate}}function h(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,1953,6877,2971,8069,1744],function(){return e(e.s=28772)}),_N_E=e.O()}]);