(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1247],{82106:function(e,t,s){Promise.resolve().then(s.bind(s,61519))},53879:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},97307:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},5835:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},37841:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},27271:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},77326:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},56227:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},10775:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},29733:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},66260:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},34059:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,t,s){"use strict";var r=s(15313);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},61519:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return w}});var r=s(57437),a=s(47907),n=s(15671),l=s(575),c=s(33277),i=s(77625),o=s(62405),d=s(16996),u=s(53879),h=s(5835),m=s(29733),x=s(10775),f=s(27271),g=s(34059),y=s(37841),j=s(97307),p=s(66260),v=s(56227),N=s(77326);function w(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),s=parseInt(e.id),{team:w,isLoading:b,error:k}=(0,o.t7)(s),{statistics:T,isLoading:Z,error:C}=(0,o.vt)(s);if(b||Z)return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i.Od,{className:"h-10 w-20"}),(0,r.jsx)(i.Od,{className:"h-8 w-64"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:8}).map((e,t)=>(0,r.jsx)(i.Od,{className:"h-24"},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)(i.Od,{className:"h-96"}),(0,r.jsx)(i.Od,{className:"h-96"})]})]});if(k||C||!w)return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Statistics not available"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Unable to load team statistics at this time."})]})})})]});let R=T||{totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]},S=e=>{switch(e){case"W":return"bg-green-100 text-green-800";case"L":return"bg-red-100 text-red-800";case"D":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.Bf)(w.logo)?(0,r.jsx)("img",{src:(0,d.Bf)(w.logo)||"",alt:w.name,className:"w-10 h-10 object-contain rounded-full",onError:e=>{e.target.style.display="none"}}):(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(g.Z,{className:"w-5 h-5 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[w.name," Statistics"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[w.country&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:(0,d.ou)(w.country)||"",alt:"".concat(w.country," flag"),className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{children:w.country})]}),w.code&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:"font-mono text-sm",children:w.code})]})]})]})]})]}),(0,r.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>{t.push("/dashboard/teams/".concat(s))},children:[(0,r.jsx)(y.Z,{className:"w-4 h-4 mr-2"}),"View Team"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:R.totalMatches}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total Matches"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(j.Z,{className:"w-6 h-6 text-blue-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:R.wins}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Wins"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.Z,{className:"w-6 h-6 text-green-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:R.draws}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Draws"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(f.Z,{className:"w-6 h-6 text-yellow-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:R.losses}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Losses"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(x.Z,{className:"w-6 h-6 text-red-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:R.goalsScored}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Goals Scored"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(v.Z,{className:"w-6 h-6 text-blue-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:R.goalsConceded}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Goals Conceded"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(N.Z,{className:"w-6 h-6 text-orange-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-emerald-600",children:R.cleanSheets}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Clean Sheets"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(N.Z,{className:"w-6 h-6 text-emerald-600"})})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[R.winPercentage,"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Win Rate"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(m.Z,{className:"w-6 h-6 text-purple-600"})})]})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Performance Breakdown"})]})}),(0,r.jsxs)(n.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Record"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[R.wins,"W - ",R.draws,"D - ",R.losses,"L"]})]}),(0,r.jsxs)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:[(0,r.jsx)("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:"".concat(R.wins/R.totalMatches*100,"%")}}),(0,r.jsx)("div",{className:"bg-yellow-600 h-2",style:{width:"".concat(R.draws/R.totalMatches*100,"%"),marginLeft:"".concat(R.wins/R.totalMatches*100,"%")}})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Home Record"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[R.homeRecord.wins,"W - ",R.homeRecord.draws,"D - ",R.homeRecord.losses,"L"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:"".concat(R.homeRecord.wins/(R.homeRecord.wins+R.homeRecord.draws+R.homeRecord.losses)*100,"%")}})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Away Record"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[R.awayRecord.wins,"W - ",R.awayRecord.draws,"D - ",R.awayRecord.losses,"L"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-600 h-2 rounded-l-full",style:{width:"".concat(R.awayRecord.wins/(R.awayRecord.wins+R.awayRecord.draws+R.awayRecord.losses)*100,"%")}})})]}),(0,r.jsx)("div",{className:"pt-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Goal Difference"}),(0,r.jsxs)("span",{className:"text-lg font-bold ".concat(R.goalsScored-R.goalsConceded>=0?"text-green-600":"text-red-600"),children:[R.goalsScored-R.goalsConceded>=0?"+":"",R.goalsScored-R.goalsConceded]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Avg Goals Scored"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-600",children:R.avgGoalsPerMatch})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Avg Goals Conceded"}),(0,r.jsx)("p",{className:"text-lg font-bold text-orange-600",children:R.avgGoalsConcededPerMatch})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Recent Form"})]})}),(0,r.jsx)(n.aY,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Last 5 matches:"}),(0,r.jsx)("div",{className:"flex space-x-1",children:R.recentForm.map((e,t)=>(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ".concat(S(e)),children:e},t))})]})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsx)(n.Ol,{children:(0,r.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Team Info"})]})}),(0,r.jsxs)(n.aY,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Country"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:w.country&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("img",{src:(0,d.ou)(w.country)||"",alt:"".concat(w.country," flag"),className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("span",{className:"text-sm font-medium",children:w.country})]})})]}),w.founded&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Founded"}),(0,r.jsx)(c.C,{variant:"outline",className:"font-mono",children:w.founded})]}),w.code&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Team Code"}),(0,r.jsx)(c.C,{variant:"secondary",className:"font-mono",children:w.code})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Team ID"}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:["#",w.externalId]})]})]})]})]})]})]})}},33277:function(e,t,s){"use strict";s.d(t,{C:function(){return c}});var r=s(57437);s(2265);var a=s(49769),n=s(22169);let l=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...a})}},575:function(e,t,s){"use strict";s.d(t,{d:function(){return i},z:function(){return o}});var r=s(57437),a=s(2265),n=s(59143),l=s(49769),c=s(22169);let i=(0,l.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,r.jsx)(u,{className:(0,c.cn)(i({variant:a,size:l,className:s})),ref:t,...d})});o.displayName="Button"},15671:function(e,t,s){"use strict";s.d(t,{Ol:function(){return c},SZ:function(){return o},Zb:function(){return l},aY:function(){return d},ll:function(){return i}});var r=s(57437),a=s(2265),n=s(22169);let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...a})});l.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});c.displayName="CardHeader";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",s),...a})});i.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},77625:function(e,t,s){"use strict";s.d(t,{Od:function(){return n},hM:function(){return c},q4:function(){return l}});var r=s(57437),a=s(22169);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...s})}let l=e=>{let{className:t}=e;return(0,r.jsxs)("div",{className:(0,a.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-4 w-3/4"}),(0,r.jsx)(n,{className:"h-4 w-1/2"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-2/3"})]})]})},c=e=>{let{rows:t=5,columns:s=4,className:l}=e;return(0,r.jsx)("div",{className:(0,a.cn)("space-y-4",l),children:(0,r.jsxs)("div",{className:"border rounded-lg",children:[(0,r.jsx)("div",{className:"border-b p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,r.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-full"},t))})},t))]})})}},74921:function(e,t,s){"use strict";s.d(t,{x:function(){return l}});var r=s(73107),a=s(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!s._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(s.headers.Authorization="Bearer ".concat(e),this.client(s))).catch(e=>Promise.reject(e));s._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),s.headers.Authorization="Bearer ".concat(t),this.client(s);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let s=JSON.parse(t);return(null===(e=s.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=a.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let s=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!s.ok)throw Error("Token refresh failed");let{accessToken:r}=await s.json(),a=e.user;if(a)return e.setAuth(a,r,t),this.setAuthToken(r),console.log("✅ Token refreshed successfully"),r}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(s=>{let{resolve:r,reject:a}=s;e?a(e):r(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let l=new n},33016:function(e,t,s){"use strict";s.d(t,{k:function(){return a}});var r=s(74921);let a={getTeams:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=await fetch("/api/teams?".concat(t.toString()),{method:"GET",headers:(()=>{let e={"Content-Type":"application/json"};{try{let s=localStorage.getItem("auth-storage");if(s){var t;let r=JSON.parse(s),a=null===(t=r.state)||void 0===t?void 0:t.accessToken;if(a)return e.Authorization="Bearer ".concat(a),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let s=localStorage.getItem("accessToken");s&&(e.Authorization="Bearer ".concat(s))}return e})()});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch teams");return await s.json()},getTeamById:async e=>await r.x.get("/football/teams/".concat(e)),getTeamStatistics:async(e,t,s)=>{let a=new URLSearchParams({league:e.toString(),season:t.toString(),team:s.toString()});return await r.x.get("/football/teams/statistics?".concat(a.toString()))},getTeamsByLeague:async(e,t)=>{let s={league:e};return t&&(s.season=t),a.getTeams(s)},getTeamsByCountry:async e=>a.getTeams({country:e}),searchTeams:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=await a.getTeams(t),r=s.data.filter(t=>{var s;return t.name.toLowerCase().includes(e.toLowerCase())||(null===(s=t.code)||void 0===s?void 0:s.toLowerCase().includes(e.toLowerCase()))});return{data:r,meta:{...s.meta,totalItems:r.length,totalPages:Math.ceil(r.length/(t.limit||10))}}},deleteTeam:async e=>{await r.x.delete("/football/teams/".concat(e))}}},62405:function(e,t,s){"use strict";s.d(t,{t7:function(){return l},vt:function(){return c},y2:function(){return n}});var r=s(31346),a=s(33016);let n=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,r.a)({queryKey:["teams",s],queryFn:()=>a.k.getTeams(s),staleTime:6e5});return{teams:(null===(e=n.data)||void 0===e?void 0:e.data)||[],teamsMeta:null===(t=n.data)||void 0===t?void 0:t.meta,isLoading:n.isLoading,error:n.error,refetch:n.refetch}},l=e=>{let t=(0,r.a)({queryKey:["teams",e],queryFn:()=>a.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},c=e=>{let t=(0,r.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},48763:function(e,t,s){"use strict";s.d(t,{t:function(){return l}});var r=s(12574),a=s(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},l=(0,r.U)()((0,a.tJ)((e,t)=>({...n,setAuth:(t,s,r)=>{e({user:t,accessToken:s,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:s=>{let r=t().user;r&&e({user:{...r,...s}})},hasPermission:e=>{let s=t().user;if(!s)return!1;let r=Array.isArray(e)?e:[e];return"admin"===s.role||(r.includes("editor")?["admin","editor"].includes(s.role):r.includes("moderator")?["admin","editor","moderator"].includes(s.role):r.includes(s.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(75504),a=s(51367);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},16996:function(e,t,s){"use strict";function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(t)}function a(e){return r(e)}function n(e){return r(e)}function l(e){return r(e)}s.d(t,{Bf:function(){return a},Fc:function(){return l},Sc:function(){return r},ou:function(){return n}})},61266:function(e,t,s){"use strict";s.d(t,{F:function(){return n},e:function(){return l}});var r=s(2265);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let s=!1,r=e.map(e=>{let r=a(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():a(e[t],null)}}}}function l(...e){return r.useCallback(n(...e),e)}},59143:function(e,t,s){"use strict";s.d(t,{Z8:function(){return l},g7:function(){return c},sA:function(){return o}});var r=s(2265),a=s(61266),n=s(57437);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...n}=e;if(r.isValidElement(s)){let e,l;let c=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.ref:(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.props.ref:s.props.ref||s.ref,i=function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(n,s.props);return s.type!==r.Fragment&&(i.ref=t?(0,a.F)(t,c):c),r.cloneElement(s,i)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:a,...l}=e,c=r.Children.toArray(a),i=c.find(d);if(i){let e=i.props.children,a=c.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...l,ref:s,children:a})});return s.displayName=`${e}.Slot`,s}var c=l("Slot"),i=Symbol("radix.slottable");function o(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},49769:function(e,t,s){"use strict";s.d(t,{j:function(){return l}});var r=s(75504);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=r.W,l=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return n(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:l,defaultVariants:c}=t,i=Object.keys(l).map(e=>{let t=null==s?void 0:s[e],r=null==c?void 0:c[e];if(null===t)return null;let n=a(t)||a(r);return l[e][n]}),o=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return n(e,i,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:s,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...c,...o}[t]):({...c,...o})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}},function(e){e.O(0,[2150,9101,8939,1346,2971,8069,1744],function(){return e(e.s=82106)}),_N_E=e.O()}]);