(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7861],{17318:function(e,t,s){Promise.resolve().then(s.bind(s,44160))},97307:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},49108:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},37805:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14960:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},98306:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},44715:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},37841:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},65404:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},37451:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},79580:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},40834:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},28670:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},29733:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},66260:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},34059:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(57977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47907:function(e,t,s){"use strict";var a=s(15313);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},44160:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return T}});var a=s(57437),n=s(2265),r=s(47907),i=s(31346),l=s(15671),o=s(575),c=s(22782),u=s(33277),d=s(22632),h=s(11546),m=s(62405),f=s(47011),g=s(16996),x=s(56288),p=s(37841),v=s(29733),y=s(34059),j=s(40834),w=s(44715),b=s(65404),k=s(28670),N=s(37451),M=s(66260),Z=s(97307);function T(){var e,t,s,T,C,L,S;let R=(0,r.useRouter)(),{isEditor:E,isAdmin:I}=(0,h.TE)(),[O,P]=(0,n.useState)({page:1,limit:20}),[A,F]=(0,n.useState)(""),[z,D]=(0,n.useState)(""),[q,B]=(0,n.useState)(""),{teams:V,teamsMeta:H,isLoading:U,error:Y}=(0,m.y2)(O);console.log("\uD83D\uDD0D Teams Page Debug (FIXED):",{teamsCount:V.length,firstTeam:V[0],firstTeamName:null===(e=V[0])||void 0===e?void 0:e.name,firstTeamNameType:typeof(null===(t=V[0])||void 0===t?void 0:t.name),firstTeamNameLength:null===(T=V[0])||void 0===T?void 0:null===(s=T.name)||void 0===s?void 0:s.length,dataStructureValid:!!(null===(C=V[0])||void 0===C?void 0:C.name),filters:O,teamsMeta:H,error:Y}),V.length>0&&console.log("\uD83D\uDD0D All teams names (FIXED):",V.slice(0,3).map(e=>({id:null==e?void 0:e.id,name:null==e?void 0:e.name,nameType:typeof(null==e?void 0:e.name),nameValid:!!(null==e?void 0:e.name),fullTeam:e})));let{data:W}=(0,i.a)({queryKey:["leagues","all"],queryFn:()=>f.A.getLeagues({limit:100})}),_=e=>{F(e),P(t=>({...t,search:e||void 0,page:1}))},G=e=>{D(e),P(t=>({...t,league:e?parseInt(e):void 0,page:1}))},K=e=>{B(e),P(t=>({...t,country:e||void 0,page:1}))},X=Array.from(new Set(V.filter(e=>null==e?void 0:e.country).map(e=>e.country).filter(Boolean)));return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(y.Z,{className:"w-8 h-8 text-blue-600"}),"Teams Management"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Browse and manage football teams from leagues worldwide"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>window.location.reload(),children:[(0,a.jsx)(j.Z,{className:"w-4 h-4 mr-2"}),"Refresh"]}),I()&&(0,a.jsxs)(o.z,{variant:"outline",onClick:()=>x.toast.info("Export feature coming soon"),children:[(0,a.jsx)(w.Z,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.Z,{className:"w-5 h-5"}),"Search & Filters"]})}),(0,a.jsxs)(l.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(k.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,a.jsx)(c.I,{placeholder:"Search teams...",value:A,onChange:e=>F(e.target.value),onKeyDown:e=>"Enter"===e.key&&_(A),className:"pl-9"})]}),(0,a.jsx)(o.z,{onClick:()=>_(A),children:"Search"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"League"}),(0,a.jsxs)("select",{value:z,onChange:e=>G(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Leagues"}),null==W?void 0:null===(L=W.data)||void 0===L?void 0:L.map(e=>(0,a.jsxs)("option",{value:e.externalId,children:[e.name," ",e.season&&"(".concat(e.season,")")]},e.externalId))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Country"}),(0,a.jsxs)("select",{value:q,onChange:e=>K(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Countries"}),X.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)(o.z,{variant:"outline",onClick:()=>{F(""),D(""),B(""),P({page:1,limit:20})},className:"w-full",children:"Clear Filters"})})]})]})]}),(0,a.jsxs)(l.Zb,{children:[(0,a.jsx)(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Teams (",(null==H?void 0:H.totalItems)||0,")"]}),H&&(0,a.jsxs)(u.C,{variant:"outline",children:["Page ",H.currentPage," of ",H.totalPages]})]})}),(0,a.jsx)(l.aY,{children:(0,a.jsx)(d.w,{data:V,columns:[{title:"Team",key:"name",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("img",{src:(0,g.Bf)(null==t?void 0:t.logo)||"/images/default-team.png",alt:"".concat((null==t?void 0:t.name)||"Team"," logo"),className:"w-8 h-8 rounded-full object-cover",onError:e=>{e.currentTarget.src="/images/default-team.png"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:(null==t?void 0:t.name)||"Unknown Team"}),(null==t?void 0:t.code)&&(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:t.code})]})]})},{title:"Country",key:"country",render:(e,t)=>(0,a.jsx)("div",{className:"flex items-center space-x-2",children:t&&t.country&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("img",{src:(0,g.ou)(t.country)||"/images/default-flag.png",alt:"".concat(t.country," flag"),className:"w-4 h-3 object-cover",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsx)("span",{children:t.country})]})})},{title:"Founded",key:"founded",render:(e,t)=>(0,a.jsx)("div",{className:"text-center",children:(null==t?void 0:t.founded)?(0,a.jsx)(u.C,{variant:"outline",className:"font-mono",children:t.founded}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"-"})})},{title:"Actions",key:"actions",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(o.z,{variant:"ghost",size:"sm",onClick:()=>R.push("/dashboard/teams/".concat(null==t?void 0:t.externalId)),disabled:!(null==t?void 0:t.externalId),children:(0,a.jsx)(p.Z,{className:"w-4 h-4"})}),(0,a.jsx)(o.z,{variant:"ghost",size:"sm",onClick:()=>R.push("/dashboard/teams/".concat(null==t?void 0:t.externalId,"/statistics")),disabled:!(null==t?void 0:t.externalId),children:(0,a.jsx)(v.Z,{className:"w-4 h-4"})})]})}],loading:U,pagination:{page:(null==H?void 0:H.currentPage)||1,limit:(null==H?void 0:H.limit)||20,total:(null==H?void 0:H.totalItems)||0,onPageChange:e=>{P(t=>({...t,page:e}))},onLimitChange:e=>{P(t=>({...t,limit:e,page:1}))}},emptyMessage:Y?"Error loading teams: ".concat((null==Y?void 0:Y.message)||"Unknown error"):"No teams found"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.Z,{className:"w-8 h-8 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Total Teams"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:(null==H?void 0:H.totalItems)||0})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.Z,{className:"w-8 h-8 text-green-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Countries"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:X.length})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(M.Z,{className:"w-8 h-8 text-yellow-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Leagues"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:(null==W?void 0:null===(S=W.data)||void 0===S?void 0:S.length)||0})]})]})})}),(0,a.jsx)(l.Zb,{children:(0,a.jsx)(l.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Z.Z,{className:"w-8 h-8 text-purple-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Current Page"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:(null==H?void 0:H.currentPage)||1})]})]})})})]})]})}},47011:function(e,t,s){"use strict";s.d(t,{A:function(){return r}});var a=s(74921);let n=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let s=JSON.parse(t);return(null===(e=s.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},r={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,t)=>{let s=t?"".concat(e,"-").concat(t):e.toString(),a=await fetch("/api/leagues/".concat(s),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch league ".concat(e));return await a.json()},createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t,s)=>{let a=n(),i={"Content-Type":"application/json"};a&&(i.Authorization="Bearer ".concat(a));let l=await r.getLeagueById(e,s);if(!l||!l.id)throw Error("League not found: ".concat(e).concat(s?"-".concat(s):""));let o=await fetch("/api/leagues/".concat(l.id),{method:"PATCH",headers:i,body:JSON.stringify(t)});if(!o.ok)throw Error((await o.json()).message||"Failed to update league ".concat(e));return await o.json()},deleteLeague:async(e,t)=>{let s=await r.getLeagueById(e,t);if(!s||!s.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await a.x.delete("/football/leagues/".concat(s.id))},getActiveLeagues:async()=>r.getLeagues({active:!0}),getLeaguesByCountry:async e=>r.getLeagues({country:e}),toggleLeagueStatus:async(e,t,s)=>r.updateLeague(e,{active:t},s)}},33016:function(e,t,s){"use strict";s.d(t,{k:function(){return n}});var a=s(74921);let n={getTeams:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=await fetch("/api/teams?".concat(t.toString()),{method:"GET",headers:(()=>{let e={"Content-Type":"application/json"};{try{let s=localStorage.getItem("auth-storage");if(s){var t;let a=JSON.parse(s),n=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(n)return e.Authorization="Bearer ".concat(n),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let s=localStorage.getItem("accessToken");s&&(e.Authorization="Bearer ".concat(s))}return e})()});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch teams");return await s.json()},getTeamById:async e=>await a.x.get("/football/teams/".concat(e)),getTeamStatistics:async(e,t,s)=>{let n=new URLSearchParams({league:e.toString(),season:t.toString(),team:s.toString()});return await a.x.get("/football/teams/statistics?".concat(n.toString()))},getTeamsByLeague:async(e,t)=>{let s={league:e};return t&&(s.season=t),n.getTeams(s)},getTeamsByCountry:async e=>n.getTeams({country:e}),searchTeams:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=await n.getTeams(t),a=s.data.filter(t=>{var s;return t.name.toLowerCase().includes(e.toLowerCase())||(null===(s=t.code)||void 0===s?void 0:s.toLowerCase().includes(e.toLowerCase()))});return{data:a,meta:{...s.meta,totalItems:a.length,totalPages:Math.ceil(a.length/(t.limit||10))}}},deleteTeam:async e=>{await a.x.delete("/football/teams/".concat(e))}}},62405:function(e,t,s){"use strict";s.d(t,{t7:function(){return i},vt:function(){return l},y2:function(){return r}});var a=s(31346),n=s(33016);let r=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=(0,a.a)({queryKey:["teams",s],queryFn:()=>n.k.getTeams(s),staleTime:6e5});return{teams:(null===(e=r.data)||void 0===e?void 0:e.data)||[],teamsMeta:null===(t=r.data)||void 0===t?void 0:t.meta,isLoading:r.isLoading,error:r.error,refetch:r.refetch}},i=e=>{let t=(0,a.a)({queryKey:["teams",e],queryFn:()=>n.k.getTeamById(e),enabled:!!e,staleTime:6e5});return{team:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}},l=e=>{let t=(0,a.a)({queryKey:["teams","statistics",e],queryFn:()=>Promise.resolve({totalMatches:28,wins:18,draws:6,losses:4,goalsScored:54,goalsConceded:23,cleanSheets:12,winPercentage:64.3,avgGoalsPerMatch:1.93,avgGoalsConcededPerMatch:.82,homeRecord:{wins:11,draws:3,losses:0},awayRecord:{wins:7,draws:3,losses:4},recentForm:["W","W","D","W","L"]}),enabled:!!e,staleTime:3e5});return{statistics:t.data,isLoading:t.isLoading,error:t.error,refetch:t.refetch}}},11546:function(e,t,s){"use strict";s.d(t,{TE:function(){return c},a1:function(){return o}});var a=s(57437),n=s(2265),r=s(47907),i=s(27786),l=s(96146);let o=e=>{let{children:t,requiredRole:s,fallbackUrl:o="/auth/login"}=e,c=(0,r.useRouter)(),{isAuthenticated:u,user:d,isLoading:h}=(0,i.a)();if((0,n.useEffect)(()=>{if(!h){if(!u||!d){c.push(o);return}if(s&&!(Array.isArray(s)?s:[s]).includes(d.role)){c.push("/dashboard?error=unauthorized");return}}},[u,d,h,s,c,o]),h)return(0,a.jsx)(l.SX,{message:"Verifying authentication..."});if(!u||!d)return(0,a.jsx)(l.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(d.role))return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",d.role]})]})})}return(0,a.jsx)(a.Fragment,{children:t})},c=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),s=()=>t("admin"),a=()=>t(["admin","editor"]),n=()=>t(["admin","editor","moderator"]),r=()=>s(),l=()=>a(),o=()=>n(),c=()=>s();return{user:e,hasRole:t,isAdmin:s,isEditor:a,isModerator:n,canManageUsers:r,canManageContent:l,canModerate:o,canSync:c,can:e=>{switch(e){case"manage-users":return r();case"manage-content":return l();case"moderate":return o();case"sync":return c();default:return!1}}}}},16996:function(e,t,s){"use strict";function a(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(t)}function n(e){return a(e)}function r(e){return a(e)}function i(e){return a(e)}s.d(t,{Bf:function(){return n},Fc:function(){return i},Sc:function(){return a},ou:function(){return r}})},8186:function(e,t,s){"use strict";s.d(t,{D:function(){return h}});var a=s(2265),n=s(31678),r=s(34654),i=s(79522),l=s(6761);class o extends l.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let s=this.options;this.options=this.client.defaultMutationOptions(e),(0,n.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,r.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){i.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,s,a,n,r,i,l,o;e.onSuccess?(null==(t=(s=this.mutateOptions).onSuccess)||t.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(a=(n=this.mutateOptions).onSettled)||a.call(n,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(r=(i=this.mutateOptions).onError)||r.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(l=(o=this.mutateOptions).onSettled)||l.call(o,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var c=s(97536),u=s(64095),d=s(3439);function h(e,t,s){let r=(0,n.lV)(e,t,s),l=(0,u.NL)({context:r.context}),[h]=a.useState(()=>new o(l,r));a.useEffect(()=>{h.setOptions(r)},[h,r]);let f=(0,c.$)(a.useCallback(e=>h.subscribe(i.V.batchCalls(e)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),g=a.useCallback((e,t)=>{h.mutate(e,t).catch(m)},[h]);if(f.error&&(0,d.L)(h.options.useErrorBoundary,[f.error]))throw f.error;return{...f,mutate:g,mutateAsync:f.mutate}}function m(){}}},function(e){e.O(0,[2150,9101,8939,1346,2341,5481,4216,8116,3462,6877,1380,2971,8069,1744],function(){return e(e.s=17318)}),_N_E=e.O()}]);