(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{93179:function(e,t,r){Promise.resolve().then(r.t.bind(r,99646,23)),Promise.resolve().then(r.bind(r,56288)),Promise.resolve().then(r.t.bind(r,63385,23)),Promise.resolve().then(r.bind(r,90910)),Promise.resolve().then(r.bind(r,62146)),Promise.resolve().then(r.bind(r,87457))},40834:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},69724:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},575:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return l}});var s=r(57437),i=r(2265),n=r(59143),a=r(49769),u=r(22169);let o=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,t)=>{let{className:r,variant:i,size:a,asChild:l=!1,...h}=e,c=l?n.g7:"button";return(0,s.jsx)(c,{className:(0,u.cn)(o({variant:i,size:a,className:r})),ref:t,...h})});l.displayName="Button"},15671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return u},SZ:function(){return l},Zb:function(){return a},aY:function(){return h},ll:function(){return o}});var s=r(57437),i=r(2265),n=r(22169);let a=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...i})});a.displayName="Card";let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...i})});u.displayName="CardHeader";let o=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...i})});o.displayName="CardTitle";let l=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...i})});l.displayName="CardDescription";let h=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...i})});h.displayName="CardContent",i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...i})}).displayName="CardFooter"},90910:function(e,t,r){"use strict";r.r(t),r.d(t,{DefaultErrorFallback:function(){return h},ErrorBoundary:function(){return l},useErrorHandler:function(){return c}});var s=r(57437),i=r(2265),n=r(575),a=r(15671),u=r(69724),o=r(40834);class l extends i.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,s.jsx)(e,{error:this.state.error,resetError:this.resetError})}return(0,s.jsx)(h,{error:this.state.error,resetError:this.resetError})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}let h=e=>{let{error:t,resetError:r}=e;return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,s.jsxs)(a.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(a.Ol,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:(0,s.jsx)(u.Z,{className:"h-6 w-6 text-red-600"})}),(0,s.jsx)(a.ll,{className:"text-red-900",children:"Something went wrong"}),(0,s.jsx)(a.SZ,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,s.jsxs)(a.aY,{className:"space-y-4",children:[!1,(0,s.jsxs)(n.z,{onClick:r,className:"w-full",variant:"outline",children:[(0,s.jsx)(o.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})})},c=()=>{let[e,t]=i.useState(null),r=i.useCallback(()=>{t(null)},[]),s=i.useCallback(e=>{t(e)},[]);return i.useEffect(()=>{if(e)throw e},[e]),{captureError:s,resetError:r}}},62146:function(e,t,r){"use strict";r.r(t),r.d(t,{QueryProvider:function(){return q}});var s=r(57437),i=r(31678),n=r(95899),a=r(79522),u=r(34500),o=r(3864);class l extends o.F{constructor(e){super(),this.abortSignalConsumed=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.logger=e.logger||n._,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=s?s:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(e,t){let r=(0,i.oE)(this.state.data,e,this.options);return this.dispatch({data:r,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt,manual:null==t?void 0:t.manual}),r}setState(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})}cancel(e){var t;let r=this.promise;return null==(t=this.retryer)||t.cancel(e),r?r.then(i.ZT).catch(i.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(e=>!1!==e.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(e=>e.getCurrentResult().isStale)}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.Kp)(this.state.dataUpdatedAt,e)}onFocus(){var e;let t=this.observers.find(e=>e.shouldFetchOnWindowFocus());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}onOnline(){var e;let t=this.observers.find(e=>e.shouldFetchOnReconnect());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(e,t){var r,s,n,a;if("idle"!==this.state.fetchStatus){if(this.state.dataUpdatedAt&&null!=t&&t.cancelRefetch)this.cancel({silent:!0});else if(this.promise)return null==(n=this.retryer)||n.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let o=(0,i.G9)(),l={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>{if(o)return this.abortSignalConsumed=!0,o.signal}})};h(l);let c={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(l)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};h(c),null==(r=this.options.behavior)||r.onFetch(c),this.revertState=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(s=c.fetchOptions)?void 0:s.meta))&&this.dispatch({type:"fetch",meta:null==(a=c.fetchOptions)?void 0:a.meta});let d=e=>{if((0,u.DV)(e)&&e.silent||this.dispatch({type:"error",error:e}),!(0,u.DV)(e)){var t,r,s,i;null==(t=(r=this.cache.config).onError)||t.call(r,e,this),null==(s=(i=this.cache.config).onSettled)||s.call(i,this.state.data,e,this)}this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=(0,u.Mz)({fn:c.fetchFn,abort:null==o?void 0:o.abort.bind(o),onSuccess:e=>{var t,r,s,i;if(void 0===e){d(Error(this.queryHash+" data is undefined"));return}this.setData(e),null==(t=(r=this.cache.config).onSuccess)||t.call(r,e,this),null==(s=(i=this.cache.config).onSettled)||s.call(i,e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:d,onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(e){this.state=(t=>{var r,s;switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(r=e.meta)?r:null,fetchStatus:(0,u.Kw)(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(s=e.dataUpdatedAt)?s:Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=e.error;if((0,u.DV)(i)&&i.revert&&this.revertState)return{...this.revertState,fetchStatus:"idle"};return{...t,error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),a.V.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate(e)}),this.cache.notify({query:this,type:"updated",action:e})})}}var h=r(6761);class c extends h.l{constructor(e){super(),this.config=e||{},this.queries=[],this.queriesMap={}}build(e,t,r){var s;let n=t.queryKey,a=null!=(s=t.queryHash)?s:(0,i.Rm)(n,t),u=this.get(a);return u||(u=new l({cache:this,logger:e.getLogger(),queryKey:n,queryHash:a,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(u)),u}add(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"added",query:e}))}remove(e){let t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter(t=>t!==e),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"removed",query:e}))}clear(){a.V.batch(()=>{this.queries.forEach(e=>{this.remove(e)})})}get(e){return this.queriesMap[e]}getAll(){return this.queries}find(e,t){let[r]=(0,i.I6)(e,t);return void 0===r.exact&&(r.exact=!0),this.queries.find(e=>(0,i._x)(r,e))}findAll(e,t){let[r]=(0,i.I6)(e,t);return Object.keys(r).length>0?this.queries.filter(e=>(0,i._x)(r,e)):this.queries}notify(e){a.V.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}onFocus(){a.V.batch(()=>{this.queries.forEach(e=>{e.onFocus()})})}onOnline(){a.V.batch(()=>{this.queries.forEach(e=>{e.onOnline()})})}}var d=r(34654);class f extends h.l{constructor(e){super(),this.config=e||{},this.mutations=[],this.mutationId=0}build(e,t,r){let s=new d.m({mutationCache:this,logger:e.getLogger(),mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:r,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0});return this.add(s),s}add(e){this.mutations.push(e),this.notify({type:"added",mutation:e})}remove(e){this.mutations=this.mutations.filter(t=>t!==e),this.notify({type:"removed",mutation:e})}clear(){a.V.batch(()=>{this.mutations.forEach(e=>{this.remove(e)})})}getAll(){return this.mutations}find(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find(t=>(0,i.X7)(e,t))}findAll(e){return this.mutations.filter(t=>(0,i.X7)(e,t))}notify(e){a.V.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}resumePausedMutations(){var e;return this.resuming=(null!=(e=this.resuming)?e:Promise.resolve()).then(()=>{let e=this.mutations.filter(e=>e.state.isPaused);return a.V.batch(()=>e.reduce((e,t)=>e.then(()=>t.continue().catch(i.ZT)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}var m=r(83597),p=r(91507);function y(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}class v{constructor(e={}){this.queryCache=e.queryCache||new c,this.mutationCache=e.mutationCache||new f,this.logger=e.logger||n._,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=m.j.subscribe(()=>{m.j.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=p.N.subscribe(()=>{p.N.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var e,t;this.mountCount--,0===this.mountCount&&(null==(e=this.unsubscribeFocus)||e.call(this),this.unsubscribeFocus=void 0,null==(t=this.unsubscribeOnline)||t.call(this),this.unsubscribeOnline=void 0)}isFetching(e,t){let[r]=(0,i.I6)(e,t);return r.fetchStatus="fetching",this.queryCache.findAll(r).length}isMutating(e){return this.mutationCache.findAll({...e,fetching:!0}).length}getQueryData(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state.data}ensureQueryData(e,t,r){let s=(0,i._v)(e,t,r),n=this.getQueryData(s.queryKey);return n?Promise.resolve(n):this.fetchQuery(s)}getQueriesData(e){return this.getQueryCache().findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let s=this.queryCache.find(e),n=null==s?void 0:s.state.data,a=(0,i.SE)(t,n);if(void 0===a)return;let u=(0,i._v)(e),o=this.defaultQueryOptions(u);return this.queryCache.build(this,o).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return a.V.batch(()=>this.getQueryCache().findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state}removeQueries(e,t){let[r]=(0,i.I6)(e,t),s=this.queryCache;a.V.batch(()=>{s.findAll(r).forEach(e=>{s.remove(e)})})}resetQueries(e,t,r){let[s,n]=(0,i.I6)(e,t,r),u=this.queryCache,o={type:"active",...s};return a.V.batch(()=>(u.findAll(s).forEach(e=>{e.reset()}),this.refetchQueries(o,n)))}cancelQueries(e,t,r){let[s,n={}]=(0,i.I6)(e,t,r);return void 0===n.revert&&(n.revert=!0),Promise.all(a.V.batch(()=>this.queryCache.findAll(s).map(e=>e.cancel(n)))).then(i.ZT).catch(i.ZT)}invalidateQueries(e,t,r){let[s,n]=(0,i.I6)(e,t,r);return a.V.batch(()=>{var e,t;if(this.queryCache.findAll(s).forEach(e=>{e.invalidate()}),"none"===s.refetchType)return Promise.resolve();let r={...s,type:null!=(e=null!=(t=s.refetchType)?t:s.type)?e:"active"};return this.refetchQueries(r,n)})}refetchQueries(e,t,r){let[s,n]=(0,i.I6)(e,t,r),u=Promise.all(a.V.batch(()=>this.queryCache.findAll(s).filter(e=>!e.isDisabled()).map(e=>{var t;return e.fetch(void 0,{...n,cancelRefetch:null==(t=null==n?void 0:n.cancelRefetch)||t,meta:{refetchPage:s.refetchPage}})}))).then(i.ZT);return null!=n&&n.throwOnError||(u=u.catch(i.ZT)),u}fetchQuery(e,t,r){let s=(0,i._v)(e,t,r),n=this.defaultQueryOptions(s);void 0===n.retry&&(n.retry=!1);let a=this.queryCache.build(this,n);return a.isStaleByTime(n.staleTime)?a.fetch(n):Promise.resolve(a.state.data)}prefetchQuery(e,t,r){return this.fetchQuery(e,t,r).then(i.ZT).catch(i.ZT)}fetchInfiniteQuery(e,t,r){let s=(0,i._v)(e,t,r);return s.behavior={onFetch:e=>{e.fetchFn=()=>{var t,r,s,i,n,a,u;let o;let l=null==(t=e.fetchOptions)?void 0:null==(r=t.meta)?void 0:r.refetchPage,h=null==(s=e.fetchOptions)?void 0:null==(i=s.meta)?void 0:i.fetchMore,c=null==h?void 0:h.pageParam,d=(null==h?void 0:h.direction)==="forward",f=(null==h?void 0:h.direction)==="backward",m=(null==(n=e.state.data)?void 0:n.pages)||[],p=(null==(a=e.state.data)?void 0:a.pageParams)||[],v=p,g=!1,b=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>{var t,r;return null!=(t=e.signal)&&t.aborted?g=!0:null==(r=e.signal)||r.addEventListener("abort",()=>{g=!0}),e.signal}})},q=e.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+e.options.queryHash+"'")),C=(e,t,r,s)=>(v=s?[t,...v]:[...v,t],s?[r,...e]:[...e,r]),x=(t,r,s,i)=>{if(g)return Promise.reject("Cancelled");if(void 0===s&&!r&&t.length)return Promise.resolve(t);let n={queryKey:e.queryKey,pageParam:s,meta:e.options.meta};return b(n),Promise.resolve(q(n)).then(e=>C(t,s,e,i))};if(m.length){if(d){let t=void 0!==c,r=t?c:y(e.options,m);o=x(m,t,r)}else if(f){let t=void 0!==c,r=t?c:null==(u=e.options).getPreviousPageParam?void 0:u.getPreviousPageParam(m[0],m);o=x(m,t,r,!0)}else{v=[];let t=void 0===e.options.getNextPageParam;o=!l||!m[0]||l(m[0],0,m)?x([],t,p[0]):Promise.resolve(C([],p[0],m[0]));for(let r=1;r<m.length;r++)o=o.then(s=>{if(!l||!m[r]||l(m[r],r,m)){let i=t?p[r]:y(e.options,s);return x(s,t,i)}return Promise.resolve(C(s,p[r],m[r]))})}}else o=x([]);return o.then(e=>({pages:e,pageParams:v}))}}},this.fetchQuery(s)}prefetchInfiniteQuery(e,t,r){return this.fetchInfiniteQuery(e,t,r).then(i.ZT).catch(i.ZT)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(e){this.defaultOptions=e}setQueryDefaults(e,t){let r=this.queryDefaults.find(t=>(0,i.yF)(e)===(0,i.yF)(t.queryKey));r?r.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})}getQueryDefaults(e){if(!e)return;let t=this.queryDefaults.find(t=>(0,i.to)(e,t.queryKey));return null==t?void 0:t.defaultOptions}setMutationDefaults(e,t){let r=this.mutationDefaults.find(t=>(0,i.yF)(e)===(0,i.yF)(t.mutationKey));r?r.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})}getMutationDefaults(e){if(!e)return;let t=this.mutationDefaults.find(t=>(0,i.to)(e,t.mutationKey));return null==t?void 0:t.defaultOptions}defaultQueryOptions(e){if(null!=e&&e._defaulted)return e;let t={...this.defaultOptions.queries,...this.getQueryDefaults(null==e?void 0:e.queryKey),...e,_defaulted:!0};return!t.queryHash&&t.queryKey&&(t.queryHash=(0,i.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.useErrorBoundary&&(t.useErrorBoundary=!!t.suspense),t}defaultMutationOptions(e){return null!=e&&e._defaulted?e:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==e?void 0:e.mutationKey),...e,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}var g=r(64095),b=r(2265);let q=e=>{let{children:t}=e,[r]=(0,b.useState)(()=>new v({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,s.jsxs)(g.aH,{client:r,children:[t,!1]})}},87457:function(e,t,r){"use strict";r.r(t),r.d(t,{ThemeProvider:function(){return a},useTheme:function(){return u}});var s=r(57437),i=r(2265);let n=(0,i.createContext)({theme:"system",setTheme:()=>null});function a(e){let{children:t,defaultTheme:r="system",storageKey:a="cms-theme",...u}=e,[o,l]=(0,i.useState)(r),[h,c]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{var e;c(!0);let t=null===(e=localStorage)||void 0===e?void 0:e.getItem(a);t&&l(t)},[a]),(0,i.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===o){let t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(t);return}e.classList.add(o)},[o]),h)?(0,s.jsx)(n.Provider,{...u,value:{theme:o,setTheme:e=>{var t;null===(t=localStorage)||void 0===t||t.setItem(a,e),l(e)}},children:t}):null}let u=()=>{let e=(0,i.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},22169:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(75504),i=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,s.W)(t))}},63385:function(){},99646:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[2150,9101,2341,2971,8069,1744],function(){return e(e.s=93179)}),_N_E=e.O()}]);