"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7226],{53879:function(t,e,r){r.d(e,{Z:function(){return n}});let n=(0,r(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},70699:function(t,e,r){r.d(e,{Z:function(){return n}});let n=(0,r(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},69724:function(t,e,r){r.d(e,{Z:function(){return n}});let n=(0,r(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},66260:function(t,e,r){r.d(e,{Z:function(){return n}});let n=(0,r(57977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},47907:function(t,e,r){var n=r(15313);r.o(n,"useParams")&&r.d(e,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(e,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(e,{useRouter:function(){return n.useRouter}})},86969:function(t,e,r){r.d(e,{fC:function(){return x},z$:function(){return w}});var n=r(2265),s=r(61266),i=r(84104),a=r(44991),u=r(9310),o=r(65030),c=r(76769),l=r(12642),d=r(29586),h=r(57437),p="Checkbox",[f,b]=(0,i.b)(p),[v,y]=f(p);function k(t){let{__scopeCheckbox:e,checked:r,children:s,defaultChecked:i,disabled:a,form:o,name:c,onCheckedChange:l,required:d,value:f="on",internal_do_not_use_render:b}=t,[y,k]=(0,u.T)({prop:r,defaultProp:null!=i&&i,onChange:l,caller:p}),[m,R]=n.useState(null),[x,M]=n.useState(null),w=n.useRef(!1),C=!m||!!o||!!m.closest("form"),E={checked:y,disabled:a,setChecked:k,control:m,setControl:R,name:c,form:o,value:f,hasConsumerStoppedPropagationRef:w,required:d,defaultChecked:!g(i)&&i,isFormControl:C,bubbleInput:x,setBubbleInput:M};return(0,h.jsx)(v,{scope:e,...E,children:"function"==typeof b?b(E):s})}var m="CheckboxTrigger",R=n.forwardRef((t,e)=>{let{__scopeCheckbox:r,onKeyDown:i,onClick:u,...o}=t,{control:c,value:l,disabled:p,checked:f,required:b,setControl:v,setChecked:k,hasConsumerStoppedPropagationRef:R,isFormControl:x,bubbleInput:M}=y(m,r),w=(0,s.e)(e,v),C=n.useRef(f);return n.useEffect(()=>{let t=null==c?void 0:c.form;if(t){let e=()=>k(C.current);return t.addEventListener("reset",e),()=>t.removeEventListener("reset",e)}},[c,k]),(0,h.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":g(f)?"mixed":f,"aria-required":b,"data-state":O(f),"data-disabled":p?"":void 0,disabled:p,value:l,...o,ref:w,onKeyDown:(0,a.M)(i,t=>{"Enter"===t.key&&t.preventDefault()}),onClick:(0,a.M)(u,t=>{k(t=>!!g(t)||!t),M&&x&&(R.current=t.isPropagationStopped(),R.current||t.stopPropagation())})})});R.displayName=m;var x=n.forwardRef((t,e)=>{let{__scopeCheckbox:r,name:n,checked:s,defaultChecked:i,required:a,disabled:u,value:o,onCheckedChange:c,form:l,...d}=t;return(0,h.jsx)(k,{__scopeCheckbox:r,checked:s,defaultChecked:i,disabled:u,required:a,onCheckedChange:c,name:n,form:l,value:o,internal_do_not_use_render:t=>{let{isFormControl:n}=t;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(R,{...d,ref:e,__scopeCheckbox:r}),n&&(0,h.jsx)(E,{__scopeCheckbox:r})]})}})});x.displayName=p;var M="CheckboxIndicator",w=n.forwardRef((t,e)=>{let{__scopeCheckbox:r,forceMount:n,...s}=t,i=y(M,r);return(0,h.jsx)(l.z,{present:n||g(i.checked)||!0===i.checked,children:(0,h.jsx)(d.WV.span,{"data-state":O(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:e,style:{pointerEvents:"none",...t.style}})})});w.displayName=M;var C="CheckboxBubbleInput",E=n.forwardRef((t,e)=>{let{__scopeCheckbox:r,...i}=t,{control:a,hasConsumerStoppedPropagationRef:u,checked:l,defaultChecked:p,required:f,disabled:b,name:v,value:k,form:m,bubbleInput:R,setBubbleInput:x}=y(C,r),M=(0,s.e)(e,x),w=(0,o.D)(l),E=(0,c.t)(a);n.useEffect(()=>{if(!R)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,e=!u.current;if(w!==l&&t){let r=new Event("click",{bubbles:e});R.indeterminate=g(l),t.call(R,!g(l)&&l),R.dispatchEvent(r)}},[R,w,l,u]);let O=n.useRef(!g(l)&&l);return(0,h.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:O.current,required:f,disabled:b,name:v,value:k,form:m,...i,tabIndex:-1,ref:M,style:{...i.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function g(t){return"indeterminate"===t}function O(t){return g(t)?"indeterminate":t?"checked":"unchecked"}E.displayName=C},94845:function(t,e,r){r.d(e,{bU:function(){return w},fC:function(){return M}});var n=r(2265),s=r(44991),i=r(61266),a=r(84104),u=r(9310),o=r(65030),c=r(76769),l=r(29586),d=r(57437),h="Switch",[p,f]=(0,a.b)(h),[b,v]=p(h),y=n.forwardRef((t,e)=>{let{__scopeSwitch:r,name:a,checked:o,defaultChecked:c,required:p,disabled:f,value:v="on",onCheckedChange:y,form:k,...m}=t,[M,w]=n.useState(null),C=(0,i.e)(e,t=>w(t)),E=n.useRef(!1),g=!M||k||!!M.closest("form"),[O,j]=(0,u.T)({prop:o,defaultProp:null!=c&&c,onChange:y,caller:h});return(0,d.jsxs)(b,{scope:r,checked:O,disabled:f,children:[(0,d.jsx)(l.WV.button,{type:"button",role:"switch","aria-checked":O,"aria-required":p,"data-state":x(O),"data-disabled":f?"":void 0,disabled:f,value:v,...m,ref:C,onClick:(0,s.M)(t.onClick,t=>{j(t=>!t),g&&(E.current=t.isPropagationStopped(),E.current||t.stopPropagation())})}),g&&(0,d.jsx)(R,{control:M,bubbles:!E.current,name:a,value:v,checked:O,required:p,disabled:f,form:k,style:{transform:"translateX(-100%)"}})]})});y.displayName=h;var k="SwitchThumb",m=n.forwardRef((t,e)=>{let{__scopeSwitch:r,...n}=t,s=v(k,r);return(0,d.jsx)(l.WV.span,{"data-state":x(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:e})});m.displayName=k;var R=n.forwardRef((t,e)=>{let{__scopeSwitch:r,control:s,checked:a,bubbles:u=!0,...l}=t,h=n.useRef(null),p=(0,i.e)(h,e),f=(0,o.D)(a),b=(0,c.t)(s);return n.useEffect(()=>{let t=h.current;if(!t)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==a&&e){let r=new Event("click",{bubbles:u});e.call(t,a),t.dispatchEvent(r)}},[f,a,u]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:p,style:{...l.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(t){return t?"checked":"unchecked"}R.displayName="SwitchBubbleInput";var M=y,w=m},8186:function(t,e,r){r.d(e,{D:function(){return h}});var n=r(2265),s=r(31678),i=r(34654),a=r(79522),u=r(6761);class o extends u.l{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;let r=this.options;this.options=this.client.defaultMutationOptions(t),(0,s.VS)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var t;null==(t=this.currentMutation)||t.removeObserver(this)}}onMutationUpdate(t){this.updateResult();let e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let t=this.currentMutation?this.currentMutation.state:(0,i.R)(),e={...t,isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=e}notify(t){a.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var e,r,n,s,i,a,u,o;t.onSuccess?(null==(e=(r=this.mutateOptions).onSuccess)||e.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(n=(s=this.mutateOptions).onSettled)||n.call(s,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):t.onError&&(null==(i=(a=this.mutateOptions).onError)||i.call(a,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(u=(o=this.mutateOptions).onSettled)||u.call(o,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}t.listeners&&this.listeners.forEach(({listener:t})=>{t(this.currentResult)})})}}var c=r(97536),l=r(64095),d=r(3439);function h(t,e,r){let i=(0,s.lV)(t,e,r),u=(0,l.NL)({context:i.context}),[h]=n.useState(()=>new o(u,i));n.useEffect(()=>{h.setOptions(i)},[h,i]);let f=(0,c.$)(n.useCallback(t=>h.subscribe(a.V.batchCalls(t)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),b=n.useCallback((t,e)=>{h.mutate(t,e).catch(p)},[h]);if(f.error&&(0,d.L)(h.options.useErrorBoundary,[f.error]))throw f.error;return{...f,mutate:b,mutateAsync:f.mutate}}function p(){}}}]);