"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3462],{23441:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85159:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},18178:function(e,t,r){r.d(t,{VY:function(){return e_},ZA:function(){return eW},JO:function(){return eN},ck:function(){return eA},wU:function(){return eO},eT:function(){return eB},__:function(){return eH},h_:function(){return eV},fC:function(){return eI},$G:function(){return eF},u_:function(){return eK},Z0:function(){return ez},xz:function(){return eP},B4:function(){return eD},l_:function(){return eL}});var n=r(2265),l=r(54887);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(44991),i=r(5528),u=r(61266),s=r(84104),d=r(12275),c=r(1260),p=r(46165),f=r(78082),v=r(38687),h=r(12338),m=r(37881),w=r(29586),g=r(59143),x=r(39830),y=r(9310),b=r(32618),S=r(65030),C=r(11780),M=r(66674),j=r(47225),T=r(57437),k=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],E="Select",[I,P,D]=(0,i.B)(E),[N,V]=(0,s.b)(E,[D,h.D7]),_=(0,h.D7)(),[L,W]=N(E),[H,A]=N(E),B=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:u,onValueChange:s,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=_(t),[b,S]=n.useState(null),[C,M]=n.useState(null),[j,k]=n.useState(!1),R=(0,d.gm)(c),[P,D]=(0,y.T)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:E}),[N,V]=(0,y.T)({prop:i,defaultProp:u,onChange:s,caller:E}),W=n.useRef(null),A=!b||g||!!b.closest("form"),[B,O]=n.useState(new Set),K=Array.from(B).map(e=>e.props.value).join(";");return(0,T.jsx)(h.fC,{...x,children:(0,T.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:M,valueNodeHasChildren:j,onValueNodeHasChildrenChange:k,contentId:(0,v.M)(),value:N,onValueChange:V,open:P,onOpenChange:D,dir:R,triggerPointerDownPosRef:W,disabled:m,children:[(0,T.jsx)(I.Provider,{scope:t,children:(0,T.jsx)(H,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,T.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:N,onChange:e=>V(e.target.value),disabled:m,form:g,children:[void 0===N?(0,T.jsx)("option",{value:""}):null,Array.from(B)]},K):null]})})};B.displayName=E;var O="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=_(r),s=W(O,r),d=s.disabled||l,c=(0,u.e)(t,s.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,g]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=eE(t,e,r);void 0!==n&&s.onValueChange(n.value)}),x=e=>{d||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(h.ee,{asChild:!0,...i,children:(0,T.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(s.value)?"":void 0,...o,ref:c,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});K.displayName=O;var F="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,s=W(F,r),{onValueNodeHasChildrenChange:d}=s,c=void 0!==o,p=(0,u.e)(t,s.onValueNodeChange);return(0,b.b)(()=>{d(c)},[d,c]),(0,T.jsx)(w.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(s.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});z.displayName=F;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var Z=e=>(0,T.jsx)(m.h,{asChild:!0,...e});Z.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=W(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)($,{...e,ref:t}):o?l.createPortal((0,T.jsx)(X,{scope:e.__scopeSelect,children:(0,T.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[X,G]=N(q),J=(0,g.Z8)("SelectContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...C}=e,k=W(q,r),[R,E]=n.useState(null),[I,D]=n.useState(null),N=(0,u.e)(t,e=>E(e)),[V,_]=n.useState(null),[L,H]=n.useState(null),A=P(r),[B,O]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(R)return(0,M.Ry)(R)},[R]),(0,p.EW)();let F=n.useCallback(e=>{let[t,...r]=A().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[A,I]),z=n.useCallback(()=>F([V,R]),[F,V,R]);n.useEffect(()=>{B&&z()},[B,z]);let{onOpenChange:U,triggerPointerDownPosRef:Z}=k;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=Z.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=Z.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,U,Z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Y,G]=eR(e=>{let t=A().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eE(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),$=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==k.value&&k.value===t||n)&&(_(e),n&&(K.current=!0))},[k.value]),et=n.useCallback(()=>null==R?void 0:R.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==k.value&&k.value===t||n)&&H(e)},[k.value]),en="popper"===l?ee:Q,el=en===ee?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,T.jsx)(X,{scope:r,content:R,viewport:I,onViewportChange:D,itemRefCallback:$,selectedItem:V,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:z,selectedItemText:L,position:l,isPositioned:B,searchRef:Y,children:(0,T.jsx)(j.Z,{as:J,allowPinchZoom:!0,children:(0,T.jsx)(f.M,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{var t;null===(t=k.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...C,...el,onPlaced:()=>O(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=W(q,r),s=G(q,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,u.e)(t,e=>f(e)),h=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:C}=s,M=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,u=e.width+i,s=Math.max(u,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,u=e.width+i,s=Math.max(u,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-s)]);d.style.minWidth=u+"px",d.style.right=c+"px"}let a=h(),u=window.innerHeight-20,s=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+s+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),M=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,k=y.offsetHeight/2,R=f+v+(y.offsetTop+k);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;d.style.height=R+Math.max(u-T,k+(e?j:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?M:0)+k);d.style.height=t+(g-R)+"px",x.scrollTop=R-T+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=u+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,y,S,i.dir,l]);(0,b.b)(()=>M(),[M]);let[j,k]=n.useState();(0,b.b)(()=>{p&&k(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===g.current&&(M(),null==C||C(),g.current=!1)},[M,C]);return(0,T.jsx)(et,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,T.jsx)(w.WV.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=_(r);return(0,T.jsx)(h.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=G(en,r),s=er(en,r),d=(0,u.e)(t,i.onViewportChange),c=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(I.Slot,{scope:r,children:(0,T.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=N(eo),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.M)();return(0,T.jsx)(ea,{scope:r,id:l,children:(0,T.jsx)(w.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});eu.displayName=eo;var es="SelectLabel",ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(es,r);return(0,T.jsx)(w.WV.div,{id:l.id,...n,ref:t})});ed.displayName=es;var ec="SelectItem",[ep,ef]=N(ec),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...s}=e,d=W(ec,r),c=G(ec,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,u.e)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),y=(0,v.M)(),b=n.useRef("touch"),S=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,T.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,T.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:x,onFocus:(0,a.M)(s.onFocus,()=>g(!0)),onBlur:(0,a.M)(s.onBlur,()=>g(!1)),onClick:(0,a.M)(s.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.M)(s.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.M)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(s.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.M)(s.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(R.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,s=W(eh,r),d=G(eh,r),c=ef(eh,r),p=A(eh,r),[f,v]=n.useState(null),h=(0,u.e)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(w.WV.span,{id:c.textId,...i,ref:h}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(i.children,s.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,T.jsx)(w.WV.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=G(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=G("SelectScrollButton",r),u=n.useRef(null),s=P(r),d=n.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.b)(()=>{var e;let t=s().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[s]),(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{d()})})}),eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(w.WV.div,{"aria-hidden":!0,...n,ref:t})});eM.displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=_(r),o=W(ej,r),a=G(ej,r);return o.open&&"popper"===a.position?(0,T.jsx)(h.Eh,{...l,...n,ref:t}):null}).displayName=ej;var eT=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,u.e)(t,a),s=(0,S.D)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[s,l]),(0,T.jsx)(w.WV.select,{...o,style:{...C.C2,...o.style},ref:i,defaultValue:l})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,x.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eE(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}eT.displayName="SelectBubbleInput";var eI=B,eP=K,eD=z,eN=U,eV=Z,e_=Y,eL=el,eW=eu,eH=ed,eA=ev,eB=em,eO=eg,eK=ey,eF=eS,ez=eM},65030:function(e,t,r){r.d(t,{D:function(){return l}});var n=r(2265);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},11780:function(e,t,r){r.d(t,{C2:function(){return a},fC:function(){return u}});var n=r(2265),l=r(29586),o=r(57437),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=n.forwardRef((e,t)=>(0,o.jsx)(l.WV.span,{...e,ref:t,style:{...a,...e.style}}));i.displayName="VisuallyHidden";var u=i}}]);