"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5723,6607],{53879:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37841:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},70699:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},3665:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(57977).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},44991:function(e,t,r){r.d(t,{M:function(){return n}});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},84104:function(e,t,r){r.d(t,{b:function(){return i},k:function(){return u}});var n=r(2265),o=r(57437);function u(e,t){let r=n.createContext(t),u=e=>{let{children:t,...u}=e,i=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(r.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=n.useContext(r);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,u){let i=n.createContext(u),c=r.length;r=[...r,u];let a=t=>{let{scope:r,children:u,...a}=t,l=r?.[e]?.[c]||i,f=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(l.Provider,{value:f,children:u})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[c]||i,l=n.useContext(a);if(l)return l;if(void 0!==u)return u;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},29586:function(e,t,r){r.d(t,{WV:function(){return c},jH:function(){return a}});var n=r(2265),o=r(54887),u=r(59143),i=r(57437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,u.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...u}=e,c=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(c,{...u,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},51014:function(e,t,r){r.d(t,{f:function(){return l}});var n=r(2265),o=r(29586),u=r(57437),i="horizontal",c=["horizontal","vertical"],a=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=i,...a}=e,l=c.includes(n)?n:i;return(0,u.jsx)(o.WV.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...a,ref:t})});a.displayName="Separator";var l=a},94845:function(e,t,r){r.d(t,{bU:function(){return S},fC:function(){return g}});var n=r(2265),o=r(44991),u=r(61266),i=r(84104),c=r(9310),a=r(65030),l=r(76769),f=r(29586),s=r(57437),d="Switch",[p,v]=(0,i.b)(d),[h,b]=p(d),m=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:a,defaultChecked:l,required:p,disabled:v,value:b="on",onCheckedChange:m,form:y,...k}=e,[g,S]=n.useState(null),C=(0,u.e)(t,e=>S(e)),N=n.useRef(!1),E=!g||y||!!g.closest("form"),[M,j]=(0,c.T)({prop:a,defaultProp:null!=l&&l,onChange:m,caller:d});return(0,s.jsxs)(h,{scope:r,checked:M,disabled:v,children:[(0,s.jsx)(f.WV.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":v?"":void 0,disabled:v,value:b,...k,ref:C,onClick:(0,o.M)(e.onClick,e=>{j(e=>!e),E&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),E&&(0,s.jsx)(w,{control:g,bubbles:!N.current,name:i,value:b,checked:M,required:p,disabled:v,form:y,style:{transform:"translateX(-100%)"}})]})});m.displayName=d;var y="SwitchThumb",k=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=b(y,r);return(0,s.jsx)(f.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});k.displayName=y;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:c=!0,...f}=e,d=n.useRef(null),p=(0,u.e)(d,t),v=(0,a.D)(i),h=(0,l.t)(o);return n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let r=new Event("click",{bubbles:c});t.call(e,i),e.dispatchEvent(r)}},[v,i,c]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...f,tabIndex:-1,ref:p,style:{...f.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var g=m,S=k},9310:function(e,t,r){r.d(t,{T:function(){return c}});var n,o=r(2265),u=r(32618),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function c({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[u,c,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),u=o.useRef(r),c=o.useRef(t);return i(()=>{c.current=t},[t]),o.useEffect(()=>{u.current!==r&&(c.current?.(r),u.current=r)},[r,u]),[r,n,c]}({defaultProp:t,onChange:r}),l=void 0!==e,f=l?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}return[f,o.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else c(t)},[l,e,c,a])]}Symbol("RADIX:SYNC_STATE")},32618:function(e,t,r){r.d(t,{b:function(){return o}});var n=r(2265),o=globalThis?.document?n.useLayoutEffect:()=>{}},65030:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(2265);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},76769:function(e,t,r){r.d(t,{t:function(){return u}});var n=r(2265),o=r(32618);function u(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let u=t[0];if("borderBoxSize"in u){let e=u.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);