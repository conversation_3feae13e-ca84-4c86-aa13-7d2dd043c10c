"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6389],{78807:function(e,t,r){r.d(t,{L:function(){return d}});var a=r(57437),s=r(2265),n=r(23441),l=r(28670),o=r(79580),i=r(22169);let c=s.forwardRef((e,t)=>{let{label:r,placeholder:c="Select option",value:d,onValueChange:u,options:f=[],error:m,disabled:h=!1,required:p=!1,onSearch:g,isLoading:x=!1,searchPlaceholder:y="Search...",...b}=e,[j,w]=(0,s.useState)(!1),[N,v]=(0,s.useState)(""),k=(0,s.useRef)(null),T=(0,s.useRef)(null),C="http://*************";(0,s.useEffect)(()=>{console.log("\uD83D\uDD0D SearchableSelectField state change:",{isOpen:j,searchQuery:N,optionsLength:f.length,placeholder:c})},[j,N,f.length,c]),(0,s.useEffect)(()=>{console.log("\uD83D\uDCCB Options changed for:",{placeholder:c,newOptionsLength:f.length,firstFewOptions:f.slice(0,3).map(e=>e.label)})},[f,c]);let[S,R]=(0,s.useState)(!1);(0,s.useEffect)(()=>{N.trim()&&f.length>0&&(console.log("\uD83D\uDD04 Keeping dropdown open due to search results:",{searchQuery:N,optionsLength:f.length,currentIsOpen:j,placeholder:c}),R(!0),j||w(!0))},[f.length,N,j,c]),(0,s.useEffect)(()=>{let e=e=>{k.current&&!k.current.contains(e.target)&&(console.log("\uD83D\uDDB1️ Click outside detected - closing dropdown"),w(!1),R(!1))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,s.useEffect)(()=>{j&&T.current&&T.current.focus()},[j]),(0,s.useEffect)(()=>{g&&(N.trim()?g(N):g(""))},[N,g]);let L=f.find(e=>e.value===d),A=e=>{console.log("✅ Option selected - closing dropdown:",e),null==u||u(e),w(!1),v(""),R(!1)};return(0,a.jsxs)("div",{className:"space-y-2",ref:t,children:[r&&(0,a.jsxs)("label",{className:"text-sm font-medium text-gray-700",children:[r,p&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",ref:k,children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{h||(console.log("\uD83D\uDD18 Dropdown button clicked:",{currentIsOpen:j,willBeOpen:!j}),w(!j))},disabled:h,className:(0,i.cn)("w-full flex items-center justify-between px-3 py-2 text-left bg-white border rounded-md shadow-sm","focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",h&&"bg-gray-50 text-gray-500 cursor-not-allowed",m&&"border-red-500 focus:ring-red-500 focus:border-red-500",!m&&!h&&"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:L?(0,a.jsxs)(a.Fragment,{children:[L.logo&&(0,a.jsx)("img",{src:"".concat(C,"/").concat(L.logo),alt:L.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("div",{className:"truncate",children:L.label}),L.subtitle&&(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",children:L.subtitle})]})]}):(0,a.jsx)("span",{className:"text-gray-500",children:c})}),(0,a.jsx)(n.Z,{className:(0,i.cn)("w-4 h-4 text-gray-400 transition-transform",j&&"transform rotate-180")})]}),j&&(0,a.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden",children:[(0,a.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.Z,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)("input",{ref:T,type:"text",placeholder:y,value:N,onChange:e=>v(e.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,a.jsx)("div",{className:"max-h-60 overflow-y-auto",children:0!==f.length||x?(0,a.jsxs)(a.Fragment,{children:[f.map((e,t)=>(0,a.jsxs)("button",{type:"button",onClick:()=>A(e.value),className:(0,i.cn)("w-full flex items-center space-x-2 px-3 py-2 text-left text-sm hover:bg-gray-50",d===e.value&&"bg-blue-50 text-blue-700"),children:[e.logo&&(0,a.jsx)("img",{src:"".concat(C,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("div",{className:"truncate",children:e.label}),e.subtitle&&(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",children:e.subtitle})]})]},e.uniqueKey||"".concat(e.value,"-").concat(t))),x&&(0,a.jsxs)("div",{className:"w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-gray-500",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Searching..."})]})]}):(0,a.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:"No options found"})})]})]}),m&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:m})]})});c.displayName="SearchableSelectField";let d=s.memo(c)},15671:function(e,t,r){r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return l},aY:function(){return d},ll:function(){return i}});var a=r(57437),s=r(2265),n=r(22169);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...s})});l.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},17818:function(e,t,r){r.d(t,{ji:function(){return y},iN:function(){return j},hj:function(){return b},UP:function(){return p},mg:function(){return x},XL:function(){return g}});var a=r(57437),s=r(2265),n=r(12647),l=r(22782),o=r(3549),i=r(18641),c=r(86969),d=r(80037),u=r(22169);let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(c.fC,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...s,children:(0,a.jsx)(c.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})})});f.displayName=c.fC.displayName;var m=r(31657);let h=(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:l,required:o,className:i,children:c}=e;return(0,a.jsxs)("div",{ref:t,className:(0,u.cn)("space-y-2",i),children:[r&&(0,a.jsxs)(n._,{className:(0,u.cn)("text-sm font-medium",l&&"text-red-600"),children:[r,o&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),c,s&&!l&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:s}),l&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:l})]})});h.displayName="FormField";let p=(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:n,required:o,className:i,...c}=e;return(0,a.jsx)(h,{label:r,description:s,error:n,required:o,children:(0,a.jsx)(l.I,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",i),...c})})});p.displayName="InputField";let g=(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:n,required:l,className:i,...c}=e;return(0,a.jsx)(h,{label:r,description:s,error:n,required:l,children:(0,a.jsx)(o.g,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",i),...c})})});g.displayName="TextareaField";let x=(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:n,required:l,placeholder:o,value:c,onValueChange:d,options:f,className:m,disabled:p}=e,g=f.find(e=>e.value===c),x="http://*************";return(0,a.jsx)(h,{label:r,description:s,error:n,required:l,children:(0,a.jsxs)(i.Ph,{value:c,onValueChange:d,disabled:p,children:[(0,a.jsx)(i.i4,{ref:t,className:(0,u.cn)(n&&"border-red-500 focus:border-red-500",m),children:(0,a.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,a.jsx)("div",{className:"flex items-center space-x-2 flex-1",children:g?g?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[g.logo&&(0,a.jsx)("img",{src:"".concat(x,"/").concat(g.logo),alt:g.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsx)("span",{children:g.label})]}):o:(0,a.jsx)("span",{className:"text-muted-foreground",children:o})})})}),(0,a.jsx)(i.Bw,{children:f.map(e=>(0,a.jsx)(i.Ql,{value:e.value,disabled:e.disabled,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&(0,a.jsx)("img",{src:"".concat(x,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsx)("span",{children:e.label})]})},e.value))})]})})});x.displayName="SelectField";let y=(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:l,checked:o,onCheckedChange:i,className:c}=e;return(0,a.jsx)(h,{description:s,error:l,className:c,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f,{ref:t,checked:o,onCheckedChange:i,className:(0,u.cn)(l&&"border-red-500")}),r&&(0,a.jsx)(n._,{className:(0,u.cn)("text-sm font-normal cursor-pointer",l&&"text-red-600"),children:r})]})})});y.displayName="CheckboxField",(0,s.forwardRef)((e,t)=>{let{label:r,description:s,error:l,required:o,value:i,onValueChange:c,options:d,orientation:f="vertical",className:p}=e;return(0,a.jsx)(h,{label:r,description:s,error:l,required:o,className:p,children:(0,a.jsx)(m.E,{ref:t,value:i,onValueChange:c,className:(0,u.cn)("horizontal"===f?"flex flex-row space-x-4":"space-y-2"),children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.m,{value:e.value,disabled:e.disabled,className:(0,u.cn)(l&&"border-red-500")}),(0,a.jsx)(n._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})}).displayName="RadioField";let b=e=>{let{title:t,description:r,children:s,className:n}=e;return(0,a.jsxs)("div",{className:(0,u.cn)("space-y-4",n),children:[(t||r)&&(0,a.jsxs)("div",{className:"space-y-1",children:[t&&(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t}),r&&(0,a.jsx)("p",{className:"text-sm text-gray-600",children:r})]}),(0,a.jsx)("div",{className:"space-y-4",children:s})]})},j=e=>{let{children:t,className:r,align:s="right"}=e;return(0,a.jsx)("div",{className:(0,u.cn)("flex space-x-2 pt-4 border-t","left"===s&&"justify-start","center"===s&&"justify-center","right"===s&&"justify-end",r),children:t})}},22782:function(e,t,r){r.d(t,{I:function(){return l}});var a=r(57437),s=r(2265),n=r(22169);let l=s.forwardRef((e,t)=>{let{className:r,type:s,...l}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});l.displayName="Input"},12647:function(e,t,r){r.d(t,{_:function(){return c}});var a=r(57437),s=r(2265),n=r(24602),l=r(49769),o=r(22169);let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.f,{ref:t,className:(0,o.cn)(i(),r),...s})});c.displayName=n.f.displayName},31657:function(e,t,r){r.d(t,{E:function(){return i},m:function(){return c}});var a=r(57437),s=r(2265),n=r(68928),l=r(37501),o=r(22169);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.fC,{className:(0,o.cn)("grid gap-2",r),...s,ref:t})});i.displayName=n.fC.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.ck,{ref:t,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),...s,children:(0,a.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,a.jsx)(l.Z,{className:"h-3.5 w-3.5 fill-primary"})})})});c.displayName=n.ck.displayName},18641:function(e,t,r){r.d(t,{Bw:function(){return p},Ph:function(){return d},Ql:function(){return g},i4:function(){return f},ki:function(){return u}});var a=r(57437),s=r(2265),n=r(18178),l=r(23441),o=r(85159),i=r(80037),c=r(22169);let d=n.fC;n.ZA;let u=n.B4,f=s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.xz,{ref:t,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[s,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=n.xz.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})})});m.displayName=n.u_.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});h.displayName=n.$G.displayName;let p=s.forwardRef((e,t)=>{let{className:r,children:s,position:l="popper",...o}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:t,className:(0,c.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...o,children:[(0,a.jsx)(m,{}),(0,a.jsx)(n.l_,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});p.displayName=n.VY.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.__,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",r),...s})}).displayName=n.__.displayName;let g=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(n.ck,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:s})]})});g.displayName=n.ck.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.Z0,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=n.Z0.displayName},77625:function(e,t,r){r.d(t,{Od:function(){return n},hM:function(){return o},q4:function(){return l}});var a=r(57437),s=r(22169);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...r})}let l=e=>{let{className:t}=e;return(0,a.jsxs)("div",{className:(0,s.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n,{className:"h-4 w-3/4"}),(0,a.jsx)(n,{className:"h-4 w-1/2"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n,{className:"h-3 w-full"}),(0,a.jsx)(n,{className:"h-3 w-full"}),(0,a.jsx)(n,{className:"h-3 w-2/3"})]})]})},o=e=>{let{rows:t=5,columns:r=4,className:l}=e;return(0,a.jsx)("div",{className:(0,s.cn)("space-y-4",l),children:(0,a.jsxs)("div",{className:"border rounded-lg",children:[(0,a.jsx)("div",{className:"border-b p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,a.jsx)(n,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,a.jsx)(n,{className:"h-4 w-full"},t))})},t))]})})}},86468:function(e,t,r){r.d(t,{r:function(){return o}});var a=r(57437),s=r(2265),n=r(94845),l=r(22169);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.fC,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...s,ref:t,children:(0,a.jsx)(n.bU,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});o.displayName=n.fC.displayName},3549:function(e,t,r){r.d(t,{g:function(){return l}});var a=r(57437),s=r(2265),n=r(22169);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});l.displayName="Textarea"},85110:function(e,t,r){r.d(t,{Z:function(){return l}});var a=r(57437);r(2265);var s=r(86468),n=r(12647);function l(e){let{checked:t,onCheckedChange:r,label:l,description:o,disabled:i=!1,size:c="md",variant:d="default"}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(s.r,{id:l,checked:t,onCheckedChange:r,disabled:i}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(n._,{htmlFor:l,className:"font-medium cursor-pointer ".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[c]," ").concat({default:t?"text-blue-700":"text-gray-700",success:t?"text-green-700":"text-gray-700",warning:t?"text-yellow-700":"text-gray-700",danger:t?"text-red-700":"text-gray-700"}[d]," ").concat(i?"opacity-50":""),children:l}),o&&(0,a.jsx)("span",{className:"text-xs text-gray-500 ".concat(i?"opacity-50":""),children:o})]})]})}},74921:function(e,t,r){r.d(t,{x:function(){return l}});var a=r(73107),s=r(48763);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=s.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:a}=await r.json(),s=e.user;if(s)return e.setAuth(s,a,t),this.setAuthToken(a),console.log("✅ Token refreshed successfully"),a}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:a,reject:s}=r;e?s(e):a(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let l=new n},47011:function(e,t,r){r.d(t,{A:function(){return n}});var a=r(74921);let s=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch leagues");return await r.json()},getLeagueById:async(e,t)=>{let r=t?"".concat(e,"-").concat(t):e.toString(),a=await fetch("/api/leagues/".concat(r),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch league ".concat(e));return await a.json()},createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t,r)=>{let a=s(),l={"Content-Type":"application/json"};a&&(l.Authorization="Bearer ".concat(a));let o=await n.getLeagueById(e,r);if(!o||!o.id)throw Error("League not found: ".concat(e).concat(r?"-".concat(r):""));let i=await fetch("/api/leagues/".concat(o.id),{method:"PATCH",headers:l,body:JSON.stringify(t)});if(!i.ok)throw Error((await i.json()).message||"Failed to update league ".concat(e));return await i.json()},deleteLeague:async(e,t)=>{let r=await n.getLeagueById(e,t);if(!r||!r.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await a.x.delete("/football/leagues/".concat(r.id))},getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t,r)=>n.updateLeague(e,{active:t},r)}},33016:function(e,t,r){r.d(t,{k:function(){return s}});var a=r(74921);let s={getTeams:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/teams?".concat(t.toString()),{method:"GET",headers:(()=>{let e={"Content-Type":"application/json"};{try{let r=localStorage.getItem("auth-storage");if(r){var t;let a=JSON.parse(r),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return e.Authorization="Bearer ".concat(s),e}}catch(e){console.warn("Failed to parse auth storage:",e)}let r=localStorage.getItem("accessToken");r&&(e.Authorization="Bearer ".concat(r))}return e})()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch teams");return await r.json()},getTeamById:async e=>await a.x.get("/football/teams/".concat(e)),getTeamStatistics:async(e,t,r)=>{let s=new URLSearchParams({league:e.toString(),season:t.toString(),team:r.toString()});return await a.x.get("/football/teams/statistics?".concat(s.toString()))},getTeamsByLeague:async(e,t)=>{let r={league:e};return t&&(r.season=t),s.getTeams(r)},getTeamsByCountry:async e=>s.getTeams({country:e}),searchTeams:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=await s.getTeams(t),a=r.data.filter(t=>{var r;return t.name.toLowerCase().includes(e.toLowerCase())||(null===(r=t.code)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase()))});return{data:a,meta:{...r.meta,totalItems:a.length,totalPages:Math.ceil(a.length/(t.limit||10))}}},deleteTeam:async e=>{await a.x.delete("/football/teams/".concat(e))}}},48763:function(e,t,r){r.d(t,{t:function(){return l}});var a=r(12574),s=r(65249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},l=(0,a.U)()((0,s.tJ)((e,t)=>({...n,setAuth:(t,r,a)=>{e({user:t,accessToken:r,refreshToken:a,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let a=t().user;a&&e({user:{...a,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let a=Array.isArray(e)?e:[e];return"admin"===r.role||(a.includes("editor")?["admin","editor"].includes(r.role):a.includes("moderator")?["admin","editor","moderator"].includes(r.role):a.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){r.d(t,{cn:function(){return n}});var a=r(75504),s=r(51367);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}}}]);