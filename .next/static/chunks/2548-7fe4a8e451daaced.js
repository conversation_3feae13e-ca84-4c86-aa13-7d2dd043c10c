"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2548],{57977:function(e,t,r){r.d(t,{Z:function(){return d}});var n=r(2265);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:l,className:i="",children:o,iconNode:s,...d},f)=>(0,n.createElement)("svg",{ref:f,...c,width:t,height:t,stroke:e,strokeWidth:l?24*Number(r)/Number(t):r,className:a("lucide",i),...!o&&!u(d)&&{"aria-hidden":"true"},...d},[...s.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(o)?o:[o]])),d=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},u)=>(0,n.createElement)(s,{ref:u,iconNode:t,className:a(`lucide-${l(o(e))}`,`lucide-${e}`,r),...i}));return r.displayName=o(e),r}},86969:function(e,t,r){r.d(t,{fC:function(){return w},z$:function(){return C}});var n=r(2265),l=r(61266),i=r(84104),o=r(44991),a=r(9310),u=r(65030),c=r(76769),s=r(12642),d=r(29586),f=r(57437),p="Checkbox",[m,h]=(0,i.b)(p),[y,v]=m(p);function b(e){let{__scopeCheckbox:t,checked:r,children:l,defaultChecked:i,disabled:o,form:u,name:c,onCheckedChange:s,required:d,value:m="on",internal_do_not_use_render:h}=e,[v,b]=(0,a.T)({prop:r,defaultProp:null!=i&&i,onChange:s,caller:p}),[k,g]=n.useState(null),[w,x]=n.useState(null),C=n.useRef(!1),j=!k||!!u||!!k.closest("form"),E={checked:v,disabled:o,setChecked:b,control:k,setControl:g,name:c,form:u,value:m,hasConsumerStoppedPropagationRef:C,required:d,defaultChecked:!N(i)&&i,isFormControl:j,bubbleInput:w,setBubbleInput:x};return(0,f.jsx)(y,{scope:t,...E,children:"function"==typeof h?h(E):l})}var k="CheckboxTrigger",g=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:i,onClick:a,...u}=e,{control:c,value:s,disabled:p,checked:m,required:h,setControl:y,setChecked:b,hasConsumerStoppedPropagationRef:g,isFormControl:w,bubbleInput:x}=v(k,r),C=(0,l.e)(t,y),j=n.useRef(m);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>b(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,b]),(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":N(m)?"mixed":m,"aria-required":h,"data-state":R(m),"data-disabled":p?"":void 0,disabled:p,value:s,...u,ref:C,onKeyDown:(0,o.M)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.M)(a,e=>{b(e=>!!N(e)||!e),x&&w&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});g.displayName=k;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:l,defaultChecked:i,required:o,disabled:a,value:u,onCheckedChange:c,form:s,...d}=e;return(0,f.jsx)(b,{__scopeCheckbox:r,checked:l,defaultChecked:i,disabled:a,required:o,onCheckedChange:c,name:n,form:s,value:u,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(g,{...d,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(E,{__scopeCheckbox:r})]})}})});w.displayName=p;var x="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...l}=e,i=v(x,r);return(0,f.jsx)(s.z,{present:n||N(i.checked)||!0===i.checked,children:(0,f.jsx)(d.WV.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=x;var j="CheckboxBubbleInput",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...i}=e,{control:o,hasConsumerStoppedPropagationRef:a,checked:s,defaultChecked:p,required:m,disabled:h,name:y,value:b,form:k,bubbleInput:g,setBubbleInput:w}=v(j,r),x=(0,l.e)(t,w),C=(0,u.D)(s),E=(0,c.t)(o);n.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(C!==s&&e){let r=new Event("click",{bubbles:t});g.indeterminate=N(s),e.call(g,!N(s)&&s),g.dispatchEvent(r)}},[g,C,s,a]);let R=n.useRef(!N(s)&&s);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:R.current,required:m,disabled:h,name:y,value:b,form:k,...i,tabIndex:-1,ref:x,style:{...i.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function N(e){return"indeterminate"===e}function R(e){return N(e)?"indeterminate":e?"checked":"unchecked"}E.displayName=j},61266:function(e,t,r){r.d(t,{F:function(){return i},e:function(){return o}});var n=r(2265);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},59143:function(e,t,r){r.d(t,{Z8:function(){return o},g7:function(){return a},sA:function(){return c}});var n=r(2265),l=r(61266),i=r(57437);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,o;let a=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,u=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,l.F)(t,a):a),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),u=a.find(s);if(u){let e=u.props.children,l=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var a=o("Slot"),u=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},49769:function(e,t,r){r.d(t,{j:function(){return o}});var n=r(75504);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.W,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=t,u=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=l(t)||l(n);return o[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,u,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}}]);