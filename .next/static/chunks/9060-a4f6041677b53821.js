"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9060],{22007:function(e,t,a){a.d(t,{V:function(){return u}});var r=a(57437),s=a(2265),n=a(52235),o=a(23441),i=a(28670),l=a(79580),c=a(22169);let u=e=>{let{value:t,onChange:a,onSearch:u,placeholder:d="Search...",label:f,description:m,error:g,disabled:h=!1,className:b,emptyMessage:p="No results found",loadingMessage:y="Searching...",clearable:x=!0}=e,[v,w]=(0,s.useState)(!1),[j,N]=(0,s.useState)(""),[C,I]=(0,s.useState)([]),[S,F]=(0,s.useState)(!1),[k,T]=(0,s.useState)(null),E=(0,s.useRef)(null),L=(0,s.useRef)(null),A=(0,s.useRef)();(0,s.useEffect)(()=>(A.current&&clearTimeout(A.current),j.length>=2?A.current=setTimeout(async()=>{F(!0);try{let e=await u(j);I(e)}catch(e){console.error("Search error:",e),I([])}finally{F(!1)}},300):I([]),()=>{A.current&&clearTimeout(A.current)}),[j,u]),(0,s.useEffect)(()=>{let e=e=>{E.current&&!E.current.contains(e.target)&&w(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,s.useEffect)(()=>{if(t&&C.length>0){let e=C.find(e=>e.value===t);e&&T(e)}else t||T(null)},[t,C]);let D=e=>{T(e),a(e.value),w(!1),N("")},P=()=>{T(null),a(""),N("")};return(0,r.jsxs)("div",{className:(0,c.cn)("space-y-2",b),children:[f&&(0,r.jsx)("label",{className:"text-sm font-semibold text-slate-700",children:f}),(0,r.jsxs)("div",{ref:E,className:"relative",children:[k&&!v&&(0,r.jsxs)("div",{className:(0,c.cn)("flex items-center gap-3 p-3 border rounded-lg bg-white",g?"border-red-300":"border-slate-300",h?"bg-slate-50 cursor-not-allowed":"cursor-pointer hover:border-slate-400"),onClick:()=>!h&&w(!0),children:[k.image&&(0,r.jsx)("img",{src:k.image,alt:k.label,className:"w-6 h-6 rounded object-cover"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 truncate",children:k.label}),k.subtitle&&(0,r.jsx)("p",{className:"text-sm text-slate-500 truncate",children:k.subtitle})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[x&&(0,r.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),P()},className:"p-1 hover:bg-slate-100 rounded",disabled:h,children:(0,r.jsx)(n.Z,{className:"w-4 h-4 text-slate-400"})}),(0,r.jsx)(o.Z,{className:"w-4 h-4 text-slate-400"})]})]}),(!k||v)&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(i.Z,{className:"h-4 w-4 text-slate-400"})}),(0,r.jsx)("input",{ref:L,type:"text",value:j,onChange:e=>N(e.target.value),onFocus:()=>{w(!0),L.current&&L.current.select()},placeholder:d,disabled:h,className:(0,c.cn)("w-full pl-10 pr-10 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",g?"border-red-300 focus:border-red-500":"border-slate-300 focus:border-blue-500",h&&"bg-slate-50 cursor-not-allowed")}),x&&(k||j)&&(0,r.jsx)("button",{type:"button",onClick:P,className:"absolute inset-y-0 right-0 pr-3 flex items-center",disabled:h,children:(0,r.jsx)(n.Z,{className:"h-4 w-4 text-slate-400 hover:text-slate-600"})})]}),v&&(0,r.jsx)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-slate-200 rounded-lg shadow-lg max-h-60 overflow-auto",children:S?(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)(l.Z,{className:"w-4 h-4 animate-spin mr-2"}),(0,r.jsx)("span",{className:"text-sm text-slate-500",children:y})]}):C.length>0?(0,r.jsx)("div",{className:"py-1",children:C.map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>D(e),className:"w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-slate-50 focus:bg-slate-50 focus:outline-none",children:[e.image&&(0,r.jsx)("img",{src:e.image,alt:e.label,className:"w-6 h-6 rounded object-cover flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-slate-900 truncate",children:e.label}),e.subtitle&&(0,r.jsx)("p",{className:"text-sm text-slate-500 truncate",children:e.subtitle})]})]},e.value))}):j.length>=2?(0,r.jsx)("div",{className:"py-4 text-center text-sm text-slate-500",children:p}):(0,r.jsx)("div",{className:"py-4 text-center text-sm text-slate-500",children:"Type at least 2 characters to search"})})]}),m&&!g&&(0,r.jsx)("p",{className:"text-sm text-slate-500",children:m}),g&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:g})]})}},32910:function(e,t,a){a.d(t,{Z:function(){return k}});var r=a(57437),s=a(69102),n=a.n(s),o=a(2265),i=a(22169),l=a(72733),c=a(94415),u=a(42804),d=a(33707),f=a(20403),m=a(99925),g=a(22455),h=a(34864),b=a(65694),p=a(48541),y=a(2866),x=a(91403),v=a(92894),w=a(38018),j=a(32989),N=a(20244),C=a(65561),I=a(12882),S=a(36636),F=a(575);function k(e){var t;let{value:a="",onChange:s,placeholder:k="Start writing...",className:T,error:E,minHeight:L=300,showCharCount:A=!1,maxLength:D}=e,P=(0,o.useRef)(null),[q,z]=(0,o.useState)(!1),U=P.current&&(null===(t=P.current.textContent)||void 0===t?void 0:t.length)||0,H=!!D&&U>D,B=()=>{if(P.current){let e=P.current;e.style.height="auto";let t=Math.max(e.scrollHeight,L);e.style.height="".concat(t,"px")}};(0,o.useEffect)(()=>{P.current&&P.current.innerHTML!==a&&(P.current.innerHTML=a,setTimeout(B,0))},[a,L]),(0,o.useEffect)(()=>{let e=new ResizeObserver(()=>{B()});return P.current&&(e.observe(P.current),setTimeout(B,100)),()=>{e.disconnect()}},[]);let Z=()=>{P.current&&s&&(s(P.current.innerHTML),setTimeout(B,0))},K=(e,t)=>{var a;document.execCommand(e,!1,t),null===(a=P.current)||void 0===a||a.focus(),Z()},Q=e=>{K("formatBlock",e)},R=[{icon:l.Z,command:"bold",title:"Bold"},{icon:c.Z,command:"italic",title:"Italic"},{icon:u.Z,command:"underline",title:"Underline"},{icon:d.Z,command:"strikeThrough",title:"Strikethrough"},{icon:f.Z,command:"formatBlock",value:"pre",title:"Code Block"},{icon:m.Z,command:"insertUnorderedList",title:"Bullet List"},{icon:g.Z,command:"insertOrderedList",title:"Numbered List"},{icon:h.Z,command:"formatBlock",value:"blockquote",title:"Quote"},{icon:b.Z,command:"justifyLeft",title:"Align Left"},{icon:p.Z,command:"justifyCenter",title:"Align Center"},{icon:y.Z,command:"justifyRight",title:"Align Right"},{icon:x.Z,command:"subscript",title:"Subscript"},{icon:v.Z,command:"superscript",title:"Superscript"},{icon:w.Z,command:"undo",title:"Undo"},{icon:j.Z,command:"redo",title:"Redo"}];return(0,r.jsxs)("div",{className:"jsx-fa9b18c3ff7e3fd3 "+((0,i.cn)("w-full",T)||""),children:[(0,r.jsxs)("div",{className:"jsx-fa9b18c3ff7e3fd3 border border-slate-300 border-b-0 rounded-t-lg bg-slate-50 p-2 flex flex-wrap gap-1",children:[(0,r.jsxs)("select",{onChange:e=>Q(e.target.value),defaultValue:"",className:"jsx-fa9b18c3ff7e3fd3 px-2 py-1 text-sm border border-slate-200 rounded bg-white",children:[(0,r.jsx)("option",{value:"",className:"jsx-fa9b18c3ff7e3fd3",children:"Normal"}),(0,r.jsx)("option",{value:"h1",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 1"}),(0,r.jsx)("option",{value:"h2",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 2"}),(0,r.jsx)("option",{value:"h3",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 3"}),(0,r.jsx)("option",{value:"h4",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 4"}),(0,r.jsx)("option",{value:"h5",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 5"}),(0,r.jsx)("option",{value:"h6",className:"jsx-fa9b18c3ff7e3fd3",children:"Heading 6"}),(0,r.jsx)("option",{value:"p",className:"jsx-fa9b18c3ff7e3fd3",children:"Paragraph"})]}),(0,r.jsx)("div",{className:"jsx-fa9b18c3ff7e3fd3 w-px h-6 bg-slate-300 mx-1"}),R.map(e=>{let{icon:t,command:a,value:s,title:n}=e;return(0,r.jsx)(F.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>s?K(a,s):K(a),title:n,children:(0,r.jsx)(t,{className:"jsx-fa9b18c3ff7e3fd3 h-4 w-4"})},a)}),(0,r.jsx)("div",{className:"jsx-fa9b18c3ff7e3fd3 w-px h-6 bg-slate-300 mx-1"}),(0,r.jsx)(F.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter URL:");e&&K("createLink",e)},title:"Insert Link",children:(0,r.jsx)(N.Z,{className:"h-4 w-4"})}),(0,r.jsx)(F.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter image URL:");e&&K("insertImage",e)},title:"Insert Image",children:(0,r.jsx)(C.Z,{className:"h-4 w-4"})}),(0,r.jsx)(F.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter color (hex, rgb, or color name):");e&&K("foreColor",e)},title:"Text Color",children:(0,r.jsx)(I.Z,{className:"h-4 w-4"})}),(0,r.jsx)(F.z,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-slate-200",onClick:()=>{let e=prompt("Enter background color (hex, rgb, or color name):");e&&K("hiliteColor",e)},title:"Background Color",children:(0,r.jsx)(S.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{ref:P,contentEditable:!0,onInput:Z,onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text/html");if(t){let e=document.createElement("div");e.innerHTML=t,e.querySelectorAll("script, style, meta, link").forEach(e=>e.remove()),e.querySelectorAll("*").forEach(e=>{let t=["href","src","alt","title"];Array.from(e.attributes).forEach(a=>{t.includes(a.name)||e.removeAttribute(a.name)})}),t=e.innerHTML}else t=e.clipboardData.getData("text/plain");t&&document.execCommand("insertHTML",!1,t),setTimeout(()=>{Z()},0)},onKeyDown:e=>{"Enter"===e.key&&setTimeout(B,0)},onFocus:()=>{z(!0),setTimeout(B,0)},onBlur:()=>z(!1),style:{minHeight:"".concat(L,"px"),height:"".concat(L,"px"),transition:"height 0.2s ease-in-out"},"data-placeholder":k,suppressContentEditableWarning:!0,className:"jsx-fa9b18c3ff7e3fd3 "+((0,i.cn)("w-full px-4 py-3 border border-slate-300 rounded-b-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500","prose prose-sm max-w-none overflow-hidden resize-none",E&&"border-red-300 focus:border-red-500 focus:ring-red-500",!a&&"text-slate-500")||"")}),!a&&!q&&(0,r.jsx)("div",{style:{top:"52px"},className:"jsx-fa9b18c3ff7e3fd3 absolute pointer-events-none text-slate-500 px-4 py-3",children:k}),E&&(0,r.jsx)("p",{className:"jsx-fa9b18c3ff7e3fd3 text-sm text-red-600 mt-1",children:E}),A&&(0,r.jsx)("div",{className:"jsx-fa9b18c3ff7e3fd3 flex justify-end mt-2",children:(0,r.jsxs)("p",{className:"jsx-fa9b18c3ff7e3fd3 "+((0,i.cn)("text-xs",H?"text-red-600":"text-slate-500")||""),children:[U,D?" / ".concat(D):""," characters"]})}),(0,r.jsx)(n(),{id:"fa9b18c3ff7e3fd3",children:'[contenteditable].jsx-fa9b18c3ff7e3fd3:empty:before{content:attr(data-placeholder);color:#94a3b8}[contenteditable].jsx-fa9b18c3ff7e3fd3:focus:before{content:""}[contenteditable].jsx-fa9b18c3ff7e3fd3 h1.jsx-fa9b18c3ff7e3fd3{font-size:2rem;font-weight:bold;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h2.jsx-fa9b18c3ff7e3fd3{font-size:1.5rem;font-weight:bold;margin:.875rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h3.jsx-fa9b18c3ff7e3fd3{font-size:1.25rem;font-weight:bold;margin:.75rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h4.jsx-fa9b18c3ff7e3fd3{font-size:1.125rem;font-weight:bold;margin:.625rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h5.jsx-fa9b18c3ff7e3fd3{font-size:1rem;font-weight:bold;margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 h6.jsx-fa9b18c3ff7e3fd3{font-size:.875rem;font-weight:bold;margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 p.jsx-fa9b18c3ff7e3fd3{margin:.5rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 blockquote.jsx-fa9b18c3ff7e3fd3{border-left:4px solid#e2e8f0;padding-left:1rem;margin:1rem 0;font-style:italic;color:#64748b}[contenteditable].jsx-fa9b18c3ff7e3fd3 pre.jsx-fa9b18c3ff7e3fd3{background-color:#f1f5f9;padding:1rem;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-family:"Courier New",monospace;overflow-x:auto;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 ul.jsx-fa9b18c3ff7e3fd3,[contenteditable].jsx-fa9b18c3ff7e3fd3 ol.jsx-fa9b18c3ff7e3fd3{margin:.5rem 0;padding-left:2rem}[contenteditable].jsx-fa9b18c3ff7e3fd3 li.jsx-fa9b18c3ff7e3fd3{margin:.25rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 img.jsx-fa9b18c3ff7e3fd3{max-width:100%;height:auto;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;margin:1rem 0}[contenteditable].jsx-fa9b18c3ff7e3fd3 a.jsx-fa9b18c3ff7e3fd3{color:#3b82f6;text-decoration:underline}[contenteditable].jsx-fa9b18c3ff7e3fd3 a.jsx-fa9b18c3ff7e3fd3:hover{color:#1d4ed8}'})]})}},86468:function(e,t,a){a.d(t,{r:function(){return i}});var r=a(57437),s=a(2265),n=a(94845),o=a(22169);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.fC,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...s,ref:t,children:(0,r.jsx)(n.bU,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=n.fC.displayName},28418:function(e,t,a){a.d(t,{Kf:function(){return c},PP:function(){return u},sz:function(){return l},up:function(){return o},zf:function(){return i}});let r="http://172.31.213.61",s=e=>{if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return t.startsWith("public/")?"".concat(r,"/").concat(t):t.startsWith("images/")?"".concat(r,"/public/").concat(t):"".concat(r,"/public/images/").concat(t)},n=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},o=async e=>{try{let t=n();if(!t)throw Error("Authentication required");let a=await fetch("/api/leagues?search=".concat(encodeURIComponent(e),"&limit=20"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to search leagues");return((await a.json()).data||[]).map(e=>({value:e.externalId.toString(),label:e.name,subtitle:"".concat(e.country," • ").concat(e.season),image:s(e.logo)}))}catch(e){return console.error("Search leagues error:",e),[]}},i=async e=>{try{let t=n();if(!t)throw Error("Authentication required");let a=await fetch("/api/teams?search=".concat(encodeURIComponent(e),"&limit=20"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to search teams");return((await a.json()).data||[]).map(e=>({value:e.externalId.toString(),label:e.name,subtitle:e.country||"Team",image:s(e.logo)}))}catch(e){return console.error("Search teams error:",e),[]}},l=async e=>{try{let t=n();if(!t)throw Error("Authentication required");console.log("\uD83D\uDD0D Searching players with query:",e);let a=await fetch("/api/players?search=".concat(encodeURIComponent(e),"&limit=20"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){if(console.error("❌ Players search API error:",a.status,a.statusText),404===a.status||a.status>=500)return console.warn("⚠️ Using fallback mock data for players search"),[{value:"154",label:"Erling Haaland",subtitle:"Manchester City • Forward",image:s("public/images/players/154.png")},{value:"276",label:"Mohamed Salah",subtitle:"Liverpool • Forward",image:s("public/images/players/276.png")},{value:"524",label:"Kevin De Bruyne",subtitle:"Manchester City • Midfielder",image:s("public/images/players/524.png")},{value:"1100",label:"Cristiano Ronaldo",subtitle:"Al Nassr • Forward",image:s("public/images/players/1100.png")},{value:"1101",label:"Lionel Messi",subtitle:"Inter Miami • Forward",image:s("public/images/players/1101.png")}].filter(t=>t.label.toLowerCase().includes(e.toLowerCase())||t.subtitle.toLowerCase().includes(e.toLowerCase()));throw Error("Failed to search players")}let r=(await a.json()).data||[];return console.log("✅ Players search successful:",r.length,"players found"),r.map(e=>{var t;let a=e.player;return{value:null===(t=a.id)||void 0===t?void 0:t.toString(),label:a.name,subtitle:"".concat(a.nationality||"Unknown"," • Player"),image:s(a.photo)}})}catch(t){return console.error("❌ Search players error:",t),console.warn("⚠️ Using fallback mock data due to error"),[{value:"154",label:"Erling Haaland",subtitle:"Manchester City • Forward",image:s("public/images/players/154.png")},{value:"276",label:"Mohamed Salah",subtitle:"Liverpool • Forward",image:s("public/images/players/276.png")},{value:"524",label:"Kevin De Bruyne",subtitle:"Manchester City • Midfielder",image:s("public/images/players/524.png")}].filter(t=>t.label.toLowerCase().includes(e.toLowerCase())||t.subtitle.toLowerCase().includes(e.toLowerCase()))}},c=async e=>{try{let t=n();if(!t)throw Error("Authentication required");let a=await fetch("/api/fixtures?search=".concat(encodeURIComponent(e),"&limit=20"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to search fixtures");return((await a.json()).data||[]).map(e=>{var t,a,r,s,n;let o=(null===(t=e.homeTeam)||void 0===t?void 0:t.name)||"Home Team",i=(null===(a=e.awayTeam)||void 0===a?void 0:a.name)||"Away Team",l=new Date(e.date).toLocaleDateString(),c=(null===(r=e.league)||void 0===r?void 0:r.name)||"League";return{value:(null===(s=e.externalId)||void 0===s?void 0:s.toString())||(null===(n=e.id)||void 0===n?void 0:n.toString()),label:"".concat(o," vs ").concat(i),subtitle:"".concat(c," • ").concat(l),image:void 0}})}catch(e){return console.error("Search fixtures error:",e),[]}},u=async(e,t)=>{try{var a,r,o,i,l,c,u,d,f,m;let g=n();if(!g)throw Error("Authentication required");let h="";switch(e){case"league":h="/api/leagues/".concat(t);break;case"team":h="/api/teams/".concat(t);break;case"player":h="/api/players/".concat(t);break;case"fixture":h="/api/fixtures/".concat(t);break;default:return null}let b=await fetch(h,{headers:{Authorization:"Bearer ".concat(g)}});if(!b.ok)return null;let p=await b.json(),y=p.data||p;switch(e){case"league":return{value:(null===(a=y.externalId)||void 0===a?void 0:a.toString())||(null===(r=y.id)||void 0===r?void 0:r.toString()),label:y.name,subtitle:"".concat(y.country," • ").concat(y.season),image:s(y.logo)};case"team":return{value:(null===(o=y.externalId)||void 0===o?void 0:o.toString())||(null===(i=y.id)||void 0===i?void 0:i.toString()),label:y.name,subtitle:y.country||"Team",image:s(y.logo)};case"player":let x=y.player||y;return{value:null===(l=x.id)||void 0===l?void 0:l.toString(),label:x.name,subtitle:"".concat(x.nationality||"Unknown"," • Player"),image:s(x.photo)};case"fixture":let v=(null===(c=y.homeTeam)||void 0===c?void 0:c.name)||"Home Team",w=(null===(u=y.awayTeam)||void 0===u?void 0:u.name)||"Away Team",j=new Date(y.date).toLocaleDateString(),N=(null===(d=y.league)||void 0===d?void 0:d.name)||"League";return{value:(null===(f=y.externalId)||void 0===f?void 0:f.toString())||(null===(m=y.id)||void 0===m?void 0:m.toString()),label:"".concat(v," vs ").concat(w),subtitle:"".concat(N," • ").concat(j),image:void 0};default:return null}}catch(t){return console.error("Get ".concat(e," by ID error:"),t),null}}},91552:function(e,t,a){a.d(t,{L_:function(){return d},b5:function(){return f},HL:function(){return g},P9:function(){return b},dk:function(){return m},fu:function(){return p},Ny:function(){return h}});var r=a(31346),s=a(64095),n=a(8186),o=a(74921);class i{async getCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),void 0!==e.isActive&&t.append("isActive",e.isActive.toString()),void 0!==e.isPublic&&t.append("isPublic",e.isPublic.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let a=t.toString(),r=a?"".concat(this.baseUrl,"?").concat(a):this.baseUrl;console.log("\uD83D\uDD04 Fetching categories from:",r);let s=await o.x.get(r);if(console.log("✅ Categories fetched successfully:",s),s.data&&Array.isArray(s.data))return s;if(Array.isArray(s)){let t=e.page||1,a=e.limit||20,r=s.length,n=(t-1)*a;return{data:s.slice(n,n+a),meta:{currentPage:t,totalPages:Math.ceil(r/a),totalItems:r,limit:a}}}throw Error("Unexpected response format")}catch(e){throw console.error("❌ Error fetching categories:",e),e}}async getCategoryById(e){try{console.log("\uD83D\uDD04 Fetching category by ID:",e);let t=await o.x.get("".concat(this.baseUrl,"/").concat(e));return console.log("✅ Category fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching category by ID:",e),e}}async getPublicCategories(){return(await this.getCategories({isPublic:!0,isActive:!0})).data}async createCategory(e){return o.x.post(this.baseUrl,e)}async updateCategory(e,t){return o.x.patch("".concat(this.baseUrl,"/").concat(e),t)}async deleteCategory(e){return o.x.delete("".concat(this.baseUrl,"/").concat(e))}async toggleCategoryStatus(e,t){return o.x.patch("".concat(this.baseUrl,"/").concat(e),{isActive:t})}async reorderCategories(e){return o.x.post("".concat(this.baseUrl,"/reorder"),{categoryIds:e})}async getCategoryStats(){return o.x.get("".concat(this.baseUrl,"/stats"))}constructor(){this.baseUrl="/api/news/categories"}}let l=new i;var c=a(56288);let u={all:["categories"],lists:()=>[...u.all,"list"],list:e=>[...u.lists(),e],details:()=>[...u.all,"detail"],detail:e=>[...u.details(),e],public:()=>[...u.all,"public"]};function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:u.list(e),queryFn:()=>l.getCategories(e),staleTime:3e5})}function f(e){return(0,r.a)({queryKey:u.detail(e),queryFn:()=>l.getCategoryById(e),enabled:!!e,staleTime:6e5})}function m(){return(0,r.a)({queryKey:u.public(),queryFn:()=>l.getPublicCategories(),staleTime:9e5})}function g(){let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>l.createCategory(e),onSuccess:t=>{e.invalidateQueries({queryKey:u.all}),c.toast.success('Category "'.concat(t.name,'" created successfully'))},onError:e=>{c.toast.error("Failed to create category: "+e.message)}})}function h(){let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,data:a}=e;return l.updateCategory(t,a)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),c.toast.success('Category "'.concat(t.name,'" updated successfully'))},onError:e=>{c.toast.error("Failed to update category: "+e.message)}})}function b(){let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>l.deleteCategory(e),onSuccess:(t,a)=>{e.removeQueries({queryKey:u.detail(a)}),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()}),c.toast.success("Category deleted successfully")},onError:e=>{c.toast.error("Failed to delete category: "+e.message)}})}function p(){let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,isActive:a}=e;return l.toggleCategoryStatus(t,a)},onSuccess:t=>{e.setQueryData(u.detail(t.id),t),e.invalidateQueries({queryKey:u.lists()}),e.invalidateQueries({queryKey:u.public()});let a=t.isActive?"activated":"deactivated";c.toast.success('Category "'.concat(t.name,'" ').concat(a," successfully"))},onError:e=>{c.toast.error("Failed to toggle category status: "+e.message)}})}},72091:function(e,t,a){a.d(t,{dR:function(){return f},po:function(){return g},Kg:function(){return u},ZZ:function(){return d},FF:function(){return b},CC:function(){return h},rj:function(){return m}});var r=a(31346),s=a(64095),n=a(8186);function o(e){return{...e,author:"Author ".concat(e.authorId),summary:e.excerpt,imageUrl:e.featuredImage,isPublished:"published"===e.status,isHot:e.isFeatured,publishDate:e.publishedAt||e.createdAt}}let i=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},l={getNews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=function(e){let t={...e};return void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft",delete t.isPublished),void 0!==e.isHot&&(t.isFeatured=e.isHot,delete t.isHot),t}(e),a=new URLSearchParams;Object.entries(t).forEach(e=>{let[t,r]=e;null!=r&&(Array.isArray(r)?r.forEach(e=>a.append(t,e.toString())):a.append(t,r.toString()))});let r=i(),s={"Content-Type":"application/json"};r&&(s.Authorization="Bearer ".concat(r));let n=await fetch("/api/news?".concat(a.toString()),{method:"GET",headers:s});if(!n.ok)throw Error((await n.json()).message||"Failed to fetch news");let l=await n.json();return l.data&&(l.data=l.data.map(o)),l},getNewsById:async e=>{let t=i(),a={"Content-Type":"application/json"};t&&(a.Authorization="Bearer ".concat(t));let r=await fetch("/api/news/".concat(e),{method:"GET",headers:a});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch news ".concat(e));return o(await r.json())},createNews:async e=>{let t=i(),a={"Content-Type":"application/json"};t&&(a.Authorization="Bearer ".concat(t));let r={title:e.title,content:e.content,categoryId:e.categoryId,...e.slug&&{slug:e.slug},...e.excerpt&&{excerpt:e.excerpt},...e.featuredImage&&{featuredImage:e.featuredImage},...e.tags&&{tags:e.tags},...e.status&&{status:e.status},...e.publishedAt&&{publishedAt:e.publishedAt},...e.metaTitle&&{metaTitle:e.metaTitle},...e.metaDescription&&{metaDescription:e.metaDescription},...e.relatedLeagueId&&{relatedLeagueId:e.relatedLeagueId},...e.relatedTeamId&&{relatedTeamId:e.relatedTeamId},...e.relatedPlayerId&&{relatedPlayerId:e.relatedPlayerId},...e.relatedFixtureId&&{relatedFixtureId:e.relatedFixtureId},...void 0!==e.isFeatured&&{isFeatured:e.isFeatured},...void 0!==e.priority&&{priority:e.priority},...e.summary&&!e.excerpt&&{excerpt:e.summary},...e.imageUrl&&!e.featuredImage&&{featuredImage:e.imageUrl},...void 0!==e.isPublished&&!e.status&&{status:e.isPublished?"published":"draft"},...void 0!==e.isHot&&void 0===e.isFeatured&&{isFeatured:e.isHot}},s=await fetch("/api/news",{method:"POST",headers:a,body:JSON.stringify(r)});if(!s.ok)throw Error((await s.json()).message||"Failed to create news");return o(await s.json())},updateNews:async(e,t)=>{let a=i(),r={"Content-Type":"application/json"};a&&(r.Authorization="Bearer ".concat(a));let s=function(e){let t={};return void 0!==e.title&&(t.title=e.title),void 0!==e.content&&(t.content=e.content),void 0!==e.excerpt&&(t.excerpt=e.excerpt),void 0!==e.featuredImage&&(t.featuredImage=e.featuredImage),void 0!==e.tags&&(t.tags=e.tags),void 0!==e.status&&(t.status=e.status),void 0!==e.categoryId&&(t.categoryId="string"==typeof e.categoryId?parseInt(e.categoryId):e.categoryId),void 0!==e.metaTitle&&(t.metaTitle=e.metaTitle),void 0!==e.metaDescription&&(t.metaDescription=e.metaDescription),void 0!==e.relatedLeagueId&&(t.relatedLeagueId=e.relatedLeagueId),void 0!==e.relatedTeamId&&(t.relatedTeamId=e.relatedTeamId),void 0!==e.relatedPlayerId&&(t.relatedPlayerId=e.relatedPlayerId),void 0!==e.relatedFixtureId&&(t.relatedFixtureId=e.relatedFixtureId),void 0!==e.isFeatured&&(t.isFeatured=e.isFeatured),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.publishedAt&&(t.publishedAt=e.publishedAt),void 0!==e.summary&&(t.excerpt=e.summary),void 0!==e.imageUrl&&(t.featuredImage=e.imageUrl),void 0!==e.isPublished&&(t.status=e.isPublished?"published":"draft"),void 0!==e.isHot&&(t.isFeatured=e.isHot),t}(t),n=await fetch("/api/news/".concat(e),{method:"PATCH",headers:r,body:JSON.stringify(s)});if(!n.ok)throw Error((await n.json()).message||"Failed to update news ".concat(e));return o(await n.json())},deleteNews:async e=>{let t=i(),a={"Content-Type":"application/json"};t&&(a.Authorization="Bearer ".concat(t));let r=await fetch("/api/news/".concat(e),{method:"DELETE",headers:a});if(!r.ok){let t="Failed to delete news ".concat(e);try{t=(await r.json()).message||t}catch(a){t="Failed to delete news ".concat(e," (Status: ").concat(r.status,")")}throw Error(t)}},getPublishedNews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return l.getNews({...e,status:"published"})},getHotNews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return l.getNews({...e,isFeatured:!0})},getNewsByAuthor:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l.getNews({...t,author:e})},toggleNewsStatus:async(e,t)=>l.updateNews(e,{status:t?"published":"draft"}),toggleHotStatus:async(e,t)=>l.updateNews(e,{isFeatured:t})};var c=a(56288);let u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:["news",e],queryFn:()=>l.getNews(e),staleTime:3e5})},d=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,r.a)({queryKey:["news",e],queryFn:()=>l.getNewsById(e),enabled:!!e&&t,staleTime:6e5})},f=()=>{let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>l.createNews(e),onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News created successfully")},onError:e=>{c.toast.error("Failed to create news: ".concat(e.message))}})},m=()=>{let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,data:a}=e;return l.updateNews(t,a)},onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success("News updated successfully")},onError:e=>{c.toast.error("Failed to update news: ".concat(e.message))}})},g=()=>{let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>l.deleteNews(e),onSuccess:()=>{e.invalidateQueries({queryKey:["news"]}),c.toast.success("News deleted successfully")},onError:e=>{c.toast.error("Failed to delete news: ".concat(e.message))}})},h=()=>{let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,isPublished:a}=e;return l.toggleNewsStatus(t,a)},onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success("News ".concat("published"===t.status?"published":"unpublished"," successfully"))},onError:e=>{c.toast.error("Failed to toggle news status: ".concat(e.message))}})},b=()=>{let e=(0,s.NL)();return(0,n.D)({mutationFn:e=>{let{id:t,isHot:a}=e;return l.toggleHotStatus(t,a)},onSuccess:t=>{e.invalidateQueries({queryKey:["news"]}),e.invalidateQueries({queryKey:["news",t.id]}),c.toast.success("News ".concat(t.isFeatured?"marked as featured":"unmarked as featured"," successfully"))},onError:e=>{c.toast.error("Failed to toggle hot status: ".concat(e.message))}})}}}]);