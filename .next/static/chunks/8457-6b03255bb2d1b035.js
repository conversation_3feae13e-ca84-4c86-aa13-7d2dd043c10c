(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8457],{48541:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},65694:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},2866:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},53879:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},72733:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},97307:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},23441:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},62985:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},34187:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26490:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},20403:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},37841:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79382:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},69475:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},37451:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},65561:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},94415:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},57277:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]])},20244:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},22455:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},99925:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},79580:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},12882:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},34864:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},32989:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},70699:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},28670:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},29910:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18727:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},33707:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},91403:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("subscript",[["path",{d:"m4 5 8 8",key:"1eunvl"}],["path",{d:"m12 5-8 8",key:"1ah0jp"}],["path",{d:"M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07",key:"e8ta8j"}]])},92894:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("superscript",[["path",{d:"m4 19 8-8",key:"hr47gm"}],["path",{d:"m12 19-8-8",key:"1dhhmo"}],["path",{d:"M20 12h-4c0-1.5.442-2 1.5-2.5S20 8.334 20 7.002c0-.472-.17-.93-.484-1.29a2.105 2.105 0 0 0-2.617-.436c-.42.239-.738.614-.899 1.06",key:"1dfcux"}]])},3665:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},69724:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},36636:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},42804:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]])},38018:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},10632:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},52235:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},47907:function(e,t,n){"use strict";var r=n(15313);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},12219:function(){},89840:function(e,t,n){"use strict";var r=n(49079);n(12219);var i=n(2265),s=i&&"object"==typeof i&&"default"in i?i:{default:i};function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var o=void 0!==r&&r.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,s=void 0===i?o:i;l(a(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var u="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=u?u.getAttribute("content"):null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){if(l(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},n.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];l(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},n.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},n.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},n.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},n.makeStyleTag=function(e,t,n){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},u(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t&&u(e,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},h={};function f(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return h[r]||(h[r]="jsx-"+d(e+"-"+n)),h[r]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return h[n]||(h[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),h[n]}var y=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=r||new c({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),r&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=s,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=f(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return p(i,e)}):[p(i,t)]}}return{styleId:f(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),v=i.createContext(null);v.displayName="StyleSheetContext";var m=s.default.useInsertionEffect||s.default.useLayoutEffect,k="undefined"!=typeof window?new y:void 0;function _(e){var t=k||i.useContext(v);return t&&("undefined"==typeof window?t.add(e):m(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}_.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=_},69102:function(e,t,n){"use strict";e.exports=n(89840).style},94845:function(e,t,n){"use strict";n.d(t,{bU:function(){return M},fC:function(){return S}});var r=n(2265),i=n(44991),s=n(61266),u=n(84104),o=n(9310),a=n(65030),c=n(76769),l=n(29586),d=n(57437),h="Switch",[f,p]=(0,u.b)(h),[y,v]=f(h),m=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:u,checked:a,defaultChecked:c,required:f,disabled:p,value:v="on",onCheckedChange:m,form:k,..._}=e,[S,M]=r.useState(null),x=(0,s.e)(t,e=>M(e)),Z=r.useRef(!1),R=!S||k||!!S.closest("form"),[w,j]=(0,o.T)({prop:a,defaultProp:null!=c&&c,onChange:m,caller:h});return(0,d.jsxs)(y,{scope:n,checked:w,disabled:p,children:[(0,d.jsx)(l.WV.button,{type:"button",role:"switch","aria-checked":w,"aria-required":f,"data-state":g(w),"data-disabled":p?"":void 0,disabled:p,value:v,..._,ref:x,onClick:(0,i.M)(e.onClick,e=>{j(e=>!e),R&&(Z.current=e.isPropagationStopped(),Z.current||e.stopPropagation())})}),R&&(0,d.jsx)(b,{control:S,bubbles:!Z.current,name:u,value:v,checked:w,required:f,disabled:p,form:k,style:{transform:"translateX(-100%)"}})]})});m.displayName=h;var k="SwitchThumb",_=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,i=v(k,n);return(0,d.jsx)(l.WV.span,{"data-state":g(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:t})});_.displayName=k;var b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:i,checked:u,bubbles:o=!0,...l}=e,h=r.useRef(null),f=(0,s.e)(h,t),p=(0,a.D)(u),y=(0,c.t)(i);return r.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==u&&t){let n=new Event("click",{bubbles:o});t.call(e,u),e.dispatchEvent(n)}},[p,u,o]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:u,...l,tabIndex:-1,ref:f,style:{...l.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function g(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var S=m,M=_},64694:function(e,t,n){"use strict";n.d(t,{VY:function(){return F},aV:function(){return z},fC:function(){return j},xz:function(){return C}});var r=n(2265),i=n(44991),s=n(84104),u=n(23715),o=n(12642),a=n(29586),c=n(12275),l=n(9310),d=n(38687),h=n(57437),f="Tabs",[p,y]=(0,s.b)(f,[u.Pc]),v=(0,u.Pc)(),[m,k]=p(f),_=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:s,orientation:u="horizontal",dir:o,activationMode:p="automatic",...y}=e,v=(0,c.gm)(o),[k,_]=(0,l.T)({prop:r,onChange:i,defaultProp:null!=s?s:"",caller:f});return(0,h.jsx)(m,{scope:n,baseId:(0,d.M)(),value:k,onValueChange:_,orientation:u,dir:v,activationMode:p,children:(0,h.jsx)(a.WV.div,{dir:v,"data-orientation":u,...y,ref:t})})});_.displayName=f;var b="TabsList",g=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...i}=e,s=k(b,n),o=v(n);return(0,h.jsx)(u.fC,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:r,children:(0,h.jsx)(a.WV.div,{role:"tablist","aria-orientation":s.orientation,...i,ref:t})})});g.displayName=b;var S="TabsTrigger",M=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:s=!1,...o}=e,c=k(S,n),l=v(n),d=R(c.baseId,r),f=w(c.baseId,r),p=r===c.value;return(0,h.jsx)(u.ck,{asChild:!0,...l,focusable:!s,active:p,children:(0,h.jsx)(a.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:d,...o,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||s||!e||c.onValueChange(r)})})})});M.displayName=S;var x="TabsContent",Z=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:s,children:u,...c}=e,l=k(x,n),d=R(l.baseId,i),f=w(l.baseId,i),p=i===l.value,y=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(o.z,{present:s||p,children:n=>{let{present:r}=n;return(0,h.jsx)(a.WV.div,{"data-state":p?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:r&&u})}})});function R(e,t){return"".concat(e,"-trigger-").concat(t)}function w(e,t){return"".concat(e,"-content-").concat(t)}Z.displayName=x;var j=_,z=g,C=M,F=Z},65030:function(e,t,n){"use strict";n.d(t,{D:function(){return i}});var r=n(2265);function i(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},76769:function(e,t,n){"use strict";n.d(t,{t:function(){return s}});var r=n(2265),i=n(32618);function s(e){let[t,n]=r.useState(void 0);return(0,i.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},8186:function(e,t,n){"use strict";n.d(t,{D:function(){return h}});var r=n(2265),i=n(31678),s=n(34654),u=n(79522),o=n(6761);class a extends o.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let n=this.options;this.options=this.client.defaultMutationOptions(e),(0,i.VS)(n,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,s.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){u.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,n,r,i,s,u,o,a;e.onSuccess?(null==(t=(n=this.mutateOptions).onSuccess)||t.call(n,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(i=this.mutateOptions).onSettled)||r.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(s=(u=this.mutateOptions).onError)||s.call(u,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(a=this.mutateOptions).onSettled)||o.call(a,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var c=n(97536),l=n(64095),d=n(3439);function h(e,t,n){let s=(0,i.lV)(e,t,n),o=(0,l.NL)({context:s.context}),[h]=r.useState(()=>new a(o,s));r.useEffect(()=>{h.setOptions(s)},[h,s]);let p=(0,c.$)(r.useCallback(e=>h.subscribe(u.V.batchCalls(e)),[h]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),y=r.useCallback((e,t)=>{h.mutate(e,t).catch(f)},[h]);if(p.error&&(0,d.L)(h.options.useErrorBoundary,[p.error]))throw p.error;return{...p,mutate:y,mutateAsync:p.mutate}}function f(){}}}]);