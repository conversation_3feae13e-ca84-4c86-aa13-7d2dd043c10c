"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5543],{12647:function(e,t,a){a.d(t,{_:function(){return c}});var r=a(57437),n=a(2265),s=a(24602),i=a(49769),o=a(22169);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(s.f,{ref:t,className:(0,o.cn)(l(),a),...n})});c.displayName=s.f.displayName},4133:function(e,t,a){a.d(t,{sm:function(){return g},uB:function(){return f},u_:function(){return d}});var r=a(57437),n=a(2265),s=a(15669),i=a(691),o=a(52235),l=a(575),c=a(22169);let u={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},d=e=>{let{isOpen:t,onClose:a,title:d,description:g,children:f,size:m="md",showCloseButton:x=!0,closeOnOverlayClick:h=!0,className:y}=e;return(0,r.jsx)(s.u,{appear:!0,show:t,as:n.Fragment,children:(0,r.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:h?a:()=>{},children:[(0,r.jsx)(s.u.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(s.u.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(i.Vq.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",u[m],y),children:[(d||x)&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[d&&(0,r.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),g&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:g})]}),x&&(0,r.jsx)(l.z,{variant:"ghost",size:"sm",onClick:a,className:"h-8 w-8 p-0",children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"mt-2",children:f})]})})})})]})})},g=e=>{let{isOpen:t,onClose:a,onConfirm:n,title:s="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:o="Confirm",cancelText:c="Cancel",variant:u="default",loading:g=!1}=e;return(0,r.jsx)(d,{isOpen:t,onClose:a,title:s,size:"sm",closeOnOverlayClick:!g,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,r.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,r.jsx)(l.z,{variant:"outline",onClick:a,disabled:g,children:c}),(0,r.jsx)(l.z,{variant:"destructive"===u?"destructive":"default",onClick:n,disabled:g,children:g?"Processing...":o})]})]})})},f=e=>{let{isOpen:t,onClose:a,title:n,description:s,children:i,onSubmit:o,submitText:c="Save",cancelText:u="Cancel",loading:g=!1,size:f="md"}=e;return(0,r.jsx)(d,{isOpen:t,onClose:a,title:n,description:s,size:f,closeOnOverlayClick:!g,children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==o||o()},className:"space-y-4",children:[i,(0,r.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,r.jsx)(l.z,{type:"button",variant:"outline",onClick:a,disabled:g,children:u}),o&&(0,r.jsx)(l.z,{type:"submit",disabled:g,children:g?"Saving...":c})]})]})})}},86468:function(e,t,a){a.d(t,{r:function(){return o}});var r=a(57437),n=a(2265),s=a(94845),i=a(22169);let o=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(s.fC,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...n,ref:t,children:(0,r.jsx)(s.bU,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});o.displayName=s.fC.displayName},85110:function(e,t,a){a.d(t,{Z:function(){return i}});var r=a(57437);a(2265);var n=a(86468),s=a(12647);function i(e){let{checked:t,onCheckedChange:a,label:i,description:o,disabled:l=!1,size:c="md",variant:u="default"}=e;return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(n.r,{id:i,checked:t,onCheckedChange:a,disabled:l}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(s._,{htmlFor:i,className:"font-medium cursor-pointer ".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[c]," ").concat({default:t?"text-blue-700":"text-gray-700",success:t?"text-green-700":"text-gray-700",warning:t?"text-yellow-700":"text-gray-700",danger:t?"text-red-700":"text-gray-700"}[u]," ").concat(l?"opacity-50":""),children:i}),o&&(0,r.jsx)("span",{className:"text-xs text-gray-500 ".concat(l?"opacity-50":""),children:o})]})]})}},47011:function(e,t,a){a.d(t,{A:function(){return s}});var r=a(74921);let n=()=>{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")},s={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,r]=e;void 0!==r&&t.append(a,r.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>{let a=t?"".concat(e,"-").concat(t):e.toString(),r=await fetch("/api/leagues/".concat(a),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch league ".concat(e));return await r.json()},createLeague:async e=>await r.x.post("/football/leagues",e),updateLeague:async(e,t,a)=>{let r=n(),i={"Content-Type":"application/json"};r&&(i.Authorization="Bearer ".concat(r));let o=await s.getLeagueById(e,a);if(!o||!o.id)throw Error("League not found: ".concat(e).concat(a?"-".concat(a):""));let l=await fetch("/api/leagues/".concat(o.id),{method:"PATCH",headers:i,body:JSON.stringify(t)});if(!l.ok)throw Error((await l.json()).message||"Failed to update league ".concat(e));return await l.json()},deleteLeague:async(e,t)=>{let a=await s.getLeagueById(e,t);if(!a||!a.id)throw Error("League not found: ".concat(e).concat(t?"-".concat(t):""));await r.x.delete("/football/leagues/".concat(a.id))},getActiveLeagues:async()=>s.getLeagues({active:!0}),getLeaguesByCountry:async e=>s.getLeagues({country:e}),toggleLeagueStatus:async(e,t,a)=>s.updateLeague(e,{active:t},a)}},64915:function(e,t,a){a.d(t,{HK:function(){return l},My:function(){return c},sF:function(){return o}});var r=a(31346),n=a(64095),s=a(8186),i=a(47011);let o=function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,r.a)({queryKey:["leagues",a],queryFn:()=>i.A.getLeagues(a),staleTime:6e5});return{leagues:(null===(e=n.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=n.data)||void 0===t?void 0:t.meta,isLoading:n.isLoading,error:n.error,refetch:n.refetch}},l=(e,t)=>{let a=(0,r.a)({queryKey:["leagues",e,t],queryFn:()=>i.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},c=()=>{let e=(0,n.NL)(),t=(0,s.D)({mutationFn:e=>i.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,s.D)({mutationFn:e=>{let{externalId:t,data:a,season:r}=e;return i.A.updateLeague(t,a,r)},onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId,a.season],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),r=(0,s.D)({mutationFn:e=>{let{id:t,active:a}=e;return i.A.toggleLeagueStatus(t,a)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isLoading,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isLoading,updateError:a.error,updateData:a.data,toggleStatus:r.mutate,isToggleLoading:r.isLoading,toggleError:r.error,toggleData:r.data}}},11546:function(e,t,a){a.d(t,{TE:function(){return c},a1:function(){return l}});var r=a(57437),n=a(2265),s=a(47907),i=a(27786),o=a(96146);let l=e=>{let{children:t,requiredRole:a,fallbackUrl:l="/auth/login"}=e,c=(0,s.useRouter)(),{isAuthenticated:u,user:d,isLoading:g}=(0,i.a)();if((0,n.useEffect)(()=>{if(!g){if(!u||!d){c.push(l);return}if(a&&!(Array.isArray(a)?a:[a]).includes(d.role)){c.push("/dashboard?error=unauthorized");return}}},[u,d,g,a,c,l]),g)return(0,r.jsx)(o.SX,{message:"Verifying authentication..."});if(!u||!d)return(0,r.jsx)(o.SX,{message:"Redirecting to login..."});if(a){let e=Array.isArray(a)?a:[a];if(!e.includes(d.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",d.role]})]})})}return(0,r.jsx)(r.Fragment,{children:t})},c=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),a=()=>t("admin"),r=()=>t(["admin","editor"]),n=()=>t(["admin","editor","moderator"]),s=()=>a(),o=()=>r(),l=()=>n(),c=()=>a();return{user:e,hasRole:t,isAdmin:a,isEditor:r,isModerator:n,canManageUsers:s,canManageContent:o,canModerate:l,canSync:c,can:e=>{switch(e){case"manage-users":return s();case"manage-content":return o();case"moderate":return l();case"sync":return c();default:return!1}}}}},16996:function(e,t,a){function r(e){if(!e)return;if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=e.startsWith("/")?e.slice(1):e;return"".concat("http://172.31.213.61","/").concat(t)}function n(e){return r(e)}function s(e){return r(e)}function i(e){return r(e)}a.d(t,{Bf:function(){return n},Fc:function(){return i},Sc:function(){return r},ou:function(){return s}})}}]);