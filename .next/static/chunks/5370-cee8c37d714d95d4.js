"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5370],{14440:function(e,s,t){t.d(s,{E:function(){return n}});var r=t(57437),a=t(2265),i=t(22169);let n=a.forwardRef((e,s)=>{let{className:t,value:a=0,max:n=100,showValue:u=!1,size:o="md",variant:c="default",...l}=e,d=Math.min(Math.max(a/n*100,0),100);return(0,r.jsxs)("div",{ref:s,className:(0,i.cn)("relative w-full overflow-hidden rounded-full bg-gray-200",{sm:"h-2",md:"h-3",lg:"h-4"}[o],t),...l,children:[(0,r.jsx)("div",{className:(0,i.cn)("h-full transition-all duration-300 ease-in-out",{default:"bg-blue-600",success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600"}[c]),style:{width:"".concat(d,"%")}}),u&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-xs font-medium text-white",children:[Math.round(d),"%"]})})]})});n.displayName="Progress"},93425:function(e,s,t){t.d(s,{Il:function(){return d},Xn:function(){return y},Ow:function(){return c},ie:function(){return g},zL:function(){return l}});var r=t(31346),a=t(64095),i=t(8186),n=t(74921);let u={register:async e=>await n.x.post("/users/register",e),login:async e=>await n.x.post("/users/login",e),verifyEmail:async e=>await n.x.post("/users/verify-email",{token:e}),resendVerification:async e=>await n.x.post("/users/resend-verification",{email:e}),forgotPassword:async e=>await n.x.post("/users/forgot-password",{email:e}),resetPassword:async(e,s)=>await n.x.post("/users/reset-password",{token:e,newPassword:s}),getProfile:async()=>await n.x.get("/users/profile"),updateProfile:async e=>await n.x.put("/users/profile",e),changePassword:async e=>await n.x.post("/users/change-password",e),getApiUsage:async()=>await n.x.get("/users/api-usage"),getAllUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;return Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())}),await n.x.get("/admin/users?".concat(s.toString()))},getTierStatistics:async()=>await n.x.get("/admin/tiers/statistics"),getUsersApproachingLimits:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:80;return await n.x.get("/admin/users/approaching-limits?threshold=".concat(e))},upgradeTier:async(e,s,t)=>{let r={newTier:s};return t&&(r.subscriptionMonths=t),await n.x.post("/admin/users/".concat(e,"/upgrade-tier"),r)},downgradeTier:async(e,s)=>await n.x.post("/admin/users/".concat(e,"/downgrade-tier"),{newTier:s}),extendSubscription:async(e,s)=>await n.x.post("/admin/users/".concat(e,"/extend-subscription"),{additionalMonths:s}),getSubscriptionInfo:async e=>await n.x.get("/admin/users/".concat(e,"/subscription")),resetMonthlyApiUsage:async()=>await n.x.post("/admin/reset-api-usage"),checkApiUsageWarnings:async()=>await n.x.post("/admin/check-usage-warnings")};var o=t(56288);let c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.a)({queryKey:["registered-users",e],queryFn:()=>u.getAllUsers(e),staleTime:3e5})},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:80;return(0,r.a)({queryKey:["users-approaching-limits",e],queryFn:()=>u.getUsersApproachingLimits(e),staleTime:9e5})},d=()=>{let e=(0,a.NL)(),s=(0,i.D)({mutationFn:e=>{let{userId:s,newTier:t,subscriptionMonths:r}=e;return u.upgradeTier(s,t,r)},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t.userId]}),e.invalidateQueries({queryKey:["tier-statistics"]}),o.toast.success("User upgraded to ".concat(t.newTier," tier successfully"))},onError:e=>{o.toast.error("Failed to upgrade user: ".concat(e.message))}}),t=(0,i.D)({mutationFn:e=>{let{userId:s,newTier:t}=e;return u.downgradeTier(s,t)},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t.userId]}),e.invalidateQueries({queryKey:["tier-statistics"]}),o.toast.success("User downgraded to ".concat(t.newTier," tier successfully"))},onError:e=>{o.toast.error("Failed to downgrade user: ".concat(e.message))}}),r=(0,i.D)({mutationFn:e=>{let{userId:s,additionalMonths:t}=e;return u.extendSubscription(s,t)},onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-user",t.userId]}),e.invalidateQueries({queryKey:["user-subscription",t.userId]}),o.toast.success("Subscription extended by ".concat(t.additionalMonths," months"))},onError:e=>{o.toast.error("Failed to extend subscription: ".concat(e.message))}});return{upgradeTier:s,downgradeTier:t,extendSubscription:r,resetApiUsage:(0,i.D)({mutationFn:u.resetMonthlyApiUsage,onSuccess:()=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["users-approaching-limits"]}),o.toast.success("Monthly API usage reset successfully")},onError:e=>{o.toast.error("Failed to reset API usage: ".concat(e.message))}}),suspendUser:(0,i.D)({mutationFn:async e=>(console.log("Suspending user:",e),{message:"User suspended successfully"}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t]}),o.toast.success("User suspended successfully")},onError:e=>{o.toast.error("Failed to suspend user: ".concat(e.message))}}),reactivateUser:(0,i.D)({mutationFn:async e=>(console.log("Reactivating user:",e),{message:"User reactivated successfully"}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t]}),o.toast.success("User reactivated successfully")},onError:e=>{o.toast.error("Failed to reactivate user: ".concat(e.message))}})}},g=()=>(0,r.a)({queryKey:["user-analytics"],queryFn:async()=>({totalUsers:1247,activeUsers:1156,newUsersThisMonth:45,tierDistribution:{free:856,premium:312,enterprise:79},monthlyRevenue:34567,apiCallsThisMonth:2847392,apiUsageStats:{totalCallsThisMonth:2847392,averageCallsPerUser:188,peakUsageDay:"2024-01-15",topUsers:[{id:1,username:"api_user_premium_1",calls:9567},{id:2,username:"enterprise_client",calls:8234},{id:3,username:"mobile_app_backend",calls:7456}]},revenueMetrics:{monthlyRecurringRevenue:34567,averageRevenuePerUser:11.67,lifetimeValue:186.45}}),staleTime:3e5}),y={useList:c,useGetById:e=>(0,r.a)({queryKey:["registered-user",e],queryFn:async()=>({id:e,name:"John Doe",email:"<EMAIL>",company:"TechCorp Inc.",phone:"+****************",website:"https://techcorp.com",tier:"premium",status:"active",emailVerified:!0,apiCallsUsed:7250,apiCallsLimit:1e4,monthlySpend:29,avatar:"",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-12-15T14:22:00Z",lastLogin:"2024-12-20T09:15:00Z",lastApiCall:"2024-12-22T16:45:00Z",subscriptionStartDate:"2024-01-15T10:30:00Z",subscriptionEndDate:"2025-01-15T10:30:00Z",notes:"Premium customer with good API usage patterns"}),enabled:!!e,staleTime:3e5}),useUpdate:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Updating user:",e),{...e,updatedAt:new Date().toISOString()}),onSuccess:s=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",s.id]})}})},useDelete:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Deleting user:",e),{message:"User deleted successfully"}),onSuccess:()=>{e.invalidateQueries({queryKey:["registered-users"]})}})},useSuspend:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Suspending user:",e),{message:"User suspended successfully"}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t]})}})},useReactivate:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>(console.log("Reactivating user:",e),{message:"User reactivated successfully"}),onSuccess:(s,t)=>{e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",t]})}})},useUpgradeTier:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>{let{userId:s,newTier:t}=e;return console.log("Upgrading user tier:",s,t),{message:"User upgraded to ".concat(t," tier successfully")}},onSuccess:(s,t)=>{let{userId:r}=t;e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),e.invalidateQueries({queryKey:["tier-stats"]})}})},useDowngradeTier:()=>{let e=(0,a.NL)();return(0,i.D)({mutationFn:async e=>{let{userId:s,newTier:t}=e;return console.log("Downgrading user tier:",s,t),{message:"User downgraded to ".concat(t," tier successfully")}},onSuccess:(s,t)=>{let{userId:r}=t;e.invalidateQueries({queryKey:["registered-users"]}),e.invalidateQueries({queryKey:["registered-user",r]}),e.invalidateQueries({queryKey:["tier-stats"]})}})},useUsageStats:e=>(0,r.a)({queryKey:["usage-stats",e],queryFn:async()=>({today:125,thisWeek:890,thisMonth:3420,total:45600}),enabled:!!e,staleTime:3e5}),useApiCalls:e=>(0,r.a)({queryKey:["api-calls",e],queryFn:async()=>[{id:"1",endpoint:"/api/fixtures",status:200,responseTime:145,timestamp:"2024-12-22T16:45:00Z"},{id:"2",endpoint:"/api/teams",status:200,responseTime:98,timestamp:"2024-12-22T16:30:00Z"},{id:"3",endpoint:"/api/leagues",status:429,responseTime:50,timestamp:"2024-12-22T16:15:00Z"}],enabled:!!e,staleTime:6e4}),useTierStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";return(0,r.a)({queryKey:["tier-stats",e],queryFn:async()=>({totalUsers:1247,totalUsersChange:8.5,conversionRate:15.2,conversionRateChange:2.1,tierDistribution:[{tier:"free",count:856,percentage:68.6,monthlyGrowth:5.2,avgApiUsage:650},{tier:"premium",count:312,percentage:25,monthlyGrowth:12.8,avgApiUsage:7500},{tier:"enterprise",count:79,percentage:6.3,monthlyGrowth:18.5,avgApiUsage:45e3}],growthTrends:[{tier:"free",change:5.2},{tier:"premium",change:12.8},{tier:"enterprise",change:18.5}],apiUsageByTier:[{tier:"free",avgUsage:650,usagePercentage:65},{tier:"premium",avgUsage:7500,usagePercentage:75},{tier:"enterprise",avgUsage:45e3,usagePercentage:45}]}),staleTime:9e5})},useRevenueStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";return(0,r.a)({queryKey:["revenue-stats",e],queryFn:async()=>({monthlyRevenue:34567,revenueChange:15.3,arpu:27.72,arpuChange:3.2,mrr:34567,churnRate:3.5,revenueByTier:[{tier:"free",amount:0,percentage:0},{tier:"premium",amount:9048,percentage:26.2},{tier:"enterprise",amount:25519,percentage:73.8}]}),staleTime:9e5})},useTierMigration:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";return(0,r.a)({queryKey:["tier-migration",e],queryFn:async()=>({migrations:[{fromTier:"free",toTier:"premium",count:42,percentage:65.6},{fromTier:"premium",toTier:"enterprise",count:15,percentage:23.4},{fromTier:"premium",toTier:"free",count:7,percentage:10.9}],totalMigrations:64}),staleTime:18e5})}}}}]);