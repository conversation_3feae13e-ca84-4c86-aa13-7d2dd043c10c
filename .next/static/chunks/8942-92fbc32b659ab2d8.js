"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8942],{72632:function(e,t,a){a.d(t,{A:function(){return w}});var s=a(57437),r=a(2265),n=a(64095),i=a(31346),l=a(8186),c=a(575),o=a(22782),d=a(12647),u=a(33277),m=a(15671),x=a(75462),h=a(52235),f=a(70094),g=a(70699),j=a(67366),y=a(97404),p=a(81708),v=a(29295),D=a(50489),N=a(48763);let b=()=>{let e=N.t.getState(),t=e.accessToken;if(console.log("\uD83D\uDD11 Auth Debug:",{isAuthenticated:e.isAuthenticated,hasToken:!!t,tokenLength:(null==t?void 0:t.length)||0,tokenPreview:(null==t?void 0:t.substring(0,20))+"..."}),!t){console.error("❌ No access token found in auth store!");let e=localStorage.getItem("accessToken");if(e)return console.log("\uD83D\uDD04 Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}}return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}},k={getBroadcastLinks:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/broadcast-links?".concat(t.toString()),{method:"GET",headers:b()});if(!a.ok)throw Error("Failed to fetch broadcast links: ".concat(a.statusText));return await a.json()},getBroadcastLinksByFixture:async e=>{let t=await fetch("/api/broadcast-links/fixture/".concat(e),{method:"GET",headers:b()});if(!t.ok)throw Error("Failed to fetch broadcast links for fixture: ".concat(t.statusText));return await t.json()},getBroadcastLinkById:async e=>{let t=await fetch("/api/broadcast-links/".concat(e),{method:"GET",headers:b()});if(!t.ok)throw Error("Failed to fetch broadcast link: ".concat(t.statusText));return await t.json()},createBroadcastLink:async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:b(),body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create broadcast link: ".concat(t.statusText));return await t.json()},updateBroadcastLink:async(e,t)=>{let a=await fetch("/api/broadcast-links/".concat(e),{method:"PUT",headers:b(),body:JSON.stringify(t)});if(!a.ok)throw Error("Failed to update broadcast link: ".concat(a.statusText));return await a.json()},deleteBroadcastLink:async e=>{let t=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE",headers:b()});if(!t.ok)throw Error("Failed to delete broadcast link: ".concat(t.statusText))}};var C=a(77625);let w=e=>{let{isOpen:t,onClose:a,fixture:N}=e,[b,w]=(0,r.useState)(!1),[F,E]=(0,r.useState)(null),[T,z]=(0,r.useState)({title:"",url:"",comment:"",language:"English",quality:"HD"}),A=(0,n.NL)(),{data:S,isLoading:L,error:q}=(0,i.a)({queryKey:["broadcast-links",N.externalId||N.id],queryFn:()=>k.getBroadcastLinksByFixture(N.externalId||N.id),enabled:t}),B=(null==S?void 0:S.data)||[],M=(0,l.D)({mutationFn:e=>k.createBroadcastLink(e),onSuccess:()=>{A.invalidateQueries({queryKey:["broadcast-links"]}),w(!1),U()},onError:e=>{console.error("Failed to create broadcast link:",e.message)}}),Z=(0,l.D)({mutationFn:e=>{let{id:t,data:a}=e;return k.updateBroadcastLink(t,a)},onSuccess:()=>{A.invalidateQueries({queryKey:["broadcast-links"]}),E(null),U()},onError:e=>{console.error("Failed to update broadcast link:",e.message)}}),H=(0,l.D)({mutationFn:e=>k.deleteBroadcastLink(e),onSuccess:()=>{A.invalidateQueries({queryKey:["broadcast-links"]})},onError:e=>{console.error("Failed to delete broadcast link:",e.message)}}),U=()=>{z({title:"",url:"",comment:"",language:"English",quality:"HD"})},I=e=>{E(e),z({title:e.linkName,url:e.linkUrl,comment:e.linkComment||"",language:e.language||"English",quality:e.quality||"HD"}),w(!0)},O=e=>{confirm('Are you sure you want to delete "'.concat(e.linkName,'"?'))&&H.mutate(e.id)},_=()=>{w(!1),E(null),U()},P=e=>{switch(e.toLowerCase()){case"4k":case"uhd":return"bg-purple-100 text-purple-800";case"hd":case"1080p":return"bg-blue-100 text-blue-800";case"sd":case"720p":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},V=e=>({en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",it:"\uD83C\uDDEE\uD83C\uDDF9",pt:"\uD83C\uDDF5\uD83C\uDDF9",ar:"\uD83C\uDDF8\uD83C\uDDE6"})[e]||"\uD83C\uDF10";return t?(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto",children:[(0,s.jsx)("div",{className:"p-6 border-b",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,s.jsx)(x.Z,{className:"mr-2 h-5 w-5"}),"Broadcast Links - ",N.homeTeamName," vs ",N.awayTeamName]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage streaming links for this fixture"})]}),(0,s.jsx)(c.z,{variant:"outline",size:"sm",onClick:a,children:(0,s.jsx)(h.Z,{className:"h-4 w-4"})})]})}),(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[!b&&(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[B.length," broadcast link",1!==B.length?"s":""," available"]}),(0,s.jsxs)(c.z,{onClick:()=>w(!0),children:[(0,s.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Add Link"]})]}),b&&(0,s.jsxs)(m.Zb,{children:[(0,s.jsx)(m.Ol,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(m.ll,{className:"text-lg",children:F?"Edit Broadcast Link":"Add New Broadcast Link"}),(0,s.jsx)(c.z,{type:"button",variant:"outline",size:"sm",onClick:_,children:(0,s.jsx)(h.Z,{className:"h-4 w-4"})})]})}),(0,s.jsx)(m.aY,{children:(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!T.title.trim()||!T.url.trim()||!T.comment.trim())return;let t={fixtureId:N.externalId||N.id,linkName:T.title.trim(),linkUrl:T.url.trim(),linkComment:T.comment.trim(),language:T.language,quality:T.quality};F?Z.mutate({id:F.id,data:t}):M.mutate(t)},className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"title",children:"Title"}),(0,s.jsx)(o.I,{id:"title",value:T.title,onChange:e=>z({...T,title:e.target.value}),placeholder:"e.g., ESPN HD Stream",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"url",children:"URL"}),(0,s.jsx)(o.I,{id:"url",type:"url",value:T.url,onChange:e=>z({...T,url:e.target.value}),placeholder:"https://...",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"language",children:"Language"}),(0,s.jsxs)("select",{id:"language",value:T.language,onChange:e=>z({...T,language:e.target.value}),className:"w-full p-2 border rounded",children:[(0,s.jsx)("option",{value:"en",children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,s.jsx)("option",{value:"es",children:"\uD83C\uDDEA\uD83C\uDDF8 Spanish"}),(0,s.jsx)("option",{value:"fr",children:"\uD83C\uDDEB\uD83C\uDDF7 French"}),(0,s.jsx)("option",{value:"de",children:"\uD83C\uDDE9\uD83C\uDDEA German"}),(0,s.jsx)("option",{value:"it",children:"\uD83C\uDDEE\uD83C\uDDF9 Italian"}),(0,s.jsx)("option",{value:"pt",children:"\uD83C\uDDF5\uD83C\uDDF9 Portuguese"}),(0,s.jsx)("option",{value:"ar",children:"\uD83C\uDDF8\uD83C\uDDE6 Arabic"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"quality",children:"Quality"}),(0,s.jsxs)("select",{id:"quality",value:T.quality,onChange:e=>z({...T,quality:e.target.value}),className:"w-full p-2 border rounded",children:[(0,s.jsx)("option",{value:"4K",children:"4K Ultra HD"}),(0,s.jsx)("option",{value:"HD",children:"HD (1080p)"}),(0,s.jsx)("option",{value:"720p",children:"HD (720p)"}),(0,s.jsx)("option",{value:"SD",children:"SD (480p)"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d._,{htmlFor:"comment",children:"Comment"}),(0,s.jsx)(o.I,{id:"comment",value:T.comment,onChange:e=>z({...T,comment:e.target.value}),placeholder:"e.g., Official HD stream with English commentary",required:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,s.jsx)(c.z,{type:"button",variant:"outline",onClick:_,children:"Cancel"}),(0,s.jsxs)(c.z,{type:"submit",disabled:M.isLoading||Z.isLoading,children:[(0,s.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),F?"Update":"Add"," Link"]})]})]})})]}),(0,s.jsx)("div",{className:"space-y-4",children:L?(0,s.jsx)(C.hM,{rows:3,columns:1}):q?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load broadcast links"}),(0,s.jsx)(c.z,{onClick:()=>console.log("Mock: Retry loading"),children:"Try Again"})]}):0===B.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"No broadcast links added yet"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Add a link to get started"}),!b&&(0,s.jsxs)(c.z,{onClick:()=>w(!0),className:"mt-4",children:[(0,s.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Add First Link"]})]}):B.map(e=>(0,s.jsx)(m.Zb,{children:(0,s.jsx)(m.aY,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.linkName.toLowerCase().includes("comment")||e.linkName.toLowerCase().includes("chat")?(0,s.jsx)(j.Z,{className:"h-4 w-4 text-blue-600"}):(0,s.jsx)(x.Z,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:e.linkName})]}),(0,s.jsxs)(u.C,{className:P(e.quality||"HD"),children:[(0,s.jsx)(y.Z,{className:"mr-1 h-3 w-3"}),e.quality||"HD"]}),(0,s.jsxs)(u.C,{variant:"outline",children:[V(e.language||"English")," ",e.language||"English"]}),(0,s.jsx)(u.C,{variant:"outline",className:"bg-green-50 text-green-700",children:"Active"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 flex items-center space-x-2",children:(0,s.jsxs)("a",{href:e.linkUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]",children:[(0,s.jsx)("span",{className:"truncate",children:e.linkUrl}),(0,s.jsx)(p.Z,{className:"h-3 w-3 flex-shrink-0"})]})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.z,{size:"sm",variant:"outline",onClick:()=>I(e),disabled:(null==F?void 0:F.id)===e.id,children:(0,s.jsx)(v.Z,{className:"h-4 w-4"})}),(0,s.jsx)(c.z,{size:"sm",variant:"outline",onClick:()=>O(e),disabled:H.isLoading,children:(0,s.jsx)(D.Z,{className:"h-4 w-4"})})]})]})})},e.id))})]})]})}):null}},52569:function(e,t,a){a.d(t,{U:function(){return m}});var s=a(57437),r=a(2265),n=a(38152),i=a(22169);let l=n.zt,c=n.fC,o=n.xz,d=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...l}=e;return(0,s.jsx)(n.VY,{ref:t,sideOffset:r,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})});d.displayName=n.VY.displayName;var u=a(80928);let m=e=>{let{dateTime:t,className:a="",showDate:r=!0,showTime:n=!0,format:i,onClick:m,isClickable:x=!1}=e,h=(0,u.Bv)(t,"dd/MM/yyyy"),f=(0,u.Bv)(t,"HH:mm"),g=(0,u.Yh)(t,"dd/MM/yyyy"),j=(0,u.Yh)(t,"HH:mm"),y=(0,u.vV)(),p="\n    text-center cursor-help transition-all duration-200\n    ".concat(x?"hover:bg-blue-50 hover:shadow-sm rounded-md p-2 cursor-pointer":"","\n    ").concat(a,"\n  ").trim(),v=(0,s.jsxs)("div",{className:p,onClick:x?m:void 0,children:[r&&(0,s.jsx)("div",{className:"font-semibold text-sm text-gray-900 leading-tight",children:h}),n&&(0,s.jsx)("div",{className:"text-gray-600 text-xs font-medium mt-1 leading-tight",children:f}),x&&(0,s.jsx)("div",{className:"text-xs text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:"Click to filter"})]});return(0,s.jsx)(l,{children:(0,s.jsxs)(c,{children:[(0,s.jsx)(o,{asChild:!0,children:(0,s.jsx)("div",{className:x?"group":"",children:v})}),(0,s.jsx)(d,{side:"top",className:"max-w-xs bg-gray-900 text-white",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"font-medium text-sm",children:"UTC Time:"}),(0,s.jsxs)("div",{className:"text-sm",children:[g," ",j," (GMT+0)"]}),(0,s.jsxs)("div",{className:"text-xs text-gray-300 mt-2 border-t border-gray-700 pt-2",children:["Local: ",y]}),x&&(0,s.jsx)("div",{className:"text-xs text-blue-300 border-t border-gray-700 pt-2",children:"\uD83D\uDCA1 Click to filter by this date"})]})})]})})}},12647:function(e,t,a){a.d(t,{_:function(){return o}});var s=a(57437),r=a(2265),n=a(24602),i=a(49769),l=a(22169);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,l.cn)(c(),a),...r})});o.displayName=n.f.displayName},4133:function(e,t,a){a.d(t,{sm:function(){return m},uB:function(){return x},u_:function(){return u}});var s=a(57437),r=a(2265),n=a(15669),i=a(691),l=a(52235),c=a(575),o=a(22169);let d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=e=>{let{isOpen:t,onClose:a,title:u,description:m,children:x,size:h="md",showCloseButton:f=!0,closeOnOverlayClick:g=!0,className:j}=e;return(0,s.jsx)(n.u,{appear:!0,show:t,as:r.Fragment,children:(0,s.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:g?a:()=>{},children:[(0,s.jsx)(n.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(n.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(i.Vq.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",d[h],j),children:[(u||f)&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{children:[u&&(0,s.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:u}),m&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:m})]}),f&&(0,s.jsx)(c.z,{variant:"ghost",size:"sm",onClick:a,className:"h-8 w-8 p-0",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"mt-2",children:x})]})})})})]})})},m=e=>{let{isOpen:t,onClose:a,onConfirm:r,title:n="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:o="Cancel",variant:d="default",loading:m=!1}=e;return(0,s.jsx)(u,{isOpen:t,onClose:a,title:n,size:"sm",closeOnOverlayClick:!m,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,s.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,s.jsx)(c.z,{variant:"outline",onClick:a,disabled:m,children:o}),(0,s.jsx)(c.z,{variant:"destructive"===d?"destructive":"default",onClick:r,disabled:m,children:m?"Processing...":l})]})]})})},x=e=>{let{isOpen:t,onClose:a,title:r,description:n,children:i,onSubmit:l,submitText:o="Save",cancelText:d="Cancel",loading:m=!1,size:x="md"}=e;return(0,s.jsx)(u,{isOpen:t,onClose:a,title:r,description:n,size:x,closeOnOverlayClick:!m,children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==l||l()},className:"space-y-4",children:[i,(0,s.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,s.jsx)(c.z,{type:"button",variant:"outline",onClick:a,disabled:m,children:d}),l&&(0,s.jsx)(c.z,{type:"submit",disabled:m,children:m?"Saving...":o})]})]})})}},11546:function(e,t,a){a.d(t,{TE:function(){return o},a1:function(){return c}});var s=a(57437),r=a(2265),n=a(47907),i=a(27786),l=a(96146);let c=e=>{let{children:t,requiredRole:a,fallbackUrl:c="/auth/login"}=e,o=(0,n.useRouter)(),{isAuthenticated:d,user:u,isLoading:m}=(0,i.a)();if((0,r.useEffect)(()=>{if(!m){if(!d||!u){o.push(c);return}if(a&&!(Array.isArray(a)?a:[a]).includes(u.role)){o.push("/dashboard?error=unauthorized");return}}},[d,u,m,a,o,c]),m)return(0,s.jsx)(l.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,s.jsx)(l.SX,{message:"Redirecting to login..."});if(a){let e=Array.isArray(a)?a:[a];if(!e.includes(u.role))return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,s.jsx)(s.Fragment,{children:t})},o=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),a=()=>t("admin"),s=()=>t(["admin","editor"]),r=()=>t(["admin","editor","moderator"]),n=()=>a(),l=()=>s(),c=()=>r(),o=()=>a();return{user:e,hasRole:t,isAdmin:a,isEditor:s,isModerator:r,canManageUsers:n,canManageContent:l,canModerate:c,canSync:o,can:e=>{switch(e){case"manage-users":return n();case"manage-content":return l();case"moderate":return c();case"sync":return o();default:return!1}}}}},80928:function(e,t,a){a.d(t,{Bv:function(){return c},PM:function(){return m},Yh:function(){return o},vV:function(){return u}});var s=a(97256),r=a(85544),n=a(98594),i=a(43617);let l=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dd/MM/yyyy HH:mm";try{let a="string"==typeof e?(0,s.D)(e):e;if(!(0,r.J)(a))return"Invalid Date";let n=l();return(0,i.CV)(a,n,t)}catch(e){return console.error("Error formatting date to local time:",e),"Invalid Date"}},o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dd/MM/yyyy HH:mm";try{let a="string"==typeof e?(0,s.D)(e):e;if(!(0,r.J)(a))return"Invalid Date";return(0,i.CV)(a,"UTC",t)}catch(e){return console.error("Error formatting date to UTC:",e),"Invalid Date"}},d=()=>{let e=new Date().getTimezoneOffset();return"GMT".concat(e<=0?"+":"-").concat(Math.floor(Math.abs(e)/60).toString().padStart(2,"0"),":").concat((Math.abs(e)%60).toString().padStart(2,"0"))},u=()=>{var e;let t=l(),a=d(),s=(null===(e=new Intl.DateTimeFormat("en",{timeZoneName:"short",timeZone:t}).formatToParts(new Date).find(e=>"timeZoneName"===e.type))||void 0===e?void 0:e.value)||"";return"".concat(s," (").concat(a,")")},m=e=>{try{return(0,n.WU)(e,"yyyy-MM-dd")}catch(t){return console.error("Error converting local date to UTC:",t),(0,n.WU)(e,"yyyy-MM-dd")}}}}]);