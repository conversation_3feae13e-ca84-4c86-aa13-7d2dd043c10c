"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1953],{29295:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},50489:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},52235:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19619:function(e,t,n){/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useSyncExternalStore,i=r.useRef,u=r.useEffect,a=r.useMemo,s=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=l(e,(d=a(function(){function e(e){if(!u){if(u=!0,l=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,o(l,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(l=e,t):(l=e,i=n)}var l,i,u=!1,a=void 0===n?null:n;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,n,r,c]))[0],d[1]);return u(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},2857:function(e,t,n){e.exports=n(19619)},691:function(e,t,n){let r,o;n.d(t,{Vq:function(){return to}});var l,i,u,a,s,c,d,f,p,m,v,h,g,b,E,y,w=n(2265),S=((l=S||{}).Space=" ",l.Enter="Enter",l.Escape="Escape",l.Backspace="Backspace",l.Delete="Delete",l.ArrowLeft="ArrowLeft",l.ArrowUp="ArrowUp",l.ArrowRight="ArrowRight",l.ArrowDown="ArrowDown",l.Home="Home",l.End="End",l.PageUp="PageUp",l.PageDown="PageDown",l.Tab="Tab",l),k=n(85235);function F(e,t,n,r){let o=(0,k.E)(n);(0,w.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class P extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var T=n(55205),C=Object.defineProperty,O=(e,t,n)=>t in e?C(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,x=(e,t,n)=>(O(e,"symbol"!=typeof t?t+"":t,n),n),A=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},L=(e,t,n)=>(A(e,t,"read from private field"),n?n.call(e):t.get(e)),N=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},M=(e,t,n,r)=>(A(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class R{constructor(e){N(this,b,{}),N(this,E,new P(()=>new Set)),N(this,y,new Set),x(this,"disposables",(0,T.k)()),M(this,b,e)}dispose(){this.disposables.dispose()}get state(){return L(this,b)}subscribe(e,t){let n={selector:e,callback:t,current:e(L(this,b))};return L(this,y).add(n),this.disposables.add(()=>{L(this,y).delete(n)})}on(e,t){return L(this,E).get(e).add(t),this.disposables.add(()=>{L(this,E).get(e).delete(t)})}send(e){let t=this.reduce(L(this,b),e);if(t!==L(this,b)){for(let e of(M(this,b,t),L(this,y))){let t=e.selector(L(this,b));D(e.current,t)||(e.current=t,e.callback(t))}for(let t of L(this,E).get(e.type))t(L(this,b),e)}}}function D(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&j(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&j(e.entries(),t.entries()):!!(I(e)&&I(t))&&j(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function j(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function I(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}b=new WeakMap,E=new WeakMap,y=new WeakMap;var V=n(72640),z=Object.defineProperty,H=(e,t,n)=>t in e?z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,_=(e,t,n)=>(H(e,"symbol"!=typeof t?t+"":t,n),n),W=((i=W||{})[i.Push=0]="Push",i[i.Pop=1]="Pop",i);let U={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class Y extends R{constructor(){super(...arguments),_(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),_(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new Y({stack:[]})}reduce(e,t){return(0,V.E)(t.type,U,e,t)}}let Z=new P(()=>Y.new());var q=n(2857),B=n(10641);function G(e,t,n=D){return(0,q.useSyncExternalStoreWithSelector)((0,B.z)(t=>e.subscribe($,t)),(0,B.z)(()=>e.state),(0,B.z)(()=>e.state),(0,B.z)(t),n)}function $(e){return e}var K=n(39790);function X(e,t){let n=(0,w.useId)(),r=Z.get(t),[o,l]=G(r,(0,w.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,K.e)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!l||o)}var J=n(41879);function Q(e){var t,n;return J.O.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let ee=new Map,et=new Map;function en(e){var t;let n=null!=(t=et.get(e))?t:0;return et.set(e,n+1),0!==n||(ee.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=et.get(e))?t:1;if(1===n?et.delete(e):et.set(e,n-1),1!==n)return;let r=ee.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,ee.delete(e))})(e)}function er(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function eo(e){return er(e)&&"tagName"in e}function el(e){return eo(e)&&"accessKey"in e}function ei(e){return eo(e)&&"tabIndex"in e}let eu=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),ea=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var es=((u=es||{})[u.First=1]="First",u[u.Previous=2]="Previous",u[u.Next=4]="Next",u[u.Last=8]="Last",u[u.WrapAround=16]="WrapAround",u[u.NoScroll=32]="NoScroll",u[u.AutoFocus=64]="AutoFocus",u),ec=((a=ec||{})[a.Error=0]="Error",a[a.Overflow=1]="Overflow",a[a.Success=2]="Success",a[a.Underflow=3]="Underflow",a),ed=((s=ed||{})[s.Previous=-1]="Previous",s[s.Next=1]="Next",s),ef=((c=ef||{})[c.Strict=0]="Strict",c[c.Loose=1]="Loose",c),ep=((d=ep||{})[d.Keyboard=0]="Keyboard",d[d.Mouse=1]="Mouse",d);function em(e){null==e||e.focus({preventScroll:!0})}function ev(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var l,i,u;let a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(ea)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(eu)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:a.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,m=s.length,v;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(v=s[e])||v.focus(f),p+=c}while(v!==a.activeElement);return 6&t&&null!=(u=null==(i=null==(l=v)?void 0:l.matches)?void 0:i.call(l,"textarea,input"))&&u&&v.select(),2}function eh(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function eg(){return eh()||/Android/gi.test(window.navigator.userAgent)}function eb(e,t,n,r){let o=(0,k.E)(n);(0,w.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function eE(e,t,n,r){let o=(0,k.E)(n);(0,w.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function ey(...e){return(0,w.useMemo)(()=>Q(...e),[...e])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var ew=n(18318),eS=((f=eS||{})[f.None=1]="None",f[f.Focusable=2]="Focusable",f[f.Hidden=4]="Hidden",f);let ek=(0,ew.yV)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,ew.L6)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),eF=(0,w.createContext)(null);function eP({children:e,node:t}){let[n,r]=(0,w.useState)(null),o=eT(null!=t?t:n);return w.createElement(eF.Provider,{value:o},e,null===o&&w.createElement(ek,{features:eS.Hidden,ref:e=>{var t,n;if(e){for(let o of null!=(n=null==(t=Q(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(o!==document.body&&o!==document.head&&eo(o)&&null!=o&&o.contains(e)){r(o);break}}}}))}function eT(e=null){var t;return null!=(t=(0,w.useContext)(eF))?t:e}let eC=(p={PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,T.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},l=[eh()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,T.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",t=>{if(ei(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),i=e.querySelector(o);ei(i)&&!r(i)&&(l=i)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var n;if(ei(e.target)&&eo(n=e.target)&&"style"in n){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(ei(e.target)){var t;if(!(el(t=e.target)&&"INPUT"===t.nodeName)){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth)},after({doc:e,d:t}){let n=e.documentElement,o=Math.max(0,n.clientWidth-n.offsetWidth),l=Math.max(0,r-o);t.style(n,"paddingRight",`${l}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];l.forEach(({before:e})=>null==e?void 0:e(o)),l.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}},r=new Map,o=new Set,{getSnapshot:()=>r,subscribe:e=>(o.add(e),()=>o.delete(e)),dispatch(e,...t){let n=p[e].call(r,...t);n&&(r=n,o.forEach(e=>e()))}});eC.subscribe(()=>{let e=eC.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&eC.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&eC.dispatch("TEARDOWN",n)}});var eO=n(92144),ex=n(36601);let eA=(0,w.createContext)(()=>{});function eL(e){let{value:t,children:n}=e;return w.createElement(eA.Provider,{value:t},n)}var eN=n(88358);let eM=(0,w.createContext)(!1);function eR(e){return w.createElement(eM.Provider,{value:e.force},e.children)}let eD=(0,w.createContext)(void 0),ej=(0,w.createContext)(null);ej.displayName="DescriptionContext";let eI=Object.assign((0,ew.yV)(function(e,t){let n=(0,w.useId)(),r=(0,w.useContext)(eD),{id:o="headlessui-description-".concat(n),...l}=e,i=function e(){let t=(0,w.useContext)(ej);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=(0,ex.T)(t);(0,K.e)(()=>i.register(o),[o,i.register]);let a=r||!1,s=(0,w.useMemo)(()=>({...i.slot,disabled:a}),[i.slot,a]),c={ref:u,...i.props,id:o};return(0,ew.L6)()({ourProps:c,theirProps:l,slot:s,defaultTag:"p",name:i.name||"Description"})}),{});var eV=n(20044),ez=n(42219),eH=n(21210);function e_(e){let t=(0,B.z)(e),n=(0,w.useRef)(!1);(0,w.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,eH.Y)(()=>{n.current&&t()})}),[t])}var eW=((m=eW||{})[m.Forwards=0]="Forwards",m[m.Backwards=1]="Backwards",m);function eU(e,t){let n=(0,w.useRef)([]),r=(0,B.z)(e);(0,w.useEffect)(()=>{let e=[...n.current];for(let[o,l]of t.entries())if(n.current[o]!==l){let o=r(t,e);return n.current=t,o}},[r,...t])}let eY=[];function eZ(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)eo(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!ei(e.target)||e.target===document.body||eY[0]===e.target)return;let t=e.target;t=t.closest(eu),eY.unshift(null!=t?t:e.target),(eY=eY.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eq=((v=eq||{})[v.None=0]="None",v[v.InitialFocus=1]="InitialFocus",v[v.TabLock=2]="TabLock",v[v.FocusLock=4]="FocusLock",v[v.RestoreFocus=8]="RestoreFocus",v[v.AutoFocus=16]="AutoFocus",v);let eB=Object.assign((0,ew.yV)(function(e,t){let n,r=(0,w.useRef)(null),o=(0,ex.T)(r,t),{initialFocus:l,initialFocusFallback:i,containers:u,features:a=15,...s}=e;(0,eO.H)()||(a=0);let c=ey(r);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),o=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,w.useRef)(eY.slice());return eU((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,eH.Y)(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=eY.slice())},[e,eY,t]),(0,B.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eU(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&em(o())},[r]),e_(()=>{r&&em(o())})}(a,{ownerDocument:c});let d=function(e,t){let{ownerDocument:n,container:r,initialFocus:o,initialFocusFallback:l}=t,i=(0,w.useRef)(null),u=X(!!(1&e),"focus-trap#initial-focus"),a=(0,ez.t)();return eU(()=>{if(0===e)return;if(!u){null!=l&&l.current&&em(l.current);return}let t=r.current;t&&(0,eH.Y)(()=>{if(!a.current)return;let r=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===r){i.current=r;return}}else if(t.contains(r)){i.current=r;return}if(null!=o&&o.current)em(o.current);else{if(16&e){if(ev(t,es.First|es.AutoFocus)!==ec.Error)return}else if(ev(t,es.First)!==ec.Error)return;if(null!=l&&l.current&&(em(l.current),(null==n?void 0:n.activeElement)===l.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=null==n?void 0:n.activeElement})},[l,u,e]),i}(a,{ownerDocument:c,container:r,initialFocus:l,initialFocusFallback:i});!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:l}=t,i=(0,ez.t)(),u=!!(4&e);F(null==n?void 0:n.defaultView,"focus",e=>{if(!u||!i.current)return;let t=eZ(o);el(r.current)&&t.add(r.current);let n=l.current;if(!n)return;let a=e.target;el(a)?eG(t,a)?(l.current=a,em(a)):(e.preventDefault(),e.stopPropagation(),em(n)):em(l.current)},!0)}(a,{ownerDocument:c,container:r,containers:u,previousActiveElement:d});let f=(n=(0,w.useRef)(0),eE(!0,"keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),p=(0,B.z)(e=>{if(!el(r.current))return;let t=r.current;(0,V.E)(f.current,{[eW.Forwards]:()=>{ev(t,es.First,{skipElements:[e.relatedTarget,i]})},[eW.Backwards]:()=>{ev(t,es.Last,{skipElements:[e.relatedTarget,i]})}})}),m=X(!!(2&a),"focus-trap#tab-lock"),v=(0,eV.G)(),h=(0,w.useRef)(!1),g=(0,ew.L6)();return w.createElement(w.Fragment,null,m&&w.createElement(ek,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eS.Focusable}),g({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(h.current=!0,v.requestAnimationFrame(()=>{h.current=!1}))},onBlur(e){if(!(4&a))return;let t=eZ(u);el(r.current)&&t.add(r.current);let n=e.relatedTarget;ei(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(eG(t,n)||(h.current?ev(r.current,(0,V.E)(f.current,{[eW.Forwards]:()=>es.Next,[eW.Backwards]:()=>es.Previous})|es.WrapAround,{relativeTo:e.target}):ei(e.target)&&em(e.target)))}},theirProps:s,defaultTag:"div",name:"FocusTrap"}),m&&w.createElement(ek,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eS.Focusable}))}),{features:eq});function eG(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var e$=n(54887);let eK=w.Fragment,eX=(0,ew.yV)(function(e,t){let{ownerDocument:n=null,...r}=e,o=(0,w.useRef)(null),l=(0,ex.T)((0,ex.h)(e=>{o.current=e}),t),i=ey(o),u=null!=n?n:i,a=function(e){let t=(0,w.useContext)(eM),n=(0,w.useContext)(eQ),[r,o]=(0,w.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(J.O.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,w.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,w.useEffect)(()=>{t||null!==n&&o(n.current)},[n,o,t]),r}(u),[s]=(0,w.useState)(()=>{var e;return J.O.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),c=(0,w.useContext)(e0),d=(0,eO.H)();(0,K.e)(()=>{!a||!s||a.contains(s)||(s.setAttribute("data-headlessui-portal",""),a.appendChild(s))},[a,s]),(0,K.e)(()=>{if(s&&c)return c.register(s)},[c,s]),e_(()=>{var e;a&&s&&(er(s)&&a.contains(s)&&a.removeChild(s),a.childNodes.length<=0&&(null==(e=a.parentElement)||e.removeChild(a)))});let f=(0,ew.L6)();return d&&a&&s?(0,e$.createPortal)(f({ourProps:{ref:l},theirProps:r,slot:{},defaultTag:eK,name:"Portal"}),s):null}),eJ=w.Fragment,eQ=(0,w.createContext)(null),e0=(0,w.createContext)(null),e1=(0,ew.yV)(function(e,t){let n=(0,ex.T)(t),{enabled:r=!0,ownerDocument:o,...l}=e,i=(0,ew.L6)();return r?w.createElement(eX,{...l,ownerDocument:o,ref:n}):i({ourProps:{ref:n},theirProps:l,slot:{},defaultTag:eK,name:"Portal"})}),e2=(0,ew.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,ex.T)(t)},l=(0,ew.L6)();return w.createElement(eQ.Provider,{value:n},l({ourProps:o,theirProps:r,defaultTag:eJ,name:"Popover.Group"}))}),e4=Object.assign(e1,{Group:e2});var e6=n(15669),e5=((h=e5||{})[h.Open=0]="Open",h[h.Closed=1]="Closed",h),e8=((g=e8||{})[g.SetTitleId=0]="SetTitleId",g);let e3={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},e9=(0,w.createContext)(null);function e7(e){let t=(0,w.useContext)(e9);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,e7),t}return t}function te(e,t){return(0,V.E)(t.type,e3,e,t)}e9.displayName="DialogContext";let tt=(0,ew.yV)(function(e,t){let n,r,o,l,i,u,a,s,c,d,f=(0,w.useId)(),{id:p="headlessui-dialog-".concat(f),open:m,onClose:v,initialFocus:h,role:g="dialog",autoFocus:b=!0,__demoMode:E=!1,unmount:y=!1,...P}=e,C=(0,w.useRef)(!1);g="dialog"===g||"alertdialog"===g?g:(C.current||(C.current=!0,console.warn("Invalid role [".concat(g,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let O=(0,eN.oJ)();void 0===m&&null!==O&&(m=(O&eN.ZM.Open)===eN.ZM.Open);let x=(0,w.useRef)(null),A=(0,ex.T)(x,t),L=ey(x),N=m?0:1,[M,R]=(0,w.useReducer)(te,{titleId:null,descriptionId:null,panelRef:(0,w.createRef)()}),D=(0,B.z)(()=>v(!1)),j=(0,B.z)(e=>R({type:0,id:e})),I=!!(0,eO.H)()&&0===N,[z,H]=(n=(0,w.useContext)(e0),r=(0,w.useRef)([]),o=(0,B.z)(e=>(r.current.push(e),n&&n.register(e),()=>l(e))),l=(0,B.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),i=(0,w.useMemo)(()=>({register:o,unregister:l,portals:r}),[o,l,r]),[r,(0,w.useMemo)(()=>function(e){let{children:t}=e;return w.createElement(e0.Provider,{value:i},t)},[i])]),_=eT(),{resolveContainers:W}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=ey(n),o=(0,B.z)(()=>{var o,l;let i=[];for(let t of e)null!==t&&(eo(t)?i.push(t):"current"in t&&eo(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&eo(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:o,contains:(0,B.z)(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:_,portals:z,defaultContainers:[{get current(){var U;return null!=(U=M.panelRef.current)?U:x.current}}]}),Y=null!==O&&(O&eN.ZM.Closing)===eN.ZM.Closing;!function(e,{allowed:t,disallowed:n}={}){let r=X(e,"inert-others");(0,K.e)(()=>{var e,o;if(!r)return;let l=(0,T.k)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(en(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let e of i){if(!e)continue;let t=Q(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(en(e));n=n.parentElement}}return l.dispose},[r,t,n])}(!E&&!Y&&I,{allowed:(0,B.z)(()=>{var e,t;return[null!=(t=null==(e=x.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,B.z)(()=>{var e;return[null!=(e=null==_?void 0:_.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let q=Z.get(null);(0,K.e)(()=>{if(I)return q.actions.push(p),()=>q.actions.pop(p)},[q,p,I]);let $=G(q,(0,w.useCallback)(e=>q.selectors.isTop(e,p),[q,p]));u=(0,k.E)(e=>{e.preventDefault(),D()}),a=(0,w.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(W))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return!function(e,t=0){var n;return e!==(null==(n=Q(e))?void 0:n.body)&&(0,V.E)(t,{0:()=>e.matches(eu),1(){let t=e;for(;null!==t;){if(t.matches(eu))return!0;t=t.parentElement}return!1}})}(n,ef.Loose)&&-1!==n.tabIndex&&e.preventDefault(),u.current(e,n)}},[u,W]),s=(0,w.useRef)(null),eb($,"pointerdown",e=>{var t,n;eg()||(s.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),eb($,"pointerup",e=>{if(eg()||!s.current)return;let t=s.current;return s.current=null,a(e,()=>t)},!0),c=(0,w.useRef)({x:0,y:0}),eb($,"touchstart",e=>{c.current.x=e.touches[0].clientX,c.current.y=e.touches[0].clientY},!0),eb($,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-c.current.x)>=30||Math.abs(t.y-c.current.y)>=30))return a(e,()=>ei(e.target)?e.target:null)},!0),eE($,"blur",e=>a(e,()=>{var e;return el(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e,t="undefined"!=typeof document?document.defaultView:null,n){let r=X(e,"escape");F(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===S.Escape&&n(e))})}($,null==L?void 0:L.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),D()}),function(e,t,n=()=>[document.body]){!function(e,t,n=()=>({containers:[]})){let r=(0,w.useSyncExternalStore)(eC.subscribe,eC.getSnapshot,eC.getSnapshot),o=t?r.get(t):void 0;o&&o.count,(0,K.e)(()=>{if(!(!t||!e))return eC.dispatch("PUSH",t,n),()=>eC.dispatch("POP",t,n)},[e,t])}(X(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!E&&!Y&&I,L,W),d=(0,k.E)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&D()}),(0,w.useEffect)(()=>{if(!I)return;let e=null===x?null:el(x)?x:x.current;if(!e)return;let t=(0,T.k)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>d.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>d.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[x,d,I]);let[J,ee]=function(){let[e,t]=(0,w.useState)([]);return[e.length>0?e.join(" "):void 0,(0,w.useMemo)(()=>function(e){let n=(0,B.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,w.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return w.createElement(ej.Provider,{value:r},e.children)},[t])]}(),et=(0,w.useMemo)(()=>[{dialogState:N,close:D,setTitleId:j,unmount:y},M],[N,M,D,j,y]),er=(0,w.useMemo)(()=>({open:0===N}),[N]),ea={ref:A,id:p,role:g,tabIndex:-1,"aria-modal":E?void 0:0===N||void 0,"aria-labelledby":M.titleId,"aria-describedby":J,unmount:y},es=!function(){var e;let[t]=(0,w.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,w.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,K.e)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),ec=eq.None;I&&!E&&(ec|=eq.RestoreFocus,ec|=eq.TabLock,b&&(ec|=eq.AutoFocus),es&&(ec|=eq.InitialFocus));let ed=(0,ew.L6)();return w.createElement(eN.uu,null,w.createElement(eR,{force:!0},w.createElement(e4,null,w.createElement(e9.Provider,{value:et},w.createElement(e2,{target:x},w.createElement(eR,{force:!1},w.createElement(ee,{slot:er},w.createElement(H,null,w.createElement(eB,{initialFocus:h,initialFocusFallback:x,containers:W,features:ec},w.createElement(eL,{value:D},ed({ourProps:ea,theirProps:P,slot:er,defaultTag:tn,features:tr,visible:0===N,name:"Dialog"})))))))))))}),tn="div",tr=ew.VN.RenderStrategy|ew.VN.Static,to=Object.assign((0,ew.yV)(function(e,t){let{transition:n=!1,open:r,...o}=e,l=(0,eN.oJ)(),i=e.hasOwnProperty("open")||null!==l,u=e.hasOwnProperty("onClose");if(!i&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!i)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!l&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==r||n)&&!o.static?w.createElement(eP,null,w.createElement(e6.u,{show:r,transition:n,unmount:o.unmount},w.createElement(tt,{ref:t,...o}))):w.createElement(eP,null,w.createElement(tt,{ref:t,open:r,...o}))}),{Panel:(0,ew.yV)(function(e,t){let n=(0,w.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:o=!1,...l}=e,[{dialogState:i,unmount:u},a]=e7("Dialog.Panel"),s=(0,ex.T)(t,a.panelRef),c=(0,w.useMemo)(()=>({open:0===i}),[i]),d=(0,B.z)(e=>{e.stopPropagation()}),f=o?e6.x:w.Fragment,p=(0,ew.L6)();return w.createElement(f,{...o?{unmount:u}:{}},p({ourProps:{ref:s,id:r,onClick:d},theirProps:l,slot:c,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,ew.yV)(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:o,unmount:l}]=e7("Dialog.Backdrop"),i=(0,w.useMemo)(()=>({open:0===o}),[o]),u=n?e6.x:w.Fragment,a=(0,ew.L6)();return w.createElement(u,{...n?{unmount:l}:{}},a({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:i,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,ew.yV)(function(e,t){let n=(0,w.useId)(),{id:r="headlessui-dialog-title-".concat(n),...o}=e,[{dialogState:l,setTitleId:i}]=e7("Dialog.Title"),u=(0,ex.T)(t);(0,w.useEffect)(()=>(i(r),()=>i(null)),[r,i]);let a=(0,w.useMemo)(()=>({open:0===l}),[l]);return(0,ew.L6)()({ourProps:{ref:u,id:r},theirProps:o,slot:a,defaultTag:"h2",name:"Dialog.Title"})})),Description:eI})},15669:function(e,t,n){n.d(t,{u:function(){return M},x:function(){return N}});var r,o,l,i,u=n(2265),a=n(20044),s=n(10641),c=n(42219),d=n(39790),f=n(85235),p=n(92144),m=n(36601),v=n(55205),h=n(49079);void 0!==h&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(l=null==h?void 0:h.env)?void 0:l.NODE_ENV)==="test"&&void 0===(null==(i=null==Element?void 0:Element.prototype)?void 0:i.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var g=((r=g||{})[r.None=0]="None",r[r.Closed=1]="Closed",r[r.Enter=2]="Enter",r[r.Leave=4]="Leave",r),b=n(88358),E=n(82466),y=n(72640),w=n(18318);function S(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:O)!==u.Fragment||1===u.Children.count(e.children)}let k=(0,u.createContext)(null);k.displayName="TransitionContext";var F=((o=F||{}).Visible="visible",o.Hidden="hidden",o);let P=(0,u.createContext)(null);function T(e){return"children"in e?T(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function C(e,t){let n=(0,f.E)(e),r=(0,u.useRef)([]),o=(0,c.t)(),l=(0,a.G)(),i=(0,s.z)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:w.l4.Hidden,i=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==i&&((0,y.E)(t,{[w.l4.Unmount](){r.current.splice(i,1)},[w.l4.Hidden](){r.current[i].state="hidden"}}),l.microTask(()=>{var e;!T(r)&&o.current&&(null==(e=n.current)||e.call(n))}))}),d=(0,s.z)(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>i(e,w.l4.Unmount)}),p=(0,u.useRef)([]),m=(0,u.useRef)(Promise.resolve()),v=(0,u.useRef)({enter:[],leave:[]}),h=(0,s.z)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(v.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?m.current=m.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),g=(0,s.z)((e,t,n)=>{Promise.all(v.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,u.useMemo)(()=>({children:r,register:d,unregister:i,onStart:h,onStop:g,wait:m,chains:v}),[d,i,r,h,g,v,m])}P.displayName="NestingContext";let O=u.Fragment,x=w.VN.RenderStrategy,A=(0,w.yV)(function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...l}=e,i=(0,u.useRef)(null),a=S(e),c=(0,m.T)(...a?[i,t]:null===t?[]:[t]);(0,p.H)();let f=(0,b.oJ)();if(void 0===n&&null!==f&&(n=(f&b.ZM.Open)===b.ZM.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,h]=(0,u.useState)(n?"visible":"hidden"),g=C(()=>{n||h("hidden")}),[E,y]=(0,u.useState)(!0),F=(0,u.useRef)([n]);(0,d.e)(()=>{!1!==E&&F.current[F.current.length-1]!==n&&(F.current.push(n),y(!1))},[F,n]);let O=(0,u.useMemo)(()=>({show:n,appear:r,initial:E}),[n,r,E]);(0,d.e)(()=>{n?h("visible"):T(g)||null===i.current||h("hidden")},[n,g]);let A={unmount:o},N=(0,s.z)(()=>{var t;E&&y(!1),null==(t=e.beforeEnter)||t.call(e)}),M=(0,s.z)(()=>{var t;E&&y(!1),null==(t=e.beforeLeave)||t.call(e)}),R=(0,w.L6)();return u.createElement(P.Provider,{value:g},u.createElement(k.Provider,{value:O},R({ourProps:{...A,as:u.Fragment,children:u.createElement(L,{ref:c,...A,...l,beforeEnter:N,beforeLeave:M})},theirProps:{},defaultTag:u.Fragment,features:x,visible:"visible"===v,name:"Transition"})))}),L=(0,w.yV)(function(e,t){var n,r;let{transition:o=!0,beforeEnter:l,afterEnter:i,beforeLeave:c,afterLeave:f,enter:h,enterFrom:g,enterTo:F,entered:A,leave:L,leaveFrom:N,leaveTo:M,...R}=e,[D,j]=(0,u.useState)(null),I=(0,u.useRef)(null),V=S(e),z=(0,m.T)(...V?[I,t,j]:null===t?[]:[t]),H=null==(n=R.unmount)||n?w.l4.Unmount:w.l4.Hidden,{show:_,appear:W,initial:U}=function(){let e=(0,u.useContext)(k);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[Y,Z]=(0,u.useState)(_?"visible":"hidden"),q=function(){let e=(0,u.useContext)(P);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:B,unregister:G}=q;(0,d.e)(()=>B(I),[B,I]),(0,d.e)(()=>{if(H===w.l4.Hidden&&I.current){if(_&&"visible"!==Y){Z("visible");return}return(0,y.E)(Y,{hidden:()=>G(I),visible:()=>B(I)})}},[Y,I,B,G,_,H]);let $=(0,p.H)();(0,d.e)(()=>{if(V&&$&&"visible"===Y&&null===I.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[I,Y,$,V]);let K=U&&!W,X=W&&_&&U,J=(0,u.useRef)(!1),Q=C(()=>{J.current||(Z("hidden"),G(I))},q),ee=(0,s.z)(e=>{J.current=!0,Q.onStart(I,e?"enter":"leave",e=>{"enter"===e?null==l||l():"leave"===e&&(null==c||c())})}),et=(0,s.z)(e=>{let t=e?"enter":"leave";J.current=!1,Q.onStop(I,t,e=>{"enter"===e?null==i||i():"leave"===e&&(null==f||f())}),"leave"!==t||T(Q)||(Z("hidden"),G(I))});(0,u.useEffect)(()=>{V&&o||(ee(_),et(_))},[_,V,o]);let[,en]=function(e,t,n,r){let[o,l]=(0,u.useState)(n),{hasFlag:i,addFlag:s,removeFlag:c}=function(e=0){let[t,n]=(0,u.useState)(e),r=(0,u.useCallback)(e=>n(e),[t]),o=(0,u.useCallback)(e=>n(t=>t|e),[t]),l=(0,u.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:l,removeFlag:(0,u.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,u.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),f=(0,u.useRef)(!1),p=(0,u.useRef)(!1),m=(0,a.G)();return(0,d.e)(()=>{var o;if(e){if(n&&l(!0),!t){n&&s(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,{prepare:t,run:n,done:r,inFlight:o}){let l=(0,v.k)();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current){n();return}let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:o}),l.nextFrame(()=>{n(),l.requestAnimationFrame(()=>{l.add(function(e,t){var n,r;let o=(0,v.k)();if(!e)return o.dispose;let l=!1;o.add(()=>{l=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{l||t()}),o.dispose}(e,r))})}),l.dispose}(t,{inFlight:f,prepare(){p.current?p.current=!1:p.current=f.current,f.current=!0,p.current||(n?(s(3),c(4)):(s(4),c(2)))},run(){p.current?n?(c(3),s(4)):(c(4),s(3)):n?c(1):s(1)},done(){var e;p.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,c(7),n||l(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,m]),e?[o,{closed:i(1),enter:i(2),leave:i(4),transition:i(2)||i(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!o||!V||!$||K),D,_,{start:ee,end:et}),er=(0,w.oA)({ref:z,className:(null==(r=(0,E.A)(R.className,X&&h,X&&g,en.enter&&h,en.enter&&en.closed&&g,en.enter&&!en.closed&&F,en.leave&&L,en.leave&&!en.closed&&N,en.leave&&en.closed&&M,!en.transition&&_&&A))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}(en)}),eo=0;"visible"===Y&&(eo|=b.ZM.Open),"hidden"===Y&&(eo|=b.ZM.Closed),_&&"hidden"===Y&&(eo|=b.ZM.Opening),_||"visible"!==Y||(eo|=b.ZM.Closing);let el=(0,w.L6)();return u.createElement(P.Provider,{value:Q},u.createElement(b.up,{value:eo},el({ourProps:er,theirProps:R,defaultTag:O,features:x,visible:"visible"===Y,name:"Transition.Child"})))}),N=(0,w.yV)(function(e,t){let n=null!==(0,u.useContext)(k),r=null!==(0,b.oJ)();return u.createElement(u.Fragment,null,!n&&r?u.createElement(A,{ref:t,...e}):u.createElement(L,{ref:t,...e}))}),M=Object.assign(A,{Child:N,Root:A})},20044:function(e,t,n){n.d(t,{G:function(){return l}});var r=n(2265),o=n(55205);function l(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},10641:function(e,t,n){n.d(t,{z:function(){return l}});var r=n(2265),o=n(85235);let l=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},42219:function(e,t,n){n.d(t,{t:function(){return l}});var r=n(2265),o=n(39790);function l(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},39790:function(e,t,n){n.d(t,{e:function(){return l}});var r=n(2265),o=n(41879);let l=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},85235:function(e,t,n){n.d(t,{E:function(){return l}});var r=n(2265),o=n(39790);function l(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},92144:function(e,t,n){n.d(t,{H:function(){return i}});var r,o=n(2265),l=n(41879);function i(){let e;let t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,u]=o.useState(l.O.isHandoffComplete);return i&&!1===l.O.isHandoffComplete&&u(!1),o.useEffect(()=>{!0!==i&&u(!0)},[i]),o.useEffect(()=>l.O.handoff(),[]),!t&&i}},36601:function(e,t,n){n.d(t,{T:function(){return u},h:function(){return i}});var r=n(2265),o=n(10641);let l=Symbol();function i(e,t=!0){return Object.assign(e,{[l]:t})}function u(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[l]))?void 0:n}},88358:function(e,t,n){n.d(t,{ZM:function(){return i},oJ:function(){return u},up:function(){return a},uu:function(){return s}});var r,o=n(2265);let l=(0,o.createContext)(null);l.displayName="OpenClosedContext";var i=((r=i||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function u(){return(0,o.useContext)(l)}function a({value:e,children:t}){return o.createElement(l.Provider,{value:e},t)}function s({children:e}){return o.createElement(l.Provider,{value:null},e)}},82466:function(e,t,n){n.d(t,{A:function(){return r}});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},55205:function(e,t,n){n.d(t,{k:function(){return function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}}});var r=n(21210)},41879:function(e,t,n){n.d(t,{O:function(){return u}});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let u=new i},72640:function(e,t,n){n.d(t,{E:function(){return r}});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}},21210:function(e,t,n){n.d(t,{Y:function(){return r}});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},18318:function(e,t,n){n.d(t,{L6:function(){return c},VN:function(){return a},l4:function(){return s},oA:function(){return v},yV:function(){return m}});var r,o,l=n(2265),i=n(82466),u=n(72640),a=((r=a||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c(){let e,t;let n=(e=(0,l.useRef)([]),t=(0,l.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),(...n)=>{if(!n.every(e=>null==e))return e.current=n,t});return(0,l.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=null!=a?a:f;let s=p(t,e);if(l)return d(s,n,r,i,a);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,i,a)}if(1&c){let{unmount:e=!0,...t}=s;return(0,u.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,i,a)})}return d(s,n,r,i,a)})({mergeRefs:n,...e}),[n])}function d(e,t={},n,r,o){let{as:u=n,children:a,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof a?a(t):a;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let m={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(m["data-headlessui-state"]=n.join(" "),n))m[`data-${e}`]=""}if(u===l.Fragment&&(Object.keys(v(c)).length>0||Object.keys(v(m)).length>0)){if(!(0,l.isValidElement)(f)||Array.isArray(f)&&f.length>1){if(Object.keys(v(c)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(v(c)).concat(Object.keys(v(m))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{let e=f.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>(0,i.A)(t(...e),c.className):(0,i.A)(t,c.className),r=p(f.props,v(h(c,["ref"])));for(let e in m)e in r&&delete m[e];return(0,l.cloneElement)(f,Object.assign({},r,m,d,{ref:o(l.version.split(".")[0]>="19"?f.props.ref:f.ref,d.ref)},n?{className:n}:{}))}}return(0,l.createElement)(u,Object.assign({},h(c,["ref"]),u!==l.Fragment&&d,u!==l.Fragment&&m),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function p(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let e in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(n[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function m(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function h(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}}}]);