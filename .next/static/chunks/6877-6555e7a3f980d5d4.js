"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6877],{15671:function(e,t,r){r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return n},aY:function(){return u},ll:function(){return l}});var a=r(57437),s=r(2265),o=r(22169);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},96146:function(e,t,r){r.d(t,{SX:function(){return n},TK:function(){return o}});var a=r(57437);r(2265),r(77625),r(15671);var s=r(79580);let o=e=>{let{size:t="md",className:r=""}=e;return(0,a.jsx)(s.Z,{className:"animate-spin ".concat({sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[t]," ").concat(r)})},n=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,a.jsx)(o,{size:"lg"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]})}},77625:function(e,t,r){r.d(t,{Od:function(){return o},hM:function(){return i},q4:function(){return n}});var a=r(57437),s=r(22169);function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...r})}let n=e=>{let{className:t}=e;return(0,a.jsxs)("div",{className:(0,s.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o,{className:"h-4 w-3/4"}),(0,a.jsx)(o,{className:"h-4 w-1/2"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o,{className:"h-3 w-full"}),(0,a.jsx)(o,{className:"h-3 w-full"}),(0,a.jsx)(o,{className:"h-3 w-2/3"})]})]})},i=e=>{let{rows:t=5,columns:r=4,className:n}=e;return(0,a.jsx)("div",{className:(0,s.cn)("space-y-4",n),children:(0,a.jsxs)("div",{className:"border rounded-lg",children:[(0,a.jsx)("div",{className:"border-b p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,a.jsx)(o,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(r,", 1fr)")},children:Array.from({length:r}).map((e,t)=>(0,a.jsx)(o,{className:"h-4 w-full"},t))})},t))]})})}},90773:function(e,t,r){r.d(t,{i:function(){return o}});var a=r(74921),s=r(48763);let o={login:async e=>{console.log("\uD83D\uDD10 Attempting login via proxy...");try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Login failed")}let r=await t.json();console.log("✅ Login successful via proxy");let a=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.accessToken)}});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to fetch profile")}return{user:await a.json(),accessToken:r.accessToken,refreshToken:r.refreshToken}}catch(t){if(console.error("❌ Login failed via proxy:",t.message),(t.message.includes("fetch")||t.message.includes("network"))&&(console.warn("⚠️ Network error, using mock data"),"admin"===e.username&&"admin123456"===e.password)){let e={user:{id:1,username:"admin",email:"<EMAIL>",fullName:"System Administrator",role:"admin",isActive:!0,lastLoginAt:new Date().toISOString(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now()};return await new Promise(e=>setTimeout(e,500)),e}throw t}},logout:async e=>{let t=await fetch("/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Logout failed");return await t.json()},logoutFromAllDevices:async()=>await a.x.post("/system-auth/logout-all"),refreshToken:async e=>{let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Token refresh failed");return await t.json()},getProfile:async()=>{let e=s.t.getState(),t=e.accessToken,r=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}});if(!r.ok){if(401===r.status)throw console.warn("⚠️ Token expired, forcing logout..."),e.clearAuth(),window.location.href="/auth/login",Error("Token expired, please login again");throw Error((await r.json()).message||"Failed to fetch profile")}return await r.json()},updateProfile:async e=>await a.x.put("/system-auth/profile",e),changePassword:async e=>await a.x.post("/system-auth/change-password",e),createUser:async e=>await a.x.post("/system-auth/users",e),updateUser:async(e,t)=>await a.x.put("/system-auth/users/".concat(e),t)}},74921:function(e,t,r){r.d(t,{x:function(){return n}});var a=r(73107),s=r(48763);class o{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){if(this.isRefreshing)return new Promise((e,t)=>{this.failedQueue.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),this.client(r))).catch(e=>Promise.reject(e));r._retry=!0,this.isRefreshing=!0;try{let t=await this.refreshToken();if(t)return this.processQueue(null,t),r.headers.Authorization="Bearer ".concat(t),this.client(r);return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}catch(e){return this.processQueue(e,null),this.handleUnauthorized(),Promise.reject(e)}finally{this.isRefreshing=!1}}return Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}async refreshToken(){let e=s.t.getState(),t=e.refreshToken;if(!t)return console.warn("⚠️ No refresh token available"),null;try{console.log("\uD83D\uDD04 Attempting to refresh token...");let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:t})});if(!r.ok)throw Error("Token refresh failed");let{accessToken:a}=await r.json(),s=e.user;if(s)return e.setAuth(s,a,t),this.setAuthToken(a),console.log("✅ Token refreshed successfully"),a}catch(e){console.error("❌ Token refresh failed:",e)}return null}processQueue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.failedQueue.forEach(r=>{let{resolve:a,reject:s}=r;e?s(e):a(t)}),this.failedQueue=[]}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.isRefreshing=!1,this.failedQueue=[],this.baseURL="",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with relative path for proxy")}}let n=new o},27786:function(e,t,r){r.d(t,{a:function(){return c}});var a=r(64095),s=r(8186),o=r(31346),n=r(90773),i=r(48763),l=r(74921);let c=()=>{let e=(0,a.NL)(),{setAuth:t,clearAuth:r,setLoading:c,user:u,isAuthenticated:d}=(0,i.t)(),h=(0,s.D)({mutationFn:n.i.login,onMutate:()=>{c(!0)},onSuccess:r=>{t(r.user,r.accessToken,r.refreshToken),l.x.setAuthToken(r.accessToken),e.invalidateQueries({queryKey:["auth","profile"]})},onError:e=>{console.error("Login failed:",e),c(!1)}}),f=(0,s.D)({mutationFn:e=>n.i.logout(e),onSuccess:()=>{r(),l.x.removeAuthToken(),e.clear()},onError:()=>{r(),l.x.removeAuthToken(),e.clear()}}),m=(0,s.D)({mutationFn:n.i.logoutFromAllDevices,onSuccess:()=>{r(),l.x.removeAuthToken(),e.clear()}}),g=(0,o.a)({queryKey:["auth","profile"],queryFn:n.i.getProfile,enabled:d,staleTime:6e5}),p=(0,s.D)({mutationFn:e=>n.i.updateProfile(e),onSuccess:t=>{e.setQueryData(["auth","profile"],t),i.t.getState().updateUser(t)}}),w=(0,s.D)({mutationFn:n.i.changePassword,onSuccess:()=>{}}),y=(0,s.D)({mutationFn:e=>n.i.refreshToken(e),onSuccess:e=>{let r=i.t.getState().user,a=i.t.getState().refreshToken;r&&a&&(t(r,e.accessToken,a),l.x.setAuthToken(e.accessToken))},onError:()=>{r(),l.x.removeAuthToken(),e.clear()}});return{user:u,isAuthenticated:d,isLoading:(0,i.t)(e=>e.isLoading),profile:g.data,isProfileLoading:g.isLoading,profileError:g.error,login:h.mutate,logout:e=>f.mutate(e),logoutAll:m.mutate,updateProfile:p.mutate,changePassword:w.mutate,refreshToken:y.mutate,isLoginLoading:h.isLoading,loginError:h.error,isLogoutLoading:f.isLoading,isUpdateProfileLoading:p.isLoading,updateProfileError:p.error,isChangePasswordLoading:w.isLoading,changePasswordError:w.error}}},48763:function(e,t,r){r.d(t,{t:function(){return n}});var a=r(12574),s=r(65249);let o={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},n=(0,a.U)()((0,s.tJ)((e,t)=>({...o,setAuth:(t,r,a)=>{e({user:t,accessToken:r,refreshToken:a,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(o)},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let a=t().user;a&&e({user:{...a,...r}})},hasPermission:e=>{let r=t().user;if(!r)return!1;let a=Array.isArray(e)?e:[e];return"admin"===r.role||(a.includes("editor")?["admin","editor"].includes(r.role):a.includes("moderator")?["admin","editor","moderator"].includes(r.role):a.includes(r.role))}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},22169:function(e,t,r){r.d(t,{cn:function(){return o}});var a=r(75504),s=r(51367);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}}}]);