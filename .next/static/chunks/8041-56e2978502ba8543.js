"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8041],{37501:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(57977).Z)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},24602:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(2265),o=n(29586),a=n(57437),u=r.forwardRef((e,t)=>(0,a.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));u.displayName="Label";var i=u},12642:function(e,t,n){n.d(t,{z:function(){return u}});var r=n(2265),o=n(61266),a=n(32618),u=e=>{var t,n;let u,l;let{present:s,children:c}=e,d=function(e){var t,n;let[o,u]=r.useState(),l=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=i(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=i(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,u(e)},[])}}(s),f="function"==typeof c?c({present:d.isPresent}):r.Children.only(c),p=(0,o.e)(d.ref,(u=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in u&&u.isReactWarning?f.ref:(u=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||d.isPresent?r.cloneElement(f,{ref:p}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},68928:function(e,t,n){n.d(t,{ck:function(){return U},fC:function(){return S},z$:function(){return _}});var r=n(2265),o=n(44991),a=n(61266),u=n(84104),i=n(29586),l=n(23715),s=n(9310),c=n(12275),d=n(76769),f=n(65030),p=n(12642),v=n(57437),m="Radio",[w,y]=(0,u.b)(m),[b,g]=w(m),h=r.forwardRef((e,t)=>{let{__scopeRadio:n,name:u,checked:l=!1,required:s,disabled:c,value:d="on",onCheck:f,form:p,...m}=e,[w,y]=r.useState(null),g=(0,a.e)(t,e=>y(e)),h=r.useRef(!1),R=!w||p||!!w.closest("form");return(0,v.jsxs)(b,{scope:n,checked:l,disabled:c,children:[(0,v.jsx)(i.WV.button,{type:"button",role:"radio","aria-checked":l,"data-state":k(l),"data-disabled":c?"":void 0,disabled:c,value:d,...m,ref:g,onClick:(0,o.M)(e.onClick,e=>{l||null==f||f(),R&&(h.current=e.isPropagationStopped(),h.current||e.stopPropagation())})}),R&&(0,v.jsx)(E,{control:w,bubbles:!h.current,name:u,value:d,checked:l,required:s,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});h.displayName=m;var R="RadioIndicator",N=r.forwardRef((e,t)=>{let{__scopeRadio:n,forceMount:r,...o}=e,a=g(R,n);return(0,v.jsx)(p.z,{present:r||a.checked,children:(0,v.jsx)(i.WV.span,{"data-state":k(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});N.displayName=R;var E=r.forwardRef((e,t)=>{let{__scopeRadio:n,control:o,checked:u,bubbles:l=!0,...s}=e,c=r.useRef(null),p=(0,a.e)(c,t),m=(0,f.D)(u),w=(0,d.t)(o);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==u&&t){let n=new Event("click",{bubbles:l});t.call(e,u),e.dispatchEvent(n)}},[m,u,l]),(0,v.jsx)(i.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:u,...s,tabIndex:-1,ref:p,style:{...s.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}E.displayName="RadioBubbleInput";var M=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],x="RadioGroup",[I,T]=(0,u.b)(x,[l.Pc,y]),A=(0,l.Pc)(),D=y(),[j,C]=I(x),F=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,name:r,defaultValue:o,value:a,required:u=!1,disabled:d=!1,orientation:f,dir:p,loop:m=!0,onValueChange:w,...y}=e,b=A(n),g=(0,c.gm)(p),[h,R]=(0,s.T)({prop:a,defaultProp:null!=o?o:null,onChange:w,caller:x});return(0,v.jsx)(j,{scope:n,name:r,required:u,disabled:d,value:h,onValueChange:R,children:(0,v.jsx)(l.fC,{asChild:!0,...b,orientation:f,dir:g,loop:m,children:(0,v.jsx)(i.WV.div,{role:"radiogroup","aria-required":u,"aria-orientation":f,"data-disabled":d?"":void 0,dir:g,...y,ref:t})})})});F.displayName=x;var O="RadioGroupItem",L=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,disabled:u,...i}=e,s=C(O,n),c=s.disabled||u,d=A(n),f=D(n),p=r.useRef(null),m=(0,a.e)(t,p),w=s.value===i.value,y=r.useRef(!1);return r.useEffect(()=>{let e=e=>{M.includes(e.key)&&(y.current=!0)},t=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(l.ck,{asChild:!0,...d,focusable:!c,active:w,children:(0,v.jsx)(h,{disabled:c,required:s.required,checked:w,...f,...i,name:s.name,ref:m,onCheck:()=>s.onValueChange(i.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(i.onFocus,()=>{var e;y.current&&(null===(e=p.current)||void 0===e||e.click())})})})});L.displayName=O;var P=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,...r}=e,o=D(n);return(0,v.jsx)(N,{...o,...r,ref:t})});P.displayName="RadioGroupIndicator";var S=F,U=L,_=P},23715:function(e,t,n){n.d(t,{Pc:function(){return R},ck:function(){return j},fC:function(){return D}});var r=n(2265),o=n(44991),a=n(5528),u=n(61266),i=n(84104),l=n(38687),s=n(29586),c=n(39830),d=n(9310),f=n(12275),p=n(57437),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[y,b,g]=(0,a.B)(w),[h,R]=(0,i.b)(w,[g]),[N,E]=h(w),k=r.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(M,{...e,ref:t})})}));k.displayName=w;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:h,onEntryFocus:R,preventScrollOnEntryFocus:E=!1,...k}=e,M=r.useRef(null),x=(0,u.e)(t,M),I=(0,f.gm)(l),[T,D]=(0,d.T)({prop:y,defaultProp:null!=g?g:null,onChange:h,caller:w}),[j,C]=r.useState(!1),F=(0,c.W)(R),O=b(n),L=r.useRef(!1),[P,S]=r.useState(0);return r.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(v,F),()=>e.removeEventListener(v,F)},[F]),(0,p.jsx)(N,{scope:n,orientation:a,dir:I,loop:i,currentTabStopId:T,onItemFocus:r.useCallback(e=>D(e),[D]),onItemShiftTab:r.useCallback(()=>C(!0),[]),onFocusableItemAdd:r.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>S(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:j||0===P?-1:0,"data-orientation":a,...k,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),E)}}L.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>C(!1))})})}),x="RovingFocusGroupItem",I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:i,children:c,...d}=e,f=(0,l.M)(),v=i||f,m=E(x,n),w=m.currentTabStopId===v,g=b(n),{onFocusableItemAdd:h,onFocusableItemRemove:R,currentTabStopId:N}=m;return r.useEffect(()=>{if(a)return h(),()=>R()},[a,h,R]),(0,p.jsx)(y.ItemSlot,{scope:n,id:v,focusable:a,active:u,children:(0,p.jsx)(s.WV.span,{tabIndex:w?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=m.loop?(n=o,r=a+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(a+1)}setTimeout(()=>A(o))}}),children:"function"==typeof c?c({isCurrentTabStop:w,hasTabStop:null!=N}):c})})});I.displayName=x;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var D=k,j=I}}]);