"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1564],{78789:function(t,e,a){a.d(e,{Z:function(){return c}});var o=a(57437),r=a(575),s=a(53879),n=a(40834),i=a(47907);let c=t=>{let{variant:e,fixtureId:a,onRefresh:c,isLoading:l=!1,className:u=""}=t,f=(0,i.useRouter)();return(0,o.jsxs)("div",{className:"flex items-center space-x-4 ".concat(u),children:[(0,o.jsxs)(r.z,{variant:"outline",onClick:()=>{switch(e){case"detail":case"create":f.push("/dashboard/fixtures");break;case"edit":a?f.push("/dashboard/fixtures/".concat(a)):f.back();break;default:f.back()}},disabled:l,children:[(0,o.jsx)(s.Z,{className:"mr-2 h-4 w-4"}),(()=>{switch(e){case"detail":case"create":return"Back to Fixtures";case"edit":return"Back to Detail";default:return"Back"}})()]}),"detail"===e&&c&&(0,o.jsxs)(r.z,{variant:"outline",onClick:c,disabled:l,children:[(0,o.jsx)(n.Z,{className:"mr-2 h-4 w-4 ".concat(l?"animate-spin":"")}),"Refresh"]})]})}},575:function(t,e,a){a.d(e,{d:function(){return c},z:function(){return l}});var o=a(57437),r=a(2265),s=a(59143),n=a(49769),i=a(22169);let c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef((t,e)=>{let{className:a,variant:r,size:n,asChild:l=!1,...u}=t,f=l?s.g7:"button";return(0,o.jsx)(f,{className:(0,i.cn)(c({variant:r,size:n,className:a})),ref:e,...u})});l.displayName="Button"},2975:function(t,e,a){a.d(e,{L:function(){return r}});var o=a(74921);let r={getFixtures:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,o]=t;void 0!==o&&e.append(a,o.toString())});let a=await fetch("/api/fixtures?".concat(e.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async t=>{let e=await fetch("/api/fixtures/".concat(t),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to fetch fixture: ".concat(e.statusText));return await e.json()},getUpcomingAndLive:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,o]=t;void 0!==o&&e.append(a,o.toString())});let a=await fetch("/api/fixtures/live?".concat(e.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(e).forEach(t=>{let[e,o]=t;void 0!==o&&a.append(e,o.toString())}),await o.x.get("/football/fixtures/schedules/".concat(t,"?").concat(a.toString()))},getFixtureStatistics:async t=>await o.x.get("/football/fixtures/statistics/".concat(t)),triggerSeasonSync:async()=>{let t=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Season sync - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Season sync - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Season sync - No token found!"),t})();console.log("\uD83D\uDD04 Season sync request via proxy");let e=await fetch("/api/fixtures/sync",{method:"POST",headers:t,body:JSON.stringify({type:"season"})});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Season sync failed:",e.status,e.statusText,t),Error(t.message||"Failed to trigger season sync: ".concat(e.statusText))}let a=await e.json();return console.log("✅ Season sync successful"),a},triggerDailySync:async()=>{let t=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Daily sync - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Daily sync - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Daily sync - No token found!"),t})();console.log("\uD83D\uDD04 Daily sync request via proxy");let e=await fetch("/api/fixtures/sync",{method:"POST",headers:t,body:JSON.stringify({type:"daily"})});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Daily sync failed:",e.status,e.statusText,t),Error(t.message||"Failed to trigger daily sync: ".concat(e.statusText))}let a=await e.json();return console.log("✅ Daily sync successful"),a},getSyncStatus:async()=>{let t=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Sync status - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Sync status - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Sync status - No token found!"),t})();console.log("\uD83D\uDD04 Sync status request via proxy");let e=await fetch("/api/fixtures/sync",{method:"GET",headers:t});if(!e.ok){let t=await e.json().catch(()=>({}));throw console.error("❌ Sync status failed:",e.status,e.statusText,t),Error(t.message||"Failed to get sync status: ".concat(e.statusText))}let a=await e.json();return console.log("✅ Sync status successful"),a},createFixture:async t=>{var e;let a=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Create fixture - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Create fixture - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Create fixture - No token found!"),t})();console.log("\uD83D\uDD04 Create fixture request:",{hasAuth:!!a.Authorization,data:t});let o=await fetch("/api/fixtures",{method:"POST",headers:a,body:JSON.stringify(t)});if(!o.ok){let t=await o.json().catch(()=>({}));throw console.error("❌ Create fixture failed:",o.status,o.statusText,t),Error(t.message||"Failed to create fixture: ".concat(o.statusText))}let r=await o.json();return console.log("✅ Create fixture successful:",null===(e=r.data)||void 0===e?void 0:e.id),r.data||r},updateFixture:async(t,e)=>{let a=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Update fixture - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Update fixture - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Update fixture - No token found!"),t})();console.log("\uD83D\uDD04 Update fixture request:",{externalId:t,hasAuth:!!a.Authorization,data:e});let o=await fetch("/api/fixtures/".concat(t),{method:"PUT",headers:a,body:JSON.stringify(e)});if(!o.ok){let t=await o.json().catch(()=>({}));throw console.error("❌ Update fixture failed:",o.status,o.statusText,t),Error(t.message||"Failed to update fixture: ".concat(o.statusText))}let r=await o.json();return console.log("✅ Update fixture successful:",t),r.data||r},deleteFixture:async t=>{let e=(()=>{let t={"Content-Type":"application/json"};{try{let a=localStorage.getItem("auth-storage");if(a){var e;let o=JSON.parse(a),r=null===(e=o.state)||void 0===e?void 0:e.accessToken;if(r)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",r.substring(0,20)+"..."),t.Authorization="Bearer ".concat(r),t}}catch(t){console.warn("Failed to parse auth storage:",t)}let a=localStorage.getItem("accessToken");if(a)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),t.Authorization="Bearer ".concat(a),t}return console.warn("❌ Delete fixture - No token found!"),t})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:t,hasAuth:!!e.Authorization});let a=await fetch("/api/fixtures/".concat(t),{method:"DELETE",headers:e});if(!a.ok){let t=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,t),Error(t.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",t)},getFixtureStatistics:async t=>{let e=await fetch("/api/fixtures/".concat(t,"/statistics"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to fetch fixture statistics: ".concat(e.statusText));return await e.json()},getFixtureEvents:async t=>{let e=await fetch("/api/fixtures/".concat(t,"/events"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to fetch fixture events: ".concat(e.statusText));return await e.json()},getFixture:async t=>(await r.getFixtureById(t)).data}}}]);